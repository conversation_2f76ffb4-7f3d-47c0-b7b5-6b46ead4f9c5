<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\Payment;
use App\Models\UserSubscription;
use App\Models\Refund;
use Laravel\Sanctum\Sanctum;

class RefundApiTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function authenticated_user_can_get_refund_statistics()
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        // Create test refunds
        Refund::factory()->create([
            'user_id' => $user->id,
            'amount' => 1000,
            'status' => 'succeeded',
            'type' => 'full',
        ]);

        $response = $this->getJson('/api/refunds/statistics');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'data' => [
                        'total_refunds',
                        'total_amount',
                        'successful_refunds',
                        'pending_refunds',
                        'full_refunds',
                        'partial_refunds',
                        'prorated_refunds',
                    ],
                    'message'
                ]);
    }

    /** @test */
    public function authenticated_user_can_get_their_refunds()
    {
        $user = User::factory()->create();
        $otherUser = User::factory()->create();
        Sanctum::actingAs($user);

        // Create refunds for both users
        Refund::factory()->create(['user_id' => $user->id]);
        Refund::factory()->create(['user_id' => $otherUser->id]);

        $response = $this->getJson('/api/refunds');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'data' => [
                        'current_page',
                        'data',
                        'total'
                    ],
                    'message'
                ]);

        // Should only return user's own refunds
        $this->assertEquals(1, $response->json('data.total'));
    }

    /** @test */
    public function admin_can_get_all_refunds()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $user = User::factory()->create();
        Sanctum::actingAs($admin);

        // Create refunds for different users
        Refund::factory()->create(['user_id' => $admin->id]);
        Refund::factory()->create(['user_id' => $user->id]);

        $response = $this->getJson('/api/admin/refunds');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'data' => [
                        'current_page',
                        'data',
                        'total'
                    ],
                    'message'
                ]);

        // Should return all refunds
        $this->assertEquals(2, $response->json('data.total'));
    }

    /** @test */
    public function admin_can_get_refund_statistics()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        Sanctum::actingAs($admin);

        // Create test refunds
        Refund::factory()->create([
            'amount' => 1000,
            'status' => 'succeeded',
            'type' => 'full',
            'reason' => 'requested_by_customer'
        ]);

        Refund::factory()->create([
            'amount' => 500,
            'status' => 'pending',
            'type' => 'partial',
            'reason' => 'duplicate'
        ]);

        $response = $this->getJson('/api/admin/refunds/statistics');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'data' => [
                        'total_refunds',
                        'total_amount',
                        'successful_refunds',
                        'pending_refunds',
                        'failed_refunds',
                        'by_type' => [
                            'full',
                            'partial',
                            'prorated'
                        ],
                        'by_reason',
                        'recent_refunds',
                        'formatted_total_amount'
                    ],
                    'message'
                ]);

        $data = $response->json('data');
        $this->assertEquals(2, $data['total_refunds']);
        $this->assertEquals(1, $data['successful_refunds']);
        $this->assertEquals(1, $data['pending_refunds']);
        $this->assertEquals(1, $data['by_type']['full']);
        $this->assertEquals(1, $data['by_type']['partial']);
    }

    /** @test */
    public function unauthenticated_user_cannot_access_refund_endpoints()
    {
        $response = $this->getJson('/api/refunds');
        $response->assertStatus(401);

        $response = $this->getJson('/api/refunds/statistics');
        $response->assertStatus(401);

        $response = $this->getJson('/api/admin/refunds');
        $response->assertStatus(401);
    }

    /** @test */
    public function non_admin_cannot_access_admin_refund_endpoints()
    {
        $user = User::factory()->create(['role' => 'user']);
        Sanctum::actingAs($user);

        $response = $this->getJson('/api/admin/refunds');
        $response->assertStatus(403);

        $response = $this->getJson('/api/admin/refunds/statistics');
        $response->assertStatus(403);
    }
}
