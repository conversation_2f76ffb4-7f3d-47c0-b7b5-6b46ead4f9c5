<?php

namespace Tests\Feature\Api;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class AuthenticationTest extends TestCase
{
    use RefreshDatabase;

    public $seed = true;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Ensure user role exists
        if (!Role::where('name', 'user')->where('guard_name', 'sanctum')->exists()) {
            Role::create(['name' => 'user', 'guard_name' => 'sanctum']);
        }
    }

    public function test_user_can_register_via_api()
    {
        $userData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'Password123!',
            'password_confirmation' => 'Password123!',
        ];

        $response = $this->postJson('/api/register', $userData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'message',
                    'data' => [
                        'id',
                        'name',
                        'email',
                        'is_email_verified',
                        'role',
                        'permissions',
                        'token',
                    ]
                ]);

        $this->assertDatabaseHas('users', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);
    }

    public function test_user_cannot_register_with_invalid_data()
    {
        $userData = [
            'name' => '',
            'email' => 'invalid-email',
            'password' => '123',
            'password_confirmation' => '456',
        ];

        $response = $this->postJson('/api/register', $userData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name', 'email', 'password']);
    }

    public function test_user_can_login_via_api()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);

        $loginData = [
            'email' => '<EMAIL>',
            'password' => 'password',
        ];

        $response = $this->postJson('/api/login', $loginData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'message',
                    'data' => [
                        'id',
                        'name',
                        'email',
                        'token',
                    ]
                ]);
    }

    public function test_user_cannot_login_with_invalid_credentials()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);

        $loginData = [
            'email' => '<EMAIL>',
            'password' => 'wrong-password',
        ];

        $response = $this->postJson('/api/login', $loginData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email']);
    }

    public function test_authenticated_user_can_get_user_data()
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $response = $this->getJson('/api/user');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'message',
                    'data' => [
                        'id',
                        'name',
                        'email',
                    ]
                ]);
    }

    public function test_unauthenticated_user_cannot_get_user_data()
    {
        $response = $this->getJson('/api/user');

        $response->assertStatus(401);
    }

    public function test_authenticated_user_can_logout()
    {
        $user = User::factory()->create();
        $token = $user->createToken('test-token');
        
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token->plainTextToken,
        ])->postJson('/api/logout');

        $response->assertStatus(200)
                ->assertJson([
                    'message' => 'User logout successfully'
                ]);

        // Verify token is deleted
        $this->assertDatabaseMissing('personal_access_tokens', [
            'id' => $token->accessToken->id,
        ]);
    }

    public function test_user_can_request_password_reset()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $response = $this->postJson('/api/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(200);
    }

    public function test_user_cannot_request_password_reset_with_invalid_email()
    {
        $response = $this->postJson('/api/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email']);
    }

    public function test_authenticated_user_can_resend_verification_email()
    {
        $user = User::factory()->create([
            'email_verified_at' => null,
        ]);
        Sanctum::actingAs($user);

        $response = $this->postJson('/api/resend-verification');

        $response->assertStatus(200)
                ->assertJson([
                    'message' => 'Verification link sent'
                ]);
    }

    public function test_verified_user_cannot_resend_verification_email()
    {
        $user = User::factory()->create([
            'email_verified_at' => now(),
        ]);
        Sanctum::actingAs($user);

        $response = $this->postJson('/api/resend-verification');

        $response->assertStatus(200)
                ->assertJson([
                    'message' => 'Already verified'
                ]);
    }

    public function test_api_rate_limiting_works()
    {
        // Test rate limiting on login endpoint
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);

        $loginData = [
            'email' => '<EMAIL>',
            'password' => 'wrong-password',
        ];

        // Make 6 failed attempts (rate limit is 5)
        for ($i = 0; $i < 6; $i++) {
            $response = $this->postJson('/api/login', $loginData);
        }

        // The 6th attempt should be rate limited
        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email']);
        
        $this->assertStringContainsString('Too many login attempts', $response->json('errors.email.0'));
    }
}
