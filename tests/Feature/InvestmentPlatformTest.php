<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Category;
use App\Models\InvestorProfile;
use App\Models\StartupProfile;
use App\Models\EsgQuestion;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class InvestmentPlatformTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Run seeders
        $this->artisan('db:seed', ['--class' => 'InvestmentPlatformRolesSeeder']);
        $this->artisan('db:seed', ['--class' => 'CategoriesSeeder']);
        $this->artisan('db:seed', ['--class' => 'EsgQuestionsSeeder']);
    }

    public function test_categories_can_be_retrieved(): void
    {
        $user = User::factory()->withoutRole()->create();
        $user->assignRole('investor');
        Sanctum::actingAs($user);

        $response = $this->getJson('/api/investment/categories');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'description',
                            'type',
                            'is_active',
                            'sort_order'
                        ]
                    ]
                ]);
    }

    public function test_investor_can_create_profile(): void
    {
        $user = User::factory()->withoutRole()->create();
        $user->assignRole('investor');
        Sanctum::actingAs($user);

        $categories = Category::take(3)->pluck('id')->toArray();

        $profileData = [
            'investment_budget_min' => 10000,
            'investment_budget_max' => 100000,
            'risk_tolerance' => 'medium',
            'investment_experience' => 'intermediate',
            'bio' => 'Experienced investor looking for sustainable startups',
            'website' => 'https://example.com',
            'linkedin' => 'https://linkedin.com/in/investor',
            'investment_preferences' => ['sustainability', 'technology'],
            'categories' => $categories,
        ];

        $response = $this->postJson('/api/investment/investor-profiles', $profileData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'id',
                        'user_id',
                        'investment_budget_min',
                        'investment_budget_max',
                        'risk_tolerance',
                        'investment_experience',
                        'profile_completed'
                    ]
                ]);

        $this->assertDatabaseHas('investor_profiles', [
            'user_id' => $user->id,
            'investment_budget_min' => 10000,
            'investment_budget_max' => 100000,
        ]);
    }

    public function test_startup_can_create_profile(): void
    {
        $user = User::factory()->withoutRole()->create();
        $user->assignRole('startup');
        Sanctum::actingAs($user);

        $categories = Category::take(2)->pluck('id')->toArray();

        $profileData = [
            'company_name' => 'Green Tech Solutions',
            'company_description' => 'Sustainable technology solutions for businesses',
            'founding_date' => '2023-01-01',
            'employee_count' => 15,
            'website' => 'https://greentech.example.com',
            'linkedin' => 'https://linkedin.com/company/greentech',
            'funding_stage' => 'seed',
            'funding_amount_sought' => 500000,
            'current_valuation' => 2000000,
            'business_model' => ['B2B', 'SaaS'],
            'categories' => $categories,
        ];

        $response = $this->postJson('/api/investment/startup-profiles', $profileData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'id',
                        'user_id',
                        'company_name',
                        'company_description',
                        'funding_stage',
                        'funding_amount_sought',
                        'profile_completed'
                    ]
                ]);

        $this->assertDatabaseHas('startup_profiles', [
            'user_id' => $user->id,
            'company_name' => 'Green Tech Solutions',
            'funding_amount_sought' => 500000,
        ]);
    }

    public function test_startup_can_submit_esg_questionnaire(): void
    {
        $user = User::factory()->withoutRole()->create();
        $user->assignRole('startup');
        Sanctum::actingAs($user);

        // Create startup profile first
        $startupProfile = StartupProfile::create([
            'user_id' => $user->id,
            'company_name' => 'Test Company',
            'company_description' => 'Test Description',
            'founding_date' => '2023-01-01',
            'employee_count' => 10,
            'funding_stage' => 'seed',
            'funding_amount_sought' => 100000,
            'profile_completed' => true,
        ]);

        $questions = EsgQuestion::active()->take(3)->get();
        $responses = $questions->map(function ($question) {
            return [
                'question_id' => $question->id,
                'response_value' => $question->type === 'yes_no' ? 'Yes' : '4',
            ];
        })->toArray();

        $response = $this->postJson('/api/investment/esg/responses', [
            'responses' => $responses
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'esg_score',
                        'esg_breakdown'
                    ]
                ]);

        $this->assertDatabaseHas('startup_profiles', [
            'user_id' => $user->id,
            'esg_completed' => true,
        ]);
    }

    public function test_investor_can_discover_startups(): void
    {
        // Create investor
        $investor = User::factory()->withoutRole()->create();
        $investor->assignRole('investor');

        $investorProfile = InvestorProfile::create([
            'user_id' => $investor->id,
            'investment_budget_min' => 50000,
            'investment_budget_max' => 500000,
            'risk_tolerance' => 'medium',
            'investment_experience' => 'intermediate',
            'profile_completed' => true,
        ]);

        // Create startup
        $startup = User::factory()->withoutRole()->create();
        $startup->assignRole('startup');

        $startupProfile = StartupProfile::create([
            'user_id' => $startup->id,
            'company_name' => 'Discoverable Startup',
            'company_description' => 'A great startup',
            'founding_date' => '2023-01-01',
            'employee_count' => 5,
            'funding_stage' => 'seed',
            'funding_amount_sought' => 100000,
            'esg_score' => 75.5,
            'esg_completed' => true,
            'profile_completed' => true,
        ]);

        Sanctum::actingAs($investor);

        $response = $this->getJson('/api/investment/discovery/startups');

        if ($response->status() !== 200) {
            dump($response->getContent());
        }

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'data' => [
                            '*' => [
                                'id',
                                'company_name',
                                'funding_amount_sought',
                                'esg_score',
                                'match_score',
                                'user'
                            ]
                        ]
                    ]
                ]);
    }
}
