<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\RefundService;
use App\Services\StripeService;
use App\Models\User;
use App\Models\UserSubscription;
use App\Models\Payment;
use App\Models\Refund;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;

class RefundServiceTest extends TestCase
{
    use RefreshDatabase;

    protected RefundService $refundService;
    protected $stripeServiceMock;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock StripeService
        $this->stripeServiceMock = Mockery::mock(StripeService::class);
        $this->app->instance(StripeService::class, $this->stripeServiceMock);

        $this->refundService = new RefundService($this->stripeServiceMock);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function it_calculates_prorated_refund_correctly()
    {
        // Create test data
        $user = User::factory()->create();
        $subscription = UserSubscription::factory()->create([
            'user_id' => $user->id,
            'status' => 'active',
            'current_period_start' => Carbon::now()->subDays(10),
            'current_period_end' => Carbon::now()->addDays(20),
        ]);

        $payment = Payment::factory()->create([
            'user_id' => $user->id,
            'user_subscription_id' => $subscription->id,
            'amount' => 3000, // $30.00
            'status' => 'succeeded',
        ]);

        // Calculate prorated refund for cancellation after 10 days of 30-day period
        $cancellationDate = Carbon::now();
        $calculation = $this->refundService->calculateProratedRefund($subscription, $cancellationDate);

        // Assertions
        $this->assertEquals(3000, $calculation['original_amount']);
        $this->assertEquals(30, $calculation['total_days']);
        $this->assertEquals(10, $calculation['days_used']);
        $this->assertEquals(20, $calculation['unused_days']);
        $this->assertEquals(2000, $calculation['prorated_amount']); // 20/30 * 3000
        $this->assertEquals(66.67, round($calculation['proration_percentage'], 2));
    }

    /** @test */
    public function it_throws_exception_when_no_payment_found_for_subscription()
    {
        $user = User::factory()->create();
        $subscription = UserSubscription::factory()->create([
            'user_id' => $user->id,
            'status' => 'active',
            'current_period_start' => Carbon::now()->subDays(10),
            'current_period_end' => Carbon::now()->addDays(20),
        ]);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('No successful payment found for subscription');

        $this->refundService->calculateProratedRefund($subscription);
    }

    /** @test */
    public function it_returns_zero_refund_when_service_period_fully_used()
    {
        $user = User::factory()->create();
        $subscription = UserSubscription::factory()->create([
            'user_id' => $user->id,
            'status' => 'active',
            'current_period_start' => Carbon::now()->subDays(30),
            'current_period_end' => Carbon::now(),
        ]);

        $payment = Payment::factory()->create([
            'user_id' => $user->id,
            'user_subscription_id' => $subscription->id,
            'amount' => 3000,
            'status' => 'succeeded',
        ]);

        // Cancellation at end of period
        $cancellationDate = Carbon::now();
        $calculation = $this->refundService->calculateProratedRefund($subscription, $cancellationDate);

        $this->assertEquals(0, $calculation['prorated_amount']);
        $this->assertEquals(0, $calculation['unused_days']);
    }

    /** @test */
    public function it_gets_refund_statistics_correctly()
    {
        $user = User::factory()->create();

        // Create test refunds
        Refund::factory()->create([
            'user_id' => $user->id,
            'amount' => 1000,
            'status' => 'succeeded',
            'type' => 'full',
        ]);

        Refund::factory()->create([
            'user_id' => $user->id,
            'amount' => 500,
            'status' => 'succeeded',
            'type' => 'partial',
        ]);

        Refund::factory()->create([
            'user_id' => $user->id,
            'amount' => 2000,
            'status' => 'pending',
            'type' => 'prorated',
        ]);

        $statistics = $this->refundService->getRefundStatistics($user);

        $this->assertEquals(3, $statistics['total_refunds']);
        $this->assertEquals(1500, $statistics['total_amount']); // Only successful refunds
        $this->assertEquals(2, $statistics['successful_refunds']);
        $this->assertEquals(1, $statistics['pending_refunds']);
        $this->assertEquals(1, $statistics['full_refunds']);
        $this->assertEquals(1, $statistics['partial_refunds']);
        $this->assertEquals(1, $statistics['prorated_refunds']);
    }

    /** @test */
    public function it_generates_correct_audit_trail()
    {
        $user = User::factory()->create();
        $admin = User::factory()->create();

        $refund = Refund::factory()->create([
            'user_id' => $user->id,
            'amount' => 1000,
            'type' => 'full',
            'reason' => 'requested_by_customer',
            'status' => 'succeeded',
            'processed_by' => $admin->id,
            'proration_details' => ['test' => 'data'],
            'metadata' => ['stripe_data' => 'test'],
        ]);

        $auditTrail = $this->refundService->getRefundAuditTrail($refund);

        $this->assertEquals($refund->id, $auditTrail['refund_id']);
        $this->assertEquals($refund->stripe_refund_id, $auditTrail['stripe_refund_id']);
        $this->assertEquals(1000, $auditTrail['amount']);
        $this->assertEquals('full', $auditTrail['type']);
        $this->assertEquals('requested_by_customer', $auditTrail['reason']);
        $this->assertEquals('succeeded', $auditTrail['status']);
        $this->assertEquals(['test' => 'data'], $auditTrail['proration_details']);
        $this->assertEquals(['stripe_data' => 'test'], $auditTrail['metadata']);
    }
}
