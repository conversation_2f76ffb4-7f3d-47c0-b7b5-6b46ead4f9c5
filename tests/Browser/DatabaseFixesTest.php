<?php

namespace Tests\Browser;

use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskTestCase;
use App\Models\User;
use App\Models\SubscriptionProduct;

class DatabaseFixesTest extends DuskTestCase
{
    /**
     * Test that payment method analytics page loads without database errors
     */
    public function test_payment_method_analytics_loads_successfully()
    {
        $this->browse(function (Browser $browser) {
            // Login as super admin
            $superAdmin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($superAdmin)
                    ->visit('/admin/payment-methods/analytics')
                    ->waitFor('body', 10)
                    ->assertSee('Payment Method Analytics')
                    ->assertDontSee('SQLSTATE')
                    ->assertDontSee('Column not found')
                    ->assertDontSee('payment_method_id');
        });
    }

    /**
     * Test price consistency between admin panel and API
     */
    public function test_price_consistency_between_admin_and_api()
    {
        $this->browse(function (Browser $browser) {
            // Login as super admin
            $superAdmin = User::where('email', '<EMAIL>')->first();
            
            // Check admin panel pricing
            $browser->loginAs($superAdmin)
                    ->visit('/admin/products')
                    ->waitFor('body', 10);
            
            // Get the price from admin panel
            $adminPriceText = $browser->text('[data-testid="product-price"]');
            
            // Extract numeric value from admin price (e.g., "$10.00" -> 10.00)
            preg_match('/\$([0-9,]+\.?[0-9]*)/', $adminPriceText, $matches);
            $adminPrice = isset($matches[1]) ? (float) str_replace(',', '', $matches[1]) : 0;
            
            // Now check API response
            $browser->visit('/api/subscription-products/public')
                    ->waitFor('body', 5);
            
            $apiResponse = json_decode($browser->text('body'), true);
            $apiPrice = $apiResponse['data'][0]['price'] ?? 0;
            
            // Prices should match (within 0.01 tolerance for floating point)
            $this->assertEqualsWithDelta($adminPrice, $apiPrice, 0.01, 
                "Admin price ($adminPrice) should match API price ($apiPrice)");
        });
    }

    /**
     * Test React frontend pricing matches backend
     */
    public function test_react_frontend_pricing_consistency()
    {
        // First get the price from Laravel API
        $products = SubscriptionProduct::active()->get();
        $expectedPrice = $products->first()->price ?? 0;
        
        $this->browse(function (Browser $browser) use ($expectedPrice) {
            // Visit React frontend subscription plans
            $browser->visit('http://localhost:5175/app/subscription/plans')
                    ->waitFor('[data-testid="subscription-plan"]', 10)
                    ->assertSee('$' . number_format($expectedPrice, 2));
        });
    }

    /**
     * Test cross-browser compatibility for analytics page
     */
    public function test_analytics_cross_browser_compatibility()
    {
        $browsers = ['chrome', 'firefox', 'safari', 'edge'];
        
        foreach ($browsers as $browserName) {
            $this->browse(function (Browser $browser) use ($browserName) {
                $superAdmin = User::where('email', '<EMAIL>')->first();
                
                $browser->driver->manage()->window()->setSize(new \Facebook\WebDriver\WebDriverDimension(1280, 1024));
                
                $browser->loginAs($superAdmin)
                        ->visit('/admin/payment-methods/analytics')
                        ->waitFor('body', 10)
                        ->assertSee('Payment Method Analytics')
                        ->assertDontSee('SQLSTATE');
            });
        }
    }

    /**
     * Test responsive design for analytics page
     */
    public function test_analytics_responsive_design()
    {
        $breakpoints = [
            'mobile' => [375, 667],
            'tablet' => [768, 1024], 
            'desktop' => [1280, 1024]
        ];
        
        $this->browse(function (Browser $browser) use ($breakpoints) {
            $superAdmin = User::where('email', '<EMAIL>')->first();
            
            foreach ($breakpoints as $device => $dimensions) {
                $browser->driver->manage()->window()->setSize(
                    new \Facebook\WebDriver\WebDriverDimension($dimensions[0], $dimensions[1])
                );
                
                $browser->loginAs($superAdmin)
                        ->visit('/admin/payment-methods/analytics')
                        ->waitFor('body', 10)
                        ->assertSee('Payment Method Analytics');
            }
        });
    }

    /**
     * Test React frontend responsive pricing display
     */
    public function test_react_pricing_responsive_design()
    {
        $breakpoints = [
            'mobile' => [375, 667],
            'tablet' => [768, 1024],
            'desktop' => [1280, 1024]
        ];
        
        $this->browse(function (Browser $browser) use ($breakpoints) {
            foreach ($breakpoints as $device => $dimensions) {
                $browser->driver->manage()->window()->setSize(
                    new \Facebook\WebDriver\WebDriverDimension($dimensions[0], $dimensions[1])
                );
                
                $browser->visit('http://localhost:5175/app/subscription/plans')
                        ->waitFor('[data-testid="subscription-plan"]', 10)
                        ->assertSee('$10.00'); // Should show correct price
            }
        });
    }

    /**
     * Test admin products page shows correct pricing
     */
    public function test_admin_products_correct_pricing()
    {
        $this->browse(function (Browser $browser) {
            $superAdmin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($superAdmin)
                    ->visit('/admin/products')
                    ->waitFor('body', 10)
                    ->assertSee('$10.00') // Should show correct price, not $1,000.00
                    ->assertDontSee('$1,000.00'); // Should not show incorrect price
        });
    }

    /**
     * Test that payment methods analytics shows proper data structure
     */
    public function test_payment_methods_analytics_data_structure()
    {
        $this->browse(function (Browser $browser) {
            $superAdmin = User::where('email', '<EMAIL>')->first();
            
            $browser->loginAs($superAdmin)
                    ->visit('/admin/payment-methods/analytics')
                    ->waitFor('body', 10)
                    ->assertSee('Payment Method Analytics')
                    ->assertSee('Analytics') // Should show analytics content
                    ->assertDontSee('Error') // Should not show any errors
                    ->assertDontSee('Exception'); // Should not show any exceptions
        });
    }
}
