<?php

namespace Tests\Browser;

use App\Models\User;
use App\Models\StartupProfile;
use App\Models\InvestorProfile;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

class InvestorDiscoveryTest extends DuskTestCase
{
    use DatabaseTransactions;

    protected $startupUser;
    protected $investorUsers;

    protected function setUp(): void
    {
        parent::setUp();
        $this->createTestData();
    }

    protected function createTestData()
    {
        // Create test startup user
        $this->startupUser = User::factory()->create([
            'name' => 'Test Startup User',
            'email' => '<EMAIL>',
            'role' => 'startup',
            'email_verified_at' => now(),
            'city' => 'San Francisco',
            'country' => 'United States',
        ]);

        // Create startup profile
        $startupProfile = StartupProfile::create([
            'user_id' => $this->startupUser->id,
            'company_name' => 'Test Startup Inc',
            'company_description' => 'A revolutionary startup focused on solving real-world problems.',
            'founding_date' => '2023-01-15',
            'employee_count' => 8,
            'website' => 'https://teststartup.com',
            'funding_stage' => 'seed',
            'funding_amount_sought' => 500000,
            'current_valuation' => 2000000,
            'business_model' => 'B2B SaaS',
            'profile_completed' => true,
        ]);

        // Create test categories
        $categories = [
            Taxonomy::firstOrCreate(['name' => 'FinTech', 'type' => 'category']),
            Taxonomy::firstOrCreate(['name' => 'AI/ML', 'type' => 'category']),
        ];

        // Attach categories to startup profile
        $startupProfile->taxonomies()->attach($categories[0]->id);

        // Create test investor users
        $this->investorUsers = collect();
        
        $investorData = [
            [
                'name' => 'Sarah Johnson',
                'email' => '<EMAIL>',
                'budget_min' => 50000,
                'budget_max' => 500000,
                'experience' => 'experienced',
                'risk' => 'medium',
                'bio' => 'Experienced angel investor with 15+ years in tech startups.',
                'categories' => ['FinTech'],
            ],
            [
                'name' => 'Michael Chen',
                'email' => '<EMAIL>',
                'budget_min' => 100000,
                'budget_max' => 1000000,
                'experience' => 'expert',
                'risk' => 'high',
                'bio' => 'Venture capitalist focused on AI and sustainability.',
                'categories' => ['AI/ML'],
            ],
            [
                'name' => 'Emma Rodriguez',
                'email' => '<EMAIL>',
                'budget_min' => 25000,
                'budget_max' => 250000,
                'experience' => 'intermediate',
                'risk' => 'low',
                'bio' => 'Impact investor specializing in education and healthcare.',
                'categories' => ['FinTech'],
            ],
        ];

        foreach ($investorData as $data) {
            $investor = User::factory()->create([
                'name' => $data['name'],
                'email' => $data['email'],
                'role' => 'investor',
                'email_verified_at' => now(),
                'city' => 'San Francisco',
                'country' => 'United States',
            ]);

            InvestorProfile::create([
                'user_id' => $investor->id,
                'investment_budget_min' => $data['budget_min'],
                'investment_budget_max' => $data['budget_max'],
                'investment_experience' => $data['experience'],
                'risk_tolerance' => $data['risk'],
                'bio' => $data['bio'],
                'profile_completed' => true,
            ]);

            // Attach categories to investor
            $categoryIds = Taxonomy::whereIn('name', $data['categories'])
                ->where('type', 'category')
                ->pluck('id');
            $investor->taxonomies()->attach($categoryIds);

            $this->investorUsers->push($investor);
        }
    }

    /**
     * Test investor discovery page loads correctly
     */
    public function test_investor_discovery_page_loads()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->startupUser)
                    ->visit('/startup/investor-discovery')
                    ->waitFor('[data-testid="investor-discovery-page"]', 10)
                    ->assertSee('Discover Investors')
                    ->assertSee('Search Investors')
                    ->assertSee('Recommendations');
        });
    }

    /**
     * Test search filters functionality
     */
    public function test_search_filters_work()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->startupUser)
                    ->visit('/startup/investor-discovery')
                    ->waitFor('[data-testid="search-filters"]', 10)
                    
                    // Test budget filter
                    ->type('input[name="investment_min"]', '50000')
                    ->type('input[name="investment_max"]', '500000')
                    ->waitFor('[data-testid="investor-card"]', 5)
                    
                    // Test location filter
                    ->type('input[name="location"]', 'San Francisco')
                    ->waitFor('[data-testid="investor-card"]', 5)
                    
                    // Test experience filter
                    ->select('select[name="investment_experience"]', 'experienced')
                    ->waitFor('[data-testid="investor-card"]', 5)
                    
                    // Verify results are filtered
                    ->assertSee('Sarah Johnson');
        });
    }

    /**
     * Test recommendations tab
     */
    public function test_recommendations_tab()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->startupUser)
                    ->visit('/startup/investor-discovery')
                    ->waitFor('[data-testid="recommendations-tab"]', 10)
                    ->click('[data-testid="recommendations-tab"]')
                    ->waitFor('[data-testid="recommendations-content"]', 5)
                    ->assertSee('Personalized Recommendations')
                    ->assertSee('These investors are recommended based on your startup profile');
        });
    }

    /**
     * Test sending interest request
     */
    public function test_send_interest_request()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->startupUser)
                    ->visit('/startup/investor-discovery')
                    ->waitFor('[data-testid="investor-card"]', 10)
                    
                    // Click on first interest request button
                    ->click('[data-testid="send-interest-request"]:first-child')
                    ->waitFor('[data-testid="request-sent-indicator"]', 10)
                    ->assertSee('Request Sent');
        });
    }

    /**
     * Test investor card displays correct information
     */
    public function test_investor_card_displays_correctly()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->startupUser)
                    ->visit('/startup/investor-discovery')
                    ->waitFor('[data-testid="investor-card"]', 10)
                    
                    // Check investor information is displayed
                    ->assertSee('Sarah Johnson')
                    ->assertSee('$50,000 - $500,000')
                    ->assertSee('Experienced')
                    ->assertSee('Medium Risk')
                    ->assertSee('FinTech');
        });
    }

    /**
     * Test pagination works
     */
    public function test_pagination_works()
    {
        // Create more investors to test pagination
        for ($i = 0; $i < 25; $i++) {
            $investor = User::factory()->create([
                'name' => "Test Investor {$i}",
                'email' => "investor{$i}@example.com",
                'role' => 'investor',
                'email_verified_at' => now(),
            ]);

            InvestorProfile::create([
                'user_id' => $investor->id,
                'investment_budget_min' => 10000,
                'investment_budget_max' => 100000,
                'investment_experience' => 'beginner',
                'risk_tolerance' => 'low',
                'profile_completed' => true,
            ]);
        }

        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->startupUser)
                    ->visit('/startup/investor-discovery')
                    ->waitFor('[data-testid="pagination"]', 10)
                    ->assertSee('Page 1 of')
                    ->click('[data-testid="next-page"]')
                    ->waitFor('[data-testid="investor-card"]', 5)
                    ->assertSee('Page 2 of');
        });
    }

    /**
     * Test responsive design on mobile
     */
    public function test_responsive_design_mobile()
    {
        $this->browse(function (Browser $browser) {
            $browser->resize(375, 667) // iPhone SE size
                    ->loginAs($this->startupUser)
                    ->visit('/startup/investor-discovery')
                    ->waitFor('[data-testid="investor-discovery-page"]', 10)
                    ->assertSee('Discover Investors')
                    
                    // Check that filters are collapsible on mobile
                    ->assertPresent('[data-testid="mobile-filter-toggle"]')
                    ->click('[data-testid="mobile-filter-toggle"]')
                    ->waitFor('[data-testid="search-filters"]', 5);
        });
    }

    /**
     * Test responsive design on tablet
     */
    public function test_responsive_design_tablet()
    {
        $this->browse(function (Browser $browser) {
            $browser->resize(768, 1024) // iPad size
                    ->loginAs($this->startupUser)
                    ->visit('/startup/investor-discovery')
                    ->waitFor('[data-testid="investor-discovery-page"]', 10)
                    ->assertSee('Discover Investors')
                    ->assertPresent('[data-testid="search-filters"]')
                    ->assertPresent('[data-testid="investor-card"]');
        });
    }

    /**
     * Test responsive design on desktop
     */
    public function test_responsive_design_desktop()
    {
        $this->browse(function (Browser $browser) {
            $browser->resize(1280, 720) // Desktop size
                    ->loginAs($this->startupUser)
                    ->visit('/startup/investor-discovery')
                    ->waitFor('[data-testid="investor-discovery-page"]', 10)
                    ->assertSee('Discover Investors')
                    ->assertPresent('[data-testid="search-filters"]')
                    ->assertPresent('[data-testid="investor-card"]');
        });
    }

    /**
     * Test error handling when no profile exists
     */
    public function test_error_handling_no_profile()
    {
        $userWithoutProfile = User::factory()->create([
            'role' => 'startup',
            'email_verified_at' => now(),
        ]);

        $this->browse(function (Browser $browser) use ($userWithoutProfile) {
            $browser->loginAs($userWithoutProfile)
                    ->visit('/startup/investor-discovery')
                    ->waitFor('[data-testid="profile-required-message"]', 10)
                    ->assertSee('Please complete your startup profile')
                    ->assertPresent('[data-testid="complete-profile-button"]');
        });
    }
}
