<?php

namespace Tests;

use App\Models\User;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;

abstract class TestCase extends BaseTestCase
{
    use CreatesApplication;

    public ?User $user = null;
    public ?User $admin = null;
    public ?User $superAdmin = null;

    protected function setUp(): void
    {
        parent::setUp();

        $this->withoutVite();

        // Only set up default users if they exist (for backward compatibility)
        $this->user = User::where('email', '<EMAIL>')->first();
        $this->admin = User::where('email', '<EMAIL>')->first();
        $this->superAdmin = User::where('email', '<EMAIL>')->first();
    }


}
