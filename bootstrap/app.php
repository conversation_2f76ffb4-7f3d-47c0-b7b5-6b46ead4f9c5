<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->booting(function ($app) {
        // Manually register core Laravel service providers that are not being auto-discovered

        // Core Foundation Services
        $app->register(\Illuminate\Foundation\Providers\FoundationServiceProvider::class);
        $app->register(\Illuminate\Filesystem\FilesystemServiceProvider::class);
        $app->register(\Illuminate\Cache\CacheServiceProvider::class);
        $app->register(\Illuminate\Session\SessionServiceProvider::class);
        $app->register(\Illuminate\View\ViewServiceProvider::class);
        $app->register(\Illuminate\Encryption\EncryptionServiceProvider::class);
        $app->register(\Illuminate\Cookie\CookieServiceProvider::class);
        $app->register(\Illuminate\Database\DatabaseServiceProvider::class);
        $app->register(\Illuminate\Translation\TranslationServiceProvider::class);
        $app->register(\Illuminate\Validation\ValidationServiceProvider::class);
        $app->register(\Illuminate\Routing\RoutingServiceProvider::class);
        $app->register(\Illuminate\Pagination\PaginationServiceProvider::class);

        // Event and Bus Services
        $app->register(\Illuminate\Events\EventServiceProvider::class);
        $app->register(\Illuminate\Bus\BusServiceProvider::class);
        $app->register(\Illuminate\Pipeline\PipelineServiceProvider::class);

        // Notification and Mail Services
        $app->register(\Illuminate\Notifications\NotificationServiceProvider::class);
        $app->register(\Illuminate\Mail\MailServiceProvider::class);

        // Broadcasting Service
        $app->register(\Illuminate\Broadcasting\BroadcastServiceProvider::class);

        // Authentication and Authorization Services
        $app->register(\Illuminate\Auth\AuthServiceProvider::class);
        $app->register(\Illuminate\Auth\Passwords\PasswordResetServiceProvider::class);
        $app->register(\Illuminate\Hashing\HashServiceProvider::class);

        // Database and Migration Services
        $app->register(\Illuminate\Database\MigrationServiceProvider::class);
        $app->register(\Illuminate\Foundation\Providers\ConsoleSupportServiceProvider::class);
        $app->register(\Illuminate\Queue\QueueServiceProvider::class);

        // Logging Service
        $app->register(\Illuminate\Log\LogServiceProvider::class);

        // Redis Service (if needed)
        $app->register(\Illuminate\Redis\RedisServiceProvider::class);

        // Third-party Service Providers
        $app->register(\Spatie\LaravelSettings\LaravelSettingsServiceProvider::class);
        $app->register(\Laravolt\Avatar\ServiceProvider::class,);

        // Application Service Providers
        $app->register(\App\Providers\AppServiceProvider::class);
        $app->register(\App\Providers\RouteServiceProvider::class);
        $app->register(\App\Providers\StripeServiceProvider::class);

        // Laravel Sanctum
        $app->register(\Laravel\Sanctum\SanctumServiceProvider::class);



        // Register maintenance mode service provider
        $app->singleton(\Illuminate\Contracts\Foundation\MaintenanceMode::class, \Illuminate\Foundation\FileBasedMaintenanceMode::class);
    })
    ->withMiddleware(function (Middleware $middleware) {
        // Global middleware
        $middleware->use([
            \App\Http\Middleware\TrustProxies::class,
            \Illuminate\Http\Middleware\HandleCors::class,
            \App\Http\Middleware\PreventRequestsDuringMaintenance::class,
            \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
            \App\Http\Middleware\TrimStrings::class,
            \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
        ]);

        // Web middleware group
        $middleware->web(append: [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\SetLocale::class,
        ]);

        // API middleware group
        $middleware->api(append: [
            \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
            'throttle:api',
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\JsonResponse::class,
        ]);

        // Route middleware aliases
        $middleware->alias([
            'auth' => \App\Http\Middleware\Authenticate::class,
            'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
            'auth.session' => \Illuminate\Session\Middleware\AuthenticateSession::class,
            'cache.headers' => \Illuminate\Http\Middleware\SetCacheHeaders::class,
            'can' => \Illuminate\Auth\Middleware\Authorize::class,
            'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
            'password.confirm' => \Illuminate\Auth\Middleware\RequirePassword::class,
            'signed' => \App\Http\Middleware\ValidateSignature::class,
            'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
            'verified' => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,
            'role' => \App\Http\Middleware\RoleMiddleware::class,
            'check.account.locked' => \App\Http\Middleware\CheckAccountLocked::class,
            'check.account.status' => \App\Http\Middleware\CheckAccountStatus::class,
            'investment.role' => \App\Http\Middleware\InvestmentPlatformRole::class,
            'subscription.access' => \App\Http\Middleware\CheckSubscriptionAccess::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })
    ->booted(function () {
        // Configure rate limiting
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });
    })
    ->create();
