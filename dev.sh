#!/bin/bash

# Check if required programs are installed
# npm install -g nodemon && brew install gsed && brew install jq
command -v nodemon >/dev/null 2>&1 || { echo >&2 "nodemon is required but not installed. Aborting."; exit 1; }
command -v gsed >/dev/null 2>&1 || { echo >&2 "gsed is required but not installed. Aborting."; exit 1; }
command -v jq >/dev/null 2>&1 || { echo >&2 "jq is required but not installed. Aborting."; exit 1; }

# Function to handle SIGINT signal
cleanup() {
    echo "Cleaning up.."
    APP_URL="http://impactintels.test"
    update_app_url
    kill $(jobs -p) >/dev/null 2>&1
    pkill -f "ngrok" >/dev/null 2>&1
    exit 0
}

update_app_url() {
    gsed -i -r "s#^APP_URL=.*#APP_URL=$APP_URL#" .env
    echo "APP_URL is set: $APP_URL"
}

#echo "running trap cleanup SIGINT"

# Register signal handler
trap cleanup SIGINT

#echo "Ngrok share"
#valet share > storage/logs/valet.log 2>&1 &
ngrok http impactintels.test --host-header=rewrite > storage/logs/valet.log 2>&1 &

#echo "attempt to get share url"
attempt=1

while [ $attempt -le 5 ]; do
    APP_URL=$(curl http://127.0.0.1:4040/api/tunnels 2>/dev/null | jq '.tunnels[] | select(.proto=="https") | .public_url' | tr -d '"')
    if [[ $APP_URL == http* ]]; then
        update_app_url
        break
    fi
    sleep 1
    ((attempt++))
done

# This script is used to start the development environment
#echo "Starting NPM.."
npm run dev > storage/logs/npm.log 2>&1 &
#nodemon --exec "php artisan queue:work" -e php --ignore storage/ --ignore bootstrap/cache/ --ignore public/ --signal SIGHUP > storage/logs/queue.log 2>&1 &
nodemon --exec "php artisan horizon" -e php --ignore storage/ --ignore bootstrap/cache/ --ignore public/ --signal SIGHUP > storage/logs/queue.log 2>&1 &

echo "------------------------------------"
echo "Queue: $(pwd)/storage/logs/queue.log"
echo "Laravel: $(pwd)/storage/logs/laravel.log"
echo "NPM : $(pwd)/storage/logs/npm.log"
echo "Ngrok: http://127.0.0.1:4040"
echo "Dev environment is running at.. http://impactintels.test Press Ctrl+C to exit."
open "http://impactintels.test"
echo "------------------------------------"

if [[ $APP_URL != http* ]]; then
    echo "Unable to set valid APP_URL"
    exit 1
fi

tail -f $(pwd)/storage/logs/laravel.log

# Wait for background processes to complete
wait
