# Stripe Webhook System Documentation

## Overview

The Stripe webhook system in your Laravel + React investment platform serves as the critical bridge between <PERSON>e's payment processing and your local database. It ensures that your application stays synchronized with payment and subscription events that occur in Stripe.

## Webhook Endpoint

- **URL**: `POST /api/stripe/webhook`
- **Controller**: `App\Http\Controllers\Api\StripeWebhookController@handleWebhook`
- **Authentication**: None required (Stripe signature verification used instead)
- **Location in routes**: `routes/api.php` line 232

## When Stripe Calls the Webhook

Stripe automatically sends HTTP POST requests to your webhook endpoint when specific events occur in your Stripe account. This happens in real-time, typically within seconds of the event occurring.

## Events Handled by Your Webhook

### 1. Payment Intent Events

#### `payment_intent.succeeded`
- **When triggered**: A payment is successfully processed
- **What it does**: Updates local payment records to 'succeeded' status and sends success notifications
- **Integration**: Updates `Payment` model status and triggers user notifications

#### `payment_intent.payment_failed`
- **When triggered**: A payment attempt fails
- **What it does**: Updates payment status to 'failed' and sends failure notifications
- **Integration**: Handles retry logic and user notifications for failed payments

#### `payment_intent.requires_action`
- **When triggered**: Payment requires additional authentication (3D Secure)
- **What it does**: Updates payment status and provides 3D Secure URL for user authentication
- **Integration**: Guides users through additional authentication steps

### 2. Subscription Events

#### `customer.subscription.created`
- **When triggered**: A new subscription is successfully created in Stripe
- **What it does**: Updates local `UserSubscription` record with Stripe subscription details
- **Integration**: Synchronizes subscription status, billing periods, and metadata

#### `customer.subscription.updated`
- **When triggered**: Subscription details change (plan upgrade/downgrade, status changes)
- **What it does**: Updates local subscription record with new details
- **Integration**: Handles plan changes, billing cycle updates, and status modifications

#### `customer.subscription.deleted`
- **When triggered**: A subscription is cancelled or expires
- **What it does**: Updates local subscription status to cancelled
- **Integration**: Triggers account access restrictions and cleanup processes

### 3. Invoice Events

#### `invoice.created`
- **When triggered**: Stripe generates a new invoice for a subscription
- **What it does**: Creates local invoice record for tracking
- **Integration**: Generates PDF invoices and prepares for payment processing

#### `invoice.finalized`
- **When triggered**: Invoice is finalized and ready for payment
- **What it does**: Updates invoice status and prepares for collection
- **Integration**: Triggers invoice email notifications to users

#### `invoice.payment_succeeded`
- **When triggered**: Invoice payment is successfully processed
- **What it does**: Updates subscription status to 'active' and extends billing period
- **Integration**: Ensures continued service access and updates billing records

#### `invoice.payment_failed`
- **When triggered**: Invoice payment fails (card declined, insufficient funds, etc.)
- **What it does**: Updates subscription to 'past_due' and triggers retry logic
- **Integration**: Implements dunning management and account restrictions

## Security Features

### Signature Verification
The webhook verifies each request using Stripe's signature verification:
```php
$event = Webhook::constructEvent($payload, $sigHeader, $endpointSecret);
```

### Configuration Required
Set your webhook secret in `.env`:
```env
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
```

## How to Configure in Stripe Dashboard

1. **Login to Stripe Dashboard**
   - Go to https://dashboard.stripe.com
   - Navigate to "Developers" → "Webhooks"

2. **Add Endpoint**
   - Click "Add endpoint"
   - Enter your webhook URL: `https://yourdomain.com/api/stripe/webhook`
   - Select the events you want to listen for (see list above)

3. **Get Webhook Secret**
   - After creating the endpoint, click on it
   - Copy the "Signing secret" (starts with `whsec_`)
   - Add it to your `.env` file as `STRIPE_WEBHOOK_SECRET`

## Integration with Subscription System

### Subscription Lifecycle
1. **User subscribes** → React app creates Stripe Checkout Session
2. **Payment succeeds** → `payment_intent.succeeded` webhook updates payment status
3. **Subscription created** → `customer.subscription.created` webhook activates user access
4. **Monthly billing** → `invoice.payment_succeeded` webhook extends subscription
5. **Payment fails** → `invoice.payment_failed` webhook triggers retry logic
6. **Subscription cancelled** → `customer.subscription.deleted` webhook restricts access

### Database Synchronization
The webhook ensures your local database stays in sync with Stripe by:
- Updating `UserSubscription` records with current status
- Creating `Payment` records for transaction tracking
- Generating `Invoice` records for billing history
- Triggering email notifications for important events

### Error Handling
- All webhook events are logged for debugging
- Failed webhook processing doesn't affect Stripe operations
- Retry logic handles temporary failures
- Unhandled events are logged but don't cause errors

## Monitoring and Debugging

### Logs
Check Laravel logs for webhook activity:
```bash
tail -f storage/logs/laravel.log | grep "Stripe webhook"
```

### Stripe Dashboard
Monitor webhook delivery in Stripe Dashboard:
- "Developers" → "Webhooks" → Your endpoint → "Attempts"

### Common Issues
1. **Webhook secret mismatch**: Verify `STRIPE_WEBHOOK_SECRET` in `.env`
2. **SSL certificate issues**: Ensure your domain has valid SSL
3. **Timeout errors**: Webhook processing should complete within 30 seconds
4. **Duplicate events**: Stripe may send duplicate events; implement idempotency

## Testing

### Stripe CLI
Use Stripe CLI to test webhooks locally:
```bash
stripe listen --forward-to localhost:8000/api/stripe/webhook
```

### Test Events
Trigger test events in Stripe Dashboard:
- "Developers" → "Webhooks" → Your endpoint → "Send test webhook"

This webhook system is essential for maintaining data consistency between Stripe and your application, ensuring users have seamless access to subscription features based on their payment status.
