/*! Zabuto Calendar - v2.1.0 - 2022-12-29
* https://github.com/zabuto/calendar
* Copyright (c) 2022 <PERSON><PERSON>; Licensed MIT */

!function(a){!function(a,b,c,d){"use strict";function e(b,c){this.element=b,this._name=f,this._defaults=a.fn[f].defaults,this.settings=a.extend({},this._defaults,c),null!==this.settings.translation?this.settings.language=null:this.settings.language=this.settings.language.toLowerCase(),this.init()}var f="zabuto_calendar",g=new Date;a.fn[f]=function(b){var c;if(b!==d){var g=a.makeArray(arguments);c=g.slice(1)}return this.each(function(){var d=a.data(this,"plugin_"+f);d?"string"==typeof b&&"function"==typeof d[b]&&d[b].apply(d,c):a.data(this,"plugin_"+f,new e(this,b))})},a.fn[f].defaults={year:g.getFullYear(),month:g.getMonth()+1,language:"en",translation:null,week_starts:"monday",show_days:!0,classname:null,header_format:"[month] [year]",date_format:"y-m-d",navigation_prev:!0,navigation_next:!0,navigation_markup:{prev:"&#9668;",next:"&#9658;"},today_markup:null,events:null,ajax:null},a.fn[f].languages={},a.extend(e.prototype,{init:function(){var b=a.Event("zabuto:calendar:init");b.settings=this.settings,a(this.element).trigger(b),this.goto(this.settings.year,this.settings.month)},destroy:function(){var b=a(this.element);b.removeData("plugin_"+f),b.removeData("year"),b.removeData("month"),b.removeData("event-data"),b.empty(),b.trigger("zabuto:calendar:destroy")},reload:function(){var b=a(this.element),c=a.Event("zabuto:calendar:reload");c.year=b.data("year"),c.month=b.data("month"),b.trigger(c),this.data()},goto:function(b,c){if(!1!==this._isValidDate(b,c,1)){var d=a.Event("zabuto:calendar:goto");d.year=b,d.month=c;var e=a(this.element);e.data("year",b),e.data("month",c),e.trigger(d),this.data()}},data:function(){var b=this,c=a(this.element),d=b._getEventHandle();if(null===d)c.data("event-data",[]),this.render();else if("fixed"===d.type){var e=b._eventsToDays(d.data),f=a.Event("zabuto:calendar:data");f.type="fixed",f.eventlist=d.data,f.eventdata=e,c.data("event-data",e),c.trigger(f),b.render()}else if("ajax"===d.type){var g=d.settings;g.data={year:c.data("year"),month:c.data("month")},g.dataType="json",a.ajax(g).done(function(d){var e=b._eventsToDays(d),f=a.Event("zabuto:calendar:data");f.type="ajax",f.eventlist=d,f.eventdata=e,c.data("event-data",e),c.trigger(f),b.render()}).fail(function(d,e,f){var g=a.Event("zabuto:calendar:data-fail");g.text=e,g.error=f,c.data("event-data",[]),c.trigger(g),b.render()})}},render:function(){var b=a(this.element),c=b.data("year"),d=b.data("month"),e=a.Event("zabuto:calendar:preRender");e.year=c,e.month=d,b.trigger(e),b.empty(),this._isValidDate(c,d,1)&&b.append(this._renderTable(c,d));var f=a.Event("zabuto:calendar:render");f.year=c,f.month=d,b.trigger(f)},_renderTable:function(b,c){var d=a("<table></table>").addClass("zabuto-calendar");this.settings.classname&&d.addClass(this.settings.classname);var e=a("<thead></thead>");e.append(this._renderNavigation(b,c)),!0===this.settings.show_days&&e.append(this._renderDaysOfWeek());var f=this._renderDaysInMonth(b,c);return d.append(e),d.append(f),d},_renderNavigation:function(b,c){var d=this,e=d.settings.header_format;e=e.replace("[year]",b.toString());var f=d._getTranslation();if(null!==f&&"months"in f){var g=f.months;e=e.replace("[month]",g[c.toString()])}else e=e.replace("[month]",c.toString());var h=a("<tr></tr>").addClass("zabuto-calendar__navigation").attr("role","navigation"),i=d._calculatePrevious(b,c),j=d._calculateNext(b,c),k=a("<span></span>").text(e).data("to",{year:d.settings.year,month:d.settings.month});k.addClass("zabuto-calendar__navigation__item--header__title"),null===i&&null===j||k.on("zabuto:calendar:navigate-init",function(b){var c=a(this).data("to");b.year=c.year,b.month=c.month,d.goto(c.year,c.month)}).on("dblclick",function(){a(this).trigger("zabuto:calendar:navigate-init")});var l=a("<td></td>");return l.addClass("zabuto-calendar__navigation__item--header"),l.append(k),null===i&&null===j?h.append(l.attr("colspan",7)):(h.append(d._renderNavigationItem("prev",i)),h.append(l.attr("colspan",5)),h.append(d._renderNavigationItem("next",j))),h},_renderNavigationItem:function(b,c){var d=this;b=b.toString();var e=a("<td></td>").data("nav",b).data("to",c);return e.addClass("zabuto-calendar__navigation__item--"+b),null!==c&&(b in d.settings.navigation_markup?e.html(d.settings.navigation_markup[b]):e.html(b),e.on("zabuto:calendar:navigate",function(b){var c=a(this).data("to");b.year=c.year,b.month=c.month,d.goto(c.year,c.month)}).on("click",function(){a(this).trigger("zabuto:calendar:navigate")})),e},_renderDaysOfWeek:function(){var b=this.settings.week_starts,c={0:"0",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6"},d=this._getTranslation();null!==d&&"days"in d&&(c=d.days);var e=a("<tr></tr>").addClass("zabuto-calendar__days-of-week");return 0!==b&&"0"!==b&&"sunday"!==b||e.append(a("<th></th>").data("dow",0).text(c[0]).addClass("zabuto-calendar__days-of-week__item")),e.append(a("<th></th>").data("dow",1).text(c[1]).addClass("zabuto-calendar__days-of-week__item")),e.append(a("<th></th>").data("dow",2).text(c[2]).addClass("zabuto-calendar__days-of-week__item")),e.append(a("<th></th>").data("dow",3).text(c[3]).addClass("zabuto-calendar__days-of-week__item")),e.append(a("<th></th>").data("dow",4).text(c[4]).addClass("zabuto-calendar__days-of-week__item")),e.append(a("<th></th>").data("dow",5).text(c[5]).addClass("zabuto-calendar__days-of-week__item")),e.append(a("<th></th>").data("dow",6).text(c[6]).addClass("zabuto-calendar__days-of-week__item")),1!==b&&"1"!==b&&"monday"!==b||e.append(a("<th></th>").data("dow",0).text(c[0]).addClass("zabuto-calendar__days-of-week__item")),e},_renderDaysInMonth:function(b,c){var d=this,e=d.settings.week_starts,f=d._calculateWeeksInMonth(b,c),g=d._calculateLastDayOfMonth(b,c),h=d._calculateDayOfWeek(b,c,1),i=[0,1,2,3,4,5,6],j=h;1!==e&&"1"!==e&&"monday"!==e||(i=[1,2,3,4,5,6,7],j=0===h?7:h);for(var k=a("<tbody></tbody>"),l=1,m=1;m<=f;m++){var n=d._renderWeek(m,f);a.each(i,function(e,f){if(1===m&&f<j||l>g)n.append(a("<td></td>").addClass("zabuto-calendar__day--empty"));else{var h=d._renderDay(b,c,l,f);n.append(h),l++}}),k.append(n)}return k},_renderWeek:function(b,c){var d=a("<tr></tr>");return 1===b?d.addClass("zabuto-calendar__week--first"):b===c?d.addClass("zabuto-calendar__week--last"):d.addClass("zabuto-calendar__week"),d},_renderDay:function(b,c,d,e){var f=this._dateAsString(b,c,d),g=this._eventsForDay(f),h=a("<td></td>");if(h.data("date",f),h.data("year",b),h.data("month",c),h.data("day",d),h.data("dow",7===e?0:e),h.data("eventdata",g),this._isToday(b,c,d))if(h.data("today",1),h.addClass("zabuto-calendar__day--today"),this.settings.today_markup){var i=this.settings.today_markup;i=i.replace("[day]",d),h.html(i)}else h.text(d);else h.data("today",0),h.addClass("zabuto-calendar__day"),h.text(d);if(null!==g){if(h.data("hasEvent",1),h.addClass("zabuto-calendar__event"),a.each(g.classnames,function(a,b){h.addClass(b)}),null!==g.markup){var j=g.markup;j=j.replace("[day]",d),h.html(j)}}else h.data("hasEvent",0);return h.on("zabuto:calendar:day",function(b){b.element=a(this),b.date=new Date(a(this).data("year"),a(this).data("month")-1,a(this).data("day")),b.value=a(this).data("date"),b.today=!!a(this).data("today"),b.hasEvent=!!a(this).data("hasEvent"),b.eventdata=g}).on("click",function(){a(this).trigger("zabuto:calendar:day")}),h},_getTranslation:function(){var b=this.settings.translation;if(null!==b&&"object"==typeof b&&"months"in b&&"days"in b)return b;var c=this.settings.language,d=a.fn[f].languages;return c in d?d[c]:null},_calculateWeeksInMonth:function(a,b){var c=this.settings.week_starts,d=this._calculateLastDayOfMonth(a,b),e=this._calculateDayOfWeek(a,b,1),f=this._calculateDayOfWeek(a,b,d),g=e,h=f;1!==c&&"1"!==c&&"monday"!==c||(g=0===e?7:e,h=0===f?7:f);var i=g-h,j=d+i;return Math.ceil(j/7)},_calculateLastDayOfMonth:function(a,b){var c=b-1;return new Date(a,c+1,0).getDate()},_calculateDayOfWeek:function(a,b,c){var d=b-1;return new Date(a,d,c).getDay()},_calculatePrevious:function(a,b){if(!1===this.settings.navigation_prev)return null;var c=a,d=b-1;return 0===d&&(c=a-1,d=12),{year:c,month:d}},_calculateNext:function(a,b){if(!1===this.settings.navigation_next)return null;var c=a,d=b+1;return 13===d&&(c=a+1,d=1),{year:c,month:d}},_isValidDate:function(a,b,c){if(b<1||b>12)return!1;var d=b-1,e=new Date(a,d,c);return e.getFullYear()===a&&e.getMonth()===d&&e.getDate()===Number(c)},_isToday:function(a,b,c){var d=b-1,e=new Date;return new Date(a,d,c).toDateString()===e.toDateString()},_dateAsString:function(a,b,c){var d=this.settings.date_format;return c=c<10?"0"+c:c,b=b<10?"0"+b:b,d=d.replace("y",a),d=d.replace("m",b),d=d.replace("d",c)},_getEventHandle:function(){var a=this.settings.events;if(null!==a&&"object"==typeof a)return{type:"fixed",data:a};var b=this.settings.ajax;return null!==b?("string"==typeof b&&(b={type:"GET",url:b,cache:!1}),{type:"ajax",settings:b}):null},_eventsToDays:function(b){var c=[];return a.each(b,function(a,b){if("object"==typeof b&&"date"in b){var d=b.date,e={count:0,classnames:[],markup:null,events:[]};d in c&&(e=c[d]),e.count=e.count+1,e.events.push(b),"classname"in b&&null!==b.classname&&e.classnames.push(b.classname),"markup"in b&&null!==b.markup&&(e.markup=b.markup),c[d]=e}}),c},_eventsForDay:function(b){var c=a(this.element),d=c.data("event-data");if(!(b in d))return null;var e=d[b],f=a.Event("zabuto:calendar:day-event");return f.value=b,f.eventdata=e,c.trigger(f),e}})}(jQuery,window,document,void 0),a.fn.zabuto_calendar.languages=a.fn.zabuto_calendar.languages||{},a.fn.zabuto_calendar.languages.ar={months:{1:"يناير",2:"فبراير",3:"مارس",4:"أبريل",5:"مايو",6:"يونيو",7:"يوليو",8:"أغسطس",9:"سبتمبر",10:"أكتوبر",11:"نوفمبر",12:"ديسمبر"},days:{0:"أحد",1:"أثنين",2:"ثلاثاء",3:"اربعاء",4:"خميس",5:"جمعه",6:"سبت"}},a.fn.zabuto_calendar.languages.az={months:{1:"Yanvar",2:"Fevral",3:"Mart",4:"Aprel",5:"May",6:"İyun",7:"İyul",8:"Avqust",9:"Sentyabr",10:"Oktyabr",11:"Noyabr",12:"Dekabr"},days:{0:"Baz",1:"B.e",2:"Ç.A",3:"Çərş",4:"C.A",5:"Cümə",6:"Şən"}},a.fn.zabuto_calendar.languages.ca={months:{1:"Gener",2:"Febrer",3:"Març",4:"Abril",5:"Maig",6:"Juny",7:"Juliol",8:"Agost",9:"Setembre",10:"Octubre",11:"Novembre",12:"Desembre"},days:{0:"Dg",1:"Dl",2:"Dt",3:"Dc",4:"Dj",5:"Dv",6:"Ds"}},a.fn.zabuto_calendar.languages.cn={months:{1:"一月",2:"二月",3:"三月",4:"四月",5:"五月",6:"六月",7:"七月",8:"八月",9:"九月",10:"十月",11:"十一月",12:"十二月"},days:{0:"星期日",1:"星期一",2:"星期二",3:"星期三",4:"星期四",5:"星期五",6:"星期六"}},a.fn.zabuto_calendar.languages.cs={months:{1:"Leden",2:"Únor",3:"Březen",4:"Duben",5:"Květen",6:"Červen",7:"Červenec",8:"Srpen",9:"Září",10:"Říjen",11:"Listopad",12:"Prosinec"},days:{0:"Ne",1:"Po",2:"Út",3:"St",4:"Čt",5:"Pá",6:"So"}},a.fn.zabuto_calendar.languages.de={months:{1:"Januar",2:"Februar",3:"März",4:"April",5:"Mai",6:"Juni",7:"Juli",8:"August",9:"September",10:"Oktober",11:"November",12:"Dezember"},days:{0:"So",1:"Mo",2:"Di",3:"Mi",4:"Do",5:"Fr",6:"Sa"}},a.fn.zabuto_calendar.languages.en={months:{1:"January",2:"February",3:"March",4:"April",5:"May",6:"June",7:"July",8:"August",9:"September",10:"October",11:"November",12:"December"},days:{0:"Sun",1:"Mon",2:"Tue",3:"Wed",4:"Thu",5:"Fri",6:"Sat"}},a.fn.zabuto_calendar.languages.es={months:{1:"Enero",2:"Febrero",3:"Marzo",4:"Abril",5:"Mayo",6:"Junio",7:"Julio",8:"Agosto",9:"Septiembre",10:"Octubre",11:"Noviembre",12:"Diciembre"},days:{0:"Do",1:"Lu",2:"Ma",3:"Mi",4:"Ju",5:"Vi",6:"Sá"}},a.fn.zabuto_calendar.languages.fi={months:{1:"Tammikuu",2:"Helmikuu",3:"Maaliskuu",4:"Huhtikuu",5:"Toukokuu",6:"Kesäkuu",7:"Heinäkuu",8:"Elokuu",9:"Syyskuu",10:"Lokakuu",11:"Marraskuu",12:"Joulukuu"},days:{0:"Su",1:"Ma",2:"Ti",3:"Ke",4:"To",5:"Pe",6:"La"}},a.fn.zabuto_calendar.languages.fr={months:{1:"Janvier",2:"Février",3:"Mars",4:"Avril",5:"Mai",6:"Juin",7:"Juillet",8:"Août",9:"Septembre",10:"Octobre",11:"Novembre",12:"Décembre"},days:{0:"Dim",1:"Lun",2:"Mar",3:"Mer",4:"Jeu",5:"Ven",6:"Sam"}},a.fn.zabuto_calendar.languages.he={months:{1:"ינואר",2:"פברואר",3:"מרץ",4:"אפריל",5:"מאי",6:"יוני",7:"יולי",8:"אוגוסט",9:"ספטמבר",10:"אוקטובר",11:"נובמבר",12:"דצמבר"},days:{0:"א",1:"ב",2:"ג",3:"ד",4:"ה",5:"ו",6:"ש"}},a.fn.zabuto_calendar.languages.hu={months:{1:"Január",2:"Február",3:"Március",4:"Április",5:"Május",6:"Június",7:"Július",8:"Augusztus",9:"Szeptember",10:"Október",11:"November",12:"December"},days:{0:"Va",1:"Hé",2:"Ke",3:"Sze",4:"Cs",5:"Pé",6:"Szo"}},a.fn.zabuto_calendar.languages.id={months:{1:"Januari",2:"Februari",3:"Maret",4:"April",5:"Mei",6:"Juni",7:"Juli",8:"Agustus",9:"September",10:"Oktober",11:"November",12:"Desember"},days:{0:"Minggu",1:"Senin",2:"Selasa",3:"Rabu",4:"Kamis",5:"Jum'at",6:"Sabtu"}},a.fn.zabuto_calendar.languages.it={months:{1:"Gennaio",2:"Febbraio",3:"Marzo",4:"Aprile",5:"Maggio",6:"Giugno",7:"Luglio",8:"Agosto",9:"Settembre",10:"Ottobre",11:"Novembre",12:"Dicembre"},days:{0:"Dom",1:"Lun",2:"Mar",3:"Mer",4:"Gio",5:"Ven",6:"Sab"}},a.fn.zabuto_calendar.languages.jp={months:{1:"1月",2:"2月",3:"3月",4:"4月",5:"5月",6:"6月",7:"7月",8:"8月",9:"9月",10:"10月",11:"11月",12:"12月"},days:{0:"日",1:"月",2:"火",3:"水",4:"木",5:"金",6:"土"}},a.fn.zabuto_calendar.languages.kr={months:{1:"1월",2:"2월",3:"3월",4:"4월",5:"5월",6:"6월",7:"7월",8:"8월",9:"9월",10:"10월",11:"11월",12:"12월"},days:{0:"일",1:"월",2:"화",3:"수",4:"목",5:"금",6:"토"}},a.fn.zabuto_calendar.languages.nl={months:{1:"Januari",2:"Februari",3:"Maart",4:"April",5:"Mei",6:"Juni",7:"Juli",8:"Augustus",9:"September",10:"Oktober",11:"November",12:"December"},days:{0:"Zo",1:"Ma",2:"Di",3:"Wo",4:"Do",5:"Vr",6:"Za"}},a.fn.zabuto_calendar.languages.no={months:{1:"Januar",2:"Februar",3:"Mars",4:"April",5:"Mai",6:"Juni",7:"Juli",8:"August",9:"September",10:"Oktober",11:"November",12:"Desember"},days:{0:"Sø",1:"Ma",2:"Ti",3:"On",4:"To",5:"Fr",6:"Lø"}},a.fn.zabuto_calendar.languages.pl={months:{1:"Styczeń",2:"Luty",3:"Marzec",4:"Kwiecień",5:"Maj",6:"Czerwiec",7:"Lipiec",8:"Sierpień",9:"Wrzesień",10:"Październik",11:"Listopad",12:"Grudzień"},days:{0:"niedz.",1:"pon.",2:"wt.",3:"śr.",4:"czw.",5:"pt.",6:"sob."}},a.fn.zabuto_calendar.languages.pt={months:{1:"Janeiro",2:"Fevereiro",3:"Marco",4:"Abril",5:"Maio",6:"Junho",7:"Julho",8:"Agosto",9:"Setembro",10:"Outubro",11:"Novembro",12:"Dezembro"},days:{0:"D",1:"S",2:"T",3:"Q",4:"Q",5:"S",6:"S"}},a.fn.zabuto_calendar.languages.ru={months:{1:"Январь",2:"Февраль",3:"Март",4:"Апрель",5:"Май",6:"Июнь",7:"Июль",8:"Август",9:"Сентябрь",10:"Октябрь",11:"Ноябрь",12:"Декабрь"},days:{0:"Вск",1:"Пн",2:"Вт",3:"Ср",4:"Чт",5:"Пт",6:"Сб"}},a.fn.zabuto_calendar.languages.se={months:{1:"Januari",2:"Februari",3:"Mars",4:"April",5:"Maj",6:"Juni",7:"Juli",8:"Augusti",9:"September",10:"Oktober",11:"November",12:"December"},days:{0:"Sön",1:"Mån",2:"Tis",3:"Ons",4:"Tor",5:"Fre",6:"Lör"}},a.fn.zabuto_calendar.languages.sk={months:{1:"Január",2:"Február",3:"Marec",4:"Apríl",5:"Máj",6:"Jún",7:"Júl",8:"August",9:"September",10:"Október",11:"November",12:"December"},days:{0:"Ne",1:"Po",2:"Ut",3:"St",4:"Št",5:"Pi",6:"So"}},a.fn.zabuto_calendar.languages.sr={months:{1:"Јануар",2:"Фебруар",3:"Март",4:"Април",5:"Мај",6:"Јун",7:"Јул",8:"Август",9:"Септембар",10:"Октобар",11:"Новембар",12:"Децембар"},days:{0:"Нед",1:"Пон",2:"Уто",3:"Сре",4:"Чет",5:"Пет",6:"Суб"}},a.fn.zabuto_calendar.languages.tr={months:{1:"Ocak",2:"Şubat",3:"Mart",4:"Nisan",5:"Mayıs",6:"Haziran",7:"Temmuz",8:"Ağustos",9:"Eylül",10:"Ekim",11:"Kasım",12:"Aralık"},days:{0:"Paz",1:"Pts",2:"Salı",3:"Çar",4:"Per",5:"Cuma",6:"Cts"}},a.fn.zabuto_calendar.languages.ua={months:{1:"Січень",2:"Лютий",3:"Березень",4:"Квітень",5:"Травень",6:"Червень",7:"Липень",8:"Серпень",9:"Вересень",10:"Жовтень",11:"Листопад",12:"Грудень"},days:{0:"Нд",1:"Пн",2:"Вт",3:"Ср",4:"Чт",5:"Пт",6:"Сб"}}}(jQuery);