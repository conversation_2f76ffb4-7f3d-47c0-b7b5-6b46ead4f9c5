<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ config('app.name', 'Investment Platform') }}</title>
    
    <!-- Favicon -->
    <x-favicon />
    
    <!-- Styles -->
    @vite(['resources/css/app.scss'])
</head>
<body>
    <div id="react-app"></div>

    <!-- Test Script -->
    <script>
        console.log('Test script executing...');
        console.log('DOM ready state:', document.readyState);
        console.log('React app div:', document.getElementById('react-app'));

        // Test if we can access the React app div after DOM loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM fully loaded');
            console.log('React app div after DOM load:', document.getElementById('react-app'));
        });

        // Listen for any JavaScript errors
        window.addEventListener('error', function(e) {
            console.error('JavaScript error:', e.error);
            console.error('Error message:', e.message);
            console.error('Error source:', e.filename, 'line:', e.lineno);
        });

        // Listen for unhandled promise rejections
        window.addEventListener('unhandledrejection', function(e) {
            console.error('Unhandled promise rejection:', e.reason);
        });
    </script>

    <!-- Test ES Module -->
    <script type="module">
        console.log('ES Module test executing...');
    </script>

    <!-- Scripts -->
    @vite(['resources/js/react-app.jsx'])
</body>
</html>
