<x-app-layout>
    <div class="space-y-8">
        <div class="block sm:flex items-center justify-between mb-6">
            <x-breadcrumb :pageTitle="$pageTitle"/>

            <!-- BEGIN: Breadcrumb -->
            <div class="mb-5">
                <ul class="m-0 p-0 list-none">
                    <li class="inline-block relative top-[3px] text-base text-primary-500 font-Inter ">
                        <a href="{{ route('dashboard.index') }}">
                            <iconify-icon icon="heroicons-outline:home"></iconify-icon>
                            <iconify-icon icon="heroicons-outline:chevron-right"
                                          class="relative text-slate-500 text-sm rtl:rotate-180"></iconify-icon>
                        </a>
                    </li>
                    <li class="inline-block relative text-sm text-primary-500 font-Inter ">
                        Startup Company
                        <iconify-icon icon="heroicons-outline:chevron-right"
                                      class="relative top-[3px] text-slate-500 rtl:rotate-180"></iconify-icon>
                    </li>

                </ul>
            </div>
            <!-- END: BreadCrumb -->

        </div>

    </div>

    <div class="card">
        <header class=" card-header noborder">
            <h4 class="card-title">All Startups
            </h4>
        </header>
        <div class="card-body px-6 pb-6">
            <div class="overflow-x-auto -mx-6 dashcode-data-table">
                <span class=" col-span-8  hidden"></span>
                <span class="  col-span-4 hidden"></span>
                <div class="inline-block min-w-full align-middle">
                    <div class="overflow-hidden ">
                        <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700"
                               id="data-table">
                            <thead class=" border-t border-slate-100 dark:border-slate-800">
                            <tr>

                                <th scope="col" class=" table-th "> SL</th>

                                <th scope="col" class=" table-th ">Company Name </th>

                                <th scope="col" class=" table-th ">ESG Avg Score</th>

                                <th scope="col" class=" table-th ">X-Corn Status</th>

                                <th scope="col" class=" table-th "> Location</th>

                                <th scope="col" class=" table-th ">Total Equity Funding</th>

                                <th scope="col" class=" table-th ">Founded</th>

                                <th scope="col" class=" table-th "> Company Stage </th>

                                <th scope="col" class=" table-th "> Action</th>

                            </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                            @forelse($startups as $index => $startup)
                                <tr>
                                    <td class="table-td">{{ $startups->firstItem() + $index }}</td>
                                    <td class="table-td">
                                        <span class="flex">
                                          <span class="w-7 h-7 rounded-full ltr:mr-3 rtl:ml-3 flex-none">
                                            <img src="{{ $startup->getFirstMediaUrl('profile-image') ?: asset('images/all-img/customer_1.png') }}" alt="{{ $startup->name }}"
                                                 class="object-cover w-full h-full rounded-full">
                                          </span>
                                        <span class="text-sm text-slate-600 dark:text-slate-300 capitalize">{{ $startup->startupProfile->company_name ?? $startup->name }}</span>
                                        </span>
                                    </td>
                                    <td class="table-td">{{ $startup->startupProfile->esg_score ?? 'N/A' }}</td>
                                    <td class="table-td">{{ $startup->startupProfile->company_stage ?? 'N/A' }}</td>
                                    <td class="table-td">
                                        <div>
                                            ${{ number_format($startup->startupProfile->funding_amount ?? 0, 2) }}
                                        </div>
                                    </td>
                                    <td class="table-td">
                                        <div>
                                            ${{ number_format($startup->startupProfile->valuation ?? 0, 2) }}
                                        </div>
                                    </td>
                                    <td class="table-td">{{ $startup->created_at->format('m-d-Y') }}</td>
                                    <td class="table-td">{{ $startup->startupProfile->funding_stage ?? 'N/A' }}</td>
                                    <td class="table-td">
                                        <div>
                                            <div class="relative">
                                                <div class="dropdown relative">
                                                    <button class="text-xl text-center block w-full " type="button"
                                                            id="tableDropdownMenuButton{{ $startup->id }}" data-bs-toggle="dropdown"
                                                            aria-expanded="false">
                                                        <iconify-icon icon="heroicons-outline:dots-vertical"></iconify-icon>
                                                    </button>
                                                    <ul class=" dropdown-menu min-w-[120px] absolute text-sm text-slate-700 dark:text-white hidden bg-white dark:bg-slate-700
                                      shadow z-[2] float-left overflow-hidden list-none text-left rounded-lg mt-1 m-0 bg-clip-padding border-none">
                                                        <li>
                                                            <a href="{{ route('startups.show', $startup) }}" class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600
                                          dark:hover:text-white">
                                                                View</a>
                                                        </li>
                                                        <li>
                                                            <a href="{{ route('startups.edit', $startup) }}" class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600
                                          dark:hover:text-white">
                                                                Edit</a>
                                                        </li>
                                                        <li>
                                                            <form action="{{ route('startups.destroy', $startup) }}" method="POST" class="inline">
                                                                @csrf
                                                                @method('DELETE')
                                                                <button type="submit" class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600 dark:hover:text-white w-full text-left"
                                                                        onclick="return confirm('Are you sure you want to delete this startup?')">
                                                                    Delete
                                                                </button>
                                                            </form>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="9" class="table-td text-center">No startups found</td>
                                </tr>
                            @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    <div class="mt-6">
        @if($startups->hasPages())
            <div class="d-flex justify-content-center">
                <nav aria-label="Page navigation">
                    <ul class="pagination">
                        @if($startups->onFirstPage())
                            <li class="page-item disabled"><span class="page-link">Previous</span></li>
                        @else
                            <li class="page-item"><a class="page-link" href="{{ $startups->previousPageUrl() }}">Previous</a></li>
                        @endif

                        @for($i = 1; $i <= $startups->lastPage(); $i++)
                            @if($i == $startups->currentPage())
                                <li class="page-item active"><span class="page-link">{{ $i }}</span></li>
                            @else
                                <li class="page-item"><a class="page-link" href="{{ $startups->url($i) }}">{{ $i }}</a></li>
                            @endif
                        @endfor

                        @if($startups->hasMorePages())
                            <li class="page-item"><a class="page-link" href="{{ $startups->nextPageUrl() }}">Next</a></li>
                        @else
                            <li class="page-item disabled"><span class="page-link">Next</span></li>
                        @endif
                    </ul>
                </nav>
            </div>
        @endif
    </div>

    @vite(['resources/js/plugins-old/jquery-3.6.0.min.js'])
    @push('scripts')
        <script type="module">

        </script>
    @endpush
</x-app-layout>
