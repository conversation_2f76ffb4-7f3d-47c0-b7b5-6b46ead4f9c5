<x-app-layout>
    <div class="space-y-8">
        <div class="block sm:flex items-center justify-between mb-6">
            <x-breadcrumb :pageTitle="$pageTitle" :breadcrumbItems="$breadcrumbItems"/>
        </div>

    <div class="card">
        <header class=" card-header noborder">
            <h4 class="card-title">All Startups
            </h4>
        </header>
        <div class="card-body px-6 pb-6">
            <div class="overflow-x-auto -mx-6 dashcode-data-table">
                <span class=" col-span-8  hidden"></span>
                <span class="  col-span-4 hidden"></span>
                <div class="inline-block min-w-full align-middle">
                    <div class="overflow-hidden ">
                        <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700"
                               id="data-table">
                            <thead class=" border-t border-slate-100 dark:border-slate-800">
                            <tr>

                                <th scope="col" class=" table-th "> SL</th>

                                <th scope="col" class=" table-th ">Company Name </th>

                                <th scope="col" class=" table-th ">ESG Avg Score</th>

                                <th scope="col" class=" table-th ">X-Corn Status</th>

                                <th scope="col" class=" table-th "> Location</th>

                                <th scope="col" class=" table-th ">Total Equity Funding</th>

                                <th scope="col" class=" table-th ">Founded</th>

                                <th scope="col" class=" table-th "> Company Stage </th>

                                <th scope="col" class=" table-th "> Action</th>

                            </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                            @forelse($startups as $index => $startup)
                                <tr>
                                    <td class="table-td">{{ $index + 1 }}</td>
                                    <td class="table-td">
                                        <span class="flex">
                                          <span class="w-7 h-7 rounded-full ltr:mr-3 rtl:ml-3 flex-none">
                                            <img src="{{ $startup->getFirstMediaUrl('profile-image') ?: asset('images/all-img/customer_1.png') }}" alt="{{ $startup->name }}"
                                                 class="object-cover w-full h-full rounded-full">
                                          </span>
                                        <span class="text-sm text-slate-600 dark:text-slate-300 capitalize">{{ $startup->startupProfile->company_name ?? $startup->name }}</span>
                                        </span>
                                    </td>
                                    <td class="table-td">{{ $startup->startupProfile->esg_score ?? 'N/A' }}</td>
                                    <td class="table-td">{{ $startup->startupProfile->company_stage ?? 'N/A' }}</td>
                                    <td class="table-td">
                                        <div>
                                            ${{ number_format($startup->startupProfile->funding_amount ?? 0, 2) }}
                                        </div>
                                    </td>
                                    <td class="table-td">
                                        <div>
                                            ${{ number_format($startup->startupProfile->valuation ?? 0, 2) }}
                                        </div>
                                    </td>
                                    <td class="table-td">{{ $startup->created_at->format('m-d-Y') }}</td>
                                    <td class="table-td">{{ $startup->startupProfile->funding_stage ?? 'N/A' }}</td>
                                    <td class="table-td">
                                        <div>
                                            <div class="relative">
                                                <div class="dropdown relative">
                                                    <button class="text-xl text-center block w-full " type="button"
                                                            id="tableDropdownMenuButton{{ $startup->id }}" data-bs-toggle="dropdown"
                                                            aria-expanded="false">
                                                        <iconify-icon icon="heroicons-outline:dots-vertical"></iconify-icon>
                                                    </button>
                                                    <ul class=" dropdown-menu min-w-[120px] absolute text-sm text-slate-700 dark:text-white hidden bg-white dark:bg-slate-700
                                      shadow z-[2] float-left overflow-hidden list-none text-left rounded-lg mt-1 m-0 bg-clip-padding border-none">
                                                        <li>
                                                            <a href="{{ route('startups.show', $startup) }}" class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600
                                          dark:hover:text-white">
                                                                View</a>
                                                        </li>
                                                        <li>
                                                            <a href="{{ route('startups.edit', $startup) }}" class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600
                                          dark:hover:text-white">
                                                                Edit</a>
                                                        </li>
                                                        <li>
                                                            <form action="{{ route('startups.destroy', $startup) }}" method="POST" class="inline">
                                                                @csrf
                                                                @method('DELETE')
                                                                <button type="submit" class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600 dark:hover:text-white w-full text-left"
                                                                        onclick="return confirm('Are you sure you want to delete this startup?')">
                                                                    Delete
                                                                </button>
                                                            </form>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="9" class="table-td text-center">No startups found</td>
                                </tr>
                            @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

        @push('scripts')
            <script type="module">
                // DataTable initialization
                $("#data-table").DataTable({
                    dom: "<'grid grid-cols-12 gap-5 px-6 mt-6'<'col-span-4'l><'col-span-8 flex justify-end'f><'#pagination.flex items-center'>><'min-w-full't><'flex justify-end items-center'p>",
                    paging: true,
                    ordering: true,
                    info: false,
                    searching: true,
                    lengthChange: true,
                    lengthMenu: [10, 25, 50, 100],
                    language: {
                        lengthMenu: "Show _MENU_ entries",
                        paginate: {
                            previous: `<iconify-icon icon="ic:round-keyboard-arrow-left"></iconify-icon>`,
                            next: `<iconify-icon icon="ic:round-keyboard-arrow-right"></iconify-icon>`,
                        },
                        search: "Search:",
                    },
                });
            </script>
        @endpush
    </div>
</x-app-layout>
