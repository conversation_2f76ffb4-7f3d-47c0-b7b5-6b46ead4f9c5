<x-app-layout>
    <div class="space-y-8">
        <!-- Breadcrumb -->
        <div>
            <x-breadcrumb :page-title="$pageTitle" :breadcrumb-items="$breadcrumbItems" />
        </div>

        <!-- User Profile Header -->
        <div class="card">
            <div class="card-body p-6">
                <div class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-6">
                    <div class="flex-none">
                        <img src="{{ $startup->getFirstMediaUrl('profile-image') ?: asset('images/all-img/customer_1.png') }}"
                             alt="{{ $startup->name }}"
                             class="w-24 h-24 rounded-full object-cover">
                    </div>
                    <div class="flex-1">
                        <h2 class="text-2xl font-bold text-slate-900 dark:text-white mb-2">{{ $startup->name }}</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-slate-600 dark:text-slate-300">Email:</span>
                                <span class="font-medium ml-2">{{ $startup->email }}</span>
                            </div>
                            <div>
                                <span class="text-slate-600 dark:text-slate-300">Role:</span>
                                <span class="badge badge-primary-light ml-2">{{ ucfirst($startup->role) }}</span>
                            </div>
                            <div>
                                <span class="text-slate-600 dark:text-slate-300">Account Status:</span>
                                <span class="badge {{ $startup->account_status === 'active' ? 'badge-success-light' : 'badge-warning-light' }} ml-2">
                                    {{ ucfirst($startup->account_status) }}
                                </span>
                            </div>
                            <div>
                                <span class="text-slate-600 dark:text-slate-300">Joined:</span>
                                <span class="font-medium ml-2">{{ $startup->created_at->format('M d, Y') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Methods Section -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Payment Methods</h4>
            </div>
            <div class="card-body">
                @if($paymentMethods->count() > 0)
                    <div class="space-y-4">
                        @foreach($paymentMethods as $method)
                            <div class="flex items-center justify-between p-4 border border-slate-200 dark:border-slate-700 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                                        <iconify-icon icon="heroicons:credit-card" class="text-blue-600 dark:text-blue-400"></iconify-icon>
                                    </div>
                                    <div>
                                        <div class="font-medium">**** **** **** {{ $method->card_last_four }}</div>
                                        <div class="text-sm text-slate-500">{{ ucfirst($method->card_brand) }} • Expires {{ $method->card_exp_month }}/{{ $method->card_exp_year }}</div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    @if($method->is_default)
                                        <span class="badge badge-success-light">Default</span>
                                    @endif
                                    <span class="badge badge-primary-light">{{ ucfirst($method->type) }}</span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <iconify-icon icon="heroicons:credit-card" class="text-4xl text-slate-400 mb-2"></iconify-icon>
                        <p class="text-slate-500">No payment methods found</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Subscription History Section -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Subscription History</h4>
            </div>
            <div class="card-body">
                @if($subscriptionHistory->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
                            <thead>
                                <tr>
                                    <th class="table-th">Product</th>
                                    <th class="table-th">Status</th>
                                    <th class="table-th">Amount</th>
                                    <th class="table-th">Period</th>
                                    <th class="table-th">Created</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-slate-200 dark:divide-slate-700">
                                @foreach($subscriptionHistory as $subscription)
                                    <tr>
                                        <td class="table-td">{{ $subscription->subscriptionProduct->name }}</td>
                                        <td class="table-td">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                @if($subscription->status === 'active') bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100
                                                @elseif($subscription->status === 'canceled') bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100
                                                @elseif($subscription->status === 'past_due') bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100
                                                @else bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100 @endif">
                                                {{ ucfirst($subscription->status) }}
                                            </span>
                                        </td>
                                        <td class="table-td">${{ number_format($subscription->amount, 2) }}</td>
                                        <td class="table-td">
                                            @if($subscription->current_period_start && $subscription->current_period_end)
                                                {{ $subscription->current_period_start->format('M d') }} - {{ $subscription->current_period_end->format('M d, Y') }}
                                            @else
                                                N/A
                                            @endif
                                        </td>
                                        <td class="table-td">{{ $subscription->created_at->format('M d, Y') }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-8">
                        <iconify-icon icon="heroicons:document-text" class="text-4xl text-slate-400 mb-2"></iconify-icon>
                        <p class="text-slate-500">No subscription history found</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Invoices Section -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Invoices</h4>
            </div>
            <div class="card-body">
                @if($invoices->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
                            <thead>
                                <tr>
                                    <th class="table-th">Invoice #</th>
                                    <th class="table-th">Status</th>
                                    <th class="table-th">Amount</th>
                                    <th class="table-th">Date</th>
                                    <th class="table-th">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-slate-200 dark:divide-slate-700">
                                @foreach($invoices as $invoice)
                                    <tr>
                                        <td class="table-td">{{ $invoice->invoice_number }}</td>
                                        <td class="table-td">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                @if($invoice->status === 'paid') bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100
                                                @elseif($invoice->status === 'open') bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100
                                                @elseif($invoice->status === 'draft') bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100
                                                @else bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100 @endif">
                                                {{ ucfirst($invoice->status) }}
                                            </span>
                                        </td>
                                        <td class="table-td">${{ number_format($invoice->total, 2) }}</td>
                                        <td class="table-td">{{ $invoice->created_at->format('M d, Y') }}</td>
                                        <td class="table-td">
                                            @if($invoice->pdf_url)
                                                <a href="{{ $invoice->pdf_url }}" target="_blank" class="btn btn-sm btn-primary">
                                                    <iconify-icon icon="heroicons:arrow-down-tray" class="mr-1"></iconify-icon>
                                                    Download
                                                </a>
                                            @else
                                                <span class="text-slate-400">N/A</span>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-8">
                        <iconify-icon icon="heroicons:document-text" class="text-4xl text-slate-400 mb-2"></iconify-icon>
                        <p class="text-slate-500">No invoices found</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Interested Investors Section -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Interested Investors</h4>
            </div>
            <div class="card-body">
                @if($interestedInvestors->count() > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @foreach($interestedInvestors as $request)
                            <div class="border border-slate-200 dark:border-slate-700 rounded-lg p-4">
                                <div class="flex items-center space-x-3 mb-3">
                                    <img src="{{ $request->requester->getFirstMediaUrl('profile-image') ?: asset('images/all-img/customer_1.png') }}"
                                         alt="{{ $request->requester->name }}"
                                         class="w-10 h-10 rounded-full object-cover">
                                    <div>
                                        <div class="font-medium">{{ $request->requester->name }}</div>
                                        <div class="text-sm text-slate-500">{{ $request->requester->email }}</div>
                                    </div>
                                </div>
                                @if($request->message)
                                    <p class="text-sm text-slate-600 dark:text-slate-300 mb-2">{{ Str::limit($request->message, 100) }}</p>
                                @endif
                                @if($request->proposed_amount)
                                    <div class="text-sm">
                                        <span class="text-slate-500">Proposed Amount:</span>
                                        <span class="font-medium">${{ number_format($request->proposed_amount, 2) }}</span>
                                    </div>
                                @endif
                                <div class="text-xs text-slate-400 mt-2">
                                    Interested on {{ $request->created_at->format('M d, Y') }}
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <iconify-icon icon="heroicons:users" class="text-4xl text-slate-400 mb-2"></iconify-icon>
                        <p class="text-slate-500">No interested investors yet</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Company Profile & ESG Details -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Company Profile -->
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Company Profile</h4>
                </div>
                <div class="card-body p-6">
                    @if($startup->startupProfile)
                        <div class="space-y-4">
                            <div class="flex justify-between">
                                <span class="text-slate-600 dark:text-slate-300">Company Name:</span>
                                <span class="font-medium">{{ $startup->startupProfile->company_name ?: 'Not set' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-slate-600 dark:text-slate-300">Business Model:</span>
                                <span class="font-medium">{{ $startup->startupProfile->business_model ? implode(', ', $startup->startupProfile->business_model) : 'Not set' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-slate-600 dark:text-slate-300">Funding Stage:</span>
                                <span class="font-medium">{{ $startup->startupProfile->funding_stage ? ucfirst(str_replace('_', ' ', $startup->startupProfile->funding_stage)) : 'Not set' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-slate-600 dark:text-slate-300">Employee Count:</span>
                                <span class="font-medium">{{ $startup->startupProfile->employee_count ?: 'Not set' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-slate-600 dark:text-slate-300">Funding Sought:</span>
                                <span class="font-medium">${{ $startup->startupProfile->funding_amount_sought ? number_format($startup->startupProfile->funding_amount_sought) : '0' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-slate-600 dark:text-slate-300">Founded:</span>
                                <span class="font-medium">{{ $startup->startupProfile->founding_date ? $startup->startupProfile->founding_date->format('M Y') : 'Not set' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-slate-600 dark:text-slate-300">Website:</span>
                                <span class="font-medium">
                                    @if($startup->startupProfile->website)
                                        <a href="{{ $startup->startupProfile->website }}" target="_blank" class="text-primary-500 hover:underline">
                                            {{ $startup->startupProfile->website }}
                                        </a>
                                    @else
                                        Not set
                                    @endif
                                </span>
                            </div>
                        </div>

                        @if($startup->startupProfile->company_description)
                            <div class="mt-6">
                                <h6 class="font-semibold mb-2">Company Description</h6>
                                <p class="text-slate-600 dark:text-slate-300">{{ $startup->startupProfile->company_description }}</p>
                            </div>
                        @endif
                    @else
                        <div class="text-center py-8">
                            <iconify-icon icon="heroicons:building-office" class="text-6xl text-slate-400 mb-4"></iconify-icon>
                            <h5 class="text-lg font-semibold mb-2">No Profile Created</h5>
                            <p class="text-slate-600 dark:text-slate-300">This startup hasn't created their company profile yet.</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- ESG Details -->
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">ESG Details</h4>
                </div>
                <div class="card-body p-6">
                    @if($startup->startupProfile && $startup->startupProfile->esg_completed)
                        <!-- ESG Score Overview -->
                        <div class="text-center mb-6">
                            <div class="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br from-success-400 to-success-600 text-white mb-4">
                                <div class="text-center">
                                    <div class="text-2xl font-bold">{{ number_format($startup->startupProfile->esg_score, 1) }}</div>
                                    <div class="text-xs opacity-90">ESG</div>
                                </div>
                            </div>
                            <h5 class="text-lg font-semibold">ESG Score: {{ number_format($startup->startupProfile->esg_score, 1) }}/100</h5>
                            <p class="text-slate-600 dark:text-slate-300 text-sm">
                                @if($startup->startupProfile->esg_score >= 80)
                                    Excellent ESG practices
                                @elseif($startup->startupProfile->esg_score >= 60)
                                    Good ESG commitment
                                @elseif($startup->startupProfile->esg_score >= 40)
                                    Fair ESG performance
                                @else
                                    Needs ESG improvement
                                @endif
                            </p>
                        </div>

                        <!-- ESG Category Breakdown -->
                        @if($startup->startupProfile->esg_breakdown)
                            <div class="space-y-3 mb-6">
                                <h6 class="font-semibold">Category Breakdown</h6>
                                @foreach($startup->startupProfile->esg_breakdown as $category => $score)
                                    <div class="flex justify-between items-center">
                                        <span class="text-slate-600 dark:text-slate-300 capitalize">{{ $category }}:</span>
                                        <div class="flex items-center space-x-2">
                                            <div class="w-20 bg-slate-200 rounded-full h-2">
                                                <div class="
                                                    @if($category === 'environmental') bg-green-500
                                                    @elseif($category === 'social') bg-blue-500
                                                    @else bg-purple-500
                                                    @endif h-2 rounded-full"
                                                    style="width: {{ $score }}%"></div>
                                            </div>
                                            <span class="text-sm font-medium">{{ number_format($score, 1) }}</span>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @endif

                        <!-- ESG Completion Status -->
                        <div class="bg-success-50 dark:bg-success-900/20 p-4 rounded-lg">
                            <div class="flex items-center">
                                <iconify-icon icon="heroicons:check-circle" class="text-success-600 text-xl mr-3"></iconify-icon>
                                <div>
                                    <div class="font-semibold text-success-800 dark:text-success-200">ESG Assessment Complete</div>
                                    <div class="text-sm text-success-600 dark:text-success-300">
                                        Completed {{ $startup->startupProfile->updated_at->diffForHumans() }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="text-center py-8">
                            <iconify-icon icon="heroicons:clipboard-document-list" class="text-6xl text-slate-400 mb-4"></iconify-icon>
                            <h5 class="text-lg font-semibold mb-2">ESG Assessment Pending</h5>
                            <p class="text-slate-600 dark:text-slate-300 mb-4">This startup hasn't completed their ESG questionnaire yet.</p>

                            @if($startup->startupProfile)
                                <div class="bg-warning-50 dark:bg-warning-900/20 p-4 rounded-lg">
                                    <div class="flex items-center justify-center">
                                        <iconify-icon icon="heroicons:exclamation-triangle" class="text-warning-600 text-xl mr-3"></iconify-icon>
                                        <div class="text-sm text-warning-700 dark:text-warning-300">
                                            Profile created but ESG assessment incomplete
                                        </div>
                                    </div>
                                </div>
                            @else
                                <div class="bg-slate-50 dark:bg-slate-700 p-4 rounded-lg">
                                    <div class="text-sm text-slate-600 dark:text-slate-300">
                                        Company profile must be created before ESG assessment
                                    </div>
                                </div>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- ESG Responses Details (if completed) -->
        @if($startup->startupProfile && $startup->startupProfile->esg_completed && $startup->startupProfile->esgResponses->count() > 0)
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">ESG Questionnaire Responses</h4>
                </div>
                <div class="card-body p-6">
                    @php
                        $responses = $startup->startupProfile->esgResponses->groupBy('esgQuestion.category');
                    @endphp

                    <div class="space-y-6">
                        @foreach($responses as $category => $categoryResponses)
                            <div>
                                <h5 class="text-lg font-semibold capitalize mb-4 flex items-center">
                                    @if($category === 'environmental')
                                        <iconify-icon icon="heroicons:leaf" class="text-green-600 text-xl mr-2"></iconify-icon>
                                    @elseif($category === 'social')
                                        <iconify-icon icon="heroicons:users" class="text-blue-600 text-xl mr-2"></iconify-icon>
                                    @else
                                        <iconify-icon icon="heroicons:scale" class="text-purple-600 text-xl mr-2"></iconify-icon>
                                    @endif
                                    {{ $category }} Questions
                                </h5>

                                <div class="space-y-4">
                                    @foreach($categoryResponses as $response)
                                        <div class="border-l-4
                                            @if($category === 'environmental') border-green-500
                                            @elseif($category === 'social') border-blue-500
                                            @else border-purple-500
                                            @endif pl-4 py-2">
                                            <h6 class="font-medium mb-2">{{ $response->esgQuestion->question_text }}</h6>
                                            <div class="flex items-center justify-between">
                                                <div class="text-slate-600 dark:text-slate-300">
                                                    <strong>Answer:</strong>
                                                    @if($response->esgQuestion->type === 'scale')
                                                        {{ $response->response_value }}/5
                                                        @switch($response->response_value)
                                                            @case('1') (Poor) @break
                                                            @case('2') (Fair) @break
                                                            @case('3') (Good) @break
                                                            @case('4') (Very Good) @break
                                                            @case('5') (Excellent) @break
                                                        @endswitch
                                                    @elseif($response->esgQuestion->type === 'yes_no')
                                                        {{ ucfirst($response->response_value) }}
                                                    @else
                                                        {{ $response->response_value }}
                                                    @endif
                                                </div>
                                                @if($response->score)
                                                    <div class="text-sm">
                                                        <span class="badge badge-success-light">
                                                            Score: {{ number_format($response->score, 1) }}
                                                        </span>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @endif

        <!-- Action Buttons -->
        <div class="flex justify-end space-x-3">
            <a href="{{ route('startups.index') }}" class="btn btn-secondary">
                <iconify-icon icon="heroicons:arrow-left" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                Back to Startups
            </a>
            <a href="{{ route('startups.edit', $startup) }}" class="btn btn-primary">
                <iconify-icon icon="heroicons:pencil-square" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                Edit Startup
            </a>
        </div>
    </div>
</x-app-layout>
