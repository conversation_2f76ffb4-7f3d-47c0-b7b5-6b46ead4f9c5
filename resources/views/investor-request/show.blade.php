@extends('layouts.app')

@section('content')
<div class="content-wrapper transition-all duration-150 ltr:ml-[248px] rtl:mr-[248px]" id="content_wrapper">
    <div class="page-content">
        <div class="transition-all duration-150 container-fluid" id="page_layout">
            <div id="content_layout">
                <!-- BEGIN: Breadcrumb -->
                <div class="mb-5">
                    <ul class="m-0 p-0 list-none">
                        <li class="inline-block relative top-[3px] text-base text-primary-500 font-Inter">
                            <a href="{{ route('dashboard') }}">
                                <iconify-icon icon="heroicons-outline:home"></iconify-icon>
                                <iconify-icon icon="heroicons-outline:chevron-right" class="relative text-slate-500 text-sm rtl:rotate-180"></iconify-icon>
                            </a>
                        </li>
                        @foreach($breadcrumbItems as $item)
                        <li class="inline-block relative text-sm text-primary-500 font-Inter">
                            @if($item['url'])
                                <a href="{{ $item['url'] }}">
                                    {{ $item['name'] }}
                                </a>
                                @if(!$item['active'])
                                    <iconify-icon icon="heroicons-outline:chevron-right" class="relative top-[3px] text-slate-500 rtl:rotate-180"></iconify-icon>
                                @endif
                            @else
                                {{ $item['name'] }}
                            @endif
                        </li>
                        @endforeach
                    </ul>
                </div>
                <!-- END: Breadcrumb -->

                <div class="space-y-8">
                    <!-- Request Overview -->
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Request Overview</h4>
                        </div>
                        <div class="card-body">
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                <div>
                                    <span class="text-xs font-medium text-slate-600 dark:text-slate-300 block">Status:</span>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        @if($investorRequest->status === 'approved') bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100
                                        @elseif($investorRequest->status === 'rejected') bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100
                                        @else bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100 @endif">
                                        {{ ucfirst($investorRequest->status) }}
                                    </span>
                                </div>
                                <div>
                                    <span class="text-xs font-medium text-slate-600 dark:text-slate-300 block">Type:</span>
                                    <span class="text-sm text-slate-900 dark:text-white">{{ str_replace('_', ' ', $investorRequest->type) }}</span>
                                </div>
                                <div>
                                    <span class="text-xs font-medium text-slate-600 dark:text-slate-300 block">Request Date:</span>
                                    <span class="text-sm text-slate-900 dark:text-white">{{ $investorRequest->created_at->format('M d, Y H:i') }}</span>
                                </div>
                                @if($investorRequest->message)
                                <div class="md:col-span-2 lg:col-span-4">
                                    <span class="text-xs font-medium text-slate-600 dark:text-slate-300 block">Message:</span>
                                    <p class="text-sm text-slate-900 dark:text-white mt-1">{{ $investorRequest->message }}</p>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Startup Information -->
                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">Startup Information</h4>
                            </div>
                            <div class="card-body">
                                <div class="flex items-start space-x-4">
                                    <div class="flex-shrink-0">
                                        @if($investorRequest->requester->getFirstMediaUrl('profile'))
                                            <img src="{{ $investorRequest->requester->getFirstMediaUrl('profile') }}" alt="{{ $investorRequest->requester->name }}" class="w-12 h-12 rounded-full object-cover">
                                        @else
                                            <div class="w-12 h-12 rounded-full bg-primary-500 flex items-center justify-center text-white font-medium text-lg">
                                                {{ strtoupper(substr($investorRequest->requester->name, 0, 2)) }}
                                            </div>
                                        @endif
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <h5 class="text-lg font-medium text-slate-900 dark:text-white">{{ $investorRequest->requester->name }}</h5>
                                        <p class="text-sm text-slate-500">{{ $investorRequest->requester->email }}</p>
                                    </div>
                                </div>

                                @if($investorRequest->requester->startupProfile)
                                <div class="space-y-3 mt-4">
                                    @if($investorRequest->requester->startupProfile->company_name)
                                    <div>
                                        <span class="text-xs font-medium text-slate-600 dark:text-slate-300 block">Company:</span>
                                        <span class="text-sm text-slate-900 dark:text-white">{{ $investorRequest->requester->startupProfile->company_name }}</span>
                                    </div>
                                    @endif

                                    @if($investorRequest->requester->startupProfile->funding_stage)
                                    <div>
                                        <span class="text-xs font-medium text-slate-600 dark:text-slate-300 block">Funding Stage:</span>
                                        <span class="text-sm text-slate-900 dark:text-white">{{ $investorRequest->requester->startupProfile->funding_stage }}</span>
                                    </div>
                                    @endif
                                </div>
                                @endif

                                <div class="mt-4">
                                    <a href="{{ route('startups.show', $investorRequest->requester->id) }}" class="btn btn-outline-primary btn-sm">
                                        View Full Profile
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Investor Information -->
                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">Investor Information</h4>
                            </div>
                            <div class="card-body">
                                <div class="flex items-start space-x-4">
                                    <div class="flex-shrink-0">
                                        @if($investorRequest->target->getFirstMediaUrl('profile'))
                                            <img src="{{ $investorRequest->target->getFirstMediaUrl('profile') }}" alt="{{ $investorRequest->target->name }}" class="w-12 h-12 rounded-full object-cover">
                                        @else
                                            <div class="w-12 h-12 rounded-full bg-primary-500 flex items-center justify-center text-white font-medium text-lg">
                                                {{ strtoupper(substr($investorRequest->target->name, 0, 2)) }}
                                            </div>
                                        @endif
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <h5 class="text-lg font-medium text-slate-900 dark:text-white">{{ $investorRequest->target->name }}</h5>
                                        <p class="text-sm text-slate-500">{{ $investorRequest->target->email }}</p>
                                    </div>
                                </div>

                                @if($investorRequest->target->investorProfile)
                                <div class="space-y-3 mt-4">
                                    @if($investorRequest->target->investorProfile->bio)
                                    <div>
                                        <span class="text-xs font-medium text-slate-600 dark:text-slate-300 block">Bio:</span>
                                        <span class="text-sm text-slate-900 dark:text-white">{{ $investorRequest->target->investorProfile->bio }}</span>
                                    </div>
                                    @endif

                                    @if($investorRequest->target->investorProfile->investment_budget_min || $investorRequest->target->investorProfile->investment_budget_max)
                                    <div>
                                        <span class="text-xs font-medium text-slate-600 dark:text-slate-300 block">Investment Budget:</span>
                                        <span class="text-sm text-slate-900 dark:text-white">
                                            ${{ number_format($investorRequest->target->investorProfile->investment_budget_min ?? 0) }} - 
                                            ${{ number_format($investorRequest->target->investorProfile->investment_budget_max ?? 0) }}
                                        </span>
                                    </div>
                                    @endif

                                    @php
                                        $targetCategories = $investorRequest->target->taxonomies()->where('type', 'category')->get();
                                    @endphp
                                    @if($targetCategories->count() > 0)
                                    <div>
                                        <span class="text-xs font-medium text-slate-600 dark:text-slate-300 block mb-1">Investment Categories:</span>
                                        <div class="flex flex-wrap gap-1">
                                            @foreach($targetCategories->take(3) as $category)
                                            <span class="inline-block px-2 py-1 text-xs bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300 rounded">
                                                {{ $category->name }}
                                            </span>
                                            @endforeach
                                            @if($targetCategories->count() > 3)
                                            <span class="inline-block px-2 py-1 text-xs bg-slate-100 text-slate-600 dark:bg-slate-700 dark:text-slate-300 rounded">
                                                +{{ $targetCategories->count() - 3 }} more
                                            </span>
                                            @endif
                                        </div>
                                    </div>
                                    @endif
                                </div>
                                @endif

                                <div class="mt-4">
                                    <a href="{{ route('investors.show', $investorRequest->target->id) }}" class="btn btn-outline-primary btn-sm">
                                        View Full Profile
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
