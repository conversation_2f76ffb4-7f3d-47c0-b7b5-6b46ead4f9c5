<x-app-layout>
    <div class="space-y-8">
        <div class="block sm:flex items-center justify-between mb-6">
            <x-breadcrumb :pageTitle="$pageTitle"/>

            <!-- BEGIN: Breadcrumb -->
            <div class="mb-5">
                <ul class="m-0 p-0 list-none">
                    <li class="inline-block relative top-[3px] text-base text-primary-500 font-Inter ">
                        <a href="{{ route('dashboard.index') }}">
                            <iconify-icon icon="heroicons-outline:home"></iconify-icon>
                            <iconify-icon icon="heroicons-outline:chevron-right"
                                          class="relative text-slate-500 text-sm rtl:rotate-180"></iconify-icon>
                        </a>
                    </li>
                    <li class="inline-block relative text-sm text-primary-500 font-Inter ">
                        Startup Request
                        <iconify-icon icon="heroicons-outline:chevron-right"
                                      class="relative top-[3px] text-slate-500 rtl:rotate-180"></iconify-icon>
                    </li>

                </ul>
            </div>
            <!-- END: BreadCrumb -->

        </div>

    </div>

    <div class="card">
        <header class=" card-header noborder">
            <h4 class="card-title">All Request
            </h4>
        </header>
        <div class="card-body px-6 pb-6">
            <div class="overflow-x-auto -mx-6 dashcode-data-table">
                <span class=" col-span-8  hidden"></span>
                <span class="  col-span-4 hidden"></span>
                <div class="inline-block min-w-full align-middle">
                    <div class="overflow-hidden ">
                        <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700"
                               id="data-table">
                            <thead class=" border-t border-slate-100 dark:border-slate-800">
                            <tr>
                                <th scope="col" class=" table-th ">SL</th>
                                <th scope="col" class=" table-th "> Startup Company Name</th>
                                <th scope="col" class=" table-th ">Investor Name</th>
                                <th scope="col" class=" table-th "> Request Date & Time</th>
                                <th scope="col" class=" table-th ">Status</th>
                                <th scope="col" class=" table-th ">Action</th>

                            </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                            @forelse($startupRequests as $index => $request)
                            <tr>
                                <td class="table-td">{{ $index + 1 }}</td>
                                <td class="table-td">
                                    <span class="flex">
                                        <span class="w-7 h-7 rounded-full ltr:mr-3 rtl:ml-3 flex-none">
                                            @if($request->target && $request->target->getFirstMediaUrl('avatar'))
                                                <img src="{{ $request->target->getFirstMediaUrl('avatar') }}" alt="{{ $request->target->name }}"
                                                     class="object-cover w-full h-full rounded-full">
                                            @else
                                                <div class="w-full h-full rounded-full bg-slate-200 dark:bg-slate-600 flex items-center justify-center">
                                                    <span class="text-xs font-medium text-slate-600 dark:text-slate-300">
                                                        {{ $request->target ? strtoupper(substr($request->target->name, 0, 2)) : 'N/A' }}
                                                    </span>
                                                </div>
                                            @endif
                                        </span>
                                        <span class="text-sm text-slate-600 dark:text-slate-300 capitalize">
                                            {{ $request->target->name ?? 'N/A' }}
                                        </span>
                                    </span>
                                </td>
                                <td class="table-td">
                                    <span class="flex">
                                        <span class="w-7 h-7 rounded-full ltr:mr-3 rtl:ml-3 flex-none">
                                            @if($request->requester && $request->requester->getFirstMediaUrl('avatar'))
                                                <img src="{{ $request->requester->getFirstMediaUrl('avatar') }}" alt="{{ $request->requester->name }}"
                                                     class="object-cover w-full h-full rounded-full">
                                            @else
                                                <div class="w-full h-full rounded-full bg-slate-200 dark:bg-slate-600 flex items-center justify-center">
                                                    <span class="text-xs font-medium text-slate-600 dark:text-slate-300">
                                                        {{ $request->requester ? strtoupper(substr($request->requester->name, 0, 2)) : 'N/A' }}
                                                    </span>
                                                </div>
                                            @endif
                                        </span>
                                        <span class="text-sm text-slate-600 dark:text-slate-300 capitalize">
                                            {{ $request->requester->name ?? 'N/A' }}
                                        </span>
                                    </span>
                                </td>
                                <td class="table-td">{{ $request->created_at->format('M d, Y H:i') }}</td>
                                <td class="table-td">
                                    @php
                                        $statusClasses = [
                                            'pending' => 'text-warning-500 bg-warning-500',
                                            'approved' => 'text-success-500 bg-success-500',
                                            'rejected' => 'text-danger-500 bg-danger-500'
                                        ];
                                        $statusClass = $statusClasses[$request->status] ?? 'text-slate-500 bg-slate-500';
                                    @endphp
                                    <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 {{ $statusClass }}">
                                        {{ ucfirst($request->status) }}
                                    </div>
                                </td>
                                <td class="table-td">
                                    <div class="relative">
                                        <div class="dropdown relative">
                                            <button class="text-xl text-center block w-full" type="button"
                                                    id="tableDropdownMenuButton{{ $request->id }}" data-bs-toggle="dropdown"
                                                    aria-expanded="false">
                                                <iconify-icon icon="heroicons-outline:dots-vertical"></iconify-icon>
                                            </button>
                                            <ul class="dropdown-menu min-w-[120px] absolute text-sm text-slate-700 dark:text-white hidden bg-white dark:bg-slate-700
                              shadow z-[2] float-left overflow-hidden list-none text-left rounded-lg mt-1 m-0 bg-clip-padding border-none">
                                                <li>
                                                    <a href="{{ route('startup-request.show', $request) }}"
                                                       class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600 dark:hover:text-white">
                                                        View
                                                    </a>
                                                </li>
                                                @if($request->status === 'pending')
                                                <li>
                                                    <a href="{{ route('startup-request.edit', $request) }}"
                                                       class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600 dark:hover:text-white">
                                                        Edit
                                                    </a>
                                                </li>
                                                @endif
                                                <li>
                                                    <form method="POST" action="{{ route('startup-request.destroy', $request) }}" class="inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit"
                                                                onclick="return confirm('Are you sure you want to delete this request?')"
                                                                class="w-full text-left text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600 dark:hover:text-white">
                                                            Delete
                                                        </button>
                                                    </form>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="6" class="table-td text-center py-8">
                                    <div class="text-slate-500 dark:text-slate-400">
                                        <iconify-icon icon="heroicons-outline:inbox" class="text-4xl mb-2"></iconify-icon>
                                        <p>No startup requests found.</p>
                                    </div>
                                </td>
                            </tr>
                            @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    <div class="mt-6">
        {{ $startupRequests->links() }}
    </div>

    @vite(['resources/js/plugins-old/jquery-3.6.0.min.js'])
    @push('scripts')
        <script type="module">
            // Initialize dropdown functionality
            document.addEventListener('DOMContentLoaded', function() {
                // Handle dropdown toggles
                document.querySelectorAll('[data-bs-toggle="dropdown"]').forEach(function(button) {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        // Close all other dropdowns
                        document.querySelectorAll('.dropdown-menu').forEach(function(menu) {
                            if (menu !== button.nextElementSibling) {
                                menu.classList.add('hidden');
                            }
                        });

                        // Toggle current dropdown
                        const menu = button.nextElementSibling;
                        if (menu) {
                            menu.classList.toggle('hidden');
                        }
                    });
                });

                // Close dropdowns when clicking outside
                document.addEventListener('click', function(e) {
                    if (!e.target.closest('.dropdown')) {
                        document.querySelectorAll('.dropdown-menu').forEach(function(menu) {
                            menu.classList.add('hidden');
                        });
                    }
                });
            });
        </script>
    @endpush
</x-app-layout>
