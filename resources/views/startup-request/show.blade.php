<x-app-layout>
    <div class="space-y-8">
        <div class="block sm:flex items-center justify-between mb-6">
            <x-breadcrumb :pageTitle="$pageTitle" :breadcrumbItems="$breadcrumbItems"/>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Request Overview -->
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">Request Overview</h4>
                </header>
                <div class="card-body">
                    <div class="space-y-4">
                        <!-- Status -->
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-slate-600 dark:text-slate-300">Status:</span>
                            @php
                                $statusClasses = [
                                    'pending' => 'text-warning-500 bg-warning-500',
                                    'approved' => 'text-success-500 bg-success-500',
                                    'rejected' => 'text-danger-500 bg-danger-500'
                                ];
                                $statusClass = $statusClasses[$startupRequest->status] ?? 'text-slate-500 bg-slate-500';
                            @endphp
                            <div class="inline-block px-3 py-1 rounded-full bg-opacity-25 {{ $statusClass }}">
                                {{ ucfirst($startupRequest->status) }}
                            </div>
                        </div>

                        <!-- Request Type -->
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-slate-600 dark:text-slate-300">Type:</span>
                            <span class="text-sm text-slate-900 dark:text-white capitalize">
                                {{ str_replace('_', ' ', $startupRequest->type) }}
                            </span>
                        </div>

                        <!-- Request Date -->
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-slate-600 dark:text-slate-300">Request Date:</span>
                            <span class="text-sm text-slate-900 dark:text-white">
                                {{ $startupRequest->created_at->format('M d, Y H:i') }}
                            </span>
                        </div>

                        <!-- Proposed Amount -->
                        @if($startupRequest->proposed_amount)
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-slate-600 dark:text-slate-300">Proposed Amount:</span>
                            <span class="text-sm font-semibold text-slate-900 dark:text-white">
                                ${{ number_format($startupRequest->proposed_amount, 2) }}
                            </span>
                        </div>
                        @endif

                        <!-- Message -->
                        @if($startupRequest->message)
                        <div>
                            <span class="text-sm font-medium text-slate-600 dark:text-slate-300 block mb-2">Message:</span>
                            <div class="bg-slate-50 dark:bg-slate-700 p-4 rounded-lg">
                                <p class="text-sm text-slate-700 dark:text-slate-300">{{ $startupRequest->message }}</p>
                            </div>
                        </div>
                        @endif

                        <!-- Terms -->
                        @if($startupRequest->terms && is_array($startupRequest->terms))
                        <div>
                            <span class="text-sm font-medium text-slate-600 dark:text-slate-300 block mb-2">Terms:</span>
                            <div class="bg-slate-50 dark:bg-slate-700 p-4 rounded-lg">
                                <ul class="list-disc list-inside space-y-1">
                                    @foreach($startupRequest->terms as $term)
                                    <li class="text-sm text-slate-700 dark:text-slate-300">{{ $term }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                        @endif

                        <!-- Approval Information -->
                        @if($startupRequest->status === 'approved' && $startupRequest->approver)
                        <div class="border-t pt-4">
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium text-slate-600 dark:text-slate-300">Approved by:</span>
                                <div class="flex items-center space-x-2">
                                    <div class="w-6 h-6 rounded-full overflow-hidden">
                                        @if($startupRequest->approver->getFirstMediaUrl('avatar'))
                                            <img src="{{ $startupRequest->approver->getFirstMediaUrl('avatar') }}" 
                                                 alt="{{ $startupRequest->approver->name }}"
                                                 class="w-full h-full object-cover">
                                        @else
                                            <div class="w-full h-full bg-slate-200 dark:bg-slate-600 flex items-center justify-center">
                                                <span class="text-xs font-medium text-slate-600 dark:text-slate-300">
                                                    {{ strtoupper(substr($startupRequest->approver->name, 0, 1)) }}
                                                </span>
                                            </div>
                                        @endif
                                    </div>
                                    <span class="text-sm text-slate-900 dark:text-white">{{ $startupRequest->approver->name }}</span>
                                </div>
                            </div>
                            @if($startupRequest->approved_at)
                            <div class="flex items-center justify-between mt-2">
                                <span class="text-sm font-medium text-slate-600 dark:text-slate-300">Approved on:</span>
                                <span class="text-sm text-slate-900 dark:text-white">
                                    {{ $startupRequest->approved_at->format('M d, Y H:i') }}
                                </span>
                            </div>
                            @endif
                        </div>
                        @endif

                        <!-- Rejection Information -->
                        @if($startupRequest->status === 'rejected' && $startupRequest->rejection_reason)
                        <div class="border-t pt-4">
                            <span class="text-sm font-medium text-slate-600 dark:text-slate-300 block mb-2">Rejection Reason:</span>
                            <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800">
                                <p class="text-sm text-red-700 dark:text-red-300">{{ $startupRequest->rejection_reason }}</p>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Investor Information -->
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">Investor Information</h4>
                </header>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="w-16 h-16 rounded-full mx-auto mb-3 overflow-hidden">
                            @if($startupRequest->requester->getFirstMediaUrl('avatar'))
                                <img src="{{ $startupRequest->requester->getFirstMediaUrl('avatar') }}" 
                                     alt="{{ $startupRequest->requester->name }}"
                                     class="w-full h-full object-cover">
                            @else
                                <div class="w-full h-full bg-slate-200 dark:bg-slate-600 flex items-center justify-center">
                                    <span class="text-lg font-medium text-slate-600 dark:text-slate-300">
                                        {{ strtoupper(substr($startupRequest->requester->name, 0, 2)) }}
                                    </span>
                                </div>
                            @endif
                        </div>
                        <h5 class="text-lg font-medium text-slate-900 dark:text-white">{{ $startupRequest->requester->name }}</h5>
                        <p class="text-sm text-slate-500">{{ $startupRequest->requester->email }}</p>
                    </div>

                    @if($startupRequest->requester->investorProfile)
                    <div class="space-y-3">
                        @if($startupRequest->requester->investorProfile->bio)
                        <div>
                            <span class="text-xs font-medium text-slate-600 dark:text-slate-300 block">Bio:</span>
                            <span class="text-sm text-slate-900 dark:text-white">{{ $startupRequest->requester->investorProfile->bio }}</span>
                        </div>
                        @endif

                        @if($startupRequest->requester->investorProfile->investment_budget_min || $startupRequest->requester->investorProfile->investment_budget_max)
                        <div>
                            <span class="text-xs font-medium text-slate-600 dark:text-slate-300 block">Investment Budget:</span>
                            <span class="text-sm text-slate-900 dark:text-white">
                                ${{ number_format($startupRequest->requester->investorProfile->investment_budget_min ?? 0) }} -
                                ${{ number_format($startupRequest->requester->investorProfile->investment_budget_max ?? 0) }}
                            </span>
                        </div>
                        @endif

                        @php
                            $requesterCategories = $startupRequest->requester->taxonomies()->where('type', 'category')->get();
                        @endphp
                        @if($requesterCategories->count() > 0)
                        <div>
                            <span class="text-xs font-medium text-slate-600 dark:text-slate-300 block mb-1">Investment Categories:</span>
                            <div class="flex flex-wrap gap-1">
                                @foreach($requesterCategories->take(3) as $category)
                                <span class="inline-block px-2 py-1 text-xs bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300 rounded">
                                    {{ $category->name }}
                                </span>
                                @endforeach
                                @if($requesterCategories->count() > 3)
                                <span class="inline-block px-2 py-1 text-xs bg-slate-100 text-slate-600 dark:bg-slate-700 dark:text-slate-300 rounded">
                                    +{{ $requesterCategories->count() - 3 }} more
                                </span>
                                @endif
                            </div>
                        </div>
                        @endif
                    </div>
                    @endif

                    <div class="mt-4 pt-4 border-t">
                        <a href="{{ route('investors.show', $startupRequest->requester) }}" 
                           class="btn btn-outline-primary btn-sm w-full">
                            View Full Profile
                        </a>
                    </div>
                </div>
            </div>

            <!-- Startup Information -->
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">Startup Information</h4>
                </header>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="w-16 h-16 rounded-full mx-auto mb-3 overflow-hidden">
                            @if($startupRequest->target->getFirstMediaUrl('avatar'))
                                <img src="{{ $startupRequest->target->getFirstMediaUrl('avatar') }}" 
                                     alt="{{ $startupRequest->target->name }}"
                                     class="w-full h-full object-cover">
                            @else
                                <div class="w-full h-full bg-slate-200 dark:bg-slate-600 flex items-center justify-center">
                                    <span class="text-lg font-medium text-slate-600 dark:text-slate-300">
                                        {{ strtoupper(substr($startupRequest->target->name, 0, 2)) }}
                                    </span>
                                </div>
                            @endif
                        </div>
                        <h5 class="text-lg font-medium text-slate-900 dark:text-white">{{ $startupRequest->target->name }}</h5>
                        <p class="text-sm text-slate-500">{{ $startupRequest->target->email }}</p>
                    </div>

                    @if($startupRequest->target->startupProfile)
                    <div class="space-y-3">
                        @if($startupRequest->target->startupProfile->company_name)
                        <div>
                            <span class="text-xs font-medium text-slate-600 dark:text-slate-300 block">Company:</span>
                            <span class="text-sm text-slate-900 dark:text-white">{{ $startupRequest->target->startupProfile->company_name }}</span>
                        </div>
                        @endif

                        @if($startupRequest->target->startupProfile->funding_stage)
                        <div>
                            <span class="text-xs font-medium text-slate-600 dark:text-slate-300 block">Funding Stage:</span>
                            <span class="text-sm text-slate-900 dark:text-white capitalize">{{ str_replace('_', ' ', $startupRequest->target->startupProfile->funding_stage) }}</span>
                        </div>
                        @endif

                        @if($startupRequest->target->startupProfile->categories->count() > 0)
                        <div>
                            <span class="text-xs font-medium text-slate-600 dark:text-slate-300 block mb-1">Categories:</span>
                            <div class="flex flex-wrap gap-1">
                                @foreach($startupRequest->target->startupProfile->categories->take(3) as $category)
                                <span class="inline-block px-2 py-1 text-xs bg-success-100 text-success-700 dark:bg-success-900 dark:text-success-300 rounded">
                                    {{ $category->name }}
                                </span>
                                @endforeach
                                @if($startupRequest->target->startupProfile->categories->count() > 3)
                                <span class="inline-block px-2 py-1 text-xs bg-slate-100 text-slate-600 dark:bg-slate-700 dark:text-slate-300 rounded">
                                    +{{ $startupRequest->target->startupProfile->categories->count() - 3 }} more
                                </span>
                                @endif
                            </div>
                        </div>
                        @endif
                    </div>
                    @endif

                    <div class="mt-4 pt-4 border-t">
                        <a href="{{ route('startups.show', $startupRequest->target) }}" 
                           class="btn btn-outline-success btn-sm w-full">
                            View Full Profile
                        </a>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            @if($startupRequest->status === 'pending')
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">Actions</h4>
                </header>
                <div class="card-body space-y-3">
                    <a href="{{ route('startup-request.edit', $startupRequest) }}" 
                       class="btn btn-primary btn-sm w-full">
                        Edit Request
                    </a>
                    <form method="POST" action="{{ route('startup-request.destroy', $startupRequest) }}" class="w-full">
                        @csrf
                        @method('DELETE')
                        <button type="submit" 
                                onclick="return confirm('Are you sure you want to delete this request?')"
                                class="btn btn-danger btn-sm w-full">
                            Delete Request
                        </button>
                    </form>
                </div>
            </div>
            @endif
        </div>
    </div>
</x-app-layout>
