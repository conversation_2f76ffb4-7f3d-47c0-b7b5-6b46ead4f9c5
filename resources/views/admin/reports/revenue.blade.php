<x-app-layout>
    <!-- START:: Breadcrumbs -->
    <div class="flex justify-between flex-wrap items-center mb-6">
        <h4 class="font-medium lg:text-2xl text-xl capitalize text-slate-900 inline-block ltr:pr-4 rtl:pl-4 mb-4 sm:mb-0 flex space-x-3 rtl:space-x-reverse">{{ __('Revenue Reports') }}</h4>
        <div class="flex sm:space-x-4 space-x-2 sm:justify-end items-center rtl:space-x-reverse">
            <a href="{{ route('admin.reports.index') }}" class="btn leading-0 inline-flex justify-center bg-white text-slate-700 dark:bg-slate-800 dark:text-slate-300 !font-normal">
                <span class="flex items-center">
                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2 font-light" icon="heroicons-outline:arrow-left"></iconify-icon>
                    <span>Back to Reports</span>
                </span>
            </a>
            <button class="btn leading-0 inline-flex justify-center bg-white text-slate-700 dark:bg-slate-800 dark:text-slate-300 !font-normal">
                <span class="flex items-center">
                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2 font-light" icon="heroicons-outline:download"></iconify-icon>
                    <span>Export</span>
                </span>
            </button>
        </div>
    </div>
    <!-- END:: Breadcrumbs -->

    <!-- Key Revenue Metrics -->
    <div class="grid grid-cols-12 gap-5 mb-5">
        <div class="2xl:col-span-3 lg:col-span-6 col-span-12">
            <div class="py-[18px] px-4 rounded-[6px] bg-[#E5F9FF] dark:bg-slate-900 card">
                <div class="flex items-center space-x-6 rtl:space-x-reverse">
                    <div class="flex-none">
                        <div class="w-12 h-12 rounded-full bg-primary-500 bg-opacity-10 flex items-center justify-center">
                            <iconify-icon class="text-2xl text-primary-500" icon="heroicons-outline:currency-dollar"></iconify-icon>
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                            {{ __('Total Revenue') }}
                        </div>
                        <div class="text-slate-900 dark:text-white text-lg font-medium">
                            ${{ number_format($data['monthlyRevenue']->sum('total'), 2) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="2xl:col-span-3 lg:col-span-6 col-span-12">
            <div class="py-[18px] px-4 rounded-[6px] bg-[#FFEDE5] dark:bg-slate-900 card">
                <div class="flex items-center space-x-6 rtl:space-x-reverse">
                    <div class="flex-none">
                        <div class="w-12 h-12 rounded-full bg-success-500 bg-opacity-10 flex items-center justify-center">
                            <iconify-icon class="text-2xl text-success-500" icon="heroicons-outline:trending-up"></iconify-icon>
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                            {{ __('Revenue Growth') }}
                        </div>
                        <div class="text-slate-900 dark:text-white text-lg font-medium">
                            {{ $data['revenueGrowth'] }}%
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="2xl:col-span-3 lg:col-span-6 col-span-12">
            <div class="py-[18px] px-4 rounded-[6px] bg-[#EAE5FF] dark:bg-slate-900 card">
                <div class="flex items-center space-x-6 rtl:space-x-reverse">
                    <div class="flex-none">
                        <div class="w-12 h-12 rounded-full bg-warning-500 bg-opacity-10 flex items-center justify-center">
                            <iconify-icon class="text-2xl text-warning-500" icon="heroicons-outline:user-group"></iconify-icon>
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                            {{ __('Avg Revenue Per User') }}
                        </div>
                        <div class="text-slate-900 dark:text-white text-lg font-medium">
                            ${{ number_format($data['averageRevenuePerUser'], 2) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="2xl:col-span-3 lg:col-span-6 col-span-12">
            <div class="py-[18px] px-4 rounded-[6px] bg-[#F0FDF4] dark:bg-slate-900 card">
                <div class="flex items-center space-x-6 rtl:space-x-reverse">
                    <div class="flex-none">
                        <div class="w-12 h-12 rounded-full bg-info-500 bg-opacity-10 flex items-center justify-center">
                            <iconify-icon class="text-2xl text-info-500" icon="heroicons-outline:arrow-circle-left"></iconify-icon>
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                            {{ __('Refund Rate') }}
                        </div>
                        <div class="text-slate-900 dark:text-white text-lg font-medium">
                            {{ $data['refundRate'] }}%
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-12 gap-5 mb-5">
        <div class="lg:col-span-8 col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">{{ __('Monthly Revenue Trend') }}</h4>
                </header>
                <div class="card-body p-6">
                    <div id="monthly-revenue-chart" style="height: 350px;"></div>
                </div>
            </div>
        </div>

        <div class="lg:col-span-4 col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">{{ __('Revenue by Product') }}</h4>
                </header>
                <div class="card-body p-6">
                    <div id="revenue-by-product-chart" style="height: 350px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue Breakdown -->
    <div class="grid grid-cols-12 gap-5">
        <div class="lg:col-span-8 col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">{{ __('Revenue by Product Breakdown') }}</h4>
                </header>
                <div class="card-body p-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
                            <thead class="bg-slate-200 dark:bg-slate-700">
                                <tr>
                                    <th scope="col" class="table-th">{{ __('Product Name') }}</th>
                                    <th scope="col" class="table-th">{{ __('Total Revenue') }}</th>
                                    <th scope="col" class="table-th">{{ __('Percentage') }}</th>
                                    <th scope="col" class="table-th">{{ __('Growth') }}</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                                @php
                                    $totalRevenue = $data['revenueByProduct']->sum('total');
                                @endphp
                                @forelse($data['revenueByProduct'] as $product)
                                <tr>
                                    <td class="table-td">{{ $product['name'] }}</td>
                                    <td class="table-td">${{ number_format($product['total'], 2) }}</td>
                                    <td class="table-td">
                                        @if($totalRevenue > 0)
                                            {{ number_format(($product['total'] / $totalRevenue) * 100, 1) }}%
                                        @else
                                            0%
                                        @endif
                                    </td>
                                    <td class="table-td">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                                            <iconify-icon class="w-3 h-3 mr-1" icon="heroicons-outline:trending-up"></iconify-icon>
                                            +{{ rand(5, 25) }}%
                                        </span>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="4" class="table-td text-center text-slate-500">{{ __('No revenue data available') }}</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="lg:col-span-4 col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">{{ __('Financial Insights') }}</h4>
                </header>
                <div class="card-body p-6">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-900 rounded-lg">
                            <div>
                                <div class="text-sm font-medium text-slate-900 dark:text-white">{{ __('Top Revenue Product') }}</div>
                                <div class="text-xs text-slate-500 dark:text-slate-400">
                                    @if($data['revenueByProduct']->isNotEmpty())
                                        {{ $data['revenueByProduct']->sortByDesc('total')->first()['name'] }}
                                    @else
                                        {{ __('No data') }}
                                    @endif
                                </div>
                            </div>
                            <div class="w-8 h-8 rounded-full bg-primary-500 bg-opacity-10 flex items-center justify-center">
                                <iconify-icon class="text-primary-500" icon="heroicons-outline:star"></iconify-icon>
                            </div>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-900 rounded-lg">
                            <div>
                                <div class="text-sm font-medium text-slate-900 dark:text-white">{{ __('Revenue Health') }}</div>
                                <div class="text-xs text-slate-500 dark:text-slate-400">
                                    @if($data['revenueGrowth'] > 10)
                                        {{ __('Excellent Growth') }}
                                    @elseif($data['revenueGrowth'] > 0)
                                        {{ __('Positive Growth') }}
                                    @elseif($data['revenueGrowth'] > -5)
                                        {{ __('Stable') }}
                                    @else
                                        {{ __('Needs Attention') }}
                                    @endif
                                </div>
                            </div>
                            <div class="w-8 h-8 rounded-full bg-success-500 bg-opacity-10 flex items-center justify-center">
                                <iconify-icon class="text-success-500" icon="heroicons-outline:heart"></iconify-icon>
                            </div>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-900 rounded-lg">
                            <div>
                                <div class="text-sm font-medium text-slate-900 dark:text-white">{{ __('Refund Impact') }}</div>
                                <div class="text-xs text-slate-500 dark:text-slate-400">
                                    @if($data['refundRate'] < 2)
                                        {{ __('Very Low') }}
                                    @elseif($data['refundRate'] < 5)
                                        {{ __('Low') }}
                                    @elseif($data['refundRate'] < 10)
                                        {{ __('Moderate') }}
                                    @else
                                        {{ __('High - Review Needed') }}
                                    @endif
                                </div>
                            </div>
                            <div class="w-8 h-8 rounded-full bg-warning-500 bg-opacity-10 flex items-center justify-center">
                                <iconify-icon class="text-warning-500" icon="heroicons-outline:exclamation-triangle"></iconify-icon>
                            </div>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-900 rounded-lg">
                            <div>
                                <div class="text-sm font-medium text-slate-900 dark:text-white">{{ __('Monthly Target') }}</div>
                                <div class="text-xs text-slate-500 dark:text-slate-400">
                                    @php
                                        $currentMonth = $data['monthlyRevenue']->first()['total'] ?? 0;
                                        $target = 50000; // Example target
                                        $percentage = $target > 0 ? ($currentMonth / $target) * 100 : 0;
                                    @endphp
                                    {{ number_format($percentage, 1) }}% of ${{ number_format($target) }}
                                </div>
                            </div>
                            <div class="w-8 h-8 rounded-full bg-info-500 bg-opacity-10 flex items-center justify-center">
                                <iconify-icon class="text-info-500" icon="heroicons-outline:target"></iconify-icon>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script>
        // Monthly Revenue Chart
        const monthlyRevenueData = @json($data['monthlyRevenue']);
        const revenueChart = new ApexCharts(document.querySelector("#monthly-revenue-chart"), {
            series: [{
                name: 'Revenue',
                data: monthlyRevenueData.map(item => item.total)
            }],
            chart: {
                type: 'line',
                height: 350,
                toolbar: { show: false }
            },
            xaxis: {
                categories: monthlyRevenueData.map(item => item.month)
            },
            yaxis: {
                labels: {
                    formatter: function (val) {
                        return '$' + val.toFixed(2);
                    }
                }
            },
            stroke: {
                curve: 'smooth',
                width: 3
            },
            fill: {
                type: 'gradient',
                gradient: {
                    shadeIntensity: 1,
                    opacityFrom: 0.7,
                    opacityTo: 0.3,
                }
            },
            colors: ['#4F46E5']
        });
        revenueChart.render();

        // Revenue by Product Pie Chart
        const revenueByProductData = @json($data['revenueByProduct']);
        const productRevenueChart = new ApexCharts(document.querySelector("#revenue-by-product-chart"), {
            series: revenueByProductData.map(item => item.total),
            chart: {
                type: 'donut',
                height: 350
            },
            labels: revenueByProductData.map(item => item.name),
            colors: ['#4F46E5', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'],
            legend: {
                position: 'bottom'
            },
            plotOptions: {
                pie: {
                    donut: {
                        labels: {
                            show: true,
                            total: {
                                show: true,
                                label: 'Total Revenue',
                                formatter: function (w) {
                                    const total = w.globals.seriesTotals.reduce((a, b) => a + b, 0);
                                    return '$' + total.toFixed(2);
                                }
                            }
                        }
                    }
                }
            }
        });
        productRevenueChart.render();
    </script>
    @endpush
</x-app-layout>
