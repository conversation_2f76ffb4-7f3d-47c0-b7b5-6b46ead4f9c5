<x-app-layout>
    <!-- START:: Breadcrumbs -->
    <div class="flex justify-between flex-wrap items-center mb-6">
        <h4 class="font-medium lg:text-2xl text-xl capitalize text-slate-900 inline-block ltr:pr-4 rtl:pl-4 mb-4 sm:mb-0 flex space-x-3 rtl:space-x-reverse">{{ __('Refund Requests') }}</h4>
        <div class="flex sm:space-x-4 space-x-2 sm:justify-end items-center rtl:space-x-reverse">
            <a href="{{ route('admin.reports.index') }}" class="btn leading-0 inline-flex justify-center bg-white text-slate-700 dark:bg-slate-800 dark:text-slate-300 !font-normal">
                <span class="flex items-center">
                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2 font-light" icon="heroicons-outline:arrow-left"></iconify-icon>
                    <span>Back to Reports</span>
                </span>
            </a>
            <button class="btn leading-0 inline-flex justify-center bg-white text-slate-700 dark:bg-slate-800 dark:text-slate-300 !font-normal">
                <span class="flex items-center">
                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2 font-light" icon="heroicons-outline:download"></iconify-icon>
                    <span>Export</span>
                </span>
            </button>
        </div>
    </div>
    <!-- END:: Breadcrumbs -->

    <!-- Refund Statistics -->
    <div class="grid grid-cols-12 gap-5 mb-5">
        <div class="2xl:col-span-3 lg:col-span-6 col-span-12">
            <div class="py-[18px] px-4 rounded-[6px] bg-[#FFF3CD] dark:bg-slate-900 card">
                <div class="flex items-center space-x-6 rtl:space-x-reverse">
                    <div class="flex-none">
                        <div class="w-12 h-12 rounded-full bg-warning-500 bg-opacity-10 flex items-center justify-center">
                            <iconify-icon class="text-2xl text-warning-500" icon="heroicons-outline:clock"></iconify-icon>
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                            {{ __('Pending Requests') }}
                        </div>
                        <div class="text-slate-900 dark:text-white text-lg font-medium">
                            {{ number_format($data['refundStats']['pending']) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="2xl:col-span-3 lg:col-span-6 col-span-12">
            <div class="py-[18px] px-4 rounded-[6px] bg-[#D1ECF1] dark:bg-slate-900 card">
                <div class="flex items-center space-x-6 rtl:space-x-reverse">
                    <div class="flex-none">
                        <div class="w-12 h-12 rounded-full bg-success-500 bg-opacity-10 flex items-center justify-center">
                            <iconify-icon class="text-2xl text-success-500" icon="heroicons-outline:check-circle"></iconify-icon>
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                            {{ __('Approved') }}
                        </div>
                        <div class="text-slate-900 dark:text-white text-lg font-medium">
                            {{ number_format($data['refundStats']['approved']) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="2xl:col-span-3 lg:col-span-6 col-span-12">
            <div class="py-[18px] px-4 rounded-[6px] bg-[#F8D7DA] dark:bg-slate-900 card">
                <div class="flex items-center space-x-6 rtl:space-x-reverse">
                    <div class="flex-none">
                        <div class="w-12 h-12 rounded-full bg-danger-500 bg-opacity-10 flex items-center justify-center">
                            <iconify-icon class="text-2xl text-danger-500" icon="heroicons-outline:x-circle"></iconify-icon>
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                            {{ __('Rejected') }}
                        </div>
                        <div class="text-slate-900 dark:text-white text-lg font-medium">
                            {{ number_format($data['refundStats']['rejected']) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="2xl:col-span-3 lg:col-span-6 col-span-12">
            <div class="py-[18px] px-4 rounded-[6px] bg-[#E5F9FF] dark:bg-slate-900 card">
                <div class="flex items-center space-x-6 rtl:space-x-reverse">
                    <div class="flex-none">
                        <div class="w-12 h-12 rounded-full bg-primary-500 bg-opacity-10 flex items-center justify-center">
                            <iconify-icon class="text-2xl text-primary-500" icon="heroicons-outline:currency-dollar"></iconify-icon>
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                            {{ __('Total Refunded') }}
                        </div>
                        <div class="text-slate-900 dark:text-white text-lg font-medium">
                            ${{ number_format($data['refundStats']['total_amount'], 2) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Refund Requests -->
    <div class="grid grid-cols-12 gap-5 mb-5">
        <div class="col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">{{ __('Pending Refund Requests') }}</h4>
                    <div class="relative">
                        <button class="btn btn-sm bg-primary-500 text-white">
                            <iconify-icon class="w-4 h-4 ltr:mr-2 rtl:ml-2" icon="heroicons-outline:refresh"></iconify-icon>
                            {{ __('Refresh') }}
                        </button>
                    </div>
                </header>
                <div class="card-body p-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
                            <thead class="bg-slate-200 dark:bg-slate-700">
                                <tr>
                                    <th scope="col" class="table-th">{{ __('User') }}</th>
                                    <th scope="col" class="table-th">{{ __('Amount') }}</th>
                                    <th scope="col" class="table-th">{{ __('Reason') }}</th>
                                    <th scope="col" class="table-th">{{ __('Request Date') }}</th>
                                    <th scope="col" class="table-th">{{ __('Actions') }}</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                                @forelse($data['pendingRefunds'] as $refund)
                                <tr>
                                    <td class="table-td">
                                        <div class="flex items-center">
                                            <div class="flex-none">
                                                <div class="w-8 h-8 rounded-[100%] ltr:mr-3 rtl:ml-3">
                                                    <div class="w-full h-full rounded-[100%] bg-slate-100 dark:bg-slate-700 flex items-center justify-center">
                                                        <span class="text-xs font-medium text-slate-600 dark:text-slate-300">
                                                            {{ strtoupper(substr($refund->user->name, 0, 2)) }}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex-1 text-start">
                                                <h4 class="text-sm font-medium text-slate-600 whitespace-nowrap">
                                                    {{ $refund->user->name }}
                                                </h4>
                                                <div class="text-xs text-slate-500">{{ $refund->user->email }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="table-td">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
                                            ${{ number_format($refund->amount / 100, 2) }}
                                        </span>
                                    </td>
                                    <td class="table-td">
                                        <div class="max-w-xs truncate">
                                            {{ $refund->reason ?? 'No reason provided' }}
                                        </div>
                                    </td>
                                    <td class="table-td">{{ $refund->created_at->format('M d, Y H:i') }}</td>
                                    <td class="table-td">
                                        <div class="flex space-x-2">
                                            <button class="btn btn-sm bg-success-500 text-white">
                                                <iconify-icon class="w-4 h-4" icon="heroicons-outline:check"></iconify-icon>
                                            </button>
                                            <button class="btn btn-sm bg-danger-500 text-white">
                                                <iconify-icon class="w-4 h-4" icon="heroicons-outline:x"></iconify-icon>
                                            </button>
                                            <button class="btn btn-sm bg-info-500 text-white">
                                                <iconify-icon class="w-4 h-4" icon="heroicons-outline:eye"></iconify-icon>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="5" class="table-td text-center text-slate-500">
                                        <div class="py-8">
                                            <iconify-icon class="w-12 h-12 mx-auto text-slate-400 mb-4" icon="heroicons-outline:clipboard-check"></iconify-icon>
                                            <p>{{ __('No pending refund requests') }}</p>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Recent Activity -->
    <div class="grid grid-cols-12 gap-5">
        <div class="lg:col-span-8 col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">{{ __('Monthly Refund Trends') }}</h4>
                </header>
                <div class="card-body p-6">
                    <div id="refund-trends-chart" style="height: 350px;"></div>
                </div>
            </div>
        </div>

        <div class="lg:col-span-4 col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">{{ __('Recent Refund Activity') }}</h4>
                </header>
                <div class="card-body p-6">
                    <div class="space-y-4 max-h-80 overflow-y-auto">
                        @forelse($data['recentRefunds'] as $refund)
                        <div class="flex items-center space-x-3 rtl:space-x-reverse border-b border-slate-100 dark:border-slate-700 last:border-b-0 pb-3 last:pb-0">
                            <div class="flex-none">
                                <div class="w-8 h-8 rounded-full {{ $refund->status === 'succeeded' ? 'bg-success-500' : 'bg-danger-500' }} bg-opacity-10 flex items-center justify-center">
                                    <iconify-icon class="text-sm {{ $refund->status === 'succeeded' ? 'text-success-500' : 'text-danger-500' }}" 
                                        icon="{{ $refund->status === 'succeeded' ? 'heroicons-outline:check-circle' : 'heroicons-outline:x-circle' }}"></iconify-icon>
                                </div>
                            </div>
                            <div class="flex-1 text-start overflow-hidden">
                                <h4 class="text-sm font-medium text-slate-600 dark:text-slate-300 truncate">
                                    {{ $refund->user->name }}
                                </h4>
                                <div class="text-xs text-slate-500 dark:text-slate-400">
                                    ${{ number_format($refund->amount / 100, 2) }} • {{ $refund->created_at->diffForHumans() }}
                                </div>
                            </div>
                            <div class="flex-none">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                                    {{ $refund->status === 'succeeded' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' }}">
                                    {{ ucfirst($refund->status) }}
                                </span>
                            </div>
                        </div>
                        @empty
                        <div class="text-center py-8">
                            <iconify-icon class="w-12 h-12 mx-auto text-slate-400 mb-4" icon="heroicons-outline:clipboard-list"></iconify-icon>
                            <p class="text-slate-500">{{ __('No recent refund activity') }}</p>
                        </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script>
        // Refund Trends Chart
        const refundTrendsData = @json($data['refundsByMonth']);
        const trendsChart = new ApexCharts(document.querySelector("#refund-trends-chart"), {
            series: [
                {
                    name: 'Refund Count',
                    type: 'column',
                    data: refundTrendsData.map(item => item.count)
                },
                {
                    name: 'Refund Amount',
                    type: 'line',
                    data: refundTrendsData.map(item => item.total)
                }
            ],
            chart: {
                height: 350,
                type: 'line',
                toolbar: { show: false }
            },
            stroke: {
                width: [0, 4]
            },
            xaxis: {
                categories: refundTrendsData.map(item => item.month)
            },
            yaxis: [
                {
                    title: {
                        text: 'Count'
                    }
                },
                {
                    opposite: true,
                    title: {
                        text: 'Amount ($)'
                    },
                    labels: {
                        formatter: function (val) {
                            return '$' + val.toFixed(2);
                        }
                    }
                }
            ],
            colors: ['#4F46E5', '#10B981']
        });
        trendsChart.render();
    </script>
    @endpush
</x-app-layout>
