<x-app-layout>
    <!-- START:: Breadcrumbs -->
    <div class="flex justify-between flex-wrap items-center mb-6">
        <h4 class="font-medium lg:text-2xl text-xl capitalize text-slate-900 inline-block ltr:pr-4 rtl:pl-4 mb-4 sm:mb-0 flex space-x-3 rtl:space-x-reverse">{{ __('User Reports') }}</h4>
        <div class="flex sm:space-x-4 space-x-2 sm:justify-end items-center rtl:space-x-reverse">
            <a href="{{ route('admin.reports.index') }}" class="btn leading-0 inline-flex justify-center bg-white text-slate-700 dark:bg-slate-800 dark:text-slate-300 !font-normal">
                <span class="flex items-center">
                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2 font-light" icon="heroicons-outline:arrow-left"></iconify-icon>
                    <span>Back to Reports</span>
                </span>
            </a>
            <button class="btn leading-0 inline-flex justify-center bg-white text-slate-700 dark:bg-slate-800 dark:text-slate-300 !font-normal">
                <span class="flex items-center">
                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2 font-light" icon="heroicons-outline:download"></iconify-icon>
                    <span>Export</span>
                </span>
            </button>
        </div>
    </div>
    <!-- END:: Breadcrumbs -->

    <!-- Key User Metrics -->
    <div class="grid grid-cols-12 gap-5 mb-5">
        <div class="2xl:col-span-3 lg:col-span-6 col-span-12">
            <div class="py-[18px] px-4 rounded-[6px] bg-[#E5F9FF] dark:bg-slate-900 card">
                <div class="flex items-center space-x-6 rtl:space-x-reverse">
                    <div class="flex-none">
                        <div class="w-12 h-12 rounded-full bg-primary-500 bg-opacity-10 flex items-center justify-center">
                            <iconify-icon class="text-2xl text-primary-500" icon="heroicons-outline:users"></iconify-icon>
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                            {{ __('Active Users') }}
                        </div>
                        <div class="text-slate-900 dark:text-white text-lg font-medium">
                            {{ number_format($data['usersBySubscriptionStatus']['active']) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="2xl:col-span-3 lg:col-span-6 col-span-12">
            <div class="py-[18px] px-4 rounded-[6px] bg-[#FFEDE5] dark:bg-slate-900 card">
                <div class="flex items-center space-x-6 rtl:space-x-reverse">
                    <div class="flex-none">
                        <div class="w-12 h-12 rounded-full bg-success-500 bg-opacity-10 flex items-center justify-center">
                            <iconify-icon class="text-2xl text-success-500" icon="heroicons-outline:heart"></iconify-icon>
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                            {{ __('User Retention') }}
                        </div>
                        <div class="text-slate-900 dark:text-white text-lg font-medium">
                            {{ $data['userRetention'] }}%
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="2xl:col-span-3 lg:col-span-6 col-span-12">
            <div class="py-[18px] px-4 rounded-[6px] bg-[#EAE5FF] dark:bg-slate-900 card">
                <div class="flex items-center space-x-6 rtl:space-x-reverse">
                    <div class="flex-none">
                        <div class="w-12 h-12 rounded-full bg-warning-500 bg-opacity-10 flex items-center justify-center">
                            <iconify-icon class="text-2xl text-warning-500" icon="heroicons-outline:user-add"></iconify-icon>
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                            {{ __('New Users This Month') }}
                        </div>
                        <div class="text-slate-900 dark:text-white text-lg font-medium">
                            {{ $data['userGrowth']->first()['count'] ?? 0 }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="2xl:col-span-3 lg:col-span-6 col-span-12">
            <div class="py-[18px] px-4 rounded-[6px] bg-[#F0FDF4] dark:bg-slate-900 card">
                <div class="flex items-center space-x-6 rtl:space-x-reverse">
                    <div class="flex-none">
                        <div class="w-12 h-12 rounded-full bg-info-500 bg-opacity-10 flex items-center justify-center">
                            <iconify-icon class="text-2xl text-info-500" icon="heroicons-outline:user-remove"></iconify-icon>
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                            {{ __('Inactive Users') }}
                        </div>
                        <div class="text-slate-900 dark:text-white text-lg font-medium">
                            {{ number_format($data['usersBySubscriptionStatus']['no_subscription']) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-12 gap-5 mb-5">
        <div class="lg:col-span-8 col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">{{ __('User Growth Trend') }}</h4>
                </header>
                <div class="card-body p-6">
                    <div id="user-growth-chart" style="height: 350px;"></div>
                </div>
            </div>
        </div>

        <div class="lg:col-span-4 col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">{{ __('User Distribution') }}</h4>
                </header>
                <div class="card-body p-6">
                    <div id="user-distribution-chart" style="height: 350px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Users and User Insights -->
    <div class="grid grid-cols-12 gap-5">
        <div class="lg:col-span-8 col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">{{ __('Top Users by Subscription Activity') }}</h4>
                </header>
                <div class="card-body p-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
                            <thead class="bg-slate-200 dark:bg-slate-700">
                                <tr>
                                    <th scope="col" class="table-th">{{ __('User') }}</th>
                                    <th scope="col" class="table-th">{{ __('Email') }}</th>
                                    <th scope="col" class="table-th">{{ __('Subscriptions') }}</th>
                                    <th scope="col" class="table-th">{{ __('Member Since') }}</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                                @forelse($data['topUsers'] as $user)
                                <tr>
                                    <td class="table-td">
                                        <div class="flex items-center">
                                            <div class="flex-none">
                                                <div class="w-8 h-8 rounded-[100%] ltr:mr-3 rtl:ml-3">
                                                    <div class="w-full h-full rounded-[100%] bg-slate-100 dark:bg-slate-700 flex items-center justify-center">
                                                        <span class="text-xs font-medium text-slate-600 dark:text-slate-300">
                                                            {{ strtoupper(substr($user->name, 0, 2)) }}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex-1 text-start">
                                                <h4 class="text-sm font-medium text-slate-600 whitespace-nowrap">
                                                    {{ $user->name }}
                                                </h4>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="table-td">{{ $user->email }}</td>
                                    <td class="table-td">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                                            {{ $user->subscription_count }} {{ $user->subscription_count == 1 ? 'subscription' : 'subscriptions' }}
                                        </span>
                                    </td>
                                    <td class="table-td">{{ $user->created_at->format('M d, Y') }}</td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="4" class="table-td text-center text-slate-500">{{ __('No user data available') }}</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="lg:col-span-4 col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">{{ __('User Insights') }}</h4>
                </header>
                <div class="card-body p-6">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-900 rounded-lg">
                            <div>
                                <div class="text-sm font-medium text-slate-900 dark:text-white">{{ __('Top Revenue User') }}</div>
                                <div class="text-xs text-slate-500 dark:text-slate-400">
                                    @if($data['topUsers']->isNotEmpty())
                                        {{ $data['topUsers']->first()->name }}
                                    @else
                                        {{ __('No data') }}
                                    @endif
                                </div>
                            </div>
                            <div class="w-8 h-8 rounded-full bg-primary-500 bg-opacity-10 flex items-center justify-center">
                                <iconify-icon class="text-primary-500" icon="heroicons-outline:star"></iconify-icon>
                            </div>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-900 rounded-lg">
                            <div>
                                <div class="text-sm font-medium text-slate-900 dark:text-white">{{ __('Retention Health') }}</div>
                                <div class="text-xs text-slate-500 dark:text-slate-400">
                                    @if($data['userRetention'] > 80)
                                        {{ __('Excellent') }}
                                    @elseif($data['userRetention'] > 60)
                                        {{ __('Good') }}
                                    @elseif($data['userRetention'] > 40)
                                        {{ __('Fair') }}
                                    @else
                                        {{ __('Needs Improvement') }}
                                    @endif
                                </div>
                            </div>
                            <div class="w-8 h-8 rounded-full bg-success-500 bg-opacity-10 flex items-center justify-center">
                                <iconify-icon class="text-success-500" icon="heroicons-outline:heart"></iconify-icon>
                            </div>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-900 rounded-lg">
                            <div>
                                <div class="text-sm font-medium text-slate-900 dark:text-white">{{ __('Growth Trend') }}</div>
                                <div class="text-xs text-slate-500 dark:text-slate-400">
                                    @php
                                        $currentMonth = $data['userGrowth']->first()['count'] ?? 0;
                                        $previousMonth = $data['userGrowth']->skip(1)->first()['count'] ?? 0;
                                        $growth = $previousMonth > 0 ? (($currentMonth - $previousMonth) / $previousMonth) * 100 : 0;
                                    @endphp
                                    @if($growth > 0)
                                        {{ __('Growing') }} (+{{ number_format($growth, 1) }}%)
                                    @elseif($growth < 0)
                                        {{ __('Declining') }} ({{ number_format($growth, 1) }}%)
                                    @else
                                        {{ __('Stable') }}
                                    @endif
                                </div>
                            </div>
                            <div class="w-8 h-8 rounded-full bg-warning-500 bg-opacity-10 flex items-center justify-center">
                                <iconify-icon class="text-warning-500" icon="heroicons-outline:trending-up"></iconify-icon>
                            </div>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-900 rounded-lg">
                            <div>
                                <div class="text-sm font-medium text-slate-900 dark:text-white">{{ __('Conversion Rate') }}</div>
                                <div class="text-xs text-slate-500 dark:text-slate-400">
                                    @php
                                        $totalUsers = array_sum($data['usersBySubscriptionStatus']);
                                        $activeUsers = $data['usersBySubscriptionStatus']['active'];
                                        $conversionRate = $totalUsers > 0 ? ($activeUsers / $totalUsers) * 100 : 0;
                                    @endphp
                                    {{ number_format($conversionRate, 1) }}% to paid
                                </div>
                            </div>
                            <div class="w-8 h-8 rounded-full bg-info-500 bg-opacity-10 flex items-center justify-center">
                                <iconify-icon class="text-info-500" icon="heroicons-outline:chart-pie"></iconify-icon>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script>
        // User Growth Chart
        const userGrowthData = @json($data['userGrowth']);
        const growthChart = new ApexCharts(document.querySelector("#user-growth-chart"), {
            series: [{
                name: 'New Users',
                data: userGrowthData.map(item => item.count)
            }],
            chart: {
                type: 'area',
                height: 350,
                toolbar: { show: false }
            },
            xaxis: {
                categories: userGrowthData.map(item => item.month)
            },
            yaxis: {
                labels: {
                    formatter: function (val) {
                        return Math.round(val);
                    }
                }
            },
            fill: {
                type: 'gradient',
                gradient: {
                    shadeIntensity: 1,
                    opacityFrom: 0.7,
                    opacityTo: 0.3,
                }
            },
            stroke: {
                curve: 'smooth',
                width: 2
            },
            colors: ['#4F46E5']
        });
        growthChart.render();

        // User Distribution Pie Chart
        const userDistributionData = @json($data['usersBySubscriptionStatus']);
        const distributionChart = new ApexCharts(document.querySelector("#user-distribution-chart"), {
            series: [
                userDistributionData.active,
                userDistributionData.canceled,
                userDistributionData.no_subscription
            ],
            chart: {
                type: 'donut',
                height: 350
            },
            labels: ['Active Subscribers', 'Canceled', 'No Subscription'],
            colors: ['#10B981', '#F59E0B', '#EF4444'],
            legend: {
                position: 'bottom'
            },
            plotOptions: {
                pie: {
                    donut: {
                        labels: {
                            show: true,
                            total: {
                                show: true,
                                label: 'Total Users',
                                formatter: function (w) {
                                    return w.globals.seriesTotals.reduce((a, b) => a + b, 0);
                                }
                            }
                        }
                    }
                }
            }
        });
        distributionChart.render();
    </script>
    @endpush
</x-app-layout>
