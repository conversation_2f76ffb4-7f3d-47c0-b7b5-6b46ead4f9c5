<x-app-layout>
    <!-- START:: Breadcrumbs -->
    <div class="flex justify-between flex-wrap items-center mb-6">
        <h4 class="font-medium lg:text-2xl text-xl capitalize text-slate-900 inline-block ltr:pr-4 rtl:pl-4 mb-4 sm:mb-0 flex space-x-3 rtl:space-x-reverse">{{ __('Subscription Reports') }}</h4>
        <div class="flex sm:space-x-4 space-x-2 sm:justify-end items-center rtl:space-x-reverse">
            <a href="{{ route('admin.reports.index') }}" class="btn leading-0 inline-flex justify-center bg-white text-slate-700 dark:bg-slate-800 dark:text-slate-300 !font-normal">
                <span class="flex items-center">
                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2 font-light" icon="heroicons-outline:arrow-left"></iconify-icon>
                    <span>Back to Reports</span>
                </span>
            </a>
            <button class="btn leading-0 inline-flex justify-center bg-white text-slate-700 dark:bg-slate-800 dark:text-slate-300 !font-normal">
                <span class="flex items-center">
                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2 font-light" icon="heroicons-outline:download"></iconify-icon>
                    <span>Export</span>
                </span>
            </button>
        </div>
    </div>
    <!-- END:: Breadcrumbs -->

    <!-- Key Metrics -->
    <div class="grid grid-cols-12 gap-5 mb-5">
        <div class="2xl:col-span-3 lg:col-span-6 col-span-12">
            <div class="py-[18px] px-4 rounded-[6px] bg-[#E5F9FF] dark:bg-slate-900 card">
                <div class="flex items-center space-x-6 rtl:space-x-reverse">
                    <div class="flex-none">
                        <div class="w-12 h-12 rounded-full bg-primary-500 bg-opacity-10 flex items-center justify-center">
                            <iconify-icon class="text-2xl text-primary-500" icon="heroicons-outline:credit-card"></iconify-icon>
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                            {{ __('Active Subscriptions') }}
                        </div>
                        <div class="text-slate-900 dark:text-white text-lg font-medium">
                            {{ $data['subscriptionsByStatus']->where('status', 'active')->first()->count ?? 0 }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="2xl:col-span-3 lg:col-span-6 col-span-12">
            <div class="py-[18px] px-4 rounded-[6px] bg-[#FFEDE5] dark:bg-slate-900 card">
                <div class="flex items-center space-x-6 rtl:space-x-reverse">
                    <div class="flex-none">
                        <div class="w-12 h-12 rounded-full bg-warning-500 bg-opacity-10 flex items-center justify-center">
                            <iconify-icon class="text-2xl text-warning-500" icon="heroicons-outline:x-circle"></iconify-icon>
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                            {{ __('Churn Rate') }}
                        </div>
                        <div class="text-slate-900 dark:text-white text-lg font-medium">
                            {{ $data['churnRate'] }}%
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="2xl:col-span-3 lg:col-span-6 col-span-12">
            <div class="py-[18px] px-4 rounded-[6px] bg-[#EAE5FF] dark:bg-slate-900 card">
                <div class="flex items-center space-x-6 rtl:space-x-reverse">
                    <div class="flex-none">
                        <div class="w-12 h-12 rounded-full bg-success-500 bg-opacity-10 flex items-center justify-center">
                            <iconify-icon class="text-2xl text-success-500" icon="heroicons-outline:clock"></iconify-icon>
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                            {{ __('Avg. Lifetime (Days)') }}
                        </div>
                        <div class="text-slate-900 dark:text-white text-lg font-medium">
                            {{ $data['averageLifetime'] }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="2xl:col-span-3 lg:col-span-6 col-span-12">
            <div class="py-[18px] px-4 rounded-[6px] bg-[#F0FDF4] dark:bg-slate-900 card">
                <div class="flex items-center space-x-6 rtl:space-x-reverse">
                    <div class="flex-none">
                        <div class="w-12 h-12 rounded-full bg-info-500 bg-opacity-10 flex items-center justify-center">
                            <iconify-icon class="text-2xl text-info-500" icon="heroicons-outline:trending-up"></iconify-icon>
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                            {{ __('Total Subscriptions') }}
                        </div>
                        <div class="text-slate-900 dark:text-white text-lg font-medium">
                            {{ $data['subscriptionsByStatus']->sum('count') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-12 gap-5 mb-5">
        <div class="lg:col-span-8 col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">{{ __('Monthly Subscription Growth') }}</h4>
                </header>
                <div class="card-body p-6">
                    <div id="monthly-subscriptions-chart" style="height: 350px;"></div>
                </div>
            </div>
        </div>

        <div class="lg:col-span-4 col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">{{ __('Subscription Status') }}</h4>
                </header>
                <div class="card-body p-6">
                    <div id="subscription-status-chart" style="height: 350px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Subscription by Product -->
    <div class="grid grid-cols-12 gap-5">
        <div class="lg:col-span-8 col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">{{ __('Subscriptions by Product') }}</h4>
                </header>
                <div class="card-body p-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
                            <thead class="bg-slate-200 dark:bg-slate-700">
                                <tr>
                                    <th scope="col" class="table-th">{{ __('Product Name') }}</th>
                                    <th scope="col" class="table-th">{{ __('Active Subscriptions') }}</th>
                                    <th scope="col" class="table-th">{{ __('Percentage') }}</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                                @php
                                    $totalSubscriptions = $data['subscriptionsByProduct']->sum('count');
                                @endphp
                                @forelse($data['subscriptionsByProduct'] as $product)
                                <tr>
                                    <td class="table-td">{{ $product->name }}</td>
                                    <td class="table-td">{{ number_format($product->count) }}</td>
                                    <td class="table-td">
                                        @if($totalSubscriptions > 0)
                                            {{ number_format(($product->count / $totalSubscriptions) * 100, 1) }}%
                                        @else
                                            0%
                                        @endif
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="3" class="table-td text-center text-slate-500">{{ __('No subscription data available') }}</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="lg:col-span-4 col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">{{ __('Subscription Insights') }}</h4>
                </header>
                <div class="card-body p-6">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-900 rounded-lg">
                            <div>
                                <div class="text-sm font-medium text-slate-900 dark:text-white">{{ __('Most Popular Product') }}</div>
                                <div class="text-xs text-slate-500 dark:text-slate-400">
                                    @if($data['subscriptionsByProduct']->isNotEmpty())
                                        {{ $data['subscriptionsByProduct']->sortByDesc('count')->first()->name }}
                                    @else
                                        {{ __('No data') }}
                                    @endif
                                </div>
                            </div>
                            <div class="w-8 h-8 rounded-full bg-primary-500 bg-opacity-10 flex items-center justify-center">
                                <iconify-icon class="text-primary-500" icon="heroicons-outline:star"></iconify-icon>
                            </div>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-900 rounded-lg">
                            <div>
                                <div class="text-sm font-medium text-slate-900 dark:text-white">{{ __('Cancellation Rate') }}</div>
                                <div class="text-xs text-slate-500 dark:text-slate-400">{{ $data['churnRate'] }}% this period</div>
                            </div>
                            <div class="w-8 h-8 rounded-full bg-warning-500 bg-opacity-10 flex items-center justify-center">
                                <iconify-icon class="text-warning-500" icon="heroicons-outline:exclamation-triangle"></iconify-icon>
                            </div>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-900 rounded-lg">
                            <div>
                                <div class="text-sm font-medium text-slate-900 dark:text-white">{{ __('Retention Health') }}</div>
                                <div class="text-xs text-slate-500 dark:text-slate-400">
                                    @if($data['churnRate'] < 5)
                                        {{ __('Excellent') }}
                                    @elseif($data['churnRate'] < 10)
                                        {{ __('Good') }}
                                    @elseif($data['churnRate'] < 20)
                                        {{ __('Fair') }}
                                    @else
                                        {{ __('Needs Attention') }}
                                    @endif
                                </div>
                            </div>
                            <div class="w-8 h-8 rounded-full bg-success-500 bg-opacity-10 flex items-center justify-center">
                                <iconify-icon class="text-success-500" icon="heroicons-outline:heart"></iconify-icon>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script>
        // Monthly Subscriptions Chart
        const monthlySubscriptionsData = @json($data['monthlySubscriptions']);
        const subscriptionsChart = new ApexCharts(document.querySelector("#monthly-subscriptions-chart"), {
            series: [{
                name: 'New Subscriptions',
                data: monthlySubscriptionsData.map(item => item.count)
            }],
            chart: {
                type: 'area',
                height: 350,
                toolbar: { show: false }
            },
            xaxis: {
                categories: monthlySubscriptionsData.map(item => item.month)
            },
            yaxis: {
                labels: {
                    formatter: function (val) {
                        return Math.round(val);
                    }
                }
            },
            fill: {
                type: 'gradient',
                gradient: {
                    shadeIntensity: 1,
                    opacityFrom: 0.7,
                    opacityTo: 0.3,
                }
            },
            colors: ['#4F46E5']
        });
        subscriptionsChart.render();

        // Subscription Status Pie Chart
        const statusData = @json($data['subscriptionsByStatus']);
        const statusChart = new ApexCharts(document.querySelector("#subscription-status-chart"), {
            series: statusData.map(item => item.count),
            chart: {
                type: 'donut',
                height: 350
            },
            labels: statusData.map(item => item.status.charAt(0).toUpperCase() + item.status.slice(1)),
            colors: ['#10B981', '#F59E0B', '#EF4444', '#8B5CF6'],
            legend: {
                position: 'bottom'
            }
        });
        statusChart.render();
    </script>
    @endpush
</x-app-layout>
