<x-app-layout>
    <div class="space-y-8">
        <div class="block sm:flex items-center justify-between mb-6">
            <x-breadcrumb :pageTitle="$pageTitle" :breadcrumbItems="$breadcrumbItems"/>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="card">
                <div class="card-body">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                                <iconify-icon icon="heroicons:credit-card" class="text-blue-600 dark:text-blue-400 text-lg"></iconify-icon>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-slate-500 dark:text-slate-400 truncate">Total Payment Methods</dt>
                                <dd class="text-lg font-medium text-slate-900 dark:text-white">{{ number_format($stats['total']) }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                                <iconify-icon icon="heroicons:check-circle" class="text-green-600 dark:text-green-400 text-lg"></iconify-icon>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-slate-500 dark:text-slate-400 truncate">Active Methods</dt>
                                <dd class="text-lg font-medium text-slate-900 dark:text-white">{{ number_format($stats['active']) }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                                <iconify-icon icon="heroicons:building-office" class="text-purple-600 dark:text-purple-400 text-lg"></iconify-icon>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-slate-500 dark:text-slate-400 truncate">Investor Methods</dt>
                                <dd class="text-lg font-medium text-slate-900 dark:text-white">{{ number_format($stats['investor']) }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                                <iconify-icon icon="heroicons:rocket-launch" class="text-orange-600 dark:text-orange-400 text-lg"></iconify-icon>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-slate-500 dark:text-slate-400 truncate">Startup Methods</dt>
                                <dd class="text-lg font-medium text-slate-900 dark:text-white">{{ number_format($stats['startup']) }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Card Brand Distribution -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Card Brand Distribution</h4>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    @foreach($stats['card_brands'] as $brand)
                        <div class="text-center">
                            <div class="text-2xl font-bold text-slate-900 dark:text-white">{{ $brand->count }}</div>
                            <div class="text-sm text-slate-500 dark:text-slate-400 capitalize">{{ $brand->card_brand ?: 'Unknown' }}</div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="flex flex-wrap gap-4">
            <a href="{{ route('admin.payment-methods.three-d-secure') }}" class="btn btn-primary">
                <iconify-icon icon="heroicons:shield-check" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                3D Secure Events
            </a>
            <a href="{{ route('admin.payment-methods.analytics') }}" class="btn btn-secondary">
                <iconify-icon icon="heroicons:chart-bar" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                Analytics
            </a>
        </div>

        <!-- Filters Card -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Search & Filter</h4>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ route('admin.payment-methods.index') }}">
                    <div class="grid grid-cols-12 gap-5">
                        <div class="lg:col-span-3 col-span-12">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search"
                                   value="{{ $filters['search'] }}" placeholder="Search by user name, email, or card">
                        </div>
                        <div class="lg:col-span-2 col-span-12">
                            <label for="role" class="form-label">User Role</label>
                            <select class="form-control" id="role" name="role">
                                <option value="">All Roles</option>
                                <option value="investor" {{ $filters['role'] === 'investor' ? 'selected' : '' }}>Investor</option>
                                <option value="startup" {{ $filters['role'] === 'startup' ? 'selected' : '' }}>Startup</option>
                            </select>
                        </div>
                        <div class="lg:col-span-2 col-span-12">
                            <label for="card_brand" class="form-label">Card Brand</label>
                            <select class="form-control" id="card_brand" name="card_brand">
                                <option value="">All Brands</option>
                                @foreach($cardBrands as $brand)
                                    <option value="{{ $brand }}" {{ $filters['card_brand'] === $brand ? 'selected' : '' }}>
                                        {{ ucfirst($brand) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="lg:col-span-2 col-span-12">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">All Statuses</option>
                                <option value="active" {{ $filters['status'] === 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ $filters['status'] === 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                        </div>
                        <div class="lg:col-span-2 col-span-12">
                            <label for="per_page" class="form-label">Per Page</label>
                            <select class="form-control" id="per_page" name="per_page">
                                <option value="25" {{ $filters['per_page'] == 25 ? 'selected' : '' }}>25</option>
                                <option value="50" {{ $filters['per_page'] == 50 ? 'selected' : '' }}>50</option>
                                <option value="100" {{ $filters['per_page'] == 100 ? 'selected' : '' }}>100</option>
                            </select>
                        </div>
                        <div class="lg:col-span-1 col-span-12 flex items-end">
                            <button type="submit" class="btn inline-flex justify-center btn-primary w-full">
                                <iconify-icon icon="heroicons:magnifying-glass" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                                Search
                            </button>
                        </div>
                    </div>
                    <div class="mt-5">
                        <a href="{{ route('admin.payment-methods.index') }}" class="btn inline-flex justify-center btn-secondary btn-sm">
                            <iconify-icon icon="heroicons:x-mark" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                            Clear Filters
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Payment Methods Table -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Payment Methods ({{ $paymentMethods->total() }} total)</h4>
            </div>
            <div class="card-body px-6 pb-6">
                @if($paymentMethods->count() > 0)
                    <div class="overflow-x-auto -mx-6 dashcode-data-table">
                        <div class="inline-block min-w-full align-middle">
                            <div class="overflow-hidden">
                                <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700" id="data-table">
                                    <thead class="border-t border-slate-100 dark:border-slate-800">
                                        <tr>
                                            <th scope="col" class="table-th">User</th>
                                            <th scope="col" class="table-th">Role</th>
                                            <th scope="col" class="table-th">Card Details</th>
                                            <th scope="col" class="table-th">Status</th>
                                            <th scope="col" class="table-th">Default</th>
                                            <th scope="col" class="table-th">Added</th>
                                            <th scope="col" class="table-th">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                                        @foreach($paymentMethods as $paymentMethod)
                                            <tr>
                                                <td class="table-td">
                                                    <div class="flex items-center">
                                                        <div class="flex-shrink-0 h-10 w-10">
                                                            <div class="h-10 w-10 rounded-full bg-slate-200 dark:bg-slate-600 flex items-center justify-center">
                                                                <span class="text-sm font-medium text-slate-700 dark:text-slate-300">
                                                                    {{ strtoupper(substr($paymentMethod->user->name, 0, 2)) }}
                                                                </span>
                                                            </div>
                                                        </div>
                                                        <div class="ml-4">
                                                            <div class="text-sm font-medium text-slate-900 dark:text-white">
                                                                {{ $paymentMethod->user->name }}
                                                            </div>
                                                            <div class="text-sm text-slate-500 dark:text-slate-400">
                                                                {{ $paymentMethod->user->email }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="table-td">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                        {{ $paymentMethod->user->role === 'investor' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' }}">
                                                        {{ ucfirst($paymentMethod->user->role) }}
                                                    </span>
                                                </td>
                                                <td class="table-td">
                                                    <div class="flex items-center">
                                                        <div class="text-sm font-medium text-slate-900 dark:text-white">
                                                            {{ ucfirst($paymentMethod->card_brand) }} •••• {{ $paymentMethod->card_last_four }}
                                                        </div>
                                                    </div>
                                                    <div class="text-sm text-slate-500 dark:text-slate-400">
                                                        Expires {{ $paymentMethod->card_exp_month }}/{{ $paymentMethod->card_exp_year }}
                                                    </div>
                                                </td>
                                                <td class="table-td">
                                                    @if($paymentMethod->is_active)
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                            Active
                                                        </span>
                                                    @else
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                                            Inactive
                                                        </span>
                                                    @endif
                                                </td>
                                                <td class="table-td">
                                                    @if($paymentMethod->is_default)
                                                        <iconify-icon icon="heroicons:check-circle" class="text-green-500 text-lg"></iconify-icon>
                                                    @else
                                                        <iconify-icon icon="heroicons:minus-circle" class="text-slate-400 text-lg"></iconify-icon>
                                                    @endif
                                                </td>
                                                <td class="table-td">
                                                    {{ $paymentMethod->created_at->format('M d, Y') }}
                                                </td>
                                                <td class="table-td">
                                                    <div class="flex space-x-3">
                                                        <a href="{{ route('admin.investment.users') }}?search={{ urlencode($paymentMethod->user->email) }}"
                                                           class="action-btn" data-tippy-content="View User">
                                                            <iconify-icon icon="heroicons:eye"></iconify-icon>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-6">
                        {{ $paymentMethods->appends($filters)->links() }}
                    </div>
                @else
                    <div class="text-center py-12">
                        <iconify-icon icon="heroicons:credit-card" class="text-4xl text-slate-400 mb-4"></iconify-icon>
                        <h3 class="text-lg font-medium text-slate-900 dark:text-white mb-2">No payment methods found</h3>
                        <p class="text-slate-500 dark:text-slate-400">No payment methods match your current filters.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</x-app-layout>
