<x-app-layout>
    <div class="space-y-8">
        <div class="block sm:flex items-center justify-between mb-6">
            <x-breadcrumb :pageTitle="$pageTitle" :breadcrumbItems="$breadcrumbItems"/>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
            <div class="card">
                <div class="card-body">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                                <iconify-icon icon="heroicons:shield-check" class="text-blue-600 dark:text-blue-400 text-lg"></iconify-icon>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-slate-500 dark:text-slate-400 truncate">Total 3D Secure</dt>
                                <dd class="text-lg font-medium text-slate-900 dark:text-white">{{ number_format($stats['total']) }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                                <iconify-icon icon="heroicons:check-circle" class="text-green-600 dark:text-green-400 text-lg"></iconify-icon>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-slate-500 dark:text-slate-400 truncate">Successful</dt>
                                <dd class="text-lg font-medium text-slate-900 dark:text-white">{{ number_format($stats['successful']) }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center">
                                <iconify-icon icon="heroicons:x-circle" class="text-red-600 dark:text-red-400 text-lg"></iconify-icon>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-slate-500 dark:text-slate-400 truncate">Failed</dt>
                                <dd class="text-lg font-medium text-slate-900 dark:text-white">{{ number_format($stats['failed']) }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center">
                                <iconify-icon icon="heroicons:clock" class="text-yellow-600 dark:text-yellow-400 text-lg"></iconify-icon>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-slate-500 dark:text-slate-400 truncate">Pending</dt>
                                <dd class="text-lg font-medium text-slate-900 dark:text-white">{{ number_format($stats['pending']) }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                                <iconify-icon icon="heroicons:chart-bar" class="text-purple-600 dark:text-purple-400 text-lg"></iconify-icon>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-slate-500 dark:text-slate-400 truncate">Success Rate</dt>
                                <dd class="text-lg font-medium text-slate-900 dark:text-white">{{ $stats['success_rate'] }}%</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="flex flex-wrap gap-4">
            <a href="{{ route('admin.payment-methods.index') }}" class="btn btn-secondary">
                <iconify-icon icon="heroicons:credit-card" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                Payment Methods
            </a>
            <a href="{{ route('admin.payment-methods.analytics') }}" class="btn btn-primary">
                <iconify-icon icon="heroicons:chart-bar" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                Analytics
            </a>
        </div>

        <!-- Filters Card -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Search & Filter</h4>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ route('admin.payment-methods.three-d-secure') }}">
                    <div class="grid grid-cols-12 gap-5">
                        <div class="lg:col-span-3 col-span-12">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search"
                                   value="{{ $filters['search'] }}" placeholder="Search by user name or email">
                        </div>
                        <div class="lg:col-span-2 col-span-12">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">All Statuses</option>
                                <option value="requires_action" {{ $filters['status'] === 'requires_action' ? 'selected' : '' }}>Requires Action</option>
                                <option value="succeeded" {{ $filters['status'] === 'succeeded' ? 'selected' : '' }}>Succeeded</option>
                                <option value="failed" {{ $filters['status'] === 'failed' ? 'selected' : '' }}>Failed</option>
                            </select>
                        </div>
                        <div class="lg:col-span-2 col-span-12">
                            <label for="date_from" class="form-label">From Date</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="{{ $filters['date_from'] }}">
                        </div>
                        <div class="lg:col-span-2 col-span-12">
                            <label for="date_to" class="form-label">To Date</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="{{ $filters['date_to'] }}">
                        </div>
                        <div class="lg:col-span-2 col-span-12">
                            <label for="per_page" class="form-label">Per Page</label>
                            <select class="form-control" id="per_page" name="per_page">
                                <option value="25" {{ $filters['per_page'] == 25 ? 'selected' : '' }}>25</option>
                                <option value="50" {{ $filters['per_page'] == 50 ? 'selected' : '' }}>50</option>
                                <option value="100" {{ $filters['per_page'] == 100 ? 'selected' : '' }}>100</option>
                            </select>
                        </div>
                        <div class="lg:col-span-1 col-span-12 flex items-end">
                            <button type="submit" class="btn inline-flex justify-center btn-primary w-full">
                                <iconify-icon icon="heroicons:magnifying-glass" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                                Search
                            </button>
                        </div>
                    </div>
                    <div class="mt-5">
                        <a href="{{ route('admin.payment-methods.three-d-secure') }}" class="btn inline-flex justify-center btn-secondary btn-sm">
                            <iconify-icon icon="heroicons:x-mark" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                            Clear Filters
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- 3D Secure Events Table -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">3D Secure Events ({{ $threeDSecureEvents->total() }} total)</h4>
            </div>
            <div class="card-body px-6 pb-6">
                @if($threeDSecureEvents->count() > 0)
                    <div class="overflow-x-auto -mx-6 dashcode-data-table">
                        <div class="inline-block min-w-full align-middle">
                            <div class="overflow-hidden">
                                <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700" id="data-table">
                                    <thead class="border-t border-slate-100 dark:border-slate-800">
                                        <tr>
                                            <th scope="col" class="table-th">User</th>
                                            <th scope="col" class="table-th">Payment Method</th>
                                            <th scope="col" class="table-th">Amount</th>
                                            <th scope="col" class="table-th">Status</th>
                                            <th scope="col" class="table-th">3D Secure Status</th>
                                            <th scope="col" class="table-th">Date</th>
                                            <th scope="col" class="table-th">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                                        @foreach($threeDSecureEvents as $event)
                                            <tr>
                                                <td class="table-td">
                                                    <div class="flex items-center">
                                                        <div class="flex-shrink-0 h-10 w-10">
                                                            <div class="h-10 w-10 rounded-full bg-slate-200 dark:bg-slate-600 flex items-center justify-center">
                                                                <span class="text-sm font-medium text-slate-700 dark:text-slate-300">
                                                                    {{ strtoupper(substr($event->user->name, 0, 2)) }}
                                                                </span>
                                                            </div>
                                                        </div>
                                                        <div class="ml-4">
                                                            <div class="text-sm font-medium text-slate-900 dark:text-white">
                                                                {{ $event->user->name }}
                                                            </div>
                                                            <div class="text-sm text-slate-500 dark:text-slate-400">
                                                                {{ $event->user->email }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="table-td">
                                                    @if($event->paymentMethod)
                                                        <div class="text-sm font-medium text-slate-900 dark:text-white">
                                                            {{ ucfirst($event->paymentMethod->card_brand) }} •••• {{ $event->paymentMethod->card_last_four }}
                                                        </div>
                                                    @else
                                                        <span class="text-sm text-slate-500 dark:text-slate-400">N/A</span>
                                                    @endif
                                                </td>
                                                <td class="table-td">
                                                    <div class="text-sm font-medium text-slate-900 dark:text-white">
                                                        ${{ number_format($event->amount, 2) }}
                                                    </div>
                                                    <div class="text-sm text-slate-500 dark:text-slate-400">
                                                        {{ strtoupper($event->currency) }}
                                                    </div>
                                                </td>
                                                <td class="table-td">
                                                    @php
                                                        $statusClasses = [
                                                            'succeeded' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
                                                            'failed' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
                                                            'requires_action' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
                                                            'pending' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
                                                        ];
                                                        $statusClass = $statusClasses[$event->status] ?? 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
                                                    @endphp
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $statusClass }}">
                                                        {{ ucfirst(str_replace('_', ' ', $event->status)) }}
                                                    </span>
                                                </td>
                                                <td class="table-td">
                                                    @if($event->metadata && isset($event->metadata['three_d_secure_required']) && $event->metadata['three_d_secure_required'])
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                                                            Required
                                                        </span>
                                                    @elseif($event->status === 'requires_action')
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                                            Action Required
                                                        </span>
                                                    @else
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                                                            N/A
                                                        </span>
                                                    @endif
                                                </td>
                                                <td class="table-td">
                                                    {{ $event->created_at->format('M d, Y H:i') }}
                                                </td>
                                                <td class="table-td">
                                                    <div class="flex space-x-3">
                                                        <a href="{{ route('admin.financial.payments.show', $event) }}" 
                                                           class="action-btn" data-tippy-content="View Payment">
                                                            <iconify-icon icon="heroicons:eye"></iconify-icon>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-6">
                        {{ $threeDSecureEvents->appends($filters)->links() }}
                    </div>
                @else
                    <div class="text-center py-12">
                        <iconify-icon icon="heroicons:shield-check" class="text-4xl text-slate-400 mb-4"></iconify-icon>
                        <h3 class="text-lg font-medium text-slate-900 dark:text-white mb-2">No 3D Secure events found</h3>
                        <p class="text-slate-500 dark:text-slate-400">No 3D Secure events match your current filters.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</x-app-layout>
