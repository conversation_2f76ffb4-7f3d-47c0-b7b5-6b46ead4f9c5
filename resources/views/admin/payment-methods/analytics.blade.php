<x-app-layout>
    <!-- START:: Breadcrumbs -->
    <div class="flex justify-between flex-wrap items-center mb-6">
        <h4 class="font-medium lg:text-2xl text-xl capitalize text-slate-900 inline-block ltr:pr-4 rtl:pl-4 mb-4 sm:mb-0 flex space-x-3 rtl:space-x-reverse">{{ __('Payment Method Analytics') }}</h4>
        <div class="flex sm:space-x-4 space-x-2 sm:justify-end items-center rtl:space-x-reverse">
            <button class="btn leading-0 inline-flex justify-center bg-white text-slate-700 dark:bg-slate-800 dark:text-slate-300 !font-normal" data-period="7">
                <span class="flex items-center">
                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2 font-light" icon="heroicons-outline:calendar"></iconify-icon>
                    <span>7 Days</span>
                </span>
            </button>
            <button class="btn leading-0 inline-flex justify-center bg-primary-500 text-white !font-normal" data-period="30">
                <span class="flex items-center">
                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2 font-light" icon="heroicons-outline:calendar"></iconify-icon>
                    <span>30 Days</span>
                </span>
            </button>
            <button class="btn leading-0 inline-flex justify-center bg-white text-slate-700 dark:bg-slate-800 dark:text-slate-300 !font-normal" data-period="90">
                <span class="flex items-center">
                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2 font-light" icon="heroicons-outline:calendar"></iconify-icon>
                    <span>90 Days</span>
                </span>
            </button>
        </div>
    </div>
    <!-- END:: Breadcrumbs -->

    <!-- Analytics Overview -->
    <div class="grid grid-cols-12 gap-5 mb-5">
        <div class="lg:col-span-8 col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">{{ __('Payment Methods Added Over Time') }}</h4>
                </header>
                <div class="card-body p-6">
                    <div id="payment-methods-timeline-chart" style="height: 350px;"></div>
                </div>
            </div>
        </div>

        <div class="lg:col-span-4 col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">{{ __('Quick Stats') }}</h4>
                </header>
                <div class="card-body p-6">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-900 rounded-lg">
                            <div>
                                <div class="text-sm font-medium text-slate-900 dark:text-white">{{ __('Total Payment Methods') }}</div>
                                <div class="text-xs text-slate-500 dark:text-slate-400">{{ __('All time') }}</div>
                            </div>
                            <div class="text-2xl font-bold text-primary-500">
                                {{ $analytics['payment_methods_over_time']->sum('count') }}
                            </div>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-900 rounded-lg">
                            <div>
                                <div class="text-sm font-medium text-slate-900 dark:text-white">{{ __('Most Active User') }}</div>
                                <div class="text-xs text-slate-500 dark:text-slate-400">
                                    @if($analytics['usage_analytics']->isNotEmpty())
                                        {{ $analytics['usage_analytics']->first()->user_name }}
                                    @else
                                        {{ __('No data') }}
                                    @endif
                                </div>
                            </div>
                            <div class="w-8 h-8 rounded-full bg-success-500 bg-opacity-10 flex items-center justify-center">
                                <iconify-icon class="text-success-500" icon="heroicons-outline:star"></iconify-icon>
                            </div>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-900 rounded-lg">
                            <div>
                                <div class="text-sm font-medium text-slate-900 dark:text-white">{{ __('Total Transactions') }}</div>
                                <div class="text-xs text-slate-500 dark:text-slate-400">{{ __('Using tracked methods') }}</div>
                            </div>
                            <div class="text-2xl font-bold text-warning-500">
                                {{ $analytics['usage_analytics']->sum('payment_count') }}
                            </div>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-900 rounded-lg">
                            <div>
                                <div class="text-sm font-medium text-slate-900 dark:text-white">{{ __('Total Volume') }}</div>
                                <div class="text-xs text-slate-500 dark:text-slate-400">{{ __('Processed amount') }}</div>
                            </div>
                            <div class="text-2xl font-bold text-info-500">
                                ${{ number_format($analytics['usage_analytics']->sum('total_amount') / 100, 2) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Usage Analytics Table -->
    <div class="grid grid-cols-12 gap-5">
        <div class="col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">{{ __('Payment Method Usage Analytics') }}</h4>
                    <div class="relative">
                        <button class="btn btn-sm bg-primary-500 text-white">
                            <iconify-icon class="w-4 h-4 ltr:mr-2 rtl:ml-2" icon="heroicons-outline:download"></iconify-icon>
                            {{ __('Export') }}
                        </button>
                    </div>
                </header>
                <div class="card-body p-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
                            <thead class="bg-slate-200 dark:bg-slate-700">
                                <tr>
                                    <th scope="col" class="table-th">{{ __('User') }}</th>
                                    <th scope="col" class="table-th">{{ __('Card Details') }}</th>
                                    <th scope="col" class="table-th">{{ __('Role') }}</th>
                                    <th scope="col" class="table-th">{{ __('Payment Count') }}</th>
                                    <th scope="col" class="table-th">{{ __('Total Amount') }}</th>
                                    <th scope="col" class="table-th">{{ __('Avg. Transaction') }}</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                                @forelse($analytics['usage_analytics'] as $usage)
                                <tr>
                                    <td class="table-td">
                                        <div class="flex items-center">
                                            <div class="flex-none">
                                                <div class="w-8 h-8 rounded-[100%] ltr:mr-3 rtl:ml-3">
                                                    <div class="w-full h-full rounded-[100%] bg-slate-100 dark:bg-slate-700 flex items-center justify-center">
                                                        <span class="text-xs font-medium text-slate-600 dark:text-slate-300">
                                                            {{ strtoupper(substr($usage->user_name, 0, 2)) }}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex-1 text-start">
                                                <h4 class="text-sm font-medium text-slate-600 whitespace-nowrap">
                                                    {{ $usage->user_name }}
                                                </h4>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="table-td">
                                        <div class="flex items-center space-x-2">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                                                {{ strtoupper($usage->card_brand) }}
                                            </span>
                                            <span class="text-sm text-slate-500">•••• {{ $usage->card_last_four }}</span>
                                        </div>
                                    </td>
                                    <td class="table-td">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                            {{ $usage->user_role === 'investor' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' : 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400' }}">
                                            {{ ucfirst($usage->user_role) }}
                                        </span>
                                    </td>
                                    <td class="table-td">
                                        <span class="text-sm font-medium">{{ $usage->payment_count }}</span>
                                    </td>
                                    <td class="table-td">
                                        <span class="text-sm font-medium">${{ number_format($usage->total_amount / 100, 2) }}</span>
                                    </td>
                                    <td class="table-td">
                                        <span class="text-sm text-slate-500">
                                            @if($usage->payment_count > 0)
                                                ${{ number_format(($usage->total_amount / 100) / $usage->payment_count, 2) }}
                                            @else
                                                $0.00
                                            @endif
                                        </span>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="6" class="table-td text-center text-slate-500">
                                        <div class="py-8">
                                            <iconify-icon class="w-12 h-12 mx-auto text-slate-400 mb-4" icon="heroicons-outline:credit-card"></iconify-icon>
                                            <p>{{ __('No payment method usage data available for the selected period') }}</p>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script>
        // Payment Methods Timeline Chart
        const timelineData = @json($analytics['payment_methods_over_time']);
        const timelineChart = new ApexCharts(document.querySelector("#payment-methods-timeline-chart"), {
            series: [{
                name: 'Payment Methods Added',
                data: timelineData.map(item => ({
                    x: item.date,
                    y: item.count
                }))
            }],
            chart: {
                type: 'area',
                height: 350,
                toolbar: { show: false }
            },
            xaxis: {
                type: 'datetime',
                labels: {
                    format: 'MMM dd'
                }
            },
            yaxis: {
                labels: {
                    formatter: function (val) {
                        return Math.round(val);
                    }
                }
            },
            fill: {
                type: 'gradient',
                gradient: {
                    shadeIntensity: 1,
                    opacityFrom: 0.7,
                    opacityTo: 0.3,
                }
            },
            stroke: {
                curve: 'smooth',
                width: 2
            },
            colors: ['#4F46E5'],
            tooltip: {
                x: {
                    format: 'MMM dd, yyyy'
                }
            }
        });
        timelineChart.render();

        // Period filter functionality
        document.querySelectorAll('[data-period]').forEach(button => {
            button.addEventListener('click', function() {
                const period = this.getAttribute('data-period');
                
                // Update button states
                document.querySelectorAll('[data-period]').forEach(btn => {
                    btn.classList.remove('bg-primary-500', 'text-white');
                    btn.classList.add('bg-white', 'text-slate-700', 'dark:bg-slate-800', 'dark:text-slate-300');
                });
                
                this.classList.remove('bg-white', 'text-slate-700', 'dark:bg-slate-800', 'dark:text-slate-300');
                this.classList.add('bg-primary-500', 'text-white');
                
                // Reload page with new period
                const url = new URL(window.location);
                url.searchParams.set('period', period);
                window.location.href = url.toString();
            });
        });
    </script>
    @endpush
</x-app-layout>
