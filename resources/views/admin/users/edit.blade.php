@extends('layouts.app')

@section('content')
<div class="content-wrapper transition-all duration-150 ltr:ml-0 xl:ltr:ml-[248px]">
    <div class="page-content">
        <div class="transition-all duration-150 container-fluid" id="page_layout">
            <div id="content_layout">
                <!-- Breadcrumb -->
                <x-breadcrumb :breadcrumbItems="$breadcrumbItems" :pageTitle="$pageTitle" />

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- User Info -->
                    <div class="lg:col-span-1">
                        <div class="card">
                            <div class="card-body text-center">
                                <div class="w-24 h-24 bg-slate-100 dark:bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <span class="text-2xl font-bold text-slate-600 dark:text-slate-300">
                                        {{ strtoupper(substr($user->name, 0, 2)) }}
                                    </span>
                                </div>
                                <h3 class="text-xl font-semibold text-slate-900 dark:text-white mb-2">{{ $user->name }}</h3>
                                <p class="text-slate-500 dark:text-slate-400 mb-4">{{ $user->email }}</p>
                                
                                <!-- Account Status -->
                                <div class="mb-4">
                                    @if($user->account_locked)
                                        <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100">
                                            Account Locked
                                        </span>
                                    @else
                                        <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                                            Account Active
                                        </span>
                                    @endif
                                </div>

                                <div class="text-sm text-slate-600 dark:text-slate-400">
                                    <p>Member since {{ $user->created_at->format('M d, Y') }}</p>
                                    <p>Last updated {{ $user->updated_at->diffForHumans() }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Edit Form -->
                    <div class="lg:col-span-2">
                        <form method="POST" action="{{ route('admin.users.update', $user) }}">
                            @csrf
                            @method('PUT')
                            
                            <!-- Basic Information -->
                            <div class="card mb-6">
                                <header class="card-header">
                                    <h4 class="card-title">Basic Information</h4>
                                </header>
                                <div class="card-body">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <!-- Name -->
                                        <div>
                                            <label for="name" class="form-label">Full Name</label>
                                            <input type="text" id="name" name="name" 
                                                   value="{{ old('name', $user->name) }}" 
                                                   class="form-control @error('name') is-invalid @enderror" 
                                                   required>
                                            @error('name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Email -->
                                        <div>
                                            <label for="email" class="form-label">Email Address</label>
                                            <input type="email" id="email" name="email" 
                                                   value="{{ old('email', $user->email) }}" 
                                                   class="form-control @error('email') is-invalid @enderror" 
                                                   required>
                                            @error('email')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Password -->
                                    <div class="mt-4">
                                        <label for="password" class="form-label">New Password</label>
                                        <input type="password" id="password" name="password" 
                                               class="form-control @error('password') is-invalid @enderror" 
                                               placeholder="Leave blank to keep current password">
                                        @error('password')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <div class="form-text">Leave blank to keep the current password</div>
                                    </div>

                                    <!-- Password Confirmation -->
                                    <div class="mt-4">
                                        <label for="password_confirmation" class="form-label">Confirm New Password</label>
                                        <input type="password" id="password_confirmation" name="password_confirmation" 
                                               class="form-control" 
                                               placeholder="Confirm new password">
                                    </div>
                                </div>
                            </div>

                            <!-- Roles and Permissions -->
                            <div class="card mb-6">
                                <header class="card-header">
                                    <h4 class="card-title">Roles and Permissions</h4>
                                </header>
                                <div class="card-body">
                                    <div class="mb-4">
                                        <label class="form-label">Assign Roles</label>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                            @foreach($roles as $role)
                                            <div class="form-check">
                                                <input type="checkbox" 
                                                       id="role_{{ $role->id }}" 
                                                       name="roles[]" 
                                                       value="{{ $role->id }}" 
                                                       class="form-check-input"
                                                       {{ $user->roles->contains($role->id) ? 'checked' : '' }}>
                                                <label for="role_{{ $role->id }}" class="form-check-label">
                                                    <span class="font-medium">{{ $role->name }}</span>
                                                    @if($role->permissions->count() > 0)
                                                        <span class="text-sm text-slate-500 dark:text-slate-400 block">
                                                            {{ $role->permissions->count() }} permissions
                                                        </span>
                                                    @endif
                                                </label>
                                            </div>
                                            @endforeach
                                        </div>
                                        @error('roles')
                                            <div class="invalid-feedback d-block">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Current Permissions Preview -->
                                    <div class="mt-4">
                                        <label class="form-label">Current Permissions</label>
                                        <div class="bg-slate-50 dark:bg-slate-800 rounded-lg p-4">
                                            <div id="permissionsPreview" class="flex flex-wrap gap-2">
                                                @foreach($user->getAllPermissions() as $permission)
                                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                                                        {{ $permission->name }}
                                                    </span>
                                                @endforeach
                                            </div>
                                            @if($user->getAllPermissions()->isEmpty())
                                                <p class="text-sm text-slate-500 dark:text-slate-400">No permissions assigned</p>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Account Settings -->
                            <div class="card mb-6">
                                <header class="card-header">
                                    <h4 class="card-title">Account Settings</h4>
                                </header>
                                <div class="card-body">
                                    <div class="form-check">
                                        <input type="checkbox" 
                                               id="email_verified" 
                                               name="email_verified" 
                                               value="1" 
                                               class="form-check-input"
                                               {{ $user->email_verified_at ? 'checked' : '' }}>
                                        <label for="email_verified" class="form-check-label">
                                            Email Verified
                                        </label>
                                    </div>
                                    <div class="form-text">Check this to mark the user's email as verified</div>

                                    @if($user->account_locked)
                                    <div class="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                                        <div class="flex items-start space-x-3">
                                            <iconify-icon icon="heroicons-outline:exclamation-triangle" class="text-red-500 text-xl mt-0.5"></iconify-icon>
                                            <div>
                                                <h5 class="font-medium text-red-800 dark:text-red-200">Account Locked</h5>
                                                <p class="text-sm text-red-700 dark:text-red-300 mt-1">
                                                    This account is currently locked. 
                                                    @if($user->account_locked_reason)
                                                        Reason: {{ $user->account_locked_reason }}
                                                    @endif
                                                </p>
                                                <button type="button" 
                                                        class="btn btn-sm btn-success mt-2" 
                                                        onclick="toggleAccountStatus({{ $user->id }}, 'unlock')">
                                                    <iconify-icon icon="heroicons-outline:lock-open" class="mr-2"></iconify-icon>
                                                    Unlock Account
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Form Actions -->
                            <div class="flex space-x-4">
                                <button type="submit" class="btn btn-primary">
                                    <iconify-icon icon="heroicons-outline:check" class="mr-2"></iconify-icon>
                                    Update User
                                </button>
                                <a href="{{ route('admin.users.show', $user) }}" class="btn btn-secondary">
                                    <iconify-icon icon="heroicons-outline:x" class="mr-2"></iconify-icon>
                                    Cancel
                                </a>
                                <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
                                    <iconify-icon icon="heroicons-outline:arrow-left" class="mr-2"></iconify-icon>
                                    Back to Users
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Account Status Modal -->
<div id="accountStatusModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">Unlock Account</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="accountStatusForm" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <p id="confirmText">Are you sure you want to unlock this user account?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success" id="confirmBtn">Unlock Account</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function toggleAccountStatus(userId, action) {
    const modal = document.getElementById('accountStatusModal');
    const form = document.getElementById('accountStatusForm');

    // Set form action
    form.action = `/admin/users/${userId}/account-status`;

    // Add hidden action field
    let actionInput = form.querySelector('input[name="action"]');
    if (!actionInput) {
        actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        form.appendChild(actionInput);
    }
    actionInput.value = action;

    // Show modal (assuming Bootstrap modal)
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();
}

// Role permissions preview
document.addEventListener('DOMContentLoaded', function() {
    const roleCheckboxes = document.querySelectorAll('input[name="roles[]"]');
    const permissionsPreview = document.getElementById('permissionsPreview');

    // Role permissions mapping (you might want to pass this from the controller)
    const rolePermissions = @json($roles->mapWithKeys(function($role) {
        return [$role->id => $role->permissions->pluck('name')->toArray()];
    }));

    function updatePermissionsPreview() {
        const selectedRoles = Array.from(roleCheckboxes)
            .filter(checkbox => checkbox.checked)
            .map(checkbox => parseInt(checkbox.value));

        const allPermissions = new Set();
        selectedRoles.forEach(roleId => {
            if (rolePermissions[roleId]) {
                rolePermissions[roleId].forEach(permission => allPermissions.add(permission));
            }
        });

        permissionsPreview.innerHTML = '';
        if (allPermissions.size > 0) {
            Array.from(allPermissions).sort().forEach(permission => {
                const span = document.createElement('span');
                span.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100';
                span.textContent = permission;
                permissionsPreview.appendChild(span);
            });
        } else {
            const p = document.createElement('p');
            p.className = 'text-sm text-slate-500 dark:text-slate-400';
            p.textContent = 'No permissions assigned';
            permissionsPreview.appendChild(p);
        }
    }

    roleCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updatePermissionsPreview);
    });
});
</script>
@endpush
