<x-app-layout>
    <div class="space-y-8">
        <!-- Breadcrumb -->
        <div>
            <x-breadcrumb :page-title="$pageTitle" :breadcrumb-items="$breadcrumbItems" />
        </div>

        <!-- User Profile Card -->
        <div class="card">
            <div class="card-body p-6">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                    <div class="flex items-center space-x-4">
                        <div class="flex-none w-20 h-20 rounded-full">
                            @if($user->getFirstMediaUrl('profile-image'))
                                <img src="{{ $user->getFirstMediaUrl('profile-image') }}" alt="{{ $user->name }}" 
                                     class="object-cover w-full h-full rounded-full">
                            @else
                                <div class="w-full h-full rounded-full bg-slate-600 flex items-center justify-center text-white text-2xl font-medium">
                                    {{ strtoupper(substr($user->name, 0, 2)) }}
                                </div>
                            @endif
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold text-slate-900 dark:text-white">{{ $user->name }}</h3>
                            <p class="text-slate-600 dark:text-slate-300">{{ $user->email }}</p>
                            <div class="flex items-center space-x-3 mt-2">
                                <span class="badge {{ $user->role === 'admin' ? 'badge-primary-light' : 'badge-success-light' }}">
                                    {{ ucfirst($user->role) }}
                                </span>
                                <span class="badge {{ $user->account_status === 'active' ? 'badge-success-light' : 'badge-danger-light' }}">
                                    {{ ucfirst($user->account_status) }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="flex space-x-3">
                        <a href="{{ route('admin.roles.edit', $user) }}" class="btn btn-outline-primary">
                            <iconify-icon icon="heroicons:pencil-square" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                            Edit
                        </a>
                        <form method="POST" action="{{ route('admin.roles.toggle-status', $user) }}" class="inline">
                            @csrf
                            @method('PUT')
                            <button type="submit" class="btn {{ $user->account_status === 'active' ? 'btn-outline-danger' : 'btn-outline-success' }}"
                                    onclick="return confirm('Are you sure you want to {{ $user->account_status === 'active' ? 'lock' : 'unlock' }} this user?')">
                                <iconify-icon icon="{{ $user->account_status === 'active' ? 'heroicons:lock-closed' : 'heroicons:lock-open' }}" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                                {{ $user->account_status === 'active' ? 'Lock Account' : 'Unlock Account' }}
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Details -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Basic Information -->
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Basic Information</h4>
                </div>
                <div class="card-body p-6">
                    <div class="space-y-4">
                        <div class="flex justify-between">
                            <span class="text-slate-600 dark:text-slate-300">Full Name:</span>
                            <span class="font-medium">{{ $user->name }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-slate-600 dark:text-slate-300">Email:</span>
                            <span class="font-medium">{{ $user->email }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-slate-600 dark:text-slate-300">Phone:</span>
                            <span class="font-medium">{{ $user->phone ?: 'Not provided' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-slate-600 dark:text-slate-300">Country:</span>
                            <span class="font-medium">{{ $user->country ?: 'Not provided' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-slate-600 dark:text-slate-300">City:</span>
                            <span class="font-medium">{{ $user->city ?: 'Not provided' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-slate-600 dark:text-slate-300">Role:</span>
                            <span class="badge {{ $user->role === 'admin' ? 'badge-primary-light' : 'badge-success-light' }}">
                                {{ ucfirst($user->role) }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account Status -->
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Account Status</h4>
                </div>
                <div class="card-body p-6">
                    <div class="space-y-4">
                        <div class="flex justify-between">
                            <span class="text-slate-600 dark:text-slate-300">Status:</span>
                            <span class="badge {{ $user->account_status === 'active' ? 'badge-success-light' : 'badge-danger-light' }}">
                                {{ ucfirst($user->account_status) }}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-slate-600 dark:text-slate-300">Email Verified:</span>
                            <span class="badge {{ $user->email_verified_at ? 'badge-success-light' : 'badge-warning-light' }}">
                                {{ $user->email_verified_at ? 'Verified' : 'Not Verified' }}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-slate-600 dark:text-slate-300">Created:</span>
                            <span class="font-medium">{{ $user->created_at->format('M d, Y \a\t g:i A') }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-slate-600 dark:text-slate-300">Last Updated:</span>
                            <span class="font-medium">{{ $user->updated_at->format('M d, Y \a\t g:i A') }}</span>
                        </div>
                        @if($user->account_locked_at)
                            <div class="flex justify-between">
                                <span class="text-slate-600 dark:text-slate-300">Locked At:</span>
                                <span class="font-medium text-danger-500">{{ $user->account_locked_at->format('M d, Y \a\t g:i A') }}</span>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Analyst Activity (only for analysts) -->
        @if($user->role === 'analyst' && !empty($activityStats))
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Analyst Activity</h4>
                </div>
                <div class="card-body p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-primary-500">{{ $activityStats['approved_requests'] }}</div>
                            <div class="text-slate-600 dark:text-slate-300">Approved Requests</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-success-500">{{ $activityStats['recent_activity']->count() }}</div>
                            <div class="text-slate-600 dark:text-slate-300">Recent Activities</div>
                        </div>
                    </div>

                    @if($activityStats['recent_activity']->count() > 0)
                        <div>
                            <h5 class="text-lg font-semibold mb-4">Recent Approved Requests</h5>
                            <div class="space-y-3">
                                @foreach($activityStats['recent_activity'] as $activity)
                                    <div class="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-700 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 rounded-full bg-success-500 flex items-center justify-center">
                                                <iconify-icon icon="heroicons:check" class="text-white text-sm"></iconify-icon>
                                            </div>
                                            <div>
                                                <div class="font-medium">{{ $activity->requester->name }} → {{ $activity->target->name }}</div>
                                                <div class="text-sm text-slate-600 dark:text-slate-300">Interest request approved</div>
                                            </div>
                                        </div>
                                        <div class="text-sm text-slate-500">
                                            {{ $activity->updated_at->diffForHumans() }}
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        @endif
    </div>
</x-app-layout>
