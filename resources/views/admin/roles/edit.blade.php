<x-app-layout>
    <div class="space-y-8">
        <!-- Breadcrumb -->
        <div>
            <x-breadcrumb :page-title="$pageTitle" :breadcrumb-items="$breadcrumbItems" />
        </div>

        <!-- Edit User Form -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Edit {{ ucfirst($user->role) }} Details</h4>
            </div>
            <div class="card-body p-6">
                <form method="POST" action="{{ route('admin.roles.update', $user) }}">
                    @csrf
                    @method('PUT')
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Basic Information -->
                        <div class="space-y-4">
                            <h5 class="text-lg font-semibold border-b border-slate-200 dark:border-slate-600 pb-2">Basic Information</h5>
                            
                            <div>
                                <label for="name" class="form-label">Full Name *</label>
                                <input type="text" id="name" name="name" value="{{ old('name', $user->name) }}" 
                                       class="form-control @error('name') !border-danger-500 @enderror" required>
                                @error('name')
                                    <div class="text-danger-500 text-sm mt-1">{{ $message }}</div>
                                @enderror
                            </div>

                            <div>
                                <label for="email" class="form-label">Email *</label>
                                <input type="email" id="email" name="email" value="{{ old('email', $user->email) }}" 
                                       class="form-control @error('email') !border-danger-500 @enderror" required>
                                @error('email')
                                    <div class="text-danger-500 text-sm mt-1">{{ $message }}</div>
                                @enderror
                            </div>

                            <div>
                                <label for="role" class="form-label">Role *</label>
                                <select id="role" name="role" class="form-control @error('role') !border-danger-500 @enderror" required>
                                    <option value="admin" {{ old('role', $user->role) === 'admin' ? 'selected' : '' }}>Admin</option>
                                    <option value="analyst" {{ old('role', $user->role) === 'analyst' ? 'selected' : '' }}>Analyst</option>
                                </select>
                                @error('role')
                                    <div class="text-danger-500 text-sm mt-1">{{ $message }}</div>
                                @enderror
                            </div>

                            <div>
                                <label for="phone" class="form-label">Phone</label>
                                <input type="text" id="phone" name="phone" value="{{ old('phone', $user->phone) }}" 
                                       class="form-control @error('phone') !border-danger-500 @enderror">
                                @error('phone')
                                    <div class="text-danger-500 text-sm mt-1">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Location & Security -->
                        <div class="space-y-4">
                            <h5 class="text-lg font-semibold border-b border-slate-200 dark:border-slate-600 pb-2">Location & Security</h5>
                            
                            <div>
                                <label for="country" class="form-label">Country</label>
                                <input type="text" id="country" name="country" value="{{ old('country', $user->country) }}" 
                                       class="form-control @error('country') !border-danger-500 @enderror">
                                @error('country')
                                    <div class="text-danger-500 text-sm mt-1">{{ $message }}</div>
                                @enderror
                            </div>

                            <div>
                                <label for="city" class="form-label">City</label>
                                <input type="text" id="city" name="city" value="{{ old('city', $user->city) }}" 
                                       class="form-control @error('city') !border-danger-500 @enderror">
                                @error('city')
                                    <div class="text-danger-500 text-sm mt-1">{{ $message }}</div>
                                @enderror
                            </div>

                            <div>
                                <label for="password" class="form-label">New Password</label>
                                <input type="password" id="password" name="password" 
                                       class="form-control @error('password') !border-danger-500 @enderror"
                                       placeholder="Leave blank to keep current password">
                                @error('password')
                                    <div class="text-danger-500 text-sm mt-1">{{ $message }}</div>
                                @enderror
                                <div class="text-slate-500 text-sm mt-1">Minimum 8 characters required</div>
                            </div>

                            <div>
                                <label for="password_confirmation" class="form-label">Confirm New Password</label>
                                <input type="password" id="password_confirmation" name="password_confirmation" 
                                       class="form-control"
                                       placeholder="Confirm new password">
                            </div>
                        </div>
                    </div>

                    <!-- Account Status -->
                    <div class="mt-8 pt-6 border-t border-slate-200 dark:border-slate-600">
                        <h5 class="text-lg font-semibold mb-4">Account Status</h5>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="flex items-center justify-between p-4 bg-slate-50 dark:bg-slate-700 rounded-lg">
                                <div>
                                    <div class="font-medium">Account Status</div>
                                    <div class="text-sm text-slate-600 dark:text-slate-300">
                                        Current status: 
                                        <span class="badge {{ $user->account_status === 'active' ? 'badge-success-light' : 'badge-danger-light' }}">
                                            {{ ucfirst($user->account_status) }}
                                        </span>
                                    </div>
                                </div>
                                <div>
                                    <label class="inline-flex items-center">
                                        <input type="checkbox" name="account_active" value="1" 
                                               {{ $user->account_status === 'active' ? 'checked' : '' }}
                                               class="form-checkbox">
                                        <span class="ltr:ml-2 rtl:mr-2">Active</span>
                                    </label>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-4 bg-slate-50 dark:bg-slate-700 rounded-lg">
                                <div>
                                    <div class="font-medium">Email Verification</div>
                                    <div class="text-sm text-slate-600 dark:text-slate-300">
                                        Status: 
                                        <span class="badge {{ $user->email_verified_at ? 'badge-success-light' : 'badge-warning-light' }}">
                                            {{ $user->email_verified_at ? 'Verified' : 'Not Verified' }}
                                        </span>
                                    </div>
                                </div>
                                <div>
                                    <label class="inline-flex items-center">
                                        <input type="checkbox" name="email_verified" value="1" 
                                               {{ $user->email_verified_at ? 'checked' : '' }}
                                               class="form-checkbox">
                                        <span class="ltr:ml-2 rtl:mr-2">Verified</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="flex justify-end space-x-3 mt-8 pt-6 border-t border-slate-200 dark:border-slate-600">
                        <a href="{{ route('admin.roles.show', $user) }}" class="btn btn-secondary">
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <iconify-icon icon="heroicons:check" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                            Update {{ ucfirst($user->role) }}
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Danger Zone (for non-current user) -->
        @if($user->id !== auth()->id())
            <div class="card border-danger-200">
                <div class="card-header bg-danger-50 dark:bg-danger-500/10">
                    <h4 class="card-title text-danger-600 dark:text-danger-400">Danger Zone</h4>
                </div>
                <div class="card-body p-6">
                    <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                        <div>
                            <h5 class="text-lg font-semibold text-danger-600 dark:text-danger-400">Delete User Account</h5>
                            <p class="text-slate-600 dark:text-slate-300">
                                Permanently delete this user account. This action cannot be undone and will remove all associated data.
                            </p>
                        </div>
                        <form method="POST" action="{{ route('admin.roles.destroy', $user) }}" class="inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger"
                                    onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone and will permanently remove all associated data.')">
                                <iconify-icon icon="heroicons:trash" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                                Delete Account
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        @endif
    </div>
</x-app-layout>
