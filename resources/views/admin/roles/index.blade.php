<x-app-layout>
    <div class="space-y-8">
        <!-- Breadcrumb -->
        <div>
            <x-breadcrumb :page-title="$pageTitle" :breadcrumb-items="$breadcrumbItems" />
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="card">
                <div class="card-body p-6">
                    <div class="flex items-center">
                        <div class="flex-none h-12 w-12 rounded-full bg-primary-500 text-white flex items-center justify-center">
                            <iconify-icon icon="heroicons:user-group" class="text-xl"></iconify-icon>
                        </div>
                        <div class="flex-1 ltr:ml-4 rtl:mr-4">
                            <div class="text-slate-600 dark:text-slate-300 text-sm font-medium mb-1">Total Admins</div>
                            <div class="text-slate-900 dark:text-white text-xl font-semibold">{{ $stats['total_admins'] }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body p-6">
                    <div class="flex items-center">
                        <div class="flex-none h-12 w-12 rounded-full bg-success-500 text-white flex items-center justify-center">
                            <iconify-icon icon="heroicons:chart-bar" class="text-xl"></iconify-icon>
                        </div>
                        <div class="flex-1 ltr:ml-4 rtl:mr-4">
                            <div class="text-slate-600 dark:text-slate-300 text-sm font-medium mb-1">Total Analysts</div>
                            <div class="text-slate-900 dark:text-white text-xl font-semibold">{{ $stats['total_analysts'] }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body p-6">
                    <div class="flex items-center">
                        <div class="flex-none h-12 w-12 rounded-full bg-warning-500 text-white flex items-center justify-center">
                            <iconify-icon icon="heroicons:check-circle" class="text-xl"></iconify-icon>
                        </div>
                        <div class="flex-1 ltr:ml-4 rtl:mr-4">
                            <div class="text-slate-600 dark:text-slate-300 text-sm font-medium mb-1">Active Users</div>
                            <div class="text-slate-900 dark:text-white text-xl font-semibold">{{ $stats['active_users'] }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Admin & Analyst Management -->
        <div class="card">
            <div class="card-header">
                <div class="flex justify-between items-center">
                    <h4 class="card-title">Admin & Analyst Management</h4>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createUserModal">
                        <iconify-icon icon="heroicons:plus" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                        Add Admin/Analyst
                    </button>
                </div>
            </div>

            <div class="card-body px-6 pb-6">
                <!-- Search and Filter -->
                <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 mb-6">
                    <form method="GET" class="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
                        <div class="flex-1">
                            <input type="text" name="search" value="{{ $filters['search'] ?? '' }}"
                                   placeholder="Search by name or email..."
                                   class="form-control">
                        </div>
                        <div>
                            <select name="role" class="form-control">
                                <option value="">All Roles</option>
                                <option value="admin" {{ ($filters['role'] ?? '') === 'admin' ? 'selected' : '' }}>Admin</option>
                                <option value="analyst" {{ ($filters['role'] ?? '') === 'analyst' ? 'selected' : '' }}>Analyst</option>
                            </select>
                        </div>
                        <div>
                            <select name="status" class="form-control">
                                <option value="">All Status</option>
                                <option value="active" {{ ($filters['status'] ?? '') === 'active' ? 'selected' : '' }}>Active</option>
                                <option value="locked" {{ ($filters['status'] ?? '') === 'locked' ? 'selected' : '' }}>Locked</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-outline-primary">
                            <iconify-icon icon="heroicons:magnifying-glass"></iconify-icon>
                            Search
                        </button>
                    </form>
                </div>

                <!-- Users Table -->
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
                        <thead class="border-t border-slate-100 dark:border-slate-800">
                            <tr>
                                <th scope="col" class="table-th">User</th>
                                <th scope="col" class="table-th">Role</th>
                                <th scope="col" class="table-th">Email</th>
                                <th scope="col" class="table-th">Status</th>
                                <th scope="col" class="table-th">Last Updated</th>
                                <th scope="col" class="table-th">Created</th>
                                <th scope="col" class="table-th">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                            @forelse($users as $user)
                                <tr>
                                    <td class="table-td">
                                        <div class="flex items-center">
                                            <div class="flex-none w-10 h-10 rounded-full ltr:mr-3 rtl:ml-3">
                                                @if($user->getFirstMediaUrl('profile-image'))
                                                    <img src="{{ $user->getFirstMediaUrl('profile-image') }}" alt="{{ $user->name }}"
                                                         class="object-cover w-full h-full rounded-full">
                                                @else
                                                    <div class="w-full h-full rounded-full bg-slate-600 flex items-center justify-center text-white text-sm font-medium">
                                                        {{ strtoupper(substr($user->name, 0, 2)) }}
                                                    </div>
                                                @endif
                                            </div>
                                            <div>
                                                <div class="text-sm font-medium text-slate-900 dark:text-white">{{ $user->name }}</div>
                                                <div class="text-xs text-slate-500">{{ $user->phone ?: 'No phone' }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="table-td">
                                        <span class="badge {{ $user->role === 'admin' ? 'badge-primary-light' : 'badge-success-light' }}">
                                            {{ ucfirst($user->role) }}
                                        </span>
                                    </td>
                                    <td class="table-td">{{ $user->email }}</td>
                                    <td class="table-td">
                                        <span class="badge {{ $user->accountStatus?->status === 'active' ? 'badge-success-light' : 'badge-danger-light' }}">
                                            {{ ucfirst($user->accountStatus?->status) }}
                                        </span>
                                    </td>
                                    <td class="table-td">
                                        {{ $user->updated_at->diffForHumans() }}
                                    </td>
                                    <td class="table-td">{{ $user->created_at->format('M d, Y') }}</td>
                                    <td class="table-td">
                                        <div class="flex space-x-3 rtl:space-x-reverse">
                                            <a href="{{ route('admin.roles.show', $user) }}" class="action-btn">
                                                <iconify-icon icon="heroicons:eye"></iconify-icon>
                                            </a>
                                            <a href="{{ route('admin.roles.edit', $user) }}" class="action-btn">
                                                <iconify-icon icon="heroicons:pencil-square"></iconify-icon>
                                            </a>
                                            <form method="POST" action="{{ route('admin.roles.toggle-status', $user) }}" class="inline">
                                                @csrf
                                                @method('PUT')
                                                <button type="submit" class="action-btn"
                                                        onclick="return confirm('Are you sure you want to {{ $user->accountStatus?->status === 'active' ? 'lock' : 'unlock' }} this user?')">
                                                    <iconify-icon icon="{{ $user->accountStatus?->status === 'active' ? 'heroicons:lock-closed' : 'heroicons:lock-open' }}"></iconify-icon>
                                                </button>
                                            </form>
                                            @if($user->id !== auth()->id())
                                                <form method="POST" action="{{ route('admin.roles.destroy', $user) }}" class="inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="action-btn text-danger-500"
                                                            onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                                                        <iconify-icon icon="heroicons:trash"></iconify-icon>
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="7" class="table-td text-center py-8">
                                        <div class="text-slate-500">No admin or analyst users found</div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($users->hasPages())
                    <div class="mt-6">
                        {{ $users->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Create User Modal -->
    <div class="modal fade" id="createUserModal" tabindex="-1" aria-labelledby="createUserModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createUserModalLabel">Create New Admin/Analyst</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" action="{{ route('admin.roles.store') }}">
                    @csrf
                    <div class="modal-body">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="create_name" class="form-label">Full Name *</label>
                                <input type="text" id="create_name" name="name" class="form-control" required>
                            </div>
                            <div>
                                <label for="create_email" class="form-label">Email *</label>
                                <input type="email" id="create_email" name="email" class="form-control" required>
                            </div>
                            <div>
                                <label for="create_role" class="form-label">Role *</label>
                                <select id="create_role" name="role" class="form-control" required>
                                    <option value="">Select Role</option>
                                    <option value="admin">Admin</option>
                                    <option value="analyst">Analyst</option>
                                </select>
                            </div>
                            <div>
                                <label for="create_phone" class="form-label">Phone</label>
                                <input type="text" id="create_phone" name="phone" class="form-control">
                            </div>
                            <div>
                                <label for="create_password" class="form-label">Password *</label>
                                <input type="password" id="create_password" name="password" class="form-control" required>
                            </div>
                            <div>
                                <label for="create_password_confirmation" class="form-label">Confirm Password *</label>
                                <input type="password" id="create_password_confirmation" name="password_confirmation" class="form-control" required>
                            </div>
                            <div>
                                <label for="create_country" class="form-label">Country</label>
                                <input type="text" id="create_country" name="country" class="form-control">
                            </div>
                            <div>
                                <label for="create_city" class="form-label">City</label>
                                <input type="text" id="create_city" name="city" class="form-control">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Create User</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-app-layout>
