<x-app-layout>
    <div class="space-y-8">
        <div>
            <x-breadcrumb :page-title="$pageTitle" :breadcrumb-items="$breadcrumbItems" />
        </div>

        <!-- Header with Create Button -->
        <div class="flex flex-wrap justify-between items-center gap-4">
            <div>
                <h4 class="font-medium lg:text-2xl text-xl capitalize text-slate-900 dark:text-slate-100">
                    Blog Management
                </h4>
                <p class="text-slate-600 dark:text-slate-400 text-sm">
                    Manage blog posts, create new content, and organize your blog.
                </p>
            </div>
            <div class="flex gap-3">
                <a href="{{ route('admin.blogs.create') }}" 
                   class="btn inline-flex justify-center btn-dark dark:bg-slate-700 dark:text-slate-300 m-1">
                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="heroicons-outline:plus"></iconify-icon>
                    Create Blog Post
                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="card">
            <div class="card-body">
                <form method="GET" action="{{ route('admin.blogs.index') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <!-- Search -->
                    <div class="input-area">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" id="search" name="search" class="form-control" 
                               placeholder="Search by title..." value="{{ $filters['search'] ?? '' }}">
                    </div>

                    <!-- Status Filter -->
                    <div class="input-area">
                        <label for="status" class="form-label">Status</label>
                        <select name="status" id="status" class="form-control">
                            <option value="">All Statuses</option>
                            <option value="draft" {{ ($filters['status'] ?? '') === 'draft' ? 'selected' : '' }}>Draft</option>
                            <option value="published" {{ ($filters['status'] ?? '') === 'published' ? 'selected' : '' }}>Published</option>
                            <option value="archived" {{ ($filters['status'] ?? '') === 'archived' ? 'selected' : '' }}>Archived</option>
                        </select>
                    </div>

                    <!-- Featured Filter -->
                    <div class="input-area">
                        <label for="featured" class="form-label">Featured</label>
                        <select name="featured" id="featured" class="form-control">
                            <option value="">All Posts</option>
                            <option value="1" {{ ($filters['featured'] ?? '') === '1' ? 'selected' : '' }}>Featured Only</option>
                            <option value="0" {{ ($filters['featured'] ?? '') === '0' ? 'selected' : '' }}>Not Featured</option>
                        </select>
                    </div>

                    <!-- Filter Buttons -->
                    <div class="input-area flex items-end gap-2">
                        <button type="submit" class="btn inline-flex justify-center btn-outline-dark">
                            <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="heroicons-outline:search"></iconify-icon>
                            Filter
                        </button>
                        <a href="{{ route('admin.blogs.index') }}" class="btn inline-flex justify-center btn-outline-light">
                            Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Blog Posts Table -->
        <div class="card">
            <div class="card-body px-6 pb-6">
                <div class="overflow-x-auto -mx-6 dashcode-data-table">
                    <span class="col-span-8 hidden"></span>
                    <span class="col-span-4 hidden"></span>
                    <div class="inline-block min-w-full align-middle">
                        <div class="overflow-hidden">
                            <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
                                <thead class="bg-slate-200 dark:bg-slate-700">
                                    <tr>
                                        <th scope="col" class="table-th">Featured Image</th>
                                        <th scope="col" class="table-th">Title</th>
                                        <th scope="col" class="table-th">Author</th>
                                        <th scope="col" class="table-th">Status</th>
                                        <th scope="col" class="table-th">Featured</th>
                                        <th scope="col" class="table-th">Views</th>
                                        <th scope="col" class="table-th">Published</th>
                                        <th scope="col" class="table-th">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                                    @forelse($blogs as $blog)
                                        <tr class="hover:bg-slate-200 dark:hover:bg-slate-700">
                                            <td class="table-td">
                                                @if($blog->featured_image_url)
                                                    <img src="{{ $blog->featured_image_url }}" alt="{{ $blog->title }}" 
                                                         class="w-16 h-12 object-cover rounded">
                                                @else
                                                    <div class="w-16 h-12 bg-slate-200 dark:bg-slate-600 rounded flex items-center justify-center">
                                                        <iconify-icon icon="heroicons-outline:photograph" class="text-slate-400"></iconify-icon>
                                                    </div>
                                                @endif
                                            </td>
                                            <td class="table-td">
                                                <div>
                                                    <div class="font-medium text-slate-600 dark:text-slate-300">
                                                        {{ Str::limit($blog->title, 50) }}
                                                    </div>
                                                    <div class="text-xs text-slate-500 dark:text-slate-400 mt-1">
                                                        {{ Str::limit($blog->excerpt, 80) }}
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="table-td">
                                                <div class="flex items-center space-x-3 rtl:space-x-reverse">
                                                    <div class="flex-none">
                                                        <div class="w-8 h-8 rounded-full bg-slate-200 dark:bg-slate-700 flex items-center justify-center text-xs font-medium">
                                                            {{ substr($blog->author->name, 0, 2) }}
                                                        </div>
                                                    </div>
                                                    <div class="flex-1 text-start">
                                                        <h4 class="text-sm font-medium text-slate-600 dark:text-slate-300">
                                                            {{ $blog->author->name }}
                                                        </h4>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="table-td">
                                                @if($blog->status === 'published')
                                                    <span class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-success-500 bg-success-500 text-xs">
                                                        Published
                                                    </span>
                                                @elseif($blog->status === 'draft')
                                                    <span class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-warning-500 bg-warning-500 text-xs">
                                                        Draft
                                                    </span>
                                                @else
                                                    <span class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-slate-500 bg-slate-500 text-xs">
                                                        Archived
                                                    </span>
                                                @endif
                                            </td>
                                            <td class="table-td">
                                                @if($blog->featured)
                                                    <iconify-icon icon="heroicons-solid:star" class="text-yellow-500 text-lg"></iconify-icon>
                                                @else
                                                    <iconify-icon icon="heroicons-outline:star" class="text-slate-400 text-lg"></iconify-icon>
                                                @endif
                                            </td>
                                            <td class="table-td">
                                                <span class="text-slate-600 dark:text-slate-300">{{ number_format($blog->views_count) }}</span>
                                            </td>
                                            <td class="table-td">
                                                @if($blog->published_at)
                                                    <span class="text-slate-600 dark:text-slate-300">{{ $blog->published_at->format('M d, Y') }}</span>
                                                @else
                                                    <span class="text-slate-400">Not published</span>
                                                @endif
                                            </td>
                                            <td class="table-td">
                                                <div class="flex space-x-3 rtl:space-x-reverse">
                                                    <a href="{{ route('admin.blogs.show', $blog) }}" 
                                                       class="action-btn" data-tippy-content="View">
                                                        <iconify-icon icon="heroicons:eye"></iconify-icon>
                                                    </a>
                                                    <a href="{{ route('admin.blogs.edit', $blog) }}" 
                                                       class="action-btn" data-tippy-content="Edit">
                                                        <iconify-icon icon="heroicons:pencil-square"></iconify-icon>
                                                    </a>
                                                    <form action="{{ route('admin.blogs.destroy', $blog) }}" method="POST" class="inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="action-btn" data-tippy-content="Delete"
                                                                onclick="return confirm('Are you sure you want to delete this blog post?')">
                                                            <iconify-icon icon="heroicons:trash"></iconify-icon>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="8" class="table-td text-center py-8">
                                                <div class="flex flex-col items-center">
                                                    <iconify-icon icon="heroicons-outline:document-text" class="text-6xl text-slate-300 dark:text-slate-600 mb-4"></iconify-icon>
                                                    <h3 class="text-lg font-medium text-slate-600 dark:text-slate-300 mb-2">No blog posts found</h3>
                                                    <p class="text-slate-500 dark:text-slate-400 mb-4">Get started by creating your first blog post.</p>
                                                    <a href="{{ route('admin.blogs.create') }}" 
                                                       class="btn inline-flex justify-center btn-dark">
                                                        Create Blog Post
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        @if($blogs->hasPages())
            <div class="card">
                <div class="card-body">
                    {{ $blogs->appends(request()->query())->links() }}
                </div>
            </div>
        @endif
    </div>
</x-app-layout>
