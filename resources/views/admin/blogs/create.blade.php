<x-app-layout>
    <div class="space-y-8">
        <div>
            <x-breadcrumb :page-title="$pageTitle" :breadcrumb-items="$breadcrumbItems" />
        </div>

        <!-- Header -->
        <div class="flex flex-wrap justify-between items-center gap-4">
            <div>
                <h4 class="font-medium lg:text-2xl text-xl capitalize text-slate-900 dark:text-slate-100">
                    Create Blog Post
                </h4>
                <p class="text-slate-600 dark:text-slate-400 text-sm">
                    Create a new blog post with rich content and media.
                </p>
            </div>
            <div class="flex gap-3">
                <a href="{{ route('admin.blogs.index') }}"
                   class="btn inline-flex justify-center btn-outline-dark">
                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="heroicons-outline:arrow-left"></iconify-icon>
                    Back to Blogs
                </a>
            </div>
        </div>

        <!-- Create Form -->
        <form method="POST" action="{{ route('admin.blogs.store') }}" enctype="multipart/form-data" class="space-y-6">
            @csrf

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Main Content -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Basic Information -->
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Basic Information</h4>
                        </div>
                        <div class="card-body flex flex-col p-6">
                            <div class="space-y-4">
                                <!-- Title -->
                                <div class="input-area">
                                    <label for="title" class="form-label">Title *</label>
                                    <input type="text" id="title" name="title" class="form-control"
                                           placeholder="Enter blog post title" value="{{ old('title') }}" required>
                                    <x-input-error :messages="$errors->get('title')" class="mt-2"/>
                                </div>

                                <!-- Excerpt -->
                                <div class="input-area">
                                    <label for="excerpt" class="form-label">Excerpt</label>
                                    <div id="excerpt-editor" class="quill-editor" style="min-height: 150px;"></div>
                                    <textarea id="excerpt" name="excerpt" style="display: none;">{{ old('excerpt') }}</textarea>
                                    <x-input-error :messages="$errors->get('excerpt')" class="mt-2"/>
                                    <div class="text-xs text-slate-500 dark:text-slate-400 mt-1">
                                        If left empty, an excerpt will be automatically generated from the content.
                                    </div>
                                </div>

                                <!-- Content -->
                                <div class="input-area">
                                    <label for="content" class="form-label">Content *</label>
                                    <div id="content-editor" class="quill-editor" style="min-height: 400px;"></div>
                                    <textarea id="content" name="content" style="display: none;">{{ old('content') }}</textarea>
                                    <x-input-error :messages="$errors->get('content')" class="mt-2"/>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- SEO Settings -->
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">SEO Settings</h4>
                        </div>
                        <div class="card-body flex flex-col p-6">
                            <div class="space-y-4">
                                <!-- Meta Title -->
                                <div class="input-area">
                                    <label for="meta_title" class="form-label">Meta Title</label>
                                    <input type="text" id="meta_title" name="meta_title" class="form-control"
                                           placeholder="SEO title (optional)" value="{{ old('meta_title') }}">
                                    <x-input-error :messages="$errors->get('meta_title')" class="mt-2"/>
                                </div>

                                <!-- Meta Description -->
                                <div class="input-area">
                                    <label for="meta_description" class="form-label">Meta Description</label>
                                    <textarea id="meta_description" name="meta_description" rows="3" class="form-control"
                                              placeholder="SEO description (optional)">{{ old('meta_description') }}</textarea>
                                    <x-input-error :messages="$errors->get('meta_description')" class="mt-2"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Publish Settings -->
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Publish Settings</h4>
                        </div>
                        <div class="card-body flex flex-col p-6">
                            <div class="space-y-4">
                                <!-- Status -->
                                <div class="input-area">
                                    <label for="status" class="form-label">Status *</label>
                                    <select name="status" id="status" class="form-control" required>
                                        <option value="draft" {{ old('status') === 'draft' ? 'selected' : '' }}>Draft</option>
                                        <option value="published" {{ old('status') === 'published' ? 'selected' : '' }}>Published</option>
                                        <option value="archived" {{ old('status') === 'archived' ? 'selected' : '' }}>Archived</option>
                                    </select>
                                    <x-input-error :messages="$errors->get('status')" class="mt-2"/>
                                </div>

                                <!-- Published Date -->
                                <div class="input-area">
                                    <label for="published_at" class="form-label">Publish Date</label>
                                    <input type="datetime-local" id="published_at" name="published_at" class="form-control"
                                           value="{{ old('published_at') }}">
                                    <x-input-error :messages="$errors->get('published_at')" class="mt-2"/>
                                    <div class="text-xs text-slate-500 dark:text-slate-400 mt-1">
                                        Leave empty to publish immediately when status is "Published"
                                    </div>
                                </div>

                                <!-- Featured -->
                                <div class="checkbox-area">
                                    <label class="inline-flex items-center cursor-pointer">
                                        <input type="checkbox" name="featured" value="1" class="hidden" id="featured"
                                               {{ old('featured') ? 'checked' : '' }}>
                                        <span class="h-4 w-4 border flex-none border-slate-100 dark:border-slate-800 rounded inline-flex ltr:mr-3 rtl:ml-3 relative transition-all duration-150 bg-slate-100 dark:bg-slate-900">
                                            <img src="{{ asset('assets/images/icon/ck-white.svg') }}" alt="" class="h-[10px] w-[10px] block m-auto opacity-0">
                                        </span>
                                        <span class="text-slate-500 dark:text-slate-400 text-sm leading-6">Featured Post</span>
                                    </label>
                                    <x-input-error :messages="$errors->get('featured')" class="mt-2"/>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Featured Image -->
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Featured Image</h4>
                        </div>
                        <div class="card-body flex flex-col p-6">
                            <div class="input-area">
                                <label for="featured_image" class="form-label">Upload Image</label>
                                <input type="file" id="featured_image" name="featured_image" class="form-control"
                                       accept="image/jpeg,image/png,image/jpg,image/gif,image/webp">
                                <x-input-error :messages="$errors->get('featured_image')" class="mt-2"/>
                                <div class="text-xs text-slate-500 dark:text-slate-400 mt-1">
                                    Supported formats: JPEG, PNG, JPG, GIF, WebP. Max size: 2MB
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Categories & Tags -->
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Categories & Tags</h4>
                        </div>
                        <div class="card-body flex flex-col p-6">
                            <div class="space-y-4">
                            <!-- Categories -->
                            @if($categories->count() > 0)
                                <div class="input-area">
                                    <label class="form-label">Categories</label>
                                    <div class="space-y-2 max-h-32 overflow-y-auto">
                                        @foreach($categories as $category)
                                            <label class="inline-flex items-center cursor-pointer">
                                                <input type="checkbox" name="categories[]" value="{{ $category->id }}" class="hidden"
                                                       {{ in_array($category->id, old('categories', [])) ? 'checked' : '' }}>
                                                <span class="h-4 w-4 border flex-none border-slate-100 dark:border-slate-800 rounded inline-flex ltr:mr-3 rtl:ml-3 relative transition-all duration-150 bg-slate-100 dark:bg-slate-900">
                                                    <img src="{{ asset('assets/images/icon/ck-white.svg') }}" alt="" class="h-[10px] w-[10px] block m-auto opacity-0">
                                                </span>
                                                <span class="text-slate-500 dark:text-slate-400 text-sm leading-6">{{ $category->name }}</span>
                                            </label>
                                        @endforeach
                                    </div>
                                    <x-input-error :messages="$errors->get('categories')" class="mt-2"/>
                                </div>
                            @endif

                            <!-- Tags -->
                            @if($tags->count() > 0)
                                <div class="input-area">
                                    <label class="form-label">Tags</label>
                                    <div class="space-y-2 max-h-32 overflow-y-auto">
                                        @foreach($tags as $tag)
                                            <label class="inline-flex items-center cursor-pointer">
                                                <input type="checkbox" name="tags[]" value="{{ $tag->id }}" class="hidden"
                                                       {{ in_array($tag->id, old('tags', [])) ? 'checked' : '' }}>
                                                <span class="h-4 w-4 border flex-none border-slate-100 dark:border-slate-800 rounded inline-flex ltr:mr-3 rtl:ml-3 relative transition-all duration-150 bg-slate-100 dark:bg-slate-900">
                                                    <img src="{{ asset('assets/images/icon/ck-white.svg') }}" alt="" class="h-[10px] w-[10px] block m-auto opacity-0">
                                                </span>
                                                <span class="text-slate-500 dark:text-slate-400 text-sm leading-6">{{ $tag->name }}</span>
                                            </label>
                                        @endforeach
                                    </div>
                                    <x-input-error :messages="$errors->get('tags')" class="mt-2"/>
                                </div>
                            @endif
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="card">
                        <div class="card-body flex flex-col p-6">
                            <div class="flex flex-col space-y-3">
                                <button type="submit" class="btn inline-flex justify-center btn-dark">
                                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="heroicons-outline:check"></iconify-icon>
                                    Create Blog Post
                                </button>
                                <a href="{{ route('admin.blogs.index') }}"
                                   class="btn inline-flex justify-center btn-outline-light">
                                    Cancel
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    @push('styles')
        <!-- Quill.js CSS -->
        @vite('resources/css/plugins/quill.css')
        <style>
            /* Custom styles for Quill.js integration with DashCode Tailwind CSS */
            .quill-editor {
                background-color: white;
                border: 1px solid #d1d5db;
                border-radius: 0.375rem;
                overflow: hidden;
            }

            .ql-toolbar {
                border-top: none;
                border-left: none;
                border-right: none;
                border-bottom: 1px solid #e5e7eb;
                background-color: #f9fafb;
                padding: 8px 12px;
            }

            .ql-container {
                border: none;
                font-family: inherit;
                font-size: 14px;
                line-height: 1.5;
            }

            .ql-editor {
                padding: 12px;
                min-height: inherit;
                color: #374151;
            }

            .ql-editor.ql-blank::before {
                color: #9ca3af;
                font-style: normal;
                left: 12px;
            }

            /* Toolbar button styling */
            .ql-toolbar .ql-formats {
                margin-right: 15px;
            }

            .ql-toolbar button {
                padding: 3px 5px;
                margin: 0 1px;
            }

            .ql-toolbar button:hover {
                color: #3b82f6;
            }

            .ql-toolbar button.ql-active {
                color: #3b82f6;
            }

            /* Responsive adjustments */
            @media (max-width: 768px) {
                .ql-toolbar {
                    padding: 6px 8px;
                }

                .ql-toolbar .ql-formats {
                    margin-right: 10px;
                }

                .ql-editor {
                    padding: 10px;
                }
            }

            /* Focus state */
            .quill-editor:focus-within {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }
        </style>
    @endpush

    @push('scripts')
        <!-- Quill.js JavaScript -->
        @vite('resources/js/plugins-old/quill.min.js')

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                console.log('Initializing Quill.js editors...');

                // Wait for Quill to be available
                if (typeof Quill === 'undefined') {
                    console.error('Quill.js is not loaded');
                    return;
                }

                // Initialize Content Editor with full toolbar
                const contentEditor = new Quill('#content-editor', {
                    theme: 'snow',
                    placeholder: 'Write your blog post content here...',
                    modules: {
                        toolbar: [
                            [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                            [{ 'font': [] }],
                            [{ 'size': ['small', false, 'large', 'huge'] }],
                            ['bold', 'italic', 'underline', 'strike'],
                            [{ 'color': [] }, { 'background': [] }],
                            [{ 'script': 'sub'}, { 'script': 'super' }],
                            [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                            [{ 'indent': '-1'}, { 'indent': '+1' }],
                            [{ 'direction': 'rtl' }],
                            [{ 'align': [] }],
                            ['blockquote', 'code-block'],
                            ['link', 'image', 'video'],
                            ['clean']
                        ]
                    }
                });

                // Initialize Excerpt Editor with basic toolbar
                const excerptEditor = new Quill('#excerpt-editor', {
                    theme: 'snow',
                    placeholder: 'Brief description of the blog post (optional)',
                    modules: {
                        toolbar: [
                            ['bold', 'italic', 'underline'],
                            [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                            ['link'],
                            ['clean']
                        ]
                    }
                });

                // Set initial content if available
                const initialContent = document.getElementById('content').value;
                const initialExcerpt = document.getElementById('excerpt').value;

                if (initialContent) {
                    contentEditor.root.innerHTML = initialContent;
                }

                if (initialExcerpt) {
                    excerptEditor.root.innerHTML = initialExcerpt;
                }

                // Sync editor content with hidden textareas on change
                contentEditor.on('text-change', function() {
                    document.getElementById('content').value = contentEditor.root.innerHTML;
                });

                excerptEditor.on('text-change', function() {
                    document.getElementById('excerpt').value = excerptEditor.root.innerHTML;
                });

                // Sync content before form submission
                const form = document.querySelector('form');
                if (form) {
                    form.addEventListener('submit', function() {
                        document.getElementById('content').value = contentEditor.root.innerHTML;
                        document.getElementById('excerpt').value = excerptEditor.root.innerHTML;
                    });
                }

                console.log('Quill.js editors initialized successfully!');
            });
        </script>
    @endpush
</x-app-layout>
