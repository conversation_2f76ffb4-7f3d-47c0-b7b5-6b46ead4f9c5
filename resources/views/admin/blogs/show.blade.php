<x-app-layout>
    <div class="space-y-8">
        <div>
            <x-breadcrumb :page-title="$pageTitle" :breadcrumb-items="$breadcrumbItems" />
        </div>

        <!-- Header -->
        <div class="flex flex-wrap justify-between items-center gap-4">
            <div>
                <h4 class="font-medium lg:text-2xl text-xl capitalize text-slate-900 dark:text-slate-100">
                    {{ $blog->title }}
                </h4>
                <div class="flex items-center gap-4 text-sm text-slate-600 dark:text-slate-400 mt-2">
                    <span>By {{ $blog->author->name }}</span>
                    <span>•</span>
                    <span>{{ $blog->created_at->format('M d, Y') }}</span>
                    @if($blog->published_at)
                        <span>•</span>
                        <span>Published {{ $blog->published_at->format('M d, Y') }}</span>
                    @endif
                    <span>•</span>
                    <span>{{ number_format($blog->views_count) }} views</span>
                    <span>•</span>
                    <span>{{ $blog->reading_time }} min read</span>
                </div>
            </div>
            <div class="flex gap-3">
                <a href="{{ route('admin.blogs.edit', $blog) }}" 
                   class="btn inline-flex justify-center btn-dark">
                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="heroicons-outline:pencil-square"></iconify-icon>
                    Edit Post
                </a>
                <a href="{{ route('admin.blogs.index') }}" 
                   class="btn inline-flex justify-center btn-outline-dark">
                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="heroicons-outline:arrow-left"></iconify-icon>
                    Back to Blogs
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Featured Image -->
                @if($blog->featured_image_url)
                    <div class="card">
                        <div class="card-body p-0">
                            <img src="{{ $blog->featured_image_url }}" alt="{{ $blog->title }}" 
                                 class="w-full h-64 object-cover rounded-t-md">
                        </div>
                    </div>
                @endif

                <!-- Content -->
                <div class="card">
                    <div class="card-body">
                        @if($blog->excerpt)
                            <div class="mb-6 p-4 bg-slate-50 dark:bg-slate-700 rounded-md">
                                <h5 class="font-medium text-slate-900 dark:text-slate-100 mb-2">Excerpt</h5>
                                <p class="text-slate-600 dark:text-slate-400">{{ $blog->excerpt }}</p>
                            </div>
                        @endif

                        <div class="prose prose-slate dark:prose-invert max-w-none">
                            {!! $blog->content !!}
                        </div>
                    </div>
                </div>

                <!-- Categories & Tags -->
                @if($blog->taxonomies->count() > 0)
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Categories & Tags</h4>
                        </div>
                        <div class="card-body">
                            @php
                                $categories = $blog->taxonomies->where('type', 'category');
                                $tags = $blog->taxonomies->where('type', 'tag');
                            @endphp

                            @if($categories->count() > 0)
                                <div class="mb-4">
                                    <h6 class="font-medium text-slate-900 dark:text-slate-100 mb-2">Categories</h6>
                                    <div class="flex flex-wrap gap-2">
                                        @foreach($categories as $category)
                                            <span class="inline-block px-3 py-1 rounded-full bg-primary-100 text-primary-600 dark:bg-primary-900 dark:text-primary-300 text-sm">
                                                {{ $category->name }}
                                            </span>
                                        @endforeach
                                    </div>
                                </div>
                            @endif

                            @if($tags->count() > 0)
                                <div>
                                    <h6 class="font-medium text-slate-900 dark:text-slate-100 mb-2">Tags</h6>
                                    <div class="flex flex-wrap gap-2">
                                        @foreach($tags as $tag)
                                            <span class="inline-block px-3 py-1 rounded-full bg-slate-100 text-slate-600 dark:bg-slate-700 dark:text-slate-300 text-sm">
                                                #{{ $tag->name }}
                                            </span>
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Post Status -->
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Post Status</h4>
                    </div>
                    <div class="card-body space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-slate-600 dark:text-slate-400">Status:</span>
                            @if($blog->status === 'published')
                                <span class="inline-block px-3 py-1 rounded-full bg-success-100 text-success-600 dark:bg-success-900 dark:text-success-300 text-sm">
                                    Published
                                </span>
                            @elseif($blog->status === 'draft')
                                <span class="inline-block px-3 py-1 rounded-full bg-warning-100 text-warning-600 dark:bg-warning-900 dark:text-warning-300 text-sm">
                                    Draft
                                </span>
                            @else
                                <span class="inline-block px-3 py-1 rounded-full bg-slate-100 text-slate-600 dark:bg-slate-700 dark:text-slate-300 text-sm">
                                    Archived
                                </span>
                            @endif
                        </div>

                        <div class="flex justify-between items-center">
                            <span class="text-slate-600 dark:text-slate-400">Featured:</span>
                            @if($blog->featured)
                                <iconify-icon icon="heroicons-solid:star" class="text-yellow-500 text-lg"></iconify-icon>
                            @else
                                <iconify-icon icon="heroicons-outline:star" class="text-slate-400 text-lg"></iconify-icon>
                            @endif
                        </div>

                        <div class="flex justify-between items-center">
                            <span class="text-slate-600 dark:text-slate-400">Views:</span>
                            <span class="font-medium text-slate-900 dark:text-slate-100">{{ number_format($blog->views_count) }}</span>
                        </div>

                        <div class="flex justify-between items-center">
                            <span class="text-slate-600 dark:text-slate-400">Reading Time:</span>
                            <span class="font-medium text-slate-900 dark:text-slate-100">{{ $blog->reading_time }} min</span>
                        </div>
                    </div>
                </div>

                <!-- Post Details -->
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Post Details</h4>
                    </div>
                    <div class="card-body space-y-4">
                        <div>
                            <span class="text-slate-600 dark:text-slate-400 text-sm">Author:</span>
                            <div class="flex items-center space-x-3 rtl:space-x-reverse mt-1">
                                <div class="flex-none">
                                    <div class="w-8 h-8 rounded-full bg-slate-200 dark:bg-slate-700 flex items-center justify-center text-xs font-medium">
                                        {{ substr($blog->author->name, 0, 2) }}
                                    </div>
                                </div>
                                <div class="flex-1 text-start">
                                    <h4 class="text-sm font-medium text-slate-600 dark:text-slate-300">
                                        {{ $blog->author->name }}
                                    </h4>
                                    <div class="text-xs text-slate-500 dark:text-slate-400">
                                        {{ $blog->author->email }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <span class="text-slate-600 dark:text-slate-400 text-sm">Created:</span>
                            <div class="font-medium text-slate-900 dark:text-slate-100 mt-1">
                                {{ $blog->created_at->format('M d, Y \a\t g:i A') }}
                            </div>
                        </div>

                        <div>
                            <span class="text-slate-600 dark:text-slate-400 text-sm">Last Updated:</span>
                            <div class="font-medium text-slate-900 dark:text-slate-100 mt-1">
                                {{ $blog->updated_at->format('M d, Y \a\t g:i A') }}
                            </div>
                        </div>

                        @if($blog->published_at)
                            <div>
                                <span class="text-slate-600 dark:text-slate-400 text-sm">Published:</span>
                                <div class="font-medium text-slate-900 dark:text-slate-100 mt-1">
                                    {{ $blog->published_at->format('M d, Y \a\t g:i A') }}
                                </div>
                            </div>
                        @endif

                        <div>
                            <span class="text-slate-600 dark:text-slate-400 text-sm">Slug:</span>
                            <div class="font-mono text-sm text-slate-900 dark:text-slate-100 mt-1 break-all">
                                {{ $blog->slug }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SEO Information -->
                @if($blog->meta_data && (isset($blog->meta_data['meta_title']) || isset($blog->meta_data['meta_description'])))
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">SEO Information</h4>
                        </div>
                        <div class="card-body space-y-4">
                            @if(isset($blog->meta_data['meta_title']) && $blog->meta_data['meta_title'])
                                <div>
                                    <span class="text-slate-600 dark:text-slate-400 text-sm">Meta Title:</span>
                                    <div class="font-medium text-slate-900 dark:text-slate-100 mt-1">
                                        {{ $blog->meta_data['meta_title'] }}
                                    </div>
                                </div>
                            @endif

                            @if(isset($blog->meta_data['meta_description']) && $blog->meta_data['meta_description'])
                                <div>
                                    <span class="text-slate-600 dark:text-slate-400 text-sm">Meta Description:</span>
                                    <div class="font-medium text-slate-900 dark:text-slate-100 mt-1">
                                        {{ $blog->meta_data['meta_description'] }}
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                @endif

                <!-- Actions -->
                <div class="card">
                    <div class="card-body">
                        <div class="flex flex-col space-y-3">
                            <a href="{{ route('admin.blogs.edit', $blog) }}" 
                               class="btn inline-flex justify-center btn-dark">
                                <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="heroicons-outline:pencil-square"></iconify-icon>
                                Edit Post
                            </a>
                            
                            <form action="{{ route('admin.blogs.destroy', $blog) }}" method="POST" class="inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn inline-flex justify-center btn-danger w-full"
                                        onclick="return confirm('Are you sure you want to delete this blog post? This action cannot be undone.')">
                                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="heroicons-outline:trash"></iconify-icon>
                                    Delete Post
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('styles')
        <!-- Prose styles for content display -->
        <style>
            .prose {
                color: inherit;
            }
            .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
                color: inherit;
                font-weight: 600;
            }
            .prose p {
                margin-bottom: 1rem;
            }
            .prose ul, .prose ol {
                margin-bottom: 1rem;
                padding-left: 1.5rem;
            }
            .prose blockquote {
                border-left: 4px solid #e2e8f0;
                padding-left: 1rem;
                margin: 1rem 0;
                font-style: italic;
            }
            .dark .prose blockquote {
                border-left-color: #475569;
            }
        </style>
    @endpush
</x-app-layout>
