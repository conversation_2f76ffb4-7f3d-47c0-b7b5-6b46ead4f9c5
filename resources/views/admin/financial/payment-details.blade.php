<x-app-layout>
    <div class="space-y-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
            <x-breadcrumb :pageTitle="$pageTitle" :breadcrumbItems="$breadcrumbItems"/>
            <div class="flex space-x-3 mt-4 sm:mt-0">
                @if($payment->status === 'succeeded' && $payment->amount > 0)
                    <button type="button" class="btn inline-flex justify-center btn-warning" data-bs-toggle="modal" data-bs-target="#refundModal">
                        <iconify-icon icon="heroicons:arrow-uturn-left" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                        Process Refund
                    </button>
                @endif
                <a href="{{ route('admin.financial.payments') }}" class="btn inline-flex justify-center btn-secondary">
                    <iconify-icon icon="heroicons:arrow-left" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                    Back to Payments
                </a>
            </div>
        </div>

        <div class="grid grid-cols-12 gap-5">
            <!-- Payment Information -->
            <div class="xl:col-span-8 col-span-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Payment Information</h4>
                    </div>
                    <div class="card-body">
                        <div class="grid md:grid-cols-2 grid-cols-1 gap-6">
                            <div class="space-y-4">
                                <div class="flex justify-between py-2 border-b border-slate-100 dark:border-slate-700">
                                    <span class="font-medium text-slate-600 dark:text-slate-300">Payment ID:</span>
                                    <span class="text-slate-900 dark:text-slate-100">#{{ $payment->id }}</span>
                                </div>
                                <div class="flex justify-between py-2 border-b border-slate-100 dark:border-slate-700">
                                    <span class="font-medium text-slate-600 dark:text-slate-300">Stripe Payment Intent:</span>
                                    <span class="text-slate-900 dark:text-slate-100">
                                        @if($payment->stripe_payment_intent_id)
                                            <code class="bg-slate-100 dark:bg-slate-700 px-2 py-1 rounded text-sm">{{ $payment->stripe_payment_intent_id }}</code>
                                        @else
                                            <span class="text-slate-500 dark:text-slate-400">N/A</span>
                                        @endif
                                    </span>
                                </div>
                                <div class="flex justify-between py-2 border-b border-slate-100 dark:border-slate-700">
                                    <span class="font-medium text-slate-600 dark:text-slate-300">Amount:</span>
                                    <span class="text-xl font-bold text-success-500">${{ number_format($payment->amount / 100, 2) }}</span>
                                </div>
                                <div class="flex justify-between py-2 border-b border-slate-100 dark:border-slate-700">
                                    <span class="font-medium text-slate-600 dark:text-slate-300">Status:</span>
                                    <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-{{ $payment->status === 'succeeded' ? 'success' : ($payment->status === 'failed' ? 'danger' : 'warning') }}-500 bg-{{ $payment->status === 'succeeded' ? 'success' : ($payment->status === 'failed' ? 'danger' : 'warning') }}-500">
                                        {{ ucfirst($payment->status) }}
                                    </div>
                                </div>
                                <div class="flex justify-between py-2">
                                    <span class="font-medium text-slate-600 dark:text-slate-300">Currency:</span>
                                    <span class="text-slate-900 dark:text-slate-100">{{ strtoupper($payment->currency ?? 'USD') }}</span>
                                </div>
                            </div>
                            <div class="space-y-4">
                                <div class="flex justify-between py-2 border-b border-slate-100 dark:border-slate-700">
                                    <span class="font-medium text-slate-600 dark:text-slate-300">Payment Method:</span>
                                    <span class="text-slate-900 dark:text-slate-100">
                                        @if($payment->payment_method_type)
                                            <div class="flex items-center">
                                                <iconify-icon icon="heroicons:credit-card" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                                                <span>{{ ucfirst($payment->payment_method_type) }}</span>
                                                @if($payment->payment_method_last4)
                                                    <span class="text-slate-500 dark:text-slate-400 ml-1">****{{ $payment->payment_method_last4 }}</span>
                                                @endif
                                            </div>
                                        @else
                                            <span class="text-slate-500 dark:text-slate-400">N/A</span>
                                        @endif
                                    </span>
                                </div>
                                <div class="flex justify-between py-2 border-b border-slate-100 dark:border-slate-700">
                                    <span class="font-medium text-slate-600 dark:text-slate-300">Created:</span>
                                    <span class="text-slate-900 dark:text-slate-100">{{ $payment->created_at->format('M d, Y H:i:s') }}</span>
                                </div>
                                <div class="flex justify-between py-2 border-b border-slate-100 dark:border-slate-700">
                                    <span class="font-medium text-slate-600 dark:text-slate-300">Updated:</span>
                                    <span class="text-slate-900 dark:text-slate-100">{{ $payment->updated_at->format('M d, Y H:i:s') }}</span>
                                </div>
                                @if($payment->failure_reason)
                                    <div class="flex justify-between py-2">
                                        <span class="font-medium text-slate-600 dark:text-slate-300">Failure Reason:</span>
                                        <span class="text-danger-500">{{ $payment->failure_reason }}</span>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customer Information -->
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Customer Information</h4>
                    </div>
                    <div class="card-body">
                        <div class="flex items-center space-x-4">
                            <div class="flex-none">
                                <div class="w-12 h-12 rounded-full bg-primary-500 text-white flex items-center justify-center">
                                    <iconify-icon icon="heroicons:user" class="text-xl"></iconify-icon>
                                </div>
                            </div>
                            <div class="flex-1">
                                <div class="grid md:grid-cols-2 grid-cols-1 gap-4">
                                    <div>
                                        <h4 class="text-lg font-medium text-slate-900 dark:text-slate-100">{{ $payment->user->name }}</h4>
                                        <p class="text-slate-500 dark:text-slate-400">{{ $payment->user->email }}</p>
                                    </div>
                                    <div class="text-sm text-slate-500 dark:text-slate-400">
                                        <div><strong>User ID:</strong> #{{ $payment->user->id }}</div>
                                        <div><strong>Joined:</strong> {{ $payment->user->created_at->format('M d, Y') }}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-none">
                                <a href="{{ route('admin.users.show', $payment->user) }}" class="btn inline-flex justify-center btn-outline-primary btn-sm">
                                    View Profile
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Subscription Information -->
                @if($payment->subscription)
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Subscription Information</h4>
                        </div>
                        <div class="card-body">
                            <div class="grid md:grid-cols-2 grid-cols-1 gap-6">
                                <div class="space-y-4">
                                    <div class="flex justify-between py-2 border-b border-slate-100 dark:border-slate-700">
                                        <span class="font-medium text-slate-600 dark:text-slate-300">Product:</span>
                                        <span>
                                            @if($payment->subscription->product)
                                                <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-info-500 bg-info-500">
                                                    {{ $payment->subscription->product->name }}
                                                </div>
                                            @else
                                                <span class="text-slate-500 dark:text-slate-400">N/A</span>
                                            @endif
                                        </span>
                                    </div>
                                    <div class="flex justify-between py-2">
                                        <span class="font-medium text-slate-600 dark:text-slate-300">Status:</span>
                                        <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-{{ $payment->subscription->status === 'active' ? 'success' : 'warning' }}-500 bg-{{ $payment->subscription->status === 'active' ? 'success' : 'warning' }}-500">
                                            {{ ucfirst($payment->subscription->status) }}
                                        </div>
                                    </div>
                                </div>
                                <div class="space-y-4">
                                    <div class="flex justify-between py-2 border-b border-slate-100 dark:border-slate-700">
                                        <span class="font-medium text-slate-600 dark:text-slate-300">Started:</span>
                                        <span class="text-slate-900 dark:text-slate-100">{{ $payment->subscription->created_at->format('M d, Y') }}</span>
                                    </div>
                                    <div class="flex justify-between py-2">
                                        <span class="font-medium text-slate-600 dark:text-slate-300">Next Billing:</span>
                                        <span class="text-slate-900 dark:text-slate-100">
                                            @if($payment->subscription->current_period_end)
                                                {{ $payment->subscription->current_period_end->format('M d, Y') }}
                                            @else
                                                <span class="text-slate-500 dark:text-slate-400">N/A</span>
                                            @endif
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6">
                                <a href="{{ route('admin.subscriptions.show', $payment->subscription) }}" class="btn inline-flex justify-center btn-outline-info btn-sm">
                                    View Subscription Details
                                </a>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="xl:col-span-4 col-span-12">
                <!-- Quick Actions -->
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Quick Actions</h4>
                    </div>
                    <div class="card-body">
                        <div class="space-y-2">
                            @if($payment->status === 'succeeded' && $payment->amount > 0)
                                <button type="button" class="w-full flex items-center px-4 py-3 text-left hover:bg-slate-100 dark:hover:bg-slate-700 rounded-md transition-colors"
                                        data-bs-toggle="modal" data-bs-target="#refundModal">
                                    <iconify-icon icon="heroicons:arrow-uturn-left" class="text-warning-500 text-lg ltr:mr-3 rtl:ml-3"></iconify-icon>
                                    <span class="text-slate-700 dark:text-slate-300">Process Refund</span>
                                </button>
                            @endif
                            <a href="{{ route('admin.users.show', $payment->user) }}" class="w-full flex items-center px-4 py-3 text-left hover:bg-slate-100 dark:hover:bg-slate-700 rounded-md transition-colors">
                                <iconify-icon icon="heroicons:user" class="text-primary-500 text-lg ltr:mr-3 rtl:ml-3"></iconify-icon>
                                <span class="text-slate-700 dark:text-slate-300">View Customer Profile</span>
                            </a>
                            @if($payment->subscription)
                                <a href="{{ route('admin.subscriptions.show', $payment->subscription) }}" class="w-full flex items-center px-4 py-3 text-left hover:bg-slate-100 dark:hover:bg-slate-700 rounded-md transition-colors">
                                    <iconify-icon icon="heroicons:credit-card" class="text-info-500 text-lg ltr:mr-3 rtl:ml-3"></iconify-icon>
                                    <span class="text-slate-700 dark:text-slate-300">View Subscription</span>
                                </a>
                            @endif
                            <a href="{{ route('admin.financial.payments') }}" class="w-full flex items-center px-4 py-3 text-left hover:bg-slate-100 dark:hover:bg-slate-700 rounded-md transition-colors">
                                <iconify-icon icon="heroicons:arrow-left" class="text-slate-500 text-lg ltr:mr-3 rtl:ml-3"></iconify-icon>
                                <span class="text-slate-700 dark:text-slate-300">Back to Payments</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Refunds -->
                @if($payment->refunds && $payment->refunds->count() > 0)
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Refunds</h4>
                        </div>
                        <div class="card-body">
                            <div class="space-y-4">
                                @foreach($payment->refunds as $refund)
                                    <div class="flex items-center space-x-4 pb-4 border-b border-slate-100 dark:border-slate-700 last:border-b-0 last:pb-0">
                                        <div class="flex-none">
                                            <div class="w-10 h-10 rounded-full bg-warning-500 text-white flex items-center justify-center">
                                                <iconify-icon icon="heroicons:arrow-uturn-left"></iconify-icon>
                                            </div>
                                        </div>
                                        <div class="flex-1">
                                            <div class="font-medium text-slate-900 dark:text-slate-100">${{ number_format($refund->amount / 100, 2) }}</div>
                                            <div class="text-sm text-slate-500 dark:text-slate-400">{{ $refund->created_at->format('M d, Y') }}</div>
                                            @if($refund->reason)
                                                <div class="text-sm text-slate-600 dark:text-slate-300 mt-1">{{ $refund->reason }}</div>
                                            @endif
                                        </div>
                                        <div class="flex-none">
                                            <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-{{ $refund->status === 'succeeded' ? 'success' : ($refund->status === 'failed' ? 'danger' : 'warning') }}-500 bg-{{ $refund->status === 'succeeded' ? 'success' : ($refund->status === 'failed' ? 'danger' : 'warning') }}-500">
                                                {{ ucfirst($refund->status) }}
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Refund Modal -->
    @if($payment->status === 'succeeded' && $payment->amount > 0)
    <div class="modal fade fixed inset-0 z-[99999] hidden overflow-y-auto" id="refundModal" tabindex="-1" aria-labelledby="refundModalLabel" aria-hidden="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-slate-800 opacity-50"></div>
            </div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white dark:bg-slate-700 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white dark:bg-slate-700 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-slate-800 dark:text-slate-100" id="refundModalLabel">
                            Process Refund for Payment #{{ $payment->id }}
                        </h3>
                        <button type="button" class="text-slate-400 hover:text-slate-600" data-bs-dismiss="modal" aria-label="Close">
                            <iconify-icon icon="heroicons:x-mark" class="text-2xl"></iconify-icon>
                        </button>
                    </div>
                    <form method="POST" action="{{ route('admin.financial.refunds.process') }}">
                        @csrf
                        <div class="space-y-4">
                            <input type="hidden" name="payment_id" value="{{ $payment->id }}">

                            <div class="bg-info-100 border border-info-200 text-info-800 px-4 py-3 rounded-md">
                                <div class="flex items-center">
                                    <iconify-icon icon="heroicons:information-circle" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                                    <span>You are about to process a refund for <strong>{{ $payment->user->name }}</strong>.</span>
                                </div>
                            </div>

                            <div>
                                <label for="refund_amount" class="form-label">Refund Amount</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-slate-500 dark:text-slate-400">$</span>
                                    </div>
                                    <input type="number" class="form-control pl-8" id="refund_amount" name="amount"
                                           step="0.01" min="0.01" max="{{ $payment->amount / 100 }}"
                                           value="{{ $payment->amount / 100 }}" required>
                                </div>
                                <div class="text-xs text-slate-500 dark:text-slate-400 mt-1">
                                    Maximum refundable amount: ${{ number_format($payment->amount / 100, 2) }}
                                </div>
                            </div>

                            <div>
                                <label for="refund_reason" class="form-label">Reason for Refund</label>
                                <textarea class="form-control" id="refund_reason" name="reason" rows="3"
                                          placeholder="Please provide a reason for this refund..." required></textarea>
                            </div>
                        </div>
                        <div class="flex justify-end space-x-3 mt-6">
                            <button type="button" class="btn inline-flex justify-center btn-secondary" data-bs-dismiss="modal">
                                Cancel
                            </button>
                            <button type="submit" class="btn inline-flex justify-center btn-warning">
                                <iconify-icon icon="heroicons:arrow-uturn-left" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                                Process Refund
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    @endif
</x-app-layout>
