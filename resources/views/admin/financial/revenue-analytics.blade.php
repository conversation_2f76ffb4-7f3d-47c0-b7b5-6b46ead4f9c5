<x-app-layout>
    <div class="space-y-8">
        <div class="block sm:flex items-center justify-between mb-6">
            <x-breadcrumb :pageTitle="$pageTitle" :breadcrumbItems="$breadcrumbItems"/>
            <div class="flex space-x-1 rtl:space-x-reverse">
                <button type="button" class="btn inline-flex justify-center btn-outline-primary" data-period="7">7 Days</button>
                <button type="button" class="btn inline-flex justify-center btn-outline-primary" data-period="30">30 Days</button>
                <button type="button" class="btn inline-flex justify-center btn-primary" data-period="90">90 Days</button>
                <button type="button" class="btn inline-flex justify-center btn-outline-primary" data-period="365">1 Year</button>
            </div>
        </div>

        <!-- Key Metrics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="bg-white dark:bg-slate-800 rounded-md px-5 py-4">
                <div class="pl-14 relative">
                    <div class="w-10 h-10 rounded-full bg-primary-100 text-primary-800 text-base flex items-center justify-center absolute left-0 top-2">
                        <iconify-icon icon="heroicons:currency-dollar"></iconify-icon>
                    </div>
                    <div class="text-slate-600 dark:text-slate-300 text-sm mb-1 font-medium uppercase">
                        Total Revenue
                    </div>
                    <div class="text-slate-900 dark:text-white text-xl font-medium" id="total-revenue">
                        ${{ number_format($analytics['totalRevenue'], 2) }}
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-slate-800 rounded-md px-5 py-4">
                <div class="pl-14 relative">
                    <div class="w-10 h-10 rounded-full bg-success-100 text-success-800 text-base flex items-center justify-center absolute left-0 top-2">
                        <iconify-icon icon="heroicons:chart-bar"></iconify-icon>
                    </div>
                    <div class="text-slate-600 dark:text-slate-300 text-sm mb-1 font-medium uppercase">
                        Net Revenue
                    </div>
                    <div class="text-slate-900 dark:text-white text-xl font-medium" id="net-revenue">
                        ${{ number_format($analytics['netRevenue'], 2) }}
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-slate-800 rounded-md px-5 py-4">
                <div class="pl-14 relative">
                    <div class="w-10 h-10 rounded-full bg-warning-100 text-warning-800 text-base flex items-center justify-center absolute left-0 top-2">
                        <iconify-icon icon="heroicons:arrow-path"></iconify-icon>
                    </div>
                    <div class="text-slate-600 dark:text-slate-300 text-sm mb-1 font-medium uppercase">
                        Total Refunds
                    </div>
                    <div class="text-slate-900 dark:text-white text-xl font-medium" id="total-refunds">
                        ${{ number_format($analytics['totalRefunds'], 2) }}
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-slate-800 rounded-md px-5 py-4">
                <div class="pl-14 relative">
                    <div class="w-10 h-10 rounded-full bg-info-100 text-info-800 text-base flex items-center justify-center absolute left-0 top-2">
                        <iconify-icon icon="heroicons:shopping-cart"></iconify-icon>
                    </div>
                    <div class="text-slate-600 dark:text-slate-300 text-sm mb-1 font-medium uppercase">
                        Average Order Value
                    </div>
                    <div class="text-slate-900 dark:text-white text-xl font-medium" id="avg-order-value">
                        ${{ number_format($analytics['avgOrderValue'], 2) }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="grid grid-cols-12 gap-5">
            <!-- Revenue Trend Chart -->
            <div class="xl:col-span-8 col-span-12">
                <div class="card">
                    <div class="card-header noborder">
                        <div class="flex items-center justify-between">
                            <h4 class="card-title">Revenue Trend</h4>
                            <div class="relative">
                                <div class="dropdown relative">
                                    <button class="text-xl text-center block w-full" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                                        <iconify-icon icon="heroicons-outline:dots-vertical"></iconify-icon>
                                    </button>
                                    <ul class="dropdown-menu min-w-max absolute text-sm text-slate-700 dark:text-white hidden bg-white dark:bg-slate-700 shadow z-[2] float-left overflow-hidden list-none text-left rounded-lg mt-1 m-0 bg-clip-padding border-none">
                                        <li class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600 dark:hover:text-white cursor-pointer">Chart Options:</li>
                                        <li>
                                            <a class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600 dark:hover:text-white cursor-pointer" href="#" data-chart-type="revenue">Revenue Only</a>
                                        </li>
                                        <li>
                                            <a class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600 dark:hover:text-white cursor-pointer" href="#" data-chart-type="net">Net Revenue</a>
                                        </li>
                                        <li>
                                            <a class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600 dark:hover:text-white cursor-pointer" href="#" data-chart-type="both">Both</a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="chart-area">
                            <canvas id="revenueTrendChart" style="height: 320px;"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Product Revenue Breakdown -->
            <div class="xl:col-span-4 col-span-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Revenue by Product</h4>
                    </div>
                    <div class="card-body">
                        <div class="chart-pie pt-4 pb-2">
                            <canvas id="productRevenueChart" style="height: 245px;"></canvas>
                        </div>
                        <div class="mt-4 text-center text-sm">
                            @foreach($analytics['productRevenue'] as $product => $revenue)
                                <span class="ltr:mr-2 rtl:ml-2">
                                    <iconify-icon icon="heroicons:stop" class="text-{{ $loop->index % 4 == 0 ? 'primary' : ($loop->index % 4 == 1 ? 'success' : ($loop->index % 4 == 2 ? 'info' : 'warning')) }}-500"></iconify-icon> {{ $product }}
                                </span>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Analytics -->
        <div class="grid grid-cols-12 gap-5">
            <!-- Monthly Comparison -->
            <div class="xl:col-span-6 col-span-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Monthly Comparison</h4>
                    </div>
                    <div class="card-body">
                        <div class="chart-bar">
                            <canvas id="monthlyComparisonChart" style="height: 300px;"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Methods -->
            <div class="xl:col-span-6 col-span-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Payment Methods</h4>
                    </div>
                    <div class="card-body">
                        <div class="chart-pie pt-4 pb-2">
                            <canvas id="paymentMethodsChart" style="height: 245px;"></canvas>
                        </div>
                        <div class="mt-4 text-center text-sm">
                            @foreach($analytics['paymentMethods'] as $method => $count)
                                <span class="ltr:mr-2 rtl:ml-2">
                                    <iconify-icon icon="heroicons:stop" class="text-{{ $loop->index % 3 == 0 ? 'primary' : ($loop->index % 3 == 1 ? 'success' : 'info') }}-500"></iconify-icon> {{ ucfirst($method) }} ({{ $count }})
                                </span>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Statistics Table -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Detailed Statistics</h4>
            </div>
            <div class="card-body px-6 pb-6">
                <div class="overflow-x-auto -mx-6">
                    <div class="inline-block min-w-full align-middle">
                        <div class="overflow-hidden">
                            <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
                                <thead class="bg-slate-200 dark:bg-slate-700">
                                    <tr>
                                        <th scope="col" class="table-th">Metric</th>
                                        <th scope="col" class="table-th">Current Period</th>
                                        <th scope="col" class="table-th">Previous Period</th>
                                        <th scope="col" class="table-th">Change</th>
                                        <th scope="col" class="table-th">Growth Rate</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                                    <tr>
                                        <td class="table-td font-medium">Total Revenue</td>
                                        <td class="table-td">${{ number_format($analytics['totalRevenue'], 2) }}</td>
                                        <td class="table-td">${{ number_format($analytics['previousRevenue'], 2) }}</td>
                                        <td class="table-td text-{{ $analytics['revenueChange'] >= 0 ? 'success' : 'danger' }}-500">
                                            ${{ number_format(abs($analytics['revenueChange']), 2) }}
                                            <iconify-icon icon="heroicons:arrow-{{ $analytics['revenueChange'] >= 0 ? 'up' : 'down' }}" class="inline-block ml-1"></iconify-icon>
                                        </td>
                                        <td class="table-td text-{{ $analytics['revenueGrowth'] >= 0 ? 'success' : 'danger' }}-500">
                                            {{ number_format($analytics['revenueGrowth'], 1) }}%
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="table-td font-medium">Total Payments</td>
                                        <td class="table-td">{{ number_format($analytics['totalPayments']) }}</td>
                                        <td class="table-td">{{ number_format($analytics['previousPayments']) }}</td>
                                        <td class="table-td text-{{ $analytics['paymentsChange'] >= 0 ? 'success' : 'danger' }}-500">
                                            {{ number_format(abs($analytics['paymentsChange'])) }}
                                            <iconify-icon icon="heroicons:arrow-{{ $analytics['paymentsChange'] >= 0 ? 'up' : 'down' }}" class="inline-block ml-1"></iconify-icon>
                                        </td>
                                        <td class="table-td text-{{ $analytics['paymentsGrowth'] >= 0 ? 'success' : 'danger' }}-500">
                                            {{ number_format($analytics['paymentsGrowth'], 1) }}%
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="table-td font-medium">Total Refunds</td>
                                        <td class="table-td">${{ number_format($analytics['totalRefunds'], 2) }}</td>
                                        <td class="table-td">${{ number_format($analytics['previousRefunds'], 2) }}</td>
                                        <td class="table-td text-{{ $analytics['refundsChange'] <= 0 ? 'success' : 'danger' }}-500">
                                            ${{ number_format(abs($analytics['refundsChange']), 2) }}
                                            <iconify-icon icon="heroicons:arrow-{{ $analytics['refundsChange'] >= 0 ? 'up' : 'down' }}" class="inline-block ml-1"></iconify-icon>
                                        </td>
                                        <td class="table-td text-{{ $analytics['refundsGrowth'] <= 0 ? 'success' : 'danger' }}-500">
                                            {{ number_format($analytics['refundsGrowth'], 1) }}%
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="table-td font-medium">Average Order Value</td>
                                        <td class="table-td">${{ number_format($analytics['avgOrderValue'], 2) }}</td>
                                        <td class="table-td">${{ number_format($analytics['previousAvgOrderValue'], 2) }}</td>
                                        <td class="table-td text-{{ $analytics['avgOrderValueChange'] >= 0 ? 'success' : 'danger' }}-500">
                                            ${{ number_format(abs($analytics['avgOrderValueChange']), 2) }}
                                            <iconify-icon icon="heroicons:arrow-{{ $analytics['avgOrderValueChange'] >= 0 ? 'up' : 'down' }}" class="inline-block ml-1"></iconify-icon>
                                        </td>
                                        <td class="table-td text-{{ $analytics['avgOrderValueGrowth'] >= 0 ? 'success' : 'danger' }}-500">
                                            {{ number_format($analytics['avgOrderValueGrowth'], 1) }}%
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
    // Revenue Trend Chart
    const revenueTrendCtx = document.getElementById('revenueTrendChart').getContext('2d');
    const revenueTrendChart = new Chart(revenueTrendCtx, {
        type: 'line',
        data: {
            labels: @json($chartData['revenueTrend']['labels']),
            datasets: [{
                label: 'Revenue',
                data: @json($chartData['revenueTrend']['revenue']),
                borderColor: '#4e73df',
                backgroundColor: 'rgba(78, 115, 223, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.3
            }, {
                label: 'Net Revenue',
                data: @json($chartData['revenueTrend']['netRevenue']),
                borderColor: '#1cc88a',
                backgroundColor: 'rgba(28, 200, 138, 0.1)',
                borderWidth: 2,
                fill: false,
                tension: 0.3
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': $' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Product Revenue Chart
    const productRevenueCtx = document.getElementById('productRevenueChart').getContext('2d');
    const productRevenueChart = new Chart(productRevenueCtx, {
        type: 'doughnut',
        data: {
            labels: @json(array_keys($analytics['productRevenue'])),
            datasets: [{
                data: @json(array_values($analytics['productRevenue'])),
                backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b'],
                hoverBackgroundColor: ['#2e59d9', '#17a673', '#2c9faf', '#f4b619', '#e02d1b'],
                hoverBorderColor: "rgba(234, 236, 244, 1)",
            }],
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.label + ': $' + context.parsed.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Monthly Comparison Chart
    const monthlyComparisonCtx = document.getElementById('monthlyComparisonChart').getContext('2d');
    const monthlyComparisonChart = new Chart(monthlyComparisonCtx, {
        type: 'bar',
        data: {
            labels: @json($chartData['monthlyComparison']['labels']),
            datasets: [{
                label: 'Revenue',
                data: @json($chartData['monthlyComparison']['revenue']),
                backgroundColor: '#4e73df',
                borderColor: '#4e73df',
                borderWidth: 1
            }, {
                label: 'Refunds',
                data: @json($chartData['monthlyComparison']['refunds']),
                backgroundColor: '#e74a3b',
                borderColor: '#e74a3b',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': $' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Payment Methods Chart
    const paymentMethodsCtx = document.getElementById('paymentMethodsChart').getContext('2d');
    const paymentMethodsChart = new Chart(paymentMethodsCtx, {
        type: 'pie',
        data: {
            labels: @json(array_keys($analytics['paymentMethods'])),
            datasets: [{
                data: @json(array_values($analytics['paymentMethods'])),
                backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc'],
                hoverBackgroundColor: ['#2e59d9', '#17a673', '#2c9faf'],
                hoverBorderColor: "rgba(234, 236, 244, 1)",
            }],
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // Period filter functionality
    document.querySelectorAll('[data-period]').forEach(button => {
        button.addEventListener('click', function() {
            const period = this.getAttribute('data-period');

            // Update active button
            document.querySelectorAll('[data-period]').forEach(btn => btn.classList.remove('btn-primary'));
            document.querySelectorAll('[data-period]').forEach(btn => btn.classList.add('btn-outline-primary'));
            this.classList.remove('btn-outline-primary');
            this.classList.add('btn-primary');

            // Reload page with new period
            const url = new URL(window.location);
            url.searchParams.set('period', period);
            window.location.href = url.toString();
        });
    });

    // Chart type toggle functionality
    document.querySelectorAll('[data-chart-type]').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const chartType = this.getAttribute('data-chart-type');

            if (chartType === 'revenue') {
                revenueTrendChart.data.datasets[1].hidden = true;
            } else if (chartType === 'net') {
                revenueTrendChart.data.datasets[0].hidden = true;
                revenueTrendChart.data.datasets[1].hidden = false;
            } else {
                revenueTrendChart.data.datasets[0].hidden = false;
                revenueTrendChart.data.datasets[1].hidden = false;
            }

            revenueTrendChart.update();
        });
    });
    </script>
</x-app-layout>
