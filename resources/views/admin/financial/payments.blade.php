<x-app-layout>
    <div class="space-y-8">
        <div class="block sm:flex items-center justify-between mb-6">
            <x-breadcrumb :pageTitle="$pageTitle" :breadcrumbItems="$breadcrumbItems"/>
        </div>

        <!-- Filters Card -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Search & Filter</h4>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ route('admin.financial.payments') }}">
                    <div class="grid grid-cols-12 gap-5">
                        <div class="lg:col-span-3 col-span-12">
                            <label for="q" class="form-label">Search</label>
                            <input type="text" class="form-control" id="q" name="q"
                                   value="{{ $filters['q'] }}" placeholder="Search by user name or email">
                        </div>
                        <div class="lg:col-span-2 col-span-12">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">All Statuses</option>
                                @foreach($statusOptions as $status)
                                    <option value="{{ $status }}" {{ $filters['status'] === $status ? 'selected' : '' }}>
                                        {{ ucfirst($status) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="lg:col-span-2 col-span-12">
                            <label for="date_from" class="form-label">Date From</label>
                            <input type="date" class="form-control" id="date_from" name="date_from"
                                   value="{{ $filters['date_from'] }}">
                        </div>
                        <div class="lg:col-span-2 col-span-12">
                            <label for="date_to" class="form-label">Date To</label>
                            <input type="date" class="form-control" id="date_to" name="date_to"
                                   value="{{ $filters['date_to'] }}">
                        </div>
                        <div class="lg:col-span-2 col-span-12">
                            <label for="per_page" class="form-label">Per Page</label>
                            <select class="form-control" id="per_page" name="per_page">
                                <option value="10" {{ $filters['per_page'] == 10 ? 'selected' : '' }}>10</option>
                                <option value="25" {{ $filters['per_page'] == 25 ? 'selected' : '' }}>25</option>
                                <option value="50" {{ $filters['per_page'] == 50 ? 'selected' : '' }}>50</option>
                                <option value="100" {{ $filters['per_page'] == 100 ? 'selected' : '' }}>100</option>
                            </select>
                        </div>
                        <div class="lg:col-span-1 col-span-12 flex items-end">
                            <button type="submit" class="btn inline-flex justify-center btn-primary w-full">
                                <iconify-icon icon="heroicons:magnifying-glass" class="text-lg"></iconify-icon>
                            </button>
                        </div>
                    </div>
                    <div class="mt-5">
                        <a href="{{ route('admin.financial.payments') }}" class="btn inline-flex justify-center btn-secondary btn-sm">
                            <iconify-icon icon="heroicons:x-mark" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                            Clear Filters
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Payments Table -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">
                    Payments ({{ $payments->total() }} total)
                </h4>
            </div>
            <div class="card-body px-6 pb-6">
                <div class="overflow-x-auto -mx-6">
                    <div class="inline-block min-w-full align-middle">
                        <div class="overflow-hidden">
                            <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
                                <thead class="bg-slate-200 dark:bg-slate-700">
                                    <tr>
                                        <th scope="col" class="table-th">ID</th>
                                        <th scope="col" class="table-th">User</th>
                                        <th scope="col" class="table-th">Product</th>
                                        <th scope="col" class="table-th">Amount</th>
                                        <th scope="col" class="table-th">Status</th>
                                        <th scope="col" class="table-th">Payment Method</th>
                                        <th scope="col" class="table-th">Date</th>
                                        <th scope="col" class="table-th">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                                    @forelse($payments as $payment)
                                        <tr>
                                            <td class="table-td">
                                                <a href="{{ route('admin.financial.payments.show', $payment) }}" class="text-primary-500 hover:text-primary-600 font-medium">
                                                    #{{ $payment->id }}
                                                </a>
                                            </td>
                                            <td class="table-td">
                                                <div class="flex items-center">
                                                    <div class="flex-none">
                                                        <div class="w-8 h-8 rounded-full bg-primary-500 text-white flex items-center justify-center ltr:mr-3 rtl:ml-3">
                                                            <iconify-icon icon="heroicons:user"></iconify-icon>
                                                        </div>
                                                    </div>
                                                    <div class="flex-1 text-start">
                                                        <h4 class="text-sm font-medium text-slate-600 dark:text-slate-300 whitespace-nowrap">
                                                            {{ $payment->user->name }}
                                                        </h4>
                                                        <div class="text-xs text-slate-500 dark:text-slate-400">
                                                            {{ $payment->user->email }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="table-td">
                                                @if($payment->subscription && $payment->subscription->product)
                                                    <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-info-500 bg-info-500">
                                                        {{ $payment->subscription->product->name }}
                                                    </div>
                                                @else
                                                    <span class="text-slate-500 dark:text-slate-400">N/A</span>
                                                @endif
                                            </td>
                                            <td class="table-td font-medium">${{ number_format($payment->amount / 100, 2) }}</td>
                                            <td class="table-td">
                                                <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-{{ $payment->status === 'succeeded' ? 'success' : ($payment->status === 'failed' ? 'danger' : 'warning') }}-500 bg-{{ $payment->status === 'succeeded' ? 'success' : ($payment->status === 'failed' ? 'danger' : 'warning') }}-500">
                                                    {{ ucfirst($payment->status) }}
                                                </div>
                                            </td>
                                            <td class="table-td">
                                                @if($payment->payment_method_type)
                                                    <div class="flex items-center">
                                                        <iconify-icon icon="heroicons:credit-card" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                                                        <span>{{ ucfirst($payment->payment_method_type) }}</span>
                                                        @if($payment->payment_method_last4)
                                                            <span class="text-slate-500 dark:text-slate-400 ml-1">****{{ $payment->payment_method_last4 }}</span>
                                                        @endif
                                                    </div>
                                                @else
                                                    <span class="text-slate-500 dark:text-slate-400">N/A</span>
                                                @endif
                                            </td>
                                            <td class="table-td">{{ $payment->created_at->format('M d, Y H:i') }}</td>
                                            <td class="table-td">
                                                <div class="flex space-x-3 rtl:space-x-reverse">
                                                    <a href="{{ route('admin.financial.payments.show', $payment) }}"
                                                       class="action-btn" title="View Details">
                                                        <iconify-icon icon="heroicons:eye"></iconify-icon>
                                                    </a>
                                                    @if($payment->status === 'succeeded' && $payment->amount > 0)
                                                        <button type="button" class="action-btn"
                                                                data-bs-toggle="modal" data-bs-target="#refundModal"
                                                                data-payment-id="{{ $payment->id }}"
                                                                data-payment-amount="{{ $payment->amount / 100 }}"
                                                                data-user-name="{{ $payment->user->name }}"
                                                                title="Process Refund">
                                                            <iconify-icon icon="heroicons:arrow-uturn-left"></iconify-icon>
                                                        </button>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="8" class="table-td text-center text-slate-500 dark:text-slate-400 py-6">
                                                <div class="flex flex-col items-center">
                                                    <iconify-icon icon="heroicons:credit-card" class="text-6xl mb-4 text-slate-300"></iconify-icon>
                                                    <div>No payments found matching your criteria</div>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                @if($payments->hasPages())
                    <div class="flex justify-between items-center mt-6 px-6">
                        <div class="text-slate-500 dark:text-slate-400">
                            Showing {{ $payments->firstItem() }} to {{ $payments->lastItem() }} of {{ $payments->total() }} results
                        </div>
                        <div>
                            {{ $payments->appends(request()->query())->links() }}
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Refund Modal -->
    <div class="modal fade fixed inset-0 z-[99999] hidden overflow-y-auto" id="refundModal" tabindex="-1" aria-labelledby="refundModalLabel" aria-hidden="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-slate-800 opacity-50"></div>
            </div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white dark:bg-slate-700 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white dark:bg-slate-700 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-slate-800 dark:text-slate-100" id="refundModalLabel">
                            Process Refund
                        </h3>
                        <button type="button" class="text-slate-400 hover:text-slate-600" data-bs-dismiss="modal" aria-label="Close">
                            <iconify-icon icon="heroicons:x-mark" class="text-2xl"></iconify-icon>
                        </button>
                    </div>
                    <form method="POST" action="{{ route('admin.financial.refunds.process') }}">
                        @csrf
                        <div class="space-y-4">
                            <input type="hidden" name="payment_id" id="refund_payment_id">

                            <div class="bg-info-100 border border-info-200 text-info-800 px-4 py-3 rounded-md">
                                <div class="flex items-center">
                                    <iconify-icon icon="heroicons:information-circle" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                                    <span>You are about to process a refund for <strong id="refund_user_name"></strong>.</span>
                                </div>
                            </div>

                            <div>
                                <label for="refund_amount" class="form-label">Refund Amount</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-slate-500 dark:text-slate-400">$</span>
                                    </div>
                                    <input type="number" class="form-control pl-8" id="refund_amount" name="amount"
                                           step="0.01" min="0.01" required>
                                </div>
                                <div class="text-xs text-slate-500 dark:text-slate-400 mt-1">
                                    Maximum refundable amount: $<span id="max_refund_amount"></span>
                                </div>
                            </div>

                            <div>
                                <label for="refund_reason" class="form-label">Reason for Refund</label>
                                <textarea class="form-control" id="refund_reason" name="reason" rows="3"
                                          placeholder="Please provide a reason for this refund..." required></textarea>
                            </div>
                        </div>
                        <div class="flex justify-end space-x-3 mt-6">
                            <button type="button" class="btn inline-flex justify-center btn-secondary" data-bs-dismiss="modal">
                                Cancel
                            </button>
                            <button type="submit" class="btn inline-flex justify-center btn-warning">
                                <iconify-icon icon="heroicons:arrow-uturn-left" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                                Process Refund
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const refundModal = document.getElementById('refundModal');

        if (refundModal) {
            refundModal.addEventListener('show.bs.modal', function (event) {
                const button = event.relatedTarget;
                const paymentId = button.getAttribute('data-payment-id');
                const paymentAmount = button.getAttribute('data-payment-amount');
                const userName = button.getAttribute('data-user-name');

                const modal = this;
                modal.querySelector('#refund_payment_id').value = paymentId;
                modal.querySelector('#refund_user_name').textContent = userName;
                modal.querySelector('#max_refund_amount').textContent = paymentAmount;
                const amountInput = modal.querySelector('#refund_amount');
                amountInput.setAttribute('max', paymentAmount);
                amountInput.value = paymentAmount;
            });
        }
    });
    </script>
    @endpush
</x-app-layout>
