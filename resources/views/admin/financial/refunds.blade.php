<x-app-layout>
    <div class="space-y-8">
        <div class="block sm:flex items-center justify-between mb-6">
            <x-breadcrumb :pageTitle="$pageTitle" :breadcrumbItems="$breadcrumbItems"/>
        </div>

        <!-- Filters Card -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Search & Filter</h4>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ route('admin.financial.refunds') }}">
                    <div class="grid grid-cols-12 gap-5">
                        <div class="lg:col-span-3 col-span-12">
                            <label for="q" class="form-label">Search</label>
                            <input type="text" class="form-control" id="q" name="q"
                                   value="{{ $filters['q'] }}" placeholder="Search by user name or email">
                        </div>
                        <div class="lg:col-span-2 col-span-12">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">All Statuses</option>
                                @foreach($statusOptions as $status)
                                    <option value="{{ $status }}" {{ $filters['status'] === $status ? 'selected' : '' }}>
                                        {{ ucfirst($status) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="lg:col-span-2 col-span-12">
                            <label for="date_from" class="form-label">Date From</label>
                            <input type="date" class="form-control" id="date_from" name="date_from"
                                   value="{{ $filters['date_from'] }}">
                        </div>
                        <div class="lg:col-span-2 col-span-12">
                            <label for="date_to" class="form-label">Date To</label>
                            <input type="date" class="form-control" id="date_to" name="date_to"
                                   value="{{ $filters['date_to'] }}">
                        </div>
                        <div class="lg:col-span-2 col-span-12">
                            <label for="per_page" class="form-label">Per Page</label>
                            <select class="form-control" id="per_page" name="per_page">
                                <option value="10" {{ $filters['per_page'] == 10 ? 'selected' : '' }}>10</option>
                                <option value="25" {{ $filters['per_page'] == 25 ? 'selected' : '' }}>25</option>
                                <option value="50" {{ $filters['per_page'] == 50 ? 'selected' : '' }}>50</option>
                                <option value="100" {{ $filters['per_page'] == 100 ? 'selected' : '' }}>100</option>
                            </select>
                        </div>
                        <div class="lg:col-span-1 col-span-12 flex items-end">
                            <button type="submit" class="btn inline-flex justify-center btn-primary w-full">
                                <iconify-icon icon="heroicons:magnifying-glass" class="text-lg"></iconify-icon>
                            </button>
                        </div>
                    </div>
                    <div class="mt-5">
                        <a href="{{ route('admin.financial.refunds') }}" class="btn inline-flex justify-center btn-secondary btn-sm">
                            <iconify-icon icon="heroicons:x-mark" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                            Clear Filters
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Refunds Table -->
        <div class="card">
            <div class="card-header noborder">
                <h4 class="card-title">Refunds ({{ $refunds->total() }} total)</h4>
            </div>
            <div class="card-body px-6 pb-6">
                <div class="overflow-x-auto -mx-6">
                    <div class="inline-block min-w-full align-middle">
                        <div class="overflow-hidden">
                            <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
                                <thead class="bg-slate-200 dark:bg-slate-700">
                                    <tr>
                                        <th scope="col" class="table-th">ID</th>
                                        <th scope="col" class="table-th">Payment</th>
                                        <th scope="col" class="table-th">Customer</th>
                                        <th scope="col" class="table-th">Product</th>
                                        <th scope="col" class="table-th">Amount</th>
                                        <th scope="col" class="table-th">Status</th>
                                        <th scope="col" class="table-th">Reason</th>
                                        <th scope="col" class="table-th">Date</th>
                                        <th scope="col" class="table-th">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                                    @forelse($refunds as $refund)
                                        <tr>
                                            <td class="table-td">
                                                <span class="font-medium text-warning-500">#{{ $refund->id }}</span>
                                            </td>
                                            <td class="table-td">
                                                <a href="{{ route('admin.financial.payments.show', $refund->payment) }}" class="text-primary-500 hover:text-primary-600">
                                                    #{{ $refund->payment->id }}
                                                </a>
                                            </td>
                                            <td class="table-td">
                                                <div class="flex items-center">
                                                    <div class="flex-none ltr:mr-3 rtl:ml-3">
                                                        <div class="w-8 h-8 rounded-full bg-primary-500 text-white flex items-center justify-center text-sm">
                                                            <iconify-icon icon="heroicons:user"></iconify-icon>
                                                        </div>
                                                    </div>
                                                    <div class="flex-1">
                                                        <div class="text-sm font-medium text-slate-900 dark:text-slate-100">{{ $refund->payment->user->name }}</div>
                                                        <div class="text-xs text-slate-500 dark:text-slate-400">{{ $refund->payment->user->email }}</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="table-td">
                                                @if($refund->payment->subscription && $refund->payment->subscription->product)
                                                    <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-info-500 bg-info-500">
                                                        {{ $refund->payment->subscription->product->name }}
                                                    </div>
                                                @else
                                                    <span class="text-slate-500 dark:text-slate-400">N/A</span>
                                                @endif
                                            </td>
                                            <td class="table-td font-medium text-warning-500">${{ number_format($refund->amount / 100, 2) }}</td>
                                            <td class="table-td">
                                                <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-{{ $refund->status === 'succeeded' ? 'success' : ($refund->status === 'failed' ? 'danger' : 'warning') }}-500 bg-{{ $refund->status === 'succeeded' ? 'success' : ($refund->status === 'failed' ? 'danger' : 'warning') }}-500">
                                                    {{ ucfirst($refund->status) }}
                                                </div>
                                            </td>
                                            <td class="table-td">
                                                @if($refund->reason)
                                                    <span class="truncate inline-block max-w-[150px]" title="{{ $refund->reason }}">
                                                        {{ $refund->reason }}
                                                    </span>
                                                @else
                                                    <span class="text-slate-500 dark:text-slate-400">No reason provided</span>
                                                @endif
                                            </td>
                                            <td class="table-td">{{ $refund->created_at->format('M d, Y H:i') }}</td>
                                            <td class="table-td">
                                                <div class="flex space-x-3 rtl:space-x-reverse">
                                                    <a href="{{ route('admin.financial.payments.show', $refund->payment) }}"
                                                       class="action-btn" title="View Payment">
                                                        <iconify-icon icon="heroicons:eye"></iconify-icon>
                                                    </a>
                                                    @if($refund->stripe_refund_id)
                                                        <button type="button" class="action-btn"
                                                                title="Stripe Refund ID: {{ $refund->stripe_refund_id }}">
                                                            <iconify-icon icon="simple-icons:stripe"></iconify-icon>
                                                        </button>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="9" class="table-td text-center py-6">
                                                <div class="text-slate-500 dark:text-slate-400">
                                                    <iconify-icon icon="heroicons:arrow-path" class="text-3xl mb-3 text-slate-300"></iconify-icon>
                                                    <div>No refunds found matching your criteria</div>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                @if($refunds->hasPages())
                    <div class="flex justify-between items-center mt-6">
                        <div class="text-slate-500 dark:text-slate-400">
                            Showing {{ $refunds->firstItem() }} to {{ $refunds->lastItem() }} of {{ $refunds->total() }} results
                        </div>
                        <div>
                            {{ $refunds->appends(request()->query())->links() }}
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Refund Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="bg-white dark:bg-slate-800 rounded-md px-5 py-4">
                <div class="pl-14 relative">
                    <div class="w-10 h-10 rounded-full bg-success-100 text-success-800 text-base flex items-center justify-center absolute left-0 top-2">
                        <iconify-icon icon="heroicons:check-circle"></iconify-icon>
                    </div>
                    <div class="text-slate-600 dark:text-slate-300 text-sm mb-1 font-medium uppercase">
                        Successful Refunds
                    </div>
                    <div class="text-slate-900 dark:text-white text-xl font-medium">
                        {{ $refunds->where('status', 'succeeded')->count() }}
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-slate-800 rounded-md px-5 py-4">
                <div class="pl-14 relative">
                    <div class="w-10 h-10 rounded-full bg-warning-100 text-warning-800 text-base flex items-center justify-center absolute left-0 top-2">
                        <iconify-icon icon="heroicons:clock"></iconify-icon>
                    </div>
                    <div class="text-slate-600 dark:text-slate-300 text-sm mb-1 font-medium uppercase">
                        Pending Refunds
                    </div>
                    <div class="text-slate-900 dark:text-white text-xl font-medium">
                        {{ $refunds->where('status', 'pending')->count() }}
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-slate-800 rounded-md px-5 py-4">
                <div class="pl-14 relative">
                    <div class="w-10 h-10 rounded-full bg-danger-100 text-danger-800 text-base flex items-center justify-center absolute left-0 top-2">
                        <iconify-icon icon="heroicons:x-circle"></iconify-icon>
                    </div>
                    <div class="text-slate-600 dark:text-slate-300 text-sm mb-1 font-medium uppercase">
                        Failed Refunds
                    </div>
                    <div class="text-slate-900 dark:text-white text-xl font-medium">
                        {{ $refunds->where('status', 'failed')->count() }}
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-slate-800 rounded-md px-5 py-4">
                <div class="pl-14 relative">
                    <div class="w-10 h-10 rounded-full bg-info-100 text-info-800 text-base flex items-center justify-center absolute left-0 top-2">
                        <iconify-icon icon="heroicons:currency-dollar"></iconify-icon>
                    </div>
                    <div class="text-slate-600 dark:text-slate-300 text-sm mb-1 font-medium uppercase">
                        Total Refunded
                    </div>
                    <div class="text-slate-900 dark:text-white text-xl font-medium">
                        ${{ number_format($refunds->where('status', 'succeeded')->sum(function($refund) { return $refund->amount / 100; }), 2) }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
