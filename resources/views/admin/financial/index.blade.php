<x-app-layout>
    <div class="space-y-8">
        <div class="block sm:flex items-center justify-between mb-6">
            <x-breadcrumb :pageTitle="$pageTitle" :breadcrumbItems="$breadcrumbItems"/>
        </div>

        <!-- Key Metrics Cards -->
        <div class="grid sm:grid-cols-2 xl:grid-cols-4 gap-7">
            <div class="bg-white dark:bg-slate-800 rounded-md px-5 py-4">
                <div class="pl-14 relative">
                    <div class="w-10 h-10 rounded-full bg-primary-100 text-primary-800 text-base flex items-center justify-center absolute left-0 top-2">
                        <iconify-icon icon="heroicons:currency-dollar"></iconify-icon>
                    </div>
                    <h4 class="font-Inter font-normal text-sm text-textColor dark:text-white pb-1">
                        Total Revenue
                    </h4>
                    <p class="font-Inter text-xl text-black dark:text-white font-medium">
                        ${{ number_format($metrics['totalRevenue'], 2) }}
                    </p>
                </div>
            </div>

            <div class="bg-white dark:bg-slate-800 rounded-md px-5 py-4">
                <div class="pl-14 relative">
                    <div class="w-10 h-10 rounded-full bg-success-100 text-success-800 text-base flex items-center justify-center absolute left-0 top-2">
                        <iconify-icon icon="heroicons:calendar-days"></iconify-icon>
                    </div>
                    <h4 class="font-Inter font-normal text-sm text-textColor dark:text-white pb-1">
                        Monthly Revenue
                    </h4>
                    <p class="font-Inter text-xl text-black dark:text-white font-medium">
                        ${{ number_format($metrics['monthlyRevenue'], 2) }}
                    </p>
                </div>
            </div>

            <div class="bg-white dark:bg-slate-800 rounded-md px-5 py-4">
                <div class="pl-14 relative">
                    <div class="w-10 h-10 rounded-full bg-warning-100 text-warning-800 text-base flex items-center justify-center absolute left-0 top-2">
                        <iconify-icon icon="heroicons:arrow-uturn-left"></iconify-icon>
                    </div>
                    <h4 class="font-Inter font-normal text-sm text-textColor dark:text-white pb-1">
                        Total Refunds
                    </h4>
                    <p class="font-Inter text-xl text-black dark:text-white font-medium">
                        ${{ number_format($metrics['totalRefunds'], 2) }}
                    </p>
                </div>
            </div>

            <div class="bg-white dark:bg-slate-800 rounded-md px-5 py-4">
                <div class="pl-14 relative">
                    <div class="w-10 h-10 rounded-full bg-danger-100 text-danger-800 text-base flex items-center justify-center absolute left-0 top-2">
                        <iconify-icon icon="heroicons:exclamation-triangle"></iconify-icon>
                    </div>
                    <h4 class="font-Inter font-normal text-sm text-textColor dark:text-white pb-1">
                        Failed Payments (This Month)
                    </h4>
                    <p class="font-Inter text-xl text-black dark:text-white font-medium">
                        {{ $metrics['failedPayments'] }}
                    </p>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="xl:flex gap-y-8 xl:gap-8 overflow-hidden">
            <!-- Monthly Revenue Chart -->
            <div class="xl:w-8/12 bg-white dark:bg-slate-800 overflow-hidden p-6 rounded-md">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="font-Inter font-normal text-black dark:text-white text-xl">
                        Monthly Revenue Trend
                    </h3>
                    <div class="relative">
                        <div class="dropdown relative">
                            <button class="text-xl text-center block w-full" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <span class="text-lg inline-flex h-6 w-6 flex-col items-center justify-center border border-slate-200 dark:border-slate-700 rounded dark:text-slate-400">
                                    <iconify-icon icon="heroicons-outline:dots-horizontal"></iconify-icon>
                                </span>
                            </button>
                            <ul class="dropdown-menu min-w-[120px] absolute text-sm text-slate-700 dark:text-white hidden bg-white dark:bg-slate-700 shadow z-[2] overflow-hidden list-none text-left rounded-lg mt-1 m-0 bg-clip-padding border-none">
                                <li>
                                    <a href="{{ route('admin.financial.revenue-analytics') }}" class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600 dark:hover:text-white">
                                        View Detailed Analytics
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <canvas id="financialMonthlyRevenueChart" style="height: 350px;"></canvas>
            </div>

            <!-- Payment Status Distribution -->
            <div class="mt-8 xl:mt-0 xl:w-4/12 bg-white dark:bg-slate-800 rounded-md w-full">
                <h3 class="px-6 py-5 font-Inter font-normal text-black dark:text-white text-xl border-b border-b-slate-100 dark:border-b-slate-900">
                    Payment Status Distribution
                </h3>
                <div class="overflow-hidden px-6 py-5">
                    <canvas id="financialPaymentStatusChart" style="height: 300px;"></canvas>
                    <div class="mt-4 text-center text-sm">
                        @foreach($chartData['paymentStats'] as $status => $count)
                            <span class="inline-block mr-4 mb-2">
                                <span class="inline-block w-3 h-3 rounded-full mr-2 {{ $status === 'succeeded' ? 'bg-success-500' : ($status === 'failed' ? 'bg-danger-500' : 'bg-warning-500') }}"></span>
                                <span class="text-slate-600 dark:text-slate-300">{{ ucfirst($status) }} ({{ $count }})</span>
                            </span>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white dark:bg-slate-800 rounded-md">
            <h3 class="px-6 py-5 font-Inter font-normal text-black dark:text-white text-xl border-b border-b-slate-100 dark:border-b-slate-900">
                Quick Actions
            </h3>
            <div class="px-6 py-5">
                <div class="grid md:grid-cols-4 grid-cols-1 gap-5">
                    <a href="{{ route('admin.financial.payments') }}" class="btn inline-flex justify-center btn-primary">
                        <iconify-icon icon="heroicons:credit-card" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                        Manage Payments
                    </a>
                    <a href="{{ route('admin.financial.invoices') }}" class="btn inline-flex justify-center btn-info">
                        <iconify-icon icon="heroicons:document-text" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                        View Invoices
                    </a>
                    <a href="{{ route('admin.financial.refunds') }}" class="btn inline-flex justify-center btn-warning">
                        <iconify-icon icon="heroicons:arrow-uturn-left" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                        Process Refunds
                    </a>
                    <a href="{{ route('admin.financial.revenue-analytics') }}" class="btn inline-flex justify-center btn-success">
                        <iconify-icon icon="heroicons:chart-bar" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                        Revenue Analytics
                    </a>
                </div>
            </div>
        </div>

        <!-- Recent Transactions -->
        <div class="grid grid-cols-12 gap-5">
            <!-- Recent Payments -->
            <div class="xl:col-span-8 col-span-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Recent Payments</h4>
                        <div>
                            <a href="{{ route('admin.financial.payments') }}" class="btn inline-flex justify-center btn-primary btn-sm">
                                View All
                            </a>
                        </div>
                    </div>
                    <div class="card-body px-6 pb-6">
                        <div class="overflow-x-auto -mx-6">
                            <div class="inline-block min-w-full align-middle">
                                <div class="overflow-hidden">
                                    <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
                                        <thead class="bg-slate-200 dark:bg-slate-700">
                                            <tr>
                                                <th scope="col" class="table-th">ID</th>
                                                <th scope="col" class="table-th">User</th>
                                                <th scope="col" class="table-th">Product</th>
                                                <th scope="col" class="table-th">Amount</th>
                                                <th scope="col" class="table-th">Status</th>
                                                <th scope="col" class="table-th">Date</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                                            @forelse($recentTransactions['payments'] as $payment)
                                                <tr>
                                                    <td class="table-td">
                                                        <a href="{{ route('admin.financial.payments.show', $payment) }}" class="text-primary-500 hover:text-primary-600">
                                                            #{{ $payment->id }}
                                                        </a>
                                                    </td>
                                                    <td class="table-td">
                                                        <div class="flex items-center">
                                                            <div class="flex-none">
                                                                <div class="w-8 h-8 rounded-full bg-primary-500 text-white flex items-center justify-center ltr:mr-3 rtl:ml-3">
                                                                    <iconify-icon icon="heroicons:user"></iconify-icon>
                                                                </div>
                                                            </div>
                                                            <div class="flex-1 text-start">
                                                                <h4 class="text-sm font-medium text-slate-600 dark:text-slate-300 whitespace-nowrap">
                                                                    {{ $payment->user->name }}
                                                                </h4>
                                                                <div class="text-xs text-slate-500 dark:text-slate-400">
                                                                    {{ $payment->user->email }}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td class="table-td">{{ $payment->subscription->product->name ?? 'N/A' }}</td>
                                                    <td class="table-td">${{ number_format($payment->amount / 100, 2) }}</td>
                                                    <td class="table-td">
                                                        <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-{{ $payment->status === 'succeeded' ? 'success' : ($payment->status === 'failed' ? 'danger' : 'warning') }}-500 bg-{{ $payment->status === 'succeeded' ? 'success' : ($payment->status === 'failed' ? 'danger' : 'warning') }}-500">
                                                            {{ ucfirst($payment->status) }}
                                                        </div>
                                                    </td>
                                                    <td class="table-td">{{ $payment->created_at->format('M d, Y') }}</td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="6" class="table-td text-center text-slate-500 dark:text-slate-400">
                                                        No recent payments found
                                                    </td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Refunds -->
            <div class="xl:col-span-4 col-span-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Recent Refunds</h4>
                        <div>
                            <a href="{{ route('admin.financial.refunds') }}" class="btn inline-flex justify-center btn-warning btn-sm">
                                View All
                            </a>
                        </div>
                    </div>
                    <div class="card-body p-6">
                        @forelse($recentTransactions['refunds'] as $refund)
                            <div class="flex items-center border-b border-slate-100 dark:border-slate-700 py-3 last:border-b-0">
                                <div class="flex-none ltr:mr-3 rtl:ml-3">
                                    <div class="w-8 h-8 rounded-full bg-warning-500 text-white flex items-center justify-center">
                                        <iconify-icon icon="heroicons:arrow-uturn-left"></iconify-icon>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-slate-600 dark:text-slate-300">
                                        ${{ number_format($refund->amount / 100, 2) }}
                                    </div>
                                    <div class="text-xs text-slate-500 dark:text-slate-400">
                                        {{ $refund->payment->user->name }}
                                    </div>
                                    <div class="text-xs text-slate-500 dark:text-slate-400">
                                        {{ $refund->created_at->format('M d, Y') }}
                                    </div>
                                </div>
                                <div>
                                    <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-{{ $refund->status === 'succeeded' ? 'success' : ($refund->status === 'failed' ? 'danger' : 'warning') }}-500 bg-{{ $refund->status === 'succeeded' ? 'success' : ($refund->status === 'failed' ? 'danger' : 'warning') }}-500">
                                        {{ ucfirst($refund->status) }}
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div class="text-center text-slate-500 dark:text-slate-400 py-4">
                                No recent refunds found
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Monthly Revenue Chart
        const monthlyRevenueCanvas = document.getElementById('financialMonthlyRevenueChart');
        if (monthlyRevenueCanvas) {
            const monthlyRevenueCtx = monthlyRevenueCanvas.getContext('2d');
            const monthlyRevenueChart = new Chart(monthlyRevenueCtx, {
        type: 'line',
        data: {
            labels: @json($chartData['monthlyRevenue']['labels']),
            datasets: [{
                label: 'Revenue',
                data: @json($chartData['monthlyRevenue']['data']),
                borderColor: '#4669FA',
                backgroundColor: 'rgba(70, 105, 250, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.3
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'Revenue: $' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            }
        }
    });
        }

        // Payment Status Chart
        const paymentStatusCanvas = document.getElementById('financialPaymentStatusChart');
        if (paymentStatusCanvas) {
            const paymentStatusCtx = paymentStatusCanvas.getContext('2d');
            const paymentStatusChart = new Chart(paymentStatusCtx, {
        type: 'doughnut',
        data: {
            labels: @json(array_keys($chartData['paymentStats'])),
            datasets: [{
                data: @json(array_values($chartData['paymentStats'])),
                backgroundColor: ['#1cc88a', '#e74a3b', '#f6c23e', '#36b9cc'],
                hoverBackgroundColor: ['#17a673', '#e02d1b', '#f4b619', '#2c9faf'],
                hoverBorderColor: "rgba(234, 236, 244, 1)",
            }],
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            cutout: '80%'
        }
    });
        }
    });
    </script>
    @endpush
</x-app-layout>
