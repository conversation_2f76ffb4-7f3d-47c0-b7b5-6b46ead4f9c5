<x-app-layout>
    <div class="space-y-8">
        <div class="block sm:flex items-center justify-between mb-6">
            <x-breadcrumb :pageTitle="$pageTitle" :breadcrumbItems="$breadcrumbItems"/>
        </div>

        <!-- Filters Card -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Search & Filter</h4>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ route('admin.financial.invoices') }}">
                    <div class="grid grid-cols-12 gap-5">
                        <div class="lg:col-span-4 col-span-12">
                            <label for="q" class="form-label">Search</label>
                            <input type="text" class="form-control" id="q" name="q"
                                   value="{{ $filters['q'] }}" placeholder="Search by invoice number, user name or email">
                        </div>
                        <div class="lg:col-span-3 col-span-12">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">All Statuses</option>
                                @foreach($statusOptions as $status)
                                    <option value="{{ $status }}" {{ $filters['status'] === $status ? 'selected' : '' }}>
                                        {{ ucfirst($status) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="lg:col-span-3 col-span-12">
                            <label for="per_page" class="form-label">Per Page</label>
                            <select class="form-control" id="per_page" name="per_page">
                                <option value="10" {{ $filters['per_page'] == 10 ? 'selected' : '' }}>10</option>
                                <option value="25" {{ $filters['per_page'] == 25 ? 'selected' : '' }}>25</option>
                                <option value="50" {{ $filters['per_page'] == 50 ? 'selected' : '' }}>50</option>
                                <option value="100" {{ $filters['per_page'] == 100 ? 'selected' : '' }}>100</option>
                            </select>
                        </div>
                        <div class="lg:col-span-2 col-span-12 flex items-end">
                            <button type="submit" class="btn inline-flex justify-center btn-primary w-full">
                                <iconify-icon icon="heroicons:magnifying-glass" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                                Search
                            </button>
                        </div>
                    </div>
                    <div class="mt-5">
                        <a href="{{ route('admin.financial.invoices') }}" class="btn inline-flex justify-center btn-secondary btn-sm">
                            <iconify-icon icon="heroicons:x-mark" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                            Clear Filters
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Invoices Table -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">
                    Invoices ({{ $invoices->total() }} total)
                </h4>
            </div>
            <div class="card-body px-6 pb-6">
                <div class="overflow-x-auto -mx-6">
                    <div class="inline-block min-w-full align-middle">
                        <div class="overflow-hidden">
                            <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
                                <thead class="bg-slate-200 dark:bg-slate-700">
                                    <tr>
                                        <th scope="col" class="table-th">Invoice #</th>
                                        <th scope="col" class="table-th">Customer</th>
                                        <th scope="col" class="table-th">Product</th>
                                        <th scope="col" class="table-th">Amount</th>
                                        <th scope="col" class="table-th">Status</th>
                                        <th scope="col" class="table-th">Due Date</th>
                                        <th scope="col" class="table-th">Created</th>
                                        <th scope="col" class="table-th">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                                    @forelse($invoices as $invoice)
                                        <tr>
                                            <td class="table-td">
                                                <a href="{{ route('admin.financial.invoices.show', $invoice) }}" class="text-primary-500 hover:text-primary-600 font-medium">
                                                    {{ $invoice->invoice_number }}
                                                </a>
                                            </td>
                                            <td class="table-td">
                                                <div class="flex items-center">
                                                    <div class="flex-none">
                                                        <div class="w-8 h-8 rounded-full bg-primary-500 text-white flex items-center justify-center ltr:mr-3 rtl:ml-3">
                                                            <iconify-icon icon="heroicons:user"></iconify-icon>
                                                        </div>
                                                    </div>
                                                    <div class="flex-1 text-start">
                                                        <h4 class="text-sm font-medium text-slate-600 dark:text-slate-300 whitespace-nowrap">
                                                            {{ $invoice->user->name }}
                                                        </h4>
                                                        <div class="text-xs text-slate-500 dark:text-slate-400">
                                                            {{ $invoice->user->email }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="table-td">
                                                @if($invoice->userSubscription && $invoice->userSubscription->product)
                                                    <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-info-500 bg-info-500">
                                                        {{ $invoice->userSubscription->product->name }}
                                                    </div>
                                                @else
                                                    <span class="text-slate-500 dark:text-slate-400">N/A</span>
                                                @endif
                                            </td>
                                            <td class="table-td font-medium">${{ number_format($invoice->total, 2) }}</td>
                                            <td class="table-td">
                                                @php
                                                    $statusColors = [
                                                        'draft' => 'slate',
                                                        'open' => 'warning',
                                                        'paid' => 'success',
                                                        'void' => 'danger',
                                                        'uncollectible' => 'slate'
                                                    ];
                                                    $statusColor = $statusColors[$invoice->status] ?? 'slate';
                                                @endphp
                                                <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-{{ $statusColor }}-500 bg-{{ $statusColor }}-500">
                                                    {{ ucfirst($invoice->status) }}
                                                </div>
                                            </td>
                                            <td class="table-td">
                                                @if($invoice->due_date)
                                                    <div>{{ $invoice->due_date->format('M d, Y') }}</div>
                                                    @if($invoice->due_date->isPast() && $invoice->status === 'open')
                                                        <div class="text-xs text-danger-500 mt-1">Overdue</div>
                                                    @endif
                                                @else
                                                    <span class="text-slate-500 dark:text-slate-400">N/A</span>
                                                @endif
                                            </td>
                                            <td class="table-td">{{ $invoice->created_at->format('M d, Y') }}</td>
                                            <td class="table-td">
                                                <div class="flex space-x-3 rtl:space-x-reverse">
                                                    <a href="{{ route('admin.financial.invoices.show', $invoice) }}"
                                                       class="action-btn" title="View Details">
                                                        <iconify-icon icon="heroicons:eye"></iconify-icon>
                                                    </a>
                                                    @if($invoice->pdf_url)
                                                        <a href="{{ $invoice->pdf_url }}" target="_blank"
                                                           class="action-btn" title="Download PDF">
                                                            <iconify-icon icon="heroicons:arrow-down-tray"></iconify-icon>
                                                        </a>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="8" class="table-td text-center text-slate-500 dark:text-slate-400 py-6">
                                                <div class="flex flex-col items-center">
                                                    <iconify-icon icon="heroicons:document-text" class="text-6xl mb-4 text-slate-300"></iconify-icon>
                                                    <div>No invoices found matching your criteria</div>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                @if($invoices->hasPages())
                    <div class="flex justify-between items-center mt-6 px-6">
                        <div class="text-slate-500 dark:text-slate-400">
                            Showing {{ $invoices->firstItem() }} to {{ $invoices->lastItem() }} of {{ $invoices->total() }} results
                        </div>
                        <div>
                            {{ $invoices->appends(request()->query())->links() }}
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Invoice Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="bg-white dark:bg-slate-800 rounded-md px-5 py-4">
                <div class="pl-14 relative">
                    <div class="w-10 h-10 rounded-full bg-success-100 text-success-800 text-base flex items-center justify-center absolute left-0 top-2">
                        <iconify-icon icon="heroicons:check-circle"></iconify-icon>
                    </div>
                    <div class="text-slate-600 dark:text-slate-300 text-sm mb-1 font-medium uppercase">
                        Paid Invoices
                    </div>
                    <div class="text-slate-900 dark:text-white text-xl font-medium">
                        {{ $invoices->where('status', 'paid')->count() }}
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-slate-800 rounded-md px-5 py-4">
                <div class="pl-14 relative">
                    <div class="w-10 h-10 rounded-full bg-warning-100 text-warning-800 text-base flex items-center justify-center absolute left-0 top-2">
                        <iconify-icon icon="heroicons:clock"></iconify-icon>
                    </div>
                    <div class="text-slate-600 dark:text-slate-300 text-sm mb-1 font-medium uppercase">
                        Open Invoices
                    </div>
                    <div class="text-slate-900 dark:text-white text-xl font-medium">
                        {{ $invoices->where('status', 'open')->count() }}
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-slate-800 rounded-md px-5 py-4">
                <div class="pl-14 relative">
                    <div class="w-10 h-10 rounded-full bg-danger-100 text-danger-800 text-base flex items-center justify-center absolute left-0 top-2">
                        <iconify-icon icon="heroicons:exclamation-triangle"></iconify-icon>
                    </div>
                    <div class="text-slate-600 dark:text-slate-300 text-sm mb-1 font-medium uppercase">
                        Overdue Invoices
                    </div>
                    <div class="text-slate-900 dark:text-white text-xl font-medium">
                        {{ $invoices->where('status', 'open')->filter(function($invoice) {
                            return $invoice->due_date && $invoice->due_date->isPast();
                        })->count() }}
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-slate-800 rounded-md px-5 py-4">
                <div class="pl-14 relative">
                    <div class="w-10 h-10 rounded-full bg-info-100 text-info-800 text-base flex items-center justify-center absolute left-0 top-2">
                        <iconify-icon icon="heroicons:currency-dollar"></iconify-icon>
                    </div>
                    <div class="text-slate-600 dark:text-slate-300 text-sm mb-1 font-medium uppercase">
                        Total Amount
                    </div>
                    <div class="text-slate-900 dark:text-white text-xl font-medium">
                        ${{ number_format($invoices->sum('total'), 2) }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
