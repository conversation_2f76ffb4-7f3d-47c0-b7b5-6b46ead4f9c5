<x-app-layout>
    <div class="space-y-8">
        <div class="block sm:flex items-center justify-between mb-6">
            <x-breadcrumb :pageTitle="$pageTitle" :breadcrumbItems="$breadcrumbItems"/>
            <div class="flex space-x-3 rtl:space-x-reverse">
                @if($invoice->invoice_pdf_url)
                    <a href="{{ $invoice->invoice_pdf_url }}" target="_blank" class="btn inline-flex justify-center btn-info">
                        <iconify-icon icon="heroicons:arrow-down-tray" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                        Download PDF
                    </a>
                @endif
                <a href="{{ route('admin.financial.invoices') }}" class="btn inline-flex justify-center btn-secondary">
                    <iconify-icon icon="heroicons:arrow-left" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                    Back to Invoices
                </a>
            </div>
        </div>

        <div class="grid grid-cols-12 gap-5">
            <!-- Invoice Information -->
            <div class="xl:col-span-8 col-span-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Invoice Information</h4>
                    </div>
                    <div class="card-body">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-4">
                                <div class="flex justify-between py-2 border-b border-slate-100 dark:border-slate-700">
                                    <span class="font-medium text-slate-600 dark:text-slate-300">Invoice Number:</span>
                                    <span class="text-slate-900 dark:text-slate-100">{{ $invoice->invoice_number }}</span>
                                </div>
                                <div class="flex justify-between py-2 border-b border-slate-100 dark:border-slate-700">
                                    <span class="font-medium text-slate-600 dark:text-slate-300">Stripe Invoice ID:</span>
                                    <span class="text-slate-900 dark:text-slate-100">
                                        @if($invoice->stripe_invoice_id)
                                            <code class="bg-slate-100 dark:bg-slate-700 px-2 py-1 rounded text-sm">{{ $invoice->stripe_invoice_id }}</code>
                                        @else
                                            <span class="text-slate-500 dark:text-slate-400">N/A</span>
                                        @endif
                                    </span>
                                </div>
                                <div class="flex justify-between py-2 border-b border-slate-100 dark:border-slate-700">
                                    <span class="font-medium text-slate-600 dark:text-slate-300">Amount:</span>
                                    <span class="text-xl font-bold text-success-500">${{ number_format($invoice->amount / 100, 2) }}</span>
                                </div>
                                <div class="flex justify-between py-2 border-b border-slate-100 dark:border-slate-700">
                                    <span class="font-medium text-slate-600 dark:text-slate-300">Status:</span>
                                    <span>
                                        @php
                                            $statusColors = [
                                                'draft' => 'slate',
                                                'open' => 'warning',
                                                'paid' => 'success',
                                                'void' => 'danger',
                                                'uncollectible' => 'slate'
                                            ];
                                            $statusColor = $statusColors[$invoice->status] ?? 'slate';
                                        @endphp
                                        <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-{{ $statusColor }}-500 bg-{{ $statusColor }}-500">
                                            {{ ucfirst($invoice->status) }}
                                        </div>
                                    </span>
                                </div>
                                <div class="flex justify-between py-2">
                                    <span class="font-medium text-slate-600 dark:text-slate-300">Currency:</span>
                                    <span class="text-slate-900 dark:text-slate-100">{{ strtoupper($invoice->currency ?? 'USD') }}</span>
                                </div>
                            </div>
                            <div class="space-y-4">
                                <div class="flex justify-between py-2 border-b border-slate-100 dark:border-slate-700">
                                    <span class="font-medium text-slate-600 dark:text-slate-300">Created:</span>
                                    <span class="text-slate-900 dark:text-slate-100">{{ $invoice->created_at->format('M d, Y H:i:s') }}</span>
                                </div>
                                <div class="flex justify-between py-2 border-b border-slate-100 dark:border-slate-700">
                                    <span class="font-medium text-slate-600 dark:text-slate-300">Due Date:</span>
                                    <span class="text-slate-900 dark:text-slate-100">
                                        @if($invoice->due_date)
                                            <div>{{ $invoice->due_date->format('M d, Y') }}</div>
                                            @if($invoice->due_date->isPast() && $invoice->status === 'open')
                                                <div class="text-xs text-danger-500 font-medium mt-1">Overdue</div>
                                            @endif
                                        @else
                                            <span class="text-slate-500 dark:text-slate-400">N/A</span>
                                        @endif
                                    </span>
                                </div>
                                <div class="flex justify-between py-2 border-b border-slate-100 dark:border-slate-700">
                                    <span class="font-medium text-slate-600 dark:text-slate-300">Paid Date:</span>
                                    <span class="text-slate-900 dark:text-slate-100">
                                        @if($invoice->paid_at)
                                            {{ $invoice->paid_at->format('M d, Y H:i:s') }}
                                        @else
                                            <span class="text-slate-500 dark:text-slate-400">Not paid</span>
                                        @endif
                                    </span>
                                </div>
                                <div class="flex justify-between py-2">
                                    <span class="font-medium text-slate-600 dark:text-slate-300">Updated:</span>
                                    <span class="text-slate-900 dark:text-slate-100">{{ $invoice->updated_at->format('M d, Y H:i:s') }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customer Information -->
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Customer Information</h4>
                    </div>
                    <div class="card-body">
                        <div class="flex items-center space-x-4">
                            <div class="flex-none">
                                <div class="w-12 h-12 rounded-full bg-primary-500 text-white flex items-center justify-center">
                                    <iconify-icon icon="heroicons:user" class="text-xl"></iconify-icon>
                                </div>
                            </div>
                            <div class="flex-1">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <div class="font-medium text-slate-900 dark:text-slate-100">{{ $invoice->user->name }}</div>
                                        <div class="text-slate-500 dark:text-slate-400">{{ $invoice->user->email }}</div>
                                    </div>
                                    <div class="text-sm text-slate-500 dark:text-slate-400">
                                        <div><span class="font-medium">User ID:</span> #{{ $invoice->user->id }}</div>
                                        <div><span class="font-medium">Joined:</span> {{ $invoice->user->created_at->format('M d, Y') }}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-none">
                                <a href="{{ route('admin.users.show', $invoice->user) }}" class="btn inline-flex justify-center btn-outline-primary btn-sm">
                                    View Profile
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Subscription Information -->
                @if($invoice->subscription)
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Subscription Information</h4>
                        </div>
                        <div class="card-body">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="space-y-4">
                                    <div class="flex justify-between py-2 border-b border-slate-100 dark:border-slate-700">
                                        <span class="font-medium text-slate-600 dark:text-slate-300">Product:</span>
                                        <span>
                                            @if($invoice->subscription->product)
                                                <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-info-500 bg-info-500">
                                                    {{ $invoice->subscription->product->name }}
                                                </div>
                                            @else
                                                <span class="text-slate-500 dark:text-slate-400">N/A</span>
                                            @endif
                                        </span>
                                    </div>
                                    <div class="flex justify-between py-2">
                                        <span class="font-medium text-slate-600 dark:text-slate-300">Status:</span>
                                        <span>
                                            <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-{{ $invoice->subscription->status === 'active' ? 'success' : 'warning' }}-500 bg-{{ $invoice->subscription->status === 'active' ? 'success' : 'warning' }}-500">
                                                {{ ucfirst($invoice->subscription->status) }}
                                            </div>
                                        </span>
                                    </div>
                                </div>
                                <div class="space-y-4">
                                    <div class="flex justify-between py-2 border-b border-slate-100 dark:border-slate-700">
                                        <span class="font-medium text-slate-600 dark:text-slate-300">Started:</span>
                                        <span class="text-slate-900 dark:text-slate-100">{{ $invoice->subscription->created_at->format('M d, Y') }}</span>
                                    </div>
                                    <div class="flex justify-between py-2">
                                        <span class="font-medium text-slate-600 dark:text-slate-300">Next Billing:</span>
                                        <span class="text-slate-900 dark:text-slate-100">
                                            @if($invoice->subscription->current_period_end)
                                                {{ $invoice->subscription->current_period_end->format('M d, Y') }}
                                            @else
                                                <span class="text-slate-500 dark:text-slate-400">N/A</span>
                                            @endif
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6">
                                <a href="{{ route('admin.subscriptions.show', $invoice->subscription) }}" class="btn inline-flex justify-center btn-outline-info btn-sm">
                                    View Subscription Details
                                </a>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Invoice Line Items -->
                @if($invoice->line_items && count($invoice->line_items) > 0)
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Line Items</h4>
                        </div>
                        <div class="card-body px-6 pb-6">
                            <div class="overflow-x-auto -mx-6">
                                <div class="inline-block min-w-full align-middle">
                                    <div class="overflow-hidden">
                                        <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
                                            <thead class="bg-slate-200 dark:bg-slate-700">
                                                <tr>
                                                    <th scope="col" class="table-th">Description</th>
                                                    <th scope="col" class="table-th">Quantity</th>
                                                    <th scope="col" class="table-th">Unit Price</th>
                                                    <th scope="col" class="table-th">Amount</th>
                                                </tr>
                                            </thead>
                                            <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                                                @foreach($invoice->line_items as $item)
                                                    <tr>
                                                        <td class="table-td">{{ $item['description'] ?? 'N/A' }}</td>
                                                        <td class="table-td">{{ $item['quantity'] ?? 1 }}</td>
                                                        <td class="table-td">${{ number_format(($item['unit_amount'] ?? 0) / 100, 2) }}</td>
                                                        <td class="table-td">${{ number_format(($item['amount'] ?? 0) / 100, 2) }}</td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                            <tfoot class="bg-slate-50 dark:bg-slate-700">
                                                <tr>
                                                    <td colspan="3" class="table-td font-medium text-right">Total:</td>
                                                    <td class="table-td font-bold">${{ number_format($invoice->amount / 100, 2) }}</td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="xl:col-span-4 col-span-12">
                <!-- Quick Actions -->
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Quick Actions</h4>
                    </div>
                    <div class="card-body">
                        <div class="space-y-2">
                            @if($invoice->invoice_pdf_url)
                                <a href="{{ $invoice->invoice_pdf_url }}" target="_blank" class="w-full flex items-center px-4 py-3 text-left hover:bg-slate-100 dark:hover:bg-slate-700 rounded-md transition-colors">
                                    <iconify-icon icon="heroicons:arrow-down-tray" class="text-info-500 text-lg ltr:mr-3 rtl:ml-3"></iconify-icon>
                                    <span class="text-slate-700 dark:text-slate-300">Download PDF</span>
                                </a>
                            @endif
                            <a href="{{ route('admin.users.show', $invoice->user) }}" class="w-full flex items-center px-4 py-3 text-left hover:bg-slate-100 dark:hover:bg-slate-700 rounded-md transition-colors">
                                <iconify-icon icon="heroicons:user" class="text-primary-500 text-lg ltr:mr-3 rtl:ml-3"></iconify-icon>
                                <span class="text-slate-700 dark:text-slate-300">View Customer Profile</span>
                            </a>
                            @if($invoice->subscription)
                                <a href="{{ route('admin.subscriptions.show', $invoice->subscription) }}" class="w-full flex items-center px-4 py-3 text-left hover:bg-slate-100 dark:hover:bg-slate-700 rounded-md transition-colors">
                                    <iconify-icon icon="heroicons:credit-card" class="text-info-500 text-lg ltr:mr-3 rtl:ml-3"></iconify-icon>
                                    <span class="text-slate-700 dark:text-slate-300">View Subscription</span>
                                </a>
                            @endif
                            <a href="{{ route('admin.financial.invoices') }}" class="w-full flex items-center px-4 py-3 text-left hover:bg-slate-100 dark:hover:bg-slate-700 rounded-md transition-colors">
                                <iconify-icon icon="heroicons:arrow-left" class="text-slate-500 text-lg ltr:mr-3 rtl:ml-3"></iconify-icon>
                                <span class="text-slate-700 dark:text-slate-300">Back to Invoices</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Payment Information -->
                @if($invoice->payment)
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Payment Information</h4>
                        </div>
                        <div class="card-body">
                            <div class="flex items-center space-x-4">
                                <div class="flex-none">
                                    <div class="w-10 h-10 rounded-full bg-success-500 text-white flex items-center justify-center">
                                        <iconify-icon icon="heroicons:check"></iconify-icon>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium text-slate-900 dark:text-slate-100">Payment #{{ $invoice->payment->id }}</div>
                                    <div class="text-sm text-slate-500 dark:text-slate-400">
                                        {{ $invoice->payment->created_at->format('M d, Y H:i') }}
                                    </div>
                                    <div class="text-sm text-slate-600 dark:text-slate-300">
                                        ${{ number_format($invoice->payment->amount / 100, 2) }}
                                    </div>
                                </div>
                                <div class="flex-none">
                                    <a href="{{ route('admin.financial.payments.show', $invoice->payment) }}" class="btn inline-flex justify-center btn-outline-primary btn-sm">
                                        View
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Invoice Summary -->
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Summary</h4>
                    </div>
                    <div class="card-body">
                        <div class="grid grid-cols-2 gap-4 text-center">
                            <div class="border-r border-slate-100 dark:border-slate-700 pr-4">
                                <div class="text-xl font-bold text-slate-900 dark:text-slate-100">${{ number_format($invoice->total, 2) }}</div>
                                <div class="text-sm text-slate-500 dark:text-slate-400">Total Amount</div>
                            </div>
                            <div class="pl-4">
                                <div class="text-xl font-bold text-{{ $invoice->status === 'paid' ? 'success' : 'warning' }}-500">
                                    {{ ucfirst($invoice->status) }}
                                </div>
                                <div class="text-sm text-slate-500 dark:text-slate-400">Status</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
