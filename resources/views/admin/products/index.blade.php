<x-app-layout>
    <div class="space-y-8">
        <div>
            <x-breadcrumb :page-title="'Subscription Products'" :breadcrumb-items="[
                ['name' => 'Admin', 'url' => route('admin.dashboard'), 'active' => false],
                ['name' => 'Subscription Products', 'url' => route('admin.products.index'), 'active' => true]
            ]" />
        </div>

        <!-- Filters -->
        <div class="card">
            <header class="card-header">
                <h4 class="card-title">Search & Filter</h4>
            </header>
            <div class="card-body">
                <form method="GET" action="{{ route('admin.products.index') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="input-area">
                        <label class="form-label">Search Products</label>
                        <div class="relative">
                            <input type="text" name="q" value="{{ $filters['q'] ?? '' }}"
                                   class="form-control !pl-9" placeholder="Search by name or description...">
                            <iconify-icon icon="heroicons-outline:search" class="absolute left-2 top-1/2 -translate-y-1/2 text-base text-slate-500"></iconify-icon>
                        </div>
                    </div>
                    <div class="input-area">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-control">
                            <option value="">All Statuses</option>
                            <option value="active" {{ ($filters['status'] ?? '') === 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ ($filters['status'] ?? '') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>
                    </div>
                    <div class="input-area">
                        <label class="form-label">Per Page</label>
                        <select name="per_page" class="form-control">
                            <option value="10" {{ ($filters['per_page'] ?? 10) == 10 ? 'selected' : '' }}>10</option>
                            <option value="25" {{ ($filters['per_page'] ?? 10) == 25 ? 'selected' : '' }}>25</option>
                            <option value="50" {{ ($filters['per_page'] ?? 10) == 50 ? 'selected' : '' }}>50</option>
                            <option value="100" {{ ($filters['per_page'] ?? 10) == 100 ? 'selected' : '' }}>100</option>
                        </select>
                    </div>
                    <div class="flex items-end space-x-2">
                        <button type="submit" class="btn btn-primary">
                            <iconify-icon icon="heroicons-outline:search" class="mr-2"></iconify-icon>
                            Search
                        </button>
                        <a href="{{ route('admin.products.index') }}" class="btn btn-outline-secondary">
                            <iconify-icon icon="heroicons-outline:x-mark" class="mr-2"></iconify-icon>
                            Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-5">
            <div class="card">
                <div class="card-body p-6 text-center">
                    <div class="mx-auto h-10 w-10 bg-primary-500 bg-opacity-20 flex flex-col items-center justify-center rounded-full text-primary-500 mb-4">
                        <iconify-icon icon="heroicons-outline:collection" class="text-lg"></iconify-icon>
                    </div>
                    <h3 class="text-xl font-semibold text-slate-900 dark:text-white mb-1">{{ $products->total() }}</h3>
                    <p class="text-sm text-slate-600 dark:text-slate-300">Total Products</p>
                </div>
            </div>
            <div class="card">
                <div class="card-body p-6 text-center">
                    <div class="mx-auto h-10 w-10 bg-success-500 bg-opacity-20 flex flex-col items-center justify-center rounded-full text-success-500 mb-4">
                        <iconify-icon icon="heroicons-outline:check-circle" class="text-lg"></iconify-icon>
                    </div>
                    <h3 class="text-xl font-semibold text-slate-900 dark:text-white mb-1">{{ $products->where('is_active', true)->count() }}</h3>
                    <p class="text-sm text-slate-600 dark:text-slate-300">Active</p>
                </div>
            </div>
            <div class="card">
                <div class="card-body p-6 text-center">
                    <div class="mx-auto h-10 w-10 bg-warning-500 bg-opacity-20 flex flex-col items-center justify-center rounded-full text-warning-500 mb-4">
                        <iconify-icon icon="heroicons-outline:pause" class="text-lg"></iconify-icon>
                    </div>
                    <h3 class="text-xl font-semibold text-slate-900 dark:text-white mb-1">{{ $products->where('is_active', false)->count() }}</h3>
                    <p class="text-sm text-slate-600 dark:text-slate-300">Inactive</p>
                </div>
            </div>
            <div class="card">
                <div class="card-body p-6 text-center">
                    <div class="mx-auto h-10 w-10 bg-info-500 bg-opacity-20 flex flex-col items-center justify-center rounded-full text-info-500 mb-4">
                        <iconify-icon icon="heroicons-outline:users" class="text-lg"></iconify-icon>
                    </div>
                    <h3 class="text-xl font-semibold text-slate-900 dark:text-white mb-1">{{ $products->sum('subscriptions_count') }}</h3>
                    <p class="text-sm text-slate-600 dark:text-slate-300">Total Subscriptions</p>
                </div>
            </div>
        </div>

        <!-- Products Table -->
        <div class="card">
            <header class="card-header">
                <h4 class="card-title">Subscription Products</h4>
                <div>
                    <a href="{{ route('admin.products.create') }}" class="btn inline-flex justify-center btn-primary">
                        <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="heroicons-outline:plus"></iconify-icon>
                        Add Product
                    </a>
                </div>
            </header>
            <div class="card-body px-6 pb-6">
                            <div class="overflow-x-auto -mx-6 dashcode-data-table">
                                <span class="col-span-8 hidden"></span>
                                <span class="col-span-4 hidden"></span>
                                <div class="inline-block min-w-full align-middle">
                                    <div class="overflow-hidden">
                                        <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
                                            <thead class="bg-slate-200 dark:bg-slate-700">
                                                <tr>
                                                    <th scope="col" class="table-th">Product</th>
                                                    <th scope="col" class="table-th">Price</th>
                                                    <th scope="col" class="table-th">Billing Cycle</th>
                                                    <th scope="col" class="table-th">Subscriptions</th>
                                                    <th scope="col" class="table-th">Status</th>
                                                    <th scope="col" class="table-th">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                                                @forelse($products as $product)
                                                    <tr>
                                                        <td class="table-td">
                                                            <div>
                                                                <div class="text-sm font-medium text-slate-900 dark:text-white">
                                                                    {{ $product->name }}
                                                                </div>
                                                                @if($product->description)
                                                                    <div class="text-sm text-slate-500 dark:text-slate-300">
                                                                        {{ Str::limit($product->description, 50) }}
                                                                    </div>
                                                                @endif
                                                            </div>
                                                        </td>
                                                        <td class="table-td">
                                                            <div class="text-sm font-medium text-slate-900 dark:text-white">
                                                                ${{ number_format($product->price, 2) }}
                                                            </div>
                                                        </td>
                                                        <td class="table-td">
                                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                                {{ $product->billing_cycle === 'monthly' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' }}">
                                                                {{ ucfirst($product->billing_cycle) }}
                                                            </span>
                                                        </td>
                                                        <td class="table-td">
                                                            <div class="text-sm text-slate-900 dark:text-white">
                                                                {{ $product->subscriptions_count }}
                                                            </div>
                                                        </td>
                                                        <td class="table-td">
                                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                                {{ $product->is_active ? 'bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-200' : 'bg-danger-100 text-danger-800 dark:bg-danger-900 dark:text-danger-200' }}">
                                                                {{ $product->is_active ? 'Active' : 'Inactive' }}
                                                            </span>
                                                        </td>
                                                        <td class="table-td">
                                                            <div class="flex space-x-3 rtl:space-x-reverse">
                                                                <a href="{{ route('admin.products.edit', $product) }}"
                                                                   class="action-btn" data-tippy-content="Edit">
                                                                    <iconify-icon icon="heroicons:pencil-square"></iconify-icon>
                                                                </a>
                                                                <form method="POST" action="{{ route('admin.products.toggle-status', $product) }}" class="inline">
                                                                    @csrf
                                                                    @method('PUT')
                                                                    <button type="submit" class="action-btn"
                                                                            data-tippy-content="{{ $product->is_active ? 'Deactivate' : 'Activate' }}">
                                                                        <iconify-icon icon="{{ $product->is_active ? 'heroicons:pause' : 'heroicons:play' }}"></iconify-icon>
                                                                    </button>
                                                                </form>
                                                                @if(!$product->subscriptions_count)
                                                                    <form method="POST" action="{{ route('admin.products.destroy', $product) }}"
                                                                          class="inline" onsubmit="return confirm('Are you sure you want to delete this product?')">
                                                                        @csrf
                                                                        @method('DELETE')
                                                                        <button type="submit" class="action-btn" data-tippy-content="Delete">
                                                                            <iconify-icon icon="heroicons:trash"></iconify-icon>
                                                                        </button>
                                                                    </form>
                                                                @endif
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="6" class="table-td text-center py-8">
                                                            <div class="text-center">
                                                                <iconify-icon icon="heroicons-outline:collection" class="mx-auto h-12 w-12 text-slate-400"></iconify-icon>
                                                                <p class="mt-2 text-sm text-slate-600 dark:text-slate-300">No subscription products found</p>
                                                                <a href="{{ route('admin.products.create') }}"
                                                                   class="mt-4 btn btn-primary inline-flex items-center">
                                                                    <iconify-icon icon="heroicons-outline:plus" class="mr-2"></iconify-icon>
                                                                    Create First Product
                                                                </a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
            </div>
            @if($products->hasPages())
                <div class="card-footer">
                    <div class="flex justify-between items-center">
                        <div class="text-sm text-slate-600 dark:text-slate-300">
                            Showing {{ $products->firstItem() }} to {{ $products->lastItem() }} of {{ $products->total() }} products
                        </div>
                        {{ $products->links() }}
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
