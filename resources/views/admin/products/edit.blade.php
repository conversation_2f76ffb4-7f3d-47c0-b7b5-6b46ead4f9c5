<x-app-layout>
    <div class="content-wrapper transition-all duration-150 ltr:ml-0 xl:ltr:ml-[248px]">
        <div class="page-content">
            <div class="transition-all duration-150 container-fluid" id="page_layout">
                <div id="content_layout">
                    <!-- Breadcrumb -->
                    <div class="mb-5">
                        <ul class="m-0 p-0 list-none">
                            <li class="inline-block relative top-[3px] text-base text-primary font-Inter">
                                <a href="{{ route('admin.dashboard') }}">
                                    <iconify-icon icon="heroicons-outline:home"></iconify-icon>
                                    <iconify-icon icon="heroicons-outline:chevron-right" class="relative text-slate-500 text-sm rtl:rotate-180"></iconify-icon>
                                </a>
                            </li>
                            <li class="inline-block relative text-sm text-primary font-Inter">
                                <a href="{{ route('admin.dashboard') }}">Admin</a>
                                <iconify-icon icon="heroicons-outline:chevron-right" class="relative top-[3px] text-slate-500 rtl:rotate-180"></iconify-icon>
                            </li>
                            <li class="inline-block relative text-sm text-primary font-Inter">
                                <a href="{{ route('admin.products.index') }}">Subscription Products</a>
                                <iconify-icon icon="heroicons-outline:chevron-right" class="relative top-[3px] text-slate-500 rtl:rotate-180"></iconify-icon>
                            </li>
                            <li class="inline-block relative text-sm text-slate-500 font-Inter dark:text-white">
                                Edit {{ $product->name }}
                            </li>
                        </ul>
                    </div>

                    <!-- Page Header -->
                    <div class="flex justify-between flex-wrap items-center mb-6">
                        <h4 class="font-medium lg:text-2xl text-xl capitalize text-slate-900 inline-block ltr:pr-4 rtl:pl-4 mb-4 sm:mb-0 flex space-x-3 rtl:space-x-reverse">
                            Edit Subscription Product
                        </h4>
                        <div class="flex sm:space-x-4 space-x-2 sm:justify-end items-center rtl:space-x-reverse">
                            <a href="{{ route('admin.products.index') }}" class="btn inline-flex justify-center btn-secondary">
                                <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="heroicons-outline:arrow-left"></iconify-icon>
                                Back to Products
                            </a>
                        </div>
                    </div>

                    <!-- Edit Form -->
                    <div class="card">
                        <div class="card-body">
                            <form method="POST" action="{{ route('admin.products.update', $product) }}" id="productForm">
                                @csrf
                                @method('PUT')
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    <!-- Basic Information -->
                                    <div class="space-y-4">
                                        <h5 class="text-lg font-medium text-slate-900 dark:text-white mb-4">Basic Information</h5>

                                        <div>
                                            <label for="name" class="form-label">Product Name <span class="text-danger-500">*</span></label>
                                            <input type="text" id="name" name="name" value="{{ old('name', $product->name) }}"
                                                   class="form-control @error('name') !border-danger-500 @enderror"
                                                   placeholder="Enter product name" required>
                                            @error('name')
                                                <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div>
                                            <label for="description" class="form-label">Description</label>
                                            <textarea id="description" name="description" rows="4"
                                                      class="form-control @error('description') !border-danger-500 @enderror"
                                                      placeholder="Enter product description">{{ old('description', $product->description) }}</textarea>
                                            @error('description')
                                                <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Price and Billing Cycle (Read-only for existing products) -->
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <label class="form-label">Price (USD)</label>
                                                <div class="relative">
                                                    <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-500">$</span>
                                                    <input type="text" value="${{ number_format($product->price / 100, 2) }}"
                                                           class="form-control pl-8 bg-slate-100 dark:bg-slate-700" readonly>
                                                </div>
                                                <p class="text-xs text-slate-500 mt-1">Price cannot be changed for existing products</p>
                                            </div>

                                            <div>
                                                <label class="form-label">Billing Cycle</label>
                                                <input type="text" value="{{ ucfirst($product->billing_cycle) }}"
                                                       class="form-control bg-slate-100 dark:bg-slate-700" readonly>
                                                <p class="text-xs text-slate-500 mt-1">Billing cycle cannot be changed for existing products</p>
                                            </div>
                                        </div>

                                        <div>
                                            <div class="checkbox-area">
                                                <label class="inline-flex items-center cursor-pointer">
                                                    <input type="checkbox" id="is_active" name="is_active" value="1"
                                                           class="hidden" {{ old('is_active', $product->is_active) ? 'checked' : '' }}>
                                                    <span class="h-4 w-4 border flex-none border-slate-100 dark:border-slate-800 rounded inline-flex ltr:mr-3 rtl:ml-3 relative transition-all duration-150 bg-slate-100 dark:bg-slate-900">
                                                        <img src="{{ asset('assets/images/icon/ck-white.svg') }}" alt="" class="h-[10px] w-[10px] block m-auto opacity-0">
                                                    </span>
                                                    <span class="text-slate-500 dark:text-slate-400 text-sm leading-6">Active (available for subscription)</span>
                                                </label>
                                            </div>
                                            @error('is_active')
                                                <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Stripe Information -->
                                        @if($product->stripe_product_id)
                                            <div class="bg-slate-50 dark:bg-slate-800 rounded-lg p-4">
                                                <h6 class="text-sm font-medium text-slate-900 dark:text-white mb-2">Stripe Integration</h6>
                                                <div class="space-y-1 text-xs text-slate-600 dark:text-slate-300">
                                                    <div><strong>Product ID:</strong> {{ $product->stripe_product_id }}</div>
                                                    <div><strong>Price ID:</strong> {{ $product->stripe_price_id }}</div>
                                                </div>
                                            </div>
                                        @endif
                                    </div>

                                    <!-- Features -->
                                    <div class="space-y-4">
                                        <h5 class="text-lg font-medium text-slate-900 dark:text-white mb-4">Product Features</h5>

                                        <div id="features-container">
                                            <label class="form-label">Features</label>
                                            <div class="space-y-2" id="features-list">
                                                @if(old('features', $product->features))
                                                    @foreach(old('features', $product->features) as $index => $feature)
                                                        <div class="flex items-center space-x-2 feature-item">
                                                            <input type="text" name="features[]" value="{{ $feature }}"
                                                                   class="form-control" placeholder="Enter feature">
                                                            <button type="button" class="btn btn-danger btn-sm remove-feature">
                                                                <iconify-icon icon="heroicons:trash"></iconify-icon>
                                                            </button>
                                                        </div>
                                                    @endforeach
                                                @else
                                                    <div class="flex items-center space-x-2 feature-item">
                                                        <input type="text" name="features[]" class="form-control" placeholder="Enter feature">
                                                        <button type="button" class="btn btn-danger btn-sm remove-feature">
                                                            <iconify-icon icon="heroicons:trash"></iconify-icon>
                                                        </button>
                                                    </div>
                                                @endif
                                            </div>
                                            <button type="button" id="add-feature" class="btn btn-secondary btn-sm mt-2">
                                                <iconify-icon icon="heroicons:plus" class="mr-1"></iconify-icon>
                                                Add Feature
                                            </button>
                                            @error('features')
                                                <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                                            @enderror
                                            @error('features.*')
                                                <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Preview Card -->
                                        <div class="mt-6">
                                            <h6 class="text-md font-medium text-slate-900 dark:text-white mb-3">Preview</h6>
                                            <div class="border border-slate-200 dark:border-slate-700 rounded-lg p-4 bg-slate-50 dark:bg-slate-800">
                                                <div class="text-center">
                                                    <h3 class="text-lg font-semibold text-slate-900 dark:text-white" id="preview-name">{{ $product->name }}</h3>
                                                    <p class="text-slate-600 dark:text-slate-300 text-sm mt-1" id="preview-description">{{ $product->description ?: 'Product description will appear here' }}</p>
                                                    <div class="mt-4">
                                                        <span class="text-3xl font-bold text-primary-500" id="preview-price">${{ number_format($product->price / 100, 2) }}</span>
                                                        <span class="text-slate-500" id="preview-cycle">/{{ $product->billing_cycle === 'yearly' ? 'year' : 'month' }}</span>
                                                    </div>
                                                    <div class="mt-4" id="preview-features">
                                                        @if($product->features && count($product->features) > 0)
                                                            <ul class="text-sm text-slate-600 dark:text-slate-300 space-y-1">
                                                                @foreach($product->features as $feature)
                                                                    <li>✓ {{ $feature }}</li>
                                                                @endforeach
                                                            </ul>
                                                        @else
                                                            <ul class="text-sm text-slate-600 dark:text-slate-300 space-y-1">
                                                                <li>Features will appear here</li>
                                                            </ul>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Form Actions -->
                                <div class="flex justify-end space-x-3 mt-8 pt-6 border-t border-slate-200 dark:border-slate-700">
                                    <a href="{{ route('admin.products.index') }}" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary">
                                        <iconify-icon icon="heroicons:check" class="mr-2"></iconify-icon>
                                        Update Product
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Feature management
            const featuresContainer = document.getElementById('features-list');
            const addFeatureBtn = document.getElementById('add-feature');

            // Add feature
            addFeatureBtn.addEventListener('click', function() {
                const featureItem = document.createElement('div');
                featureItem.className = 'flex items-center space-x-2 feature-item';
                featureItem.innerHTML = `
                    <input type="text" name="features[]" class="form-control" placeholder="Enter feature">
                    <button type="button" class="btn btn-danger btn-sm remove-feature">
                        <iconify-icon icon="heroicons:trash"></iconify-icon>
                    </button>
                `;
                featuresContainer.appendChild(featureItem);
                updatePreview();
            });

            // Remove feature
            featuresContainer.addEventListener('click', function(e) {
                if (e.target.closest('.remove-feature')) {
                    const featureItem = e.target.closest('.feature-item');
                    if (featuresContainer.children.length > 1) {
                        featureItem.remove();
                        updatePreview();
                    }
                }
            });

            // Update preview on input changes
            document.getElementById('name').addEventListener('input', updatePreview);
            document.getElementById('description').addEventListener('input', updatePreview);
            featuresContainer.addEventListener('input', updatePreview);

            function updatePreview() {
                const name = document.getElementById('name').value || '{{ $product->name }}';
                const description = document.getElementById('description').value || '{{ $product->description ?: "Product description will appear here" }}';
                const features = Array.from(document.querySelectorAll('input[name="features[]"]'))
                    .map(input => input.value.trim())
                    .filter(value => value !== '');

                document.getElementById('preview-name').textContent = name;
                document.getElementById('preview-description').textContent = description;

                const featuresContainer = document.getElementById('preview-features');
                if (features.length > 0) {
                    featuresContainer.innerHTML = '<ul class="text-sm text-slate-600 dark:text-slate-300 space-y-1">' +
                        features.map(feature => `<li>✓ ${feature}</li>`).join('') +
                        '</ul>';
                } else {
                    featuresContainer.innerHTML = '<ul class="text-sm text-slate-600 dark:text-slate-300 space-y-1"><li>Features will appear here</li></ul>';
                }
            }

            // Initial preview update
            updatePreview();
        });
    </script>
    @endpush
</x-app-layout>
