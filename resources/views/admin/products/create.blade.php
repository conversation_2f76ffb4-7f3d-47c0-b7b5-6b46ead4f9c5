<x-app-layout>
    <div class="space-y-8">
        <div>
            <x-breadcrumb :page-title="'Create Subscription Product'" :breadcrumb-items="[
                ['name' => 'Admin', 'url' => route('admin.dashboard'), 'active' => false],
                ['name' => 'Subscription Products', 'url' => route('admin.products.index'), 'active' => false],
                ['name' => 'Create Product', 'url' => route('admin.products.create'), 'active' => true]
            ]" />
        </div>

        <!-- Create Form -->
        <div class="card">
            <header class="card-header">
                <h4 class="card-title">Create Subscription Product</h4>
                <div>
                    <a href="{{ route('admin.products.index') }}" class="btn inline-flex justify-center btn-outline-secondary">
                        <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="heroicons-outline:arrow-left"></iconify-icon>
                        Back to Products
                    </a>
                </div>
            </header>
            <div class="card-body p-6">
                <form method="POST" action="{{ route('admin.products.store') }}" id="productForm">
                    @csrf
                    <div class="grid grid-cols-1 xl:grid-cols-2 gap-6">
                        <!-- Basic Information -->
                        <div class="card">
                            <div class="card-body p-6">
                                <header class="flex mb-5 items-center border-b border-slate-100 dark:border-slate-700 pb-5 -mx-6 px-6">
                                    <div class="flex-1">
                                        <div class="card-title text-slate-900 dark:text-white">Basic Information</div>
                                    </div>
                                </header>
                                <div class="space-y-4">
                                    <div class="input-area">
                                        <label for="name" class="form-label">Product Name <span class="text-danger-500">*</span></label>
                                        <div class="relative">
                                            <input type="text" id="name" name="name" value="{{ old('name') }}"
                                                   class="form-control !pl-9 @error('name') !border-danger-500 @enderror"
                                                   placeholder="Enter product name" required>
                                            <iconify-icon icon="heroicons-outline:tag" class="absolute left-2 top-1/2 -translate-y-1/2 text-base text-slate-500"></iconify-icon>
                                        </div>
                                        @error('name')
                                            <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="input-area">
                                        <label for="description" class="form-label">Description</label>
                                        <textarea id="description" name="description" rows="4"
                                                  class="form-control @error('description') !border-danger-500 @enderror"
                                                  placeholder="Enter product description">{{ old('description') }}</textarea>
                                        @error('description')
                                            <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div class="input-area">
                                            <label for="price" class="form-label">Price (USD) <span class="text-danger-500">*</span></label>
                                            <div class="relative">
                                                <input type="number" id="price" name="price" value="{{ old('price') }}"
                                                       class="form-control !pl-9 @error('price') !border-danger-500 @enderror"
                                                       placeholder="0.00" step="0.01" min="0.01" required>
                                                <iconify-icon icon="heroicons-outline:currency-dollar" class="absolute left-2 top-1/2 -translate-y-1/2 text-base text-slate-500"></iconify-icon>
                                            </div>
                                            @error('price')
                                                <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="input-area">
                                            <label for="target_role" class="form-label">Role <span class="text-danger-500">*</span></label>
                                            <div class="relative">
                                                <select id="target_role" name="target_role"
                                                        class="form-control @error('target_role') !border-danger-500 @enderror" required>
                                                    <option value="">Select Role</option>
                                                    <option value="all" {{ old('all') === 'all' ? 'selected' : '' }}>All</option>
                                                    <option value="startup" {{ old('startup') === 'startup' ? 'selected' : '' }}>Startup</option>
                                                    <option value="investor" {{ old('investor') === 'investor' ? 'selected' : '' }}>Investor</option>
                                                </select>
                                                <iconify-icon icon="heroicons-outline:clock" class="absolute right-3 top-1/2 -translate-y-1/2 text-base text-slate-500"></iconify-icon>
                                            </div>
                                            @error('target_role')
                                                <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="input-area">
                                            <label for="billing_cycle" class="form-label">Billing Cycle <span class="text-danger-500">*</span></label>
                                            <div class="relative">
                                                <select id="billing_cycle" name="billing_cycle"
                                                        class="form-control @error('billing_cycle') !border-danger-500 @enderror" required>
                                                    <option value="">Select billing cycle</option>
                                                    <option value="monthly" {{ old('billing_cycle') === 'monthly' ? 'selected' : '' }}>Monthly</option>
                                                    <option value="yearly" {{ old('billing_cycle') === 'yearly' ? 'selected' : '' }}>Yearly</option>
                                                </select>
                                                <iconify-icon icon="heroicons-outline:clock" class="absolute right-3 top-1/2 -translate-y-1/2 text-base text-slate-500"></iconify-icon>
                                            </div>
                                            @error('billing_cycle')
                                                <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="checkbox-area">
                                        <label class="inline-flex items-center cursor-pointer">
                                            <input type="checkbox" id="is_active" name="is_active" value="1"
                                                   class="hidden" {{ old('is_active', true) ? 'checked' : '' }}>
                                            <span class="h-4 w-4 border flex-none border-slate-100 dark:border-slate-800 rounded inline-flex ltr:mr-3 rtl:ml-3 relative transition-all duration-150 bg-slate-100 dark:bg-slate-900">
                                                <img src="{{ asset('assets/images/icon/ck-white.svg') }}" alt="" class="h-[10px] w-[10px] block m-auto opacity-0">
                                            </span>
                                            <span class="text-slate-500 dark:text-slate-400 text-sm leading-6">Active (available for subscription)</span>
                                        </label>
                                        @error('is_active')
                                            <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Features & Preview -->
                        <div class="space-y-6">
                            <!-- Features Card -->
                            <div class="card">
                                <div class="card-body p-6">
                                    <header class="flex mb-5 items-center border-b border-slate-100 dark:border-slate-700 pb-5 -mx-6 px-6">
                                        <div class="flex-1">
                                            <div class="card-title text-slate-900 dark:text-white">Product Features</div>
                                        </div>
                                    </header>
                                    <div id="features-container">
                                        <label class="form-label">Features</label>
                                        <div class="space-y-3" id="features-list">
                                            @if(old('features'))
                                                @foreach(old('features') as $index => $feature)
                                                    <div class="flex items-center space-x-3 feature-item">
                                                        <div class="relative flex-1">
                                                            <input type="text" name="features[]" value="{{ $feature }}"
                                                                   class="form-control !pl-9" placeholder="Enter feature">
                                                            <iconify-icon icon="heroicons-outline:check" class="absolute left-2 top-1/2 -translate-y-1/2 text-base text-slate-500"></iconify-icon>
                                                        </div>
                                                        <button type="button" class="btn btn-outline-danger btn-sm remove-feature">
                                                            <iconify-icon icon="heroicons:trash"></iconify-icon>
                                                        </button>
                                                    </div>
                                                @endforeach
                                            @else
                                                <div class="flex items-center space-x-3 feature-item">
                                                    <div class="relative flex-1">
                                                        <input type="text" name="features[]" class="form-control !pl-9" placeholder="Enter feature">
                                                        <iconify-icon icon="heroicons-outline:check" class="absolute left-2 top-1/2 -translate-y-1/2 text-base text-slate-500"></iconify-icon>
                                                    </div>
                                                    <button type="button" class="btn btn-outline-danger btn-sm remove-feature">
                                                        <iconify-icon icon="heroicons:trash"></iconify-icon>
                                                    </button>
                                                </div>
                                            @endif
                                        </div>
                                        <button type="button" id="add-feature" class="btn btn-outline-secondary btn-sm mt-3">
                                            <iconify-icon icon="heroicons:plus" class="mr-1"></iconify-icon>
                                            Add Feature
                                        </button>
                                        @error('features')
                                            <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                                        @enderror
                                        @error('features.*')
                                            <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Preview Card -->
                            <div class="card">
                                <div class="card-body p-6">
                                    <header class="flex mb-5 items-center border-b border-slate-100 dark:border-slate-700 pb-5 -mx-6 px-6">
                                        <div class="flex-1">
                                            <div class="card-title text-slate-900 dark:text-white">Live Preview</div>
                                        </div>
                                    </header>
                                    <!-- Pricing Card Preview -->
                                    <div class="price-table bg-opacity-[0.16] dark:bg-opacity-[0.36] rounded-[6px] p-6 text-slate-900 dark:text-white relative overflow-hidden z-[1] bg-primary-500">
                                        <div class="overlay absolute right-0 top-0 w-full h-full z-[-1]">
                                            <img src="{{ asset('assets/images/all-img/big-shap4.png') }}" alt="" class="ml-auto block">
                                        </div>
                                        <header class="mb-6">
                                            <h4 class="text-xl mb-5" id="preview-name">Product Name</h4>
                                            <div class="space-x-4 relative flex items-center mb-5 rtl:space-x-reverse">
                                                <span class="text-[32px] leading-10 font-medium" id="preview-price">$0.00</span>
                                            </div>
                                            <p class="text-slate-500 dark:text-slate-300 text-sm" id="preview-cycle">per month</p>
                                        </header>
                                        <div class="price-body space-y-6">
                                            <p class="text-sm leading-5 text-slate-600 dark:text-slate-300" id="preview-description">
                                                Product description will appear here
                                            </p>
                                            <div id="preview-features">
                                                <ul class="text-sm text-slate-600 dark:text-slate-300 space-y-2">
                                                    <li class="flex items-center">
                                                        <iconify-icon icon="heroicons-outline:check" class="mr-2 text-success-500"></iconify-icon>
                                                        Features will appear here
                                                    </li>
                                                </ul>
                                            </div>
                                            <div>
                                                <button class="btn-outline-dark dark:border-slate-400 w-full btn" disabled>Subscribe Now</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                                </div>

                    </div>

                    <!-- Form Actions -->
                    <div class="flex justify-end space-x-3 mt-8 pt-6 border-t border-slate-200 dark:border-slate-700">
                        <a href="{{ route('admin.products.index') }}" class="btn btn-outline-secondary">
                            <iconify-icon icon="heroicons:x-mark" class="mr-2"></iconify-icon>
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <iconify-icon icon="heroicons:check" class="mr-2"></iconify-icon>
                            Create Product
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Feature management
            const featuresContainer = document.getElementById('features-list');
            const addFeatureBtn = document.getElementById('add-feature');

            // Add feature
            addFeatureBtn.addEventListener('click', function() {
                const featureItem = document.createElement('div');
                featureItem.className = 'flex items-center space-x-3 feature-item';
                featureItem.innerHTML = `
                    <div class="relative flex-1">
                        <input type="text" name="features[]" class="form-control !pl-9" placeholder="Enter feature">
                        <iconify-icon icon="heroicons-outline:check" class="absolute left-2 top-1/2 -translate-y-1/2 text-base text-slate-500"></iconify-icon>
                    </div>
                    <button type="button" class="btn btn-outline-danger btn-sm remove-feature">
                        <iconify-icon icon="heroicons:trash"></iconify-icon>
                    </button>
                `;
                featuresContainer.appendChild(featureItem);
                updatePreview();
            });

            // Remove feature
            featuresContainer.addEventListener('click', function(e) {
                if (e.target.closest('.remove-feature')) {
                    const featureItem = e.target.closest('.feature-item');
                    if (featuresContainer.children.length > 1) {
                        featureItem.remove();
                        updatePreview();
                    }
                }
            });

            // Update preview on input changes
            document.getElementById('name').addEventListener('input', updatePreview);
            document.getElementById('description').addEventListener('input', updatePreview);
            document.getElementById('price').addEventListener('input', updatePreview);
            document.getElementById('billing_cycle').addEventListener('change', updatePreview);
            featuresContainer.addEventListener('input', updatePreview);

            function updatePreview() {
                const name = document.getElementById('name').value || 'Product Name';
                const description = document.getElementById('description').value || 'Product description will appear here';
                const price = document.getElementById('price').value || '0.00';
                const cycle = document.getElementById('billing_cycle').value;
                const features = Array.from(document.querySelectorAll('input[name="features[]"]'))
                    .map(input => input.value.trim())
                    .filter(value => value !== '');

                document.getElementById('preview-name').textContent = name;
                document.getElementById('preview-description').textContent = description;
                document.getElementById('preview-price').textContent = '$' + parseFloat(price).toFixed(2);
                document.getElementById('preview-cycle').textContent = cycle === 'yearly' ? 'per year' : 'per month';

                const featuresContainer = document.getElementById('preview-features');
                if (features.length > 0) {
                    featuresContainer.innerHTML = '<ul class="text-sm text-slate-600 dark:text-slate-300 space-y-2">' +
                        features.map(feature => `<li class="flex items-center"><iconify-icon icon="heroicons-outline:check" class="mr-2 text-success-500"></iconify-icon>${feature}</li>`).join('') +
                        '</ul>';
                } else {
                    featuresContainer.innerHTML = '<ul class="text-sm text-slate-600 dark:text-slate-300 space-y-2"><li class="flex items-center"><iconify-icon icon="heroicons-outline:check" class="mr-2 text-success-500"></iconify-icon>Features will appear here</li></ul>';
                }
            }

            // Initial preview update
            updatePreview();
        });
    </script>
    @endpush
</x-app-layout>
