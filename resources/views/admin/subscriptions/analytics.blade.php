@extends('layouts.app')

@section('content')
<div class="content-wrapper transition-all duration-150 ltr:ml-0 xl:ltr:ml-[248px]">
    <div class="page-content">
        <div class="transition-all duration-150 container-fluid" id="page_layout">
            <div id="content_layout">
                <!-- Breadcrumb -->
                <x-breadcrumb :breadcrumbItems="$breadcrumbItems" :pageTitle="$pageTitle" />

                <!-- Analytics Overview -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <!-- Total Subscriptions -->
                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-2xl font-bold text-slate-900 dark:text-white">
                                        {{ $productStats->sum('subscriptions_count') }}
                                    </h3>
                                    <p class="text-slate-500 dark:text-slate-400">Total Subscriptions</p>
                                </div>
                                <div class="w-12 h-12 bg-primary-500 bg-opacity-10 rounded-lg flex items-center justify-center">
                                    <iconify-icon icon="heroicons-outline:credit-card" class="text-primary-500 text-2xl"></iconify-icon>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Active Subscriptions -->
                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-2xl font-bold text-slate-900 dark:text-white">
                                        {{ $productStats->sum('active_subscriptions_count') }}
                                    </h3>
                                    <p class="text-slate-500 dark:text-slate-400">Active Subscriptions</p>
                                </div>
                                <div class="w-12 h-12 bg-success-500 bg-opacity-10 rounded-lg flex items-center justify-center">
                                    <iconify-icon icon="heroicons-outline:check-circle" class="text-success-500 text-2xl"></iconify-icon>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Churn Rate -->
                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    @php
                                        $totalActive = $productStats->sum('active_subscriptions_count');
                                        $recentChurn = $churnData->take(3)->sum('cancelled_count');
                                        $churnRate = $totalActive > 0 ? round(($recentChurn / $totalActive) * 100, 1) : 0;
                                    @endphp
                                    <h3 class="text-2xl font-bold text-slate-900 dark:text-white">{{ $churnRate }}%</h3>
                                    <p class="text-slate-500 dark:text-slate-400">Churn Rate (3mo)</p>
                                </div>
                                <div class="w-12 h-12 bg-warning-500 bg-opacity-10 rounded-lg flex items-center justify-center">
                                    <iconify-icon icon="heroicons-outline:trending-down" class="text-warning-500 text-2xl"></iconify-icon>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Monthly Revenue -->
                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    @php
                                        $monthlyRevenue = 0;
                                        foreach($productStats as $product) {
                                            $monthlyRevenue += ($product->active_subscriptions_count * $product->price);
                                        }
                                    @endphp
                                    <h3 class="text-2xl font-bold text-slate-900 dark:text-white">
                                        ${{ number_format($monthlyRevenue / 100, 0) }}
                                    </h3>
                                    <p class="text-slate-500 dark:text-slate-400">Monthly Revenue</p>
                                </div>
                                <div class="w-12 h-12 bg-info-500 bg-opacity-10 rounded-lg flex items-center justify-center">
                                    <iconify-icon icon="heroicons-outline:currency-dollar" class="text-info-500 text-2xl"></iconify-icon>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Row -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <!-- Subscription Trends Chart -->
                    <div class="card">
                        <header class="card-header">
                            <h4 class="card-title">Subscription Trends (12 Months)</h4>
                        </header>
                        <div class="card-body">
                            <canvas id="subscriptionTrendsChart" height="300"></canvas>
                        </div>
                    </div>

                    <!-- Product Performance Chart -->
                    <div class="card">
                        <header class="card-header">
                            <h4 class="card-title">Product Performance</h4>
                        </header>
                        <div class="card-body">
                            <canvas id="productPerformanceChart" height="300"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Product Statistics Table -->
                <div class="card mb-6">
                    <header class="card-header">
                        <h4 class="card-title">Product Statistics</h4>
                        <a href="{{ route('admin.products.index') }}" class="btn btn-outline-primary btn-sm">
                            <iconify-icon icon="heroicons-outline:cog" class="mr-2"></iconify-icon>
                            Manage Products
                        </a>
                    </header>
                    <div class="card-body">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-slate-100 dark:divide-slate-700">
                                <thead class="bg-slate-50 dark:bg-slate-800">
                                    <tr>
                                        <th class="table-th">Product</th>
                                        <th class="table-th">Price</th>
                                        <th class="table-th">Total Subscriptions</th>
                                        <th class="table-th">Active Subscriptions</th>
                                        <th class="table-th">Monthly Revenue</th>
                                        <th class="table-th">Status</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-slate-800 divide-y divide-slate-100 dark:divide-slate-700">
                                    @foreach($productStats as $product)
                                    <tr class="hover:bg-slate-50 dark:hover:bg-slate-700">
                                        <td class="table-td">
                                            <div>
                                                <div class="font-medium text-slate-900 dark:text-white">{{ $product->name }}</div>
                                                <div class="text-sm text-slate-500 dark:text-slate-400">{{ Str::limit($product->description, 50) }}</div>
                                            </div>
                                        </td>
                                        <td class="table-td font-medium text-slate-900 dark:text-white">
                                            ${{ number_format($product->price / 100, 2) }} / {{ $product->billing_cycle }}
                                        </td>
                                        <td class="table-td">
                                            <span class="font-medium text-slate-900 dark:text-white">{{ $product->subscriptions_count }}</span>
                                        </td>
                                        <td class="table-td">
                                            <span class="font-medium text-slate-900 dark:text-white">{{ $product->active_subscriptions_count }}</span>
                                            @if($product->subscriptions_count > 0)
                                                <span class="text-sm text-slate-500 dark:text-slate-400">
                                                    ({{ round(($product->active_subscriptions_count / $product->subscriptions_count) * 100, 1) }}%)
                                                </span>
                                            @endif
                                        </td>
                                        <td class="table-td font-medium text-slate-900 dark:text-white">
                                            ${{ number_format(($product->active_subscriptions_count * $product->price) / 100, 2) }}
                                        </td>
                                        <td class="table-td">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                                {{ $product->is_active ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100' }}">
                                                {{ $product->is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Churn Analysis -->
                <div class="card">
                    <header class="card-header">
                        <h4 class="card-title">Churn Analysis (Last 12 Months)</h4>
                    </header>
                    <div class="card-body">
                        @if($churnData->count() > 0)
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-slate-100 dark:divide-slate-700">
                                    <thead class="bg-slate-50 dark:bg-slate-800">
                                        <tr>
                                            <th class="table-th">Month</th>
                                            <th class="table-th">Cancellations</th>
                                            <th class="table-th">Trend</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-slate-800 divide-y divide-slate-100 dark:divide-slate-700">
                                        @foreach($churnData as $index => $churn)
                                        <tr>
                                            <td class="table-td font-medium text-slate-900 dark:text-white">
                                                {{ \Carbon\Carbon::createFromDate($churn->year, $churn->month, 1)->format('M Y') }}
                                            </td>
                                            <td class="table-td">{{ $churn->cancelled_count }}</td>
                                            <td class="table-td">
                                                @if($index < $churnData->count() - 1)
                                                    @php
                                                        $previousChurn = $churnData[$index + 1];
                                                        $trend = $churn->cancelled_count - $previousChurn->cancelled_count;
                                                    @endphp
                                                    @if($trend > 0)
                                                        <span class="inline-flex items-center text-red-600 dark:text-red-400">
                                                            <iconify-icon icon="heroicons-outline:trending-up" class="mr-1"></iconify-icon>
                                                            +{{ $trend }}
                                                        </span>
                                                    @elseif($trend < 0)
                                                        <span class="inline-flex items-center text-green-600 dark:text-green-400">
                                                            <iconify-icon icon="heroicons-outline:trending-down" class="mr-1"></iconify-icon>
                                                            {{ $trend }}
                                                        </span>
                                                    @else
                                                        <span class="text-slate-500 dark:text-slate-400">No change</span>
                                                    @endif
                                                @else
                                                    <span class="text-slate-500 dark:text-slate-400">-</span>
                                                @endif
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="text-center py-8">
                                <iconify-icon icon="heroicons-outline:chart-bar" class="text-4xl text-slate-400 mb-2"></iconify-icon>
                                <p class="text-slate-500 dark:text-slate-400">No churn data available</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Subscription Trends Chart
    const trendsCtx = document.getElementById('subscriptionTrendsChart').getContext('2d');
    new Chart(trendsCtx, {
        type: 'line',
        data: {
            labels: @json($chartData['monthly']['labels']),
            datasets: [
                {
                    label: 'New Subscriptions',
                    data: @json($chartData['monthly']['new']),
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4
                },
                {
                    label: 'Cancelled Subscriptions',
                    data: @json($chartData['monthly']['cancelled']),
                    borderColor: 'rgb(239, 68, 68)',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Product Performance Chart
    const performanceCtx = document.getElementById('productPerformanceChart').getContext('2d');
    new Chart(performanceCtx, {
        type: 'doughnut',
        data: {
            labels: @json($productStats->pluck('name')),
            datasets: [{
                data: @json($productStats->pluck('active_subscriptions_count')),
                backgroundColor: [
                    'rgb(59, 130, 246)',
                    'rgb(16, 185, 129)',
                    'rgb(245, 158, 11)',
                    'rgb(239, 68, 68)',
                    'rgb(139, 92, 246)',
                    'rgb(236, 72, 153)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });
});
</script>
@endpush
