<x-app-layout>
    <div class="space-y-8">
        <!-- Breadcrumb -->
        <x-breadcrumb :breadcrumbItems="$breadcrumbItems" :pageTitle="$pageTitle" />

                <!-- Subscription Overview -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                    <!-- Subscription Info Card -->
                    <div class="lg:col-span-1">
                        <div class="card">
                            <div class="card-body">
                                <div class="text-center mb-6">
                                    <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <iconify-icon icon="heroicons-outline:credit-card" class="text-primary-600 text-3xl"></iconify-icon>
                                    </div>
                                    <h3 class="text-xl font-semibold text-slate-900 dark:text-white mb-2">{{ $subscription->product->name }}</h3>
                                    <p class="text-slate-500 dark:text-slate-400 mb-4">Subscription #{{ $subscription->id }}</p>
                                    
                                    <!-- Status Badge -->
                                    <div class="mb-4">
                                        <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full 
                                            @if($subscription->status === 'active') bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100
                                            @elseif($subscription->status === 'canceled') bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100
                                            @elseif($subscription->status === 'paused') bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100
                                            @elseif($subscription->status === 'past_due') bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100
                                            @else bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100 @endif">
                                            {{ ucfirst($subscription->status) }}
                                        </span>
                                    </div>

                                    <!-- Actions -->
                                    <div class="flex space-x-2 justify-center">
                                        @if($subscription->status === 'active')
                                            <button type="button" class="btn btn-warning btn-sm" onclick="showActionModal({{ $subscription->id }}, 'pause')">
                                                <iconify-icon icon="heroicons-outline:pause" class="mr-2"></iconify-icon>
                                                Pause
                                            </button>
                                            <button type="button" class="btn btn-danger btn-sm" onclick="showActionModal({{ $subscription->id }}, 'cancel')">
                                                <iconify-icon icon="heroicons-outline:x-circle" class="mr-2"></iconify-icon>
                                                Cancel
                                            </button>
                                        @elseif($subscription->status === 'paused')
                                            <button type="button" class="btn btn-success btn-sm" onclick="showActionModal({{ $subscription->id }}, 'resume')">
                                                <iconify-icon icon="heroicons-outline:play" class="mr-2"></iconify-icon>
                                                Resume
                                            </button>
                                        @endif
                                    </div>
                                </div>

                                <!-- Subscription Details -->
                                <div class="space-y-4">
                                    <div class="flex justify-between">
                                        <span class="text-slate-600 dark:text-slate-400">Price</span>
                                        <span class="font-medium text-slate-900 dark:text-white">
                                            ${{ number_format($subscription->product->price / 100, 2) }} / {{ $subscription->product->billing_cycle }}
                                        </span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-slate-600 dark:text-slate-400">Started</span>
                                        <span class="font-medium text-slate-900 dark:text-white">{{ $subscription->created_at->format('M d, Y') }}</span>
                                    </div>
                                    @if($subscription->current_period_start)
                                    <div class="flex justify-between">
                                        <span class="text-slate-600 dark:text-slate-400">Current Period</span>
                                        <span class="font-medium text-slate-900 dark:text-white">
                                            {{ $subscription->current_period_start->format('M d') }} - {{ $subscription->current_period_end->format('M d, Y') }}
                                        </span>
                                    </div>
                                    @endif
                                    @if($subscription->canceled_at)
                                    <div class="flex justify-between">
                                        <span class="text-slate-600 dark:text-slate-400">Canceled</span>
                                        <span class="font-medium text-slate-900 dark:text-white">{{ $subscription->canceled_at->format('M d, Y') }}</span>
                                    </div>
                                    @endif
                                    @if($subscription->paused_at)
                                    <div class="flex justify-between">
                                        <span class="text-slate-600 dark:text-slate-400">Paused</span>
                                        <span class="font-medium text-slate-900 dark:text-white">{{ $subscription->paused_at->format('M d, Y') }}</span>
                                    </div>
                                    @endif
                                    @if($subscription->stripe_subscription_id)
                                    <div class="flex justify-between">
                                        <span class="text-slate-600 dark:text-slate-400">Stripe ID</span>
                                        <span class="font-medium text-slate-900 dark:text-white text-xs">{{ $subscription->stripe_subscription_id }}</span>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Cancellation/Pause Reason -->
                        @if($subscription->cancellation_reason || $subscription->pause_reason)
                        <div class="card mt-6">
                            <div class="card-body">
                                <h4 class="card-title mb-4">
                                    @if($subscription->cancellation_reason)
                                        Cancellation Reason
                                    @else
                                        Pause Reason
                                    @endif
                                </h4>
                                <p class="text-slate-600 dark:text-slate-400">
                                    {{ $subscription->cancellation_reason ?? $subscription->pause_reason }}
                                </p>
                            </div>
                        </div>
                        @endif
                    </div>

                    <!-- Main Content -->
                    <div class="lg:col-span-2">
                        <!-- User Information -->
                        <div class="card mb-6">
                            <header class="card-header">
                                <h4 class="card-title">Subscriber Information</h4>
                                <a href="{{ route('admin.users.show', $subscription->user) }}" class="btn btn-outline-primary btn-sm">
                                    <iconify-icon icon="heroicons-outline:user" class="mr-2"></iconify-icon>
                                    View User Profile
                                </a>
                            </header>
                            <div class="card-body">
                                <div class="flex items-center space-x-4">
                                    <div class="w-16 h-16 bg-slate-100 dark:bg-slate-700 rounded-full flex items-center justify-center">
                                        <span class="text-xl font-bold text-slate-600 dark:text-slate-300">
                                            {{ strtoupper(substr($subscription->user->name, 0, 2)) }}
                                        </span>
                                    </div>
                                    <div>
                                        <h5 class="font-medium text-slate-900 dark:text-white">{{ $subscription->user->name }}</h5>
                                        <p class="text-slate-600 dark:text-slate-400">{{ $subscription->user->email }}</p>
                                        <div class="flex items-center space-x-4 mt-2">
                                            <span class="text-sm text-slate-500 dark:text-slate-400">
                                                Member since {{ $subscription->user->created_at->format('M Y') }}
                                            </span>
                                            @if($subscription->user->account_locked)
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100">
                                                    Account Locked
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Product Information -->
                        <div class="card mb-6">
                            <header class="card-header">
                                <h4 class="card-title">Product Details</h4>
                                <a href="{{ route('admin.products.edit', $subscription->product) }}" class="btn btn-outline-primary btn-sm">
                                    <iconify-icon icon="heroicons-outline:cog" class="mr-2"></iconify-icon>
                                    Edit Product
                                </a>
                            </header>
                            <div class="card-body">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <h5 class="font-medium text-slate-900 dark:text-white mb-2">{{ $subscription->product->name }}</h5>
                                        <p class="text-slate-600 dark:text-slate-400 mb-4">{{ $subscription->product->description }}</p>
                                        <div class="space-y-2">
                                            <div class="flex justify-between">
                                                <span class="text-slate-600 dark:text-slate-400">Price</span>
                                                <span class="font-medium text-slate-900 dark:text-white">
                                                    ${{ number_format($subscription->product->price / 100, 2) }}
                                                </span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span class="text-slate-600 dark:text-slate-400">Billing Cycle</span>
                                                <span class="font-medium text-slate-900 dark:text-white">{{ ucfirst($subscription->product->billing_cycle) }}</span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span class="text-slate-600 dark:text-slate-400">Status</span>
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                                    {{ $subscription->product->is_active ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100' }}">
                                                    {{ $subscription->product->is_active ? 'Active' : 'Inactive' }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        @if($subscription->product->features)
                                        <h6 class="font-medium text-slate-900 dark:text-white mb-2">Features</h6>
                                        <ul class="space-y-1">
                                            @foreach($subscription->product->features as $feature)
                                            <li class="flex items-center text-sm text-slate-600 dark:text-slate-400">
                                                <iconify-icon icon="heroicons-outline:check" class="text-green-500 mr-2"></iconify-icon>
                                                {{ $feature }}
                                            </li>
                                            @endforeach
                                        </ul>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment History -->
                        <div class="card">
                            <header class="card-header">
                                <h4 class="card-title">Recent Payments</h4>
                                <a href="{{ route('admin.subscription-users.payment-history', $subscription->user) }}" class="btn btn-outline-primary btn-sm">
                                    <iconify-icon icon="heroicons-outline:currency-dollar" class="mr-2"></iconify-icon>
                                    View All Payments
                                </a>
                            </header>
                            <div class="card-body">
                                @if($subscription->payments->count() > 0)
                                    <div class="overflow-x-auto">
                                        <table class="min-w-full divide-y divide-slate-100 dark:divide-slate-700">
                                            <thead class="bg-slate-50 dark:bg-slate-800">
                                                <tr>
                                                    <th class="table-th">Amount</th>
                                                    <th class="table-th">Status</th>
                                                    <th class="table-th">Date</th>
                                                    <th class="table-th">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody class="bg-white dark:bg-slate-800 divide-y divide-slate-100 dark:divide-slate-700">
                                                @foreach($subscription->payments as $payment)
                                                <tr>
                                                    <td class="table-td font-medium text-slate-900 dark:text-white">
                                                        ${{ number_format($payment->amount / 100, 2) }}
                                                    </td>
                                                    <td class="table-td">
                                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                                            @if($payment->status === 'succeeded') bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100
                                                            @elseif($payment->status === 'failed') bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100
                                                            @else bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100 @endif">
                                                            {{ ucfirst($payment->status) }}
                                                        </span>
                                                    </td>
                                                    <td class="table-td">{{ $payment->created_at->format('M d, Y') }}</td>
                                                    <td class="table-td">
                                                        <a href="{{ route('admin.financial.payments.show', $payment) }}" 
                                                           class="action-btn text-slate-600 dark:text-slate-300 hover:text-primary-600" 
                                                           title="View Payment">
                                                            <iconify-icon icon="heroicons-outline:eye"></iconify-icon>
                                                        </a>
                                                    </td>
                                                </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                @else
                                    <div class="text-center py-8">
                                        <iconify-icon icon="heroicons-outline:currency-dollar" class="text-4xl text-slate-400 mb-2"></iconify-icon>
                                        <p class="text-slate-500 dark:text-slate-400">No payment history</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Action Modal (same as in index page) -->
<div id="actionModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">Action</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="actionForm" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div id="reasonField" class="mb-3">
                        <label for="reason" class="form-label">Reason</label>
                        <textarea id="reason" name="reason" class="form-control" rows="3" 
                                  placeholder="Please provide a reason for this action..."></textarea>
                    </div>
                    <div id="immediateField" class="mb-3" style="display: none;">
                        <div class="form-check">
                            <input type="checkbox" id="immediate" name="immediate" value="1" class="form-check-input">
                            <label for="immediate" class="form-check-label">
                                Cancel immediately (otherwise cancels at period end)
                            </label>
                        </div>
                    </div>
                    <p id="confirmText">Are you sure you want to perform this action?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn" id="confirmBtn">Confirm</button>
                </div>
            </form>
        </div>
    </div>
</div>
    </div>
</x-app-layout>

@push('scripts')
<script>
function showActionModal(subscriptionId, action) {
    const modal = document.getElementById('actionModal');
    const form = document.getElementById('actionForm');
    const title = document.getElementById('modalTitle');
    const confirmBtn = document.getElementById('confirmBtn');
    const confirmText = document.getElementById('confirmText');
    const reasonField = document.getElementById('reasonField');
    const immediateField = document.getElementById('immediateField');
    const reasonInput = document.getElementById('reason');

    // Reset form
    form.reset();
    reasonField.style.display = 'block';
    immediateField.style.display = 'none';

    if (action === 'cancel') {
        form.action = `/admin/subscriptions/${subscriptionId}/cancel`;
        title.textContent = 'Cancel Subscription';
        confirmBtn.textContent = 'Cancel Subscription';
        confirmBtn.className = 'btn btn-danger';
        confirmText.textContent = 'Are you sure you want to cancel this subscription?';
        immediateField.style.display = 'block';
        reasonInput.required = true;
        reasonInput.placeholder = 'Please provide a reason for cancelling this subscription...';
    } else if (action === 'pause') {
        form.action = `/admin/subscriptions/${subscriptionId}/pause`;
        title.textContent = 'Pause Subscription';
        confirmBtn.textContent = 'Pause Subscription';
        confirmBtn.className = 'btn btn-warning';
        confirmText.textContent = 'Are you sure you want to pause this subscription?';
        reasonInput.required = true;
        reasonInput.placeholder = 'Please provide a reason for pausing this subscription...';
    } else if (action === 'resume') {
        form.action = `/admin/subscriptions/${subscriptionId}/resume`;
        title.textContent = 'Resume Subscription';
        confirmBtn.textContent = 'Resume Subscription';
        confirmBtn.className = 'btn btn-success';
        confirmText.textContent = 'Are you sure you want to resume this subscription?';
        reasonField.style.display = 'none';
        reasonInput.required = false;
    }

    // Show modal (assuming Bootstrap modal)
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();
}
</script>
@endpush
