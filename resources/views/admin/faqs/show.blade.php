<x-app-layout>
    <div class="space-y-8">
        <div>
            <x-breadcrumb :page-title="$pageTitle" :breadcrumb-items="$breadcrumbItems" />
        </div>

        <!-- Header -->
        <div class="flex flex-wrap justify-between items-center gap-4">
            <div>
                <h4 class="font-medium lg:text-2xl text-xl capitalize text-slate-900 dark:text-slate-100">
                    FAQ Details
                </h4>
                <div class="flex items-center gap-4 text-sm text-slate-600 dark:text-slate-400 mt-2">
                    <span>Created by {{ $faq->creator->name }}</span>
                    <span>•</span>
                    <span>{{ $faq->created_at->format('M d, Y') }}</span>
                    <span>•</span>
                    <span>Order: {{ $faq->sort_order }}</span>
                </div>
            </div>
            <div class="flex gap-3">
                <a href="{{ route('admin.faqs.edit', $faq) }}" 
                   class="btn inline-flex justify-center btn-dark">
                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="heroicons-outline:pencil-square"></iconify-icon>
                    Edit FAQ
                </a>
                <a href="{{ route('admin.faqs.index') }}" 
                   class="btn inline-flex justify-center btn-outline-dark">
                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="heroicons-outline:arrow-left"></iconify-icon>
                    Back to FAQs
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- FAQ Content -->
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Question</h4>
                    </div>
                    <div class="card-body">
                        <h3 class="text-lg font-medium text-slate-900 dark:text-slate-100 mb-4">
                            {{ $faq->question }}
                        </h3>
                        
                        <div class="border-t border-slate-200 dark:border-slate-700 pt-4">
                            <h5 class="font-medium text-slate-900 dark:text-slate-100 mb-3">Answer</h5>
                            <div class="prose prose-slate dark:prose-invert max-w-none">
                                {!! nl2br(e($faq->answer)) !!}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Preview as User Would See -->
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">User Preview</h4>
                        <p class="text-sm text-slate-600 dark:text-slate-400">
                            This is how users will see this FAQ on the frontend.
                        </p>
                    </div>
                    <div class="card-body">
                        <div class="bg-slate-50 dark:bg-slate-700 rounded-lg p-6">
                            <!-- FAQ Item Preview -->
                            <div class="border border-slate-200 dark:border-slate-600 rounded-lg">
                                <button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-slate-100 dark:hover:bg-slate-600 transition-colors">
                                    <span class="font-medium text-slate-900 dark:text-slate-100">
                                        {{ $faq->question }}
                                    </span>
                                    <iconify-icon icon="heroicons:chevron-down" class="text-slate-500 transform transition-transform"></iconify-icon>
                                </button>
                                <div class="px-6 pb-4 text-slate-600 dark:text-slate-400">
                                    {!! nl2br(e($faq->answer)) !!}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- FAQ Status -->
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">FAQ Status</h4>
                    </div>
                    <div class="card-body space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-slate-600 dark:text-slate-400">Status:</span>
                            @if($faq->status === 'active')
                                <span class="inline-block px-3 py-1 rounded-full bg-success-100 text-success-600 dark:bg-success-900 dark:text-success-300 text-sm">
                                    Active
                                </span>
                            @else
                                <span class="inline-block px-3 py-1 rounded-full bg-slate-100 text-slate-600 dark:bg-slate-700 dark:text-slate-300 text-sm">
                                    Inactive
                                </span>
                            @endif
                        </div>

                        <div class="flex justify-between items-center">
                            <span class="text-slate-600 dark:text-slate-400">Target Role:</span>
                            @if($faq->target_role === 'investor')
                                <span class="inline-block px-3 py-1 rounded-full bg-info-100 text-info-600 dark:bg-info-900 dark:text-info-300 text-sm">
                                    Investor
                                </span>
                            @elseif($faq->target_role === 'startup')
                                <span class="inline-block px-3 py-1 rounded-full bg-warning-100 text-warning-600 dark:bg-warning-900 dark:text-warning-300 text-sm">
                                    Startup
                                </span>
                            @else
                                <span class="inline-block px-3 py-1 rounded-full bg-primary-100 text-primary-600 dark:bg-primary-900 dark:text-primary-300 text-sm">
                                    Both Roles
                                </span>
                            @endif
                        </div>

                        <div class="flex justify-between items-center">
                            <span class="text-slate-600 dark:text-slate-400">Category:</span>
                            @if($faq->category)
                                <span class="font-medium text-slate-900 dark:text-slate-100 capitalize">
                                    {{ \App\Models\Faq::getCategories()[$faq->category] ?? $faq->category }}
                                </span>
                            @else
                                <span class="text-slate-400">No category</span>
                            @endif
                        </div>

                        <div class="flex justify-between items-center">
                            <span class="text-slate-600 dark:text-slate-400">Sort Order:</span>
                            <span class="font-medium text-slate-900 dark:text-slate-100">{{ $faq->sort_order }}</span>
                        </div>
                    </div>
                </div>

                <!-- FAQ Details -->
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">FAQ Details</h4>
                    </div>
                    <div class="card-body space-y-4">
                        <div>
                            <span class="text-slate-600 dark:text-slate-400 text-sm">Created By:</span>
                            <div class="flex items-center space-x-3 rtl:space-x-reverse mt-1">
                                <div class="flex-none">
                                    <div class="w-8 h-8 rounded-full bg-slate-200 dark:bg-slate-700 flex items-center justify-center text-xs font-medium">
                                        {{ substr($faq->creator->name, 0, 2) }}
                                    </div>
                                </div>
                                <div class="flex-1 text-start">
                                    <h4 class="text-sm font-medium text-slate-600 dark:text-slate-300">
                                        {{ $faq->creator->name }}
                                    </h4>
                                    <div class="text-xs text-slate-500 dark:text-slate-400">
                                        {{ $faq->creator->email }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <span class="text-slate-600 dark:text-slate-400 text-sm">Created:</span>
                            <div class="font-medium text-slate-900 dark:text-slate-100 mt-1">
                                {{ $faq->created_at->format('M d, Y \a\t g:i A') }}
                            </div>
                        </div>

                        <div>
                            <span class="text-slate-600 dark:text-slate-400 text-sm">Last Updated:</span>
                            <div class="font-medium text-slate-900 dark:text-slate-100 mt-1">
                                {{ $faq->updated_at->format('M d, Y \a\t g:i A') }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card">
                    <div class="card-body">
                        <div class="flex flex-col space-y-3">
                            <a href="{{ route('admin.faqs.edit', $faq) }}" 
                               class="btn inline-flex justify-center btn-dark">
                                <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="heroicons-outline:pencil-square"></iconify-icon>
                                Edit FAQ
                            </a>
                            
                            <form action="{{ route('admin.faqs.toggle-status', $faq) }}" method="POST" class="inline">
                                @csrf
                                @method('PATCH')
                                <button type="submit" class="btn inline-flex justify-center {{ $faq->status === 'active' ? 'btn-warning' : 'btn-success' }} w-full">
                                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="{{ $faq->status === 'active' ? 'heroicons-outline:eye-slash' : 'heroicons-outline:eye' }}"></iconify-icon>
                                    {{ $faq->status === 'active' ? 'Deactivate' : 'Activate' }}
                                </button>
                            </form>
                            
                            <form action="{{ route('admin.faqs.destroy', $faq) }}" method="POST" class="inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn inline-flex justify-center btn-danger w-full"
                                        onclick="return confirm('Are you sure you want to delete this FAQ? This action cannot be undone.')">
                                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="heroicons-outline:trash"></iconify-icon>
                                    Delete FAQ
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('styles')
        <!-- Prose styles for content display -->
        <style>
            .prose {
                color: inherit;
            }
            .prose p {
                margin-bottom: 1rem;
            }
            .prose ul, .prose ol {
                margin-bottom: 1rem;
                padding-left: 1.5rem;
            }
            .prose strong {
                font-weight: 600;
            }
            .prose em {
                font-style: italic;
            }
        </style>
    @endpush
</x-app-layout>
