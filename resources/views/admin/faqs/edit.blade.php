<x-app-layout>
    <div class="space-y-8">
        <div>
            <x-breadcrumb :page-title="$pageTitle" :breadcrumb-items="$breadcrumbItems" />
        </div>

        <!-- Header -->
        <div class="flex flex-wrap justify-between items-center gap-4">
            <div>
                <h4 class="font-medium lg:text-2xl text-xl capitalize text-slate-900 dark:text-slate-100">
                    Edit FAQ
                </h4>
                <p class="text-slate-600 dark:text-slate-400 text-sm">
                    Update the frequently asked question and its settings.
                </p>
            </div>
            <div class="flex gap-3">
                <a href="{{ route('admin.faqs.show', $faq) }}" 
                   class="btn inline-flex justify-center btn-outline-secondary">
                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="heroicons-outline:eye"></iconify-icon>
                    Preview
                </a>
                <a href="{{ route('admin.faqs.index') }}" 
                   class="btn inline-flex justify-center btn-outline-dark">
                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="heroicons-outline:arrow-left"></iconify-icon>
                    Back to FAQs
                </a>
            </div>
        </div>

        <!-- Edit Form -->
        <form method="POST" action="{{ route('admin.faqs.update', $faq) }}" class="space-y-6">
            @csrf
            @method('PUT')
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Main Content -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- FAQ Content -->
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">FAQ Content</h4>
                        </div>
                        <div class="card-body flex flex-col p-6">
                            <div class="space-y-4">
                                <!-- Question -->
                                <div class="input-area">
                                    <label for="question" class="form-label">Question *</label>
                                    <input type="text" id="question" name="question" class="form-control"
                                           placeholder="Enter the frequently asked question" value="{{ old('question', $faq->question) }}" required>
                                    <x-input-error :messages="$errors->get('question')" class="mt-2"/>
                                </div>

                                <!-- Answer -->
                                <div class="input-area">
                                    <label for="answer" class="form-label">Answer *</label>
                                    <textarea id="answer" name="answer" rows="8" class="form-control"
                                              placeholder="Provide a detailed answer to the question" required>{{ old('answer', $faq->answer) }}</textarea>
                                    <x-input-error :messages="$errors->get('answer')" class="mt-2"/>
                                    <div class="text-xs text-slate-500 dark:text-slate-400 mt-1">
                                        You can use HTML formatting in the answer.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- FAQ Settings -->
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">FAQ Settings</h4>
                        </div>
                        <div class="card-body flex flex-col p-6">
                            <div class="space-y-4">
                                <!-- Target Role -->
                                <div class="input-area">
                                    <label for="target_role" class="form-label">Target Role *</label>
                                    <select name="target_role" id="target_role" class="form-control" required>
                                        <option value="">Select target role</option>
                                        @foreach($targetRoles as $value => $label)
                                        <option value="{{ $value }}" {{ old('target_role', $faq->target_role) === $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                    <x-input-error :messages="$errors->get('target_role')" class="mt-2"/>
                                    <div class="text-xs text-slate-500 dark:text-slate-400 mt-1">
                                        Choose which user roles can see this FAQ.
                                    </div>
                                </div>

                                <!-- Status -->
                                <div class="input-area">
                                    <label for="status" class="form-label">Status *</label>
                                    <select name="status" id="status" class="form-control" required>
                                        <option value="active" {{ old('status', $faq->status) === 'active' ? 'selected' : '' }}>Active</option>
                                        <option value="inactive" {{ old('status', $faq->status) === 'inactive' ? 'selected' : '' }}>Inactive</option>
                                    </select>
                                    <x-input-error :messages="$errors->get('status')" class="mt-2"/>
                                </div>

                                <!-- Category -->
                                <div class="input-area">
                                    <label for="category" class="form-label">Category</label>
                                    <select name="category" id="category" class="form-control">
                                        <option value="">No category</option>
                                        @foreach($categories as $value => $label)
                                            <option value="{{ $value }}" {{ old('category', $faq->category) === $value ? 'selected' : '' }}>
                                                {{ $label }}
                                            </option>
                                        @endforeach
                                    </select>
                                    <x-input-error :messages="$errors->get('category')" class="mt-2"/>
                                    <div class="text-xs text-slate-500 dark:text-slate-400 mt-1">
                                        Optional categorization for better organization.
                                    </div>
                                </div>

                                <!-- Sort Order -->
                                <div class="input-area">
                                    <label for="sort_order" class="form-label">Sort Order</label>
                                    <input type="number" id="sort_order" name="sort_order" class="form-control"
                                           placeholder="0" value="{{ old('sort_order', $faq->sort_order) }}" min="0">
                                    <x-input-error :messages="$errors->get('sort_order')" class="mt-2"/>
                                    <div class="text-xs text-slate-500 dark:text-slate-400 mt-1">
                                        Lower numbers appear first.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Details -->
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">FAQ Details</h4>
                        </div>
                        <div class="card-body space-y-4">
                            <div>
                                <span class="text-slate-600 dark:text-slate-400 text-sm">Created By:</span>
                                <div class="font-medium text-slate-900 dark:text-slate-100 mt-1">
                                    {{ $faq->creator->name }}
                                </div>
                            </div>

                            <div>
                                <span class="text-slate-600 dark:text-slate-400 text-sm">Created:</span>
                                <div class="font-medium text-slate-900 dark:text-slate-100 mt-1">
                                    {{ $faq->created_at->format('M d, Y \a\t g:i A') }}
                                </div>
                            </div>

                            <div>
                                <span class="text-slate-600 dark:text-slate-400 text-sm">Last Updated:</span>
                                <div class="font-medium text-slate-900 dark:text-slate-100 mt-1">
                                    {{ $faq->updated_at->format('M d, Y \a\t g:i A') }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="card">
                        <div class="card-body">
                            <div class="flex flex-col space-y-3">
                                <button type="submit" class="btn inline-flex justify-center btn-dark">
                                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="heroicons-outline:check"></iconify-icon>
                                    Update FAQ
                                </button>
                                <a href="{{ route('admin.faqs.show', $faq) }}" 
                                   class="btn inline-flex justify-center btn-outline-secondary">
                                    Preview Changes
                                </a>
                                <a href="{{ route('admin.faqs.index') }}" 
                                   class="btn inline-flex justify-center btn-outline-light">
                                    Cancel
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Help -->
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Help</h4>
                        </div>
                        <div class="card-body">
                            <div class="space-y-3 text-sm text-slate-600 dark:text-slate-400">
                                <div>
                                    <strong>Target Roles:</strong>
                                    <ul class="list-disc list-inside mt-1 space-y-1">
                                        <li><strong>Investor:</strong> Only investors can see this FAQ</li>
                                        <li><strong>Startup:</strong> Only startups can see this FAQ</li>
                                        <li><strong>Both:</strong> All users can see this FAQ</li>
                                    </ul>
                                </div>
                                <div>
                                    <strong>Categories:</strong>
                                    <p class="mt-1">Use categories to group related FAQs together for better organization and navigation.</p>
                                </div>
                                <div>
                                    <strong>HTML Formatting:</strong>
                                    <p class="mt-1">You can use basic HTML tags like &lt;strong&gt;, &lt;em&gt;, &lt;ul&gt;, &lt;ol&gt;, &lt;li&gt;, &lt;a&gt; in the answer.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    @push('scripts')
        <script>
            $(document).ready(function() {
                // Auto-focus on question field
                $('#question').focus();
            });
        </script>
    @endpush
</x-app-layout>
