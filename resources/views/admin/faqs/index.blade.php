<x-app-layout>
    <div class="space-y-8">
        <div>
            <x-breadcrumb :page-title="$pageTitle" :breadcrumb-items="$breadcrumbItems" />
        </div>

        <!-- Header with Create Button -->
        <div class="flex flex-wrap justify-between items-center gap-4">
            <div>
                <h4 class="font-medium lg:text-2xl text-xl capitalize text-slate-900 dark:text-slate-100">
                    FAQ Management
                </h4>
                <p class="text-slate-600 dark:text-slate-400 text-sm">
                    <PERSON><PERSON> frequently asked questions for different user roles.
                </p>
            </div>
            <div class="flex gap-3">
                <a href="{{ route('admin.faqs.create') }}" 
                   class="btn inline-flex justify-center btn-dark dark:bg-slate-700 dark:text-slate-300 m-1">
                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="heroicons-outline:plus"></iconify-icon>
                    Create FAQ
                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="card">
            <div class="card-body">
                <form method="GET" action="{{ route('admin.faqs.index') }}" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <!-- Search -->
                    <div class="input-area">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" id="search" name="search" class="form-control" 
                               placeholder="Search by question..." value="{{ $filters['search'] ?? '' }}">
                    </div>

                    <!-- Target Role Filter -->
                    <div class="input-area">
                        <label for="target_role" class="form-label">Target Role</label>
                        <select name="target_role" id="target_role" class="form-control">
                            <option value="">All Roles</option>
                            @foreach($targetRoles as $value => $label)
                                <option value="{{ $value }}" {{ ($filters['target_role'] ?? '') === $value ? 'selected' : '' }}>
                                    {{ $label }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Status Filter -->
                    <div class="input-area">
                        <label for="status" class="form-label">Status</label>
                        <select name="status" id="status" class="form-control">
                            <option value="">All Statuses</option>
                            <option value="active" {{ ($filters['status'] ?? '') === 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ ($filters['status'] ?? '') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>
                    </div>

                    <!-- Category Filter -->
                    <div class="input-area">
                        <label for="category" class="form-label">Category</label>
                        <select name="category" id="category" class="form-control">
                            <option value="">All Categories</option>
                            @foreach($categories as $value => $label)
                                <option value="{{ $value }}" {{ ($filters['category'] ?? '') === $value ? 'selected' : '' }}>
                                    {{ $label }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Filter Buttons -->
                    <div class="input-area flex items-end gap-2">
                        <button type="submit" class="btn inline-flex justify-center btn-outline-dark">
                            <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="heroicons-outline:search"></iconify-icon>
                            Filter
                        </button>
                        <a href="{{ route('admin.faqs.index') }}" class="btn inline-flex justify-center btn-outline-light">
                            Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- FAQs Table -->
        <div class="card">
            <div class="card-body px-6 pb-6">
                <div class="overflow-x-auto -mx-6 dashcode-data-table">
                    <span class="col-span-8 hidden"></span>
                    <span class="col-span-4 hidden"></span>
                    <div class="inline-block min-w-full align-middle">
                        <div class="overflow-hidden">
                            <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
                                <thead class="bg-slate-200 dark:bg-slate-700">
                                    <tr>
                                        <th scope="col" class="table-th">Order</th>
                                        <th scope="col" class="table-th">Question</th>
                                        <th scope="col" class="table-th">Target Role</th>
                                        <th scope="col" class="table-th">Category</th>
                                        <th scope="col" class="table-th">Status</th>
                                        <th scope="col" class="table-th">Created By</th>
                                        <th scope="col" class="table-th">Created</th>
                                        <th scope="col" class="table-th">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                                    @forelse($faqs as $faq)
                                        <tr class="hover:bg-slate-200 dark:hover:bg-slate-700">
                                            <td class="table-td">
                                                <span class="text-slate-600 dark:text-slate-300 font-mono text-sm">
                                                    {{ $faq->sort_order }}
                                                </span>
                                            </td>
                                            <td class="table-td">
                                                <div>
                                                    <div class="font-medium text-slate-600 dark:text-slate-300">
                                                        {{ Str::limit($faq->question, 60) }}
                                                    </div>
                                                    <div class="text-xs text-slate-500 dark:text-slate-400 mt-1">
                                                        {{ Str::limit(strip_tags($faq->answer), 80) }}
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="table-td">
                                                @if($faq->target_role === 'investor')
                                                    <span class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-info-500 bg-info-500 text-xs">
                                                        Investor
                                                    </span>
                                                @elseif($faq->target_role === 'startup')
                                                    <span class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-warning-500 bg-warning-500 text-xs">
                                                        Startup
                                                    </span>
                                                @else
                                                    <span class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-primary-500 bg-primary-500 text-xs">
                                                        Both
                                                    </span>
                                                @endif
                                            </td>
                                            <td class="table-td">
                                                @if($faq->category)
                                                    <span class="text-slate-600 dark:text-slate-300 capitalize">
                                                        {{ $categories[$faq->category] ?? $faq->category }}
                                                    </span>
                                                @else
                                                    <span class="text-slate-400">No category</span>
                                                @endif
                                            </td>
                                            <td class="table-td">
                                                @if($faq->status === 'active')
                                                    <span class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-success-500 bg-success-500 text-xs">
                                                        Active
                                                    </span>
                                                @else
                                                    <span class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-slate-500 bg-slate-500 text-xs">
                                                        Inactive
                                                    </span>
                                                @endif
                                            </td>
                                            <td class="table-td">
                                                <div class="flex items-center space-x-3 rtl:space-x-reverse">
                                                    <div class="flex-none">
                                                        <div class="w-8 h-8 rounded-full bg-slate-200 dark:bg-slate-700 flex items-center justify-center text-xs font-medium">
                                                            {{ substr($faq->creator->name, 0, 2) }}
                                                        </div>
                                                    </div>
                                                    <div class="flex-1 text-start">
                                                        <h4 class="text-sm font-medium text-slate-600 dark:text-slate-300">
                                                            {{ $faq->creator->name }}
                                                        </h4>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="table-td">
                                                <span class="text-slate-600 dark:text-slate-300">{{ $faq->created_at->format('M d, Y') }}</span>
                                            </td>
                                            <td class="table-td">
                                                <div class="flex space-x-3 rtl:space-x-reverse">
                                                    <a href="{{ route('admin.faqs.show', $faq) }}" 
                                                       class="action-btn" data-tippy-content="View">
                                                        <iconify-icon icon="heroicons:eye"></iconify-icon>
                                                    </a>
                                                    <a href="{{ route('admin.faqs.edit', $faq) }}" 
                                                       class="action-btn" data-tippy-content="Edit">
                                                        <iconify-icon icon="heroicons:pencil-square"></iconify-icon>
                                                    </a>
                                                    <form action="{{ route('admin.faqs.toggle-status', $faq) }}" method="POST" class="inline">
                                                        @csrf
                                                        @method('PATCH')
                                                        <button type="submit" class="action-btn" 
                                                                data-tippy-content="{{ $faq->status === 'active' ? 'Deactivate' : 'Activate' }}">
                                                            <iconify-icon icon="{{ $faq->status === 'active' ? 'heroicons:eye-slash' : 'heroicons:eye' }}"></iconify-icon>
                                                        </button>
                                                    </form>
                                                    <form action="{{ route('admin.faqs.destroy', $faq) }}" method="POST" class="inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="action-btn" data-tippy-content="Delete"
                                                                onclick="return confirm('Are you sure you want to delete this FAQ?')">
                                                            <iconify-icon icon="heroicons:trash"></iconify-icon>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="8" class="table-td text-center py-8">
                                                <div class="flex flex-col items-center">
                                                    <iconify-icon icon="heroicons-outline:question-mark-circle" class="text-6xl text-slate-300 dark:text-slate-600 mb-4"></iconify-icon>
                                                    <h3 class="text-lg font-medium text-slate-600 dark:text-slate-300 mb-2">No FAQs found</h3>
                                                    <p class="text-slate-500 dark:text-slate-400 mb-4">Get started by creating your first FAQ.</p>
                                                    <a href="{{ route('admin.faqs.create') }}" 
                                                       class="btn inline-flex justify-center btn-dark">
                                                        Create FAQ
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        @if($faqs->hasPages())
            <div class="card">
                <div class="card-body">
                    {{ $faqs->appends(request()->query())->links() }}
                </div>
            </div>
        @endif
    </div>
</x-app-layout>
