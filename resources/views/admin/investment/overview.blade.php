<x-app-layout>
    <div class="space-y-8">
        <div class="block sm:flex items-center justify-between mb-6">
            <x-breadcrumb :pageTitle="$pageTitle" :breadcrumbItems="$breadcrumbItems"/>
        </div>
    </div>

    <!-- Key Metrics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-6">
        <!-- Total Investors -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                            <iconify-icon icon="heroicons-outline:user-group" class="text-primary-600 dark:text-primary-400 text-xl"></iconify-icon>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-slate-600 dark:text-slate-300">Total Investors</p>
                        <p class="text-2xl font-bold text-slate-900 dark:text-white">{{ number_format($metrics['totalInvestors']) }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Startups -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-success-100 dark:bg-success-900 rounded-lg flex items-center justify-center">
                            <iconify-icon icon="heroicons-outline:building-office" class="text-success-600 dark:text-success-400 text-xl"></iconify-icon>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-slate-600 dark:text-slate-300">Total Startups</p>
                        <p class="text-2xl font-bold text-slate-900 dark:text-white">{{ number_format($metrics['totalStartups']) }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Interest Requests -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-info-100 dark:bg-info-900 rounded-lg flex items-center justify-center">
                            <iconify-icon icon="heroicons-outline:heart" class="text-info-600 dark:text-info-400 text-xl"></iconify-icon>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-slate-600 dark:text-slate-300">Total Requests</p>
                        <p class="text-2xl font-bold text-slate-900 dark:text-white">{{ number_format($metrics['totalInterestRequests']) }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Approved Requests -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-success-100 dark:bg-success-900 rounded-lg flex items-center justify-center">
                            <iconify-icon icon="heroicons-outline:check-circle" class="text-success-600 dark:text-success-400 text-xl"></iconify-icon>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-slate-600 dark:text-slate-300">Approved</p>
                        <p class="text-2xl font-bold text-slate-900 dark:text-white">{{ number_format($metrics['approvedRequests']) }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Requests -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-warning-100 dark:bg-warning-900 rounded-lg flex items-center justify-center">
                            <iconify-icon icon="heroicons-outline:clock" class="text-warning-600 dark:text-warning-400 text-xl"></iconify-icon>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-slate-600 dark:text-slate-300">Pending</p>
                        <p class="text-2xl font-bold text-slate-900 dark:text-white">{{ number_format($metrics['pendingRequests']) }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Approval Rate -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                            <iconify-icon icon="heroicons-outline:chart-pie" class="text-primary-600 dark:text-primary-400 text-xl"></iconify-icon>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-slate-600 dark:text-slate-300">Approval Rate</p>
                        <p class="text-2xl font-bold text-slate-900 dark:text-white">{{ $metrics['approvalRate'] }}%</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Monthly Interest Requests Chart -->
        <div class="card">
            <header class="card-header">
                <h4 class="card-title">Monthly Interest Requests</h4>
            </header>
            <div class="card-body">
                <div id="monthlyRequestsChart"></div>
            </div>
        </div>

        <!-- Request Status Distribution -->
        <div class="card">
            <header class="card-header">
                <h4 class="card-title">Request Status Distribution</h4>
            </header>
            <div class="card-body">
                <div id="requestStatusChart"></div>
            </div>
        </div>
    </div>

    <!-- Recent Interest Requests -->
    <div class="card">
        <header class="card-header">
            <h4 class="card-title">Recent Interest Requests</h4>
        </header>
        <div class="card-body">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
                    <thead class="bg-slate-50 dark:bg-slate-800">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                                Requester
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                                Target
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                                Type
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                                Status
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                                Date
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-slate-800 divide-y divide-slate-200 dark:divide-slate-700">
                        @forelse($recentRequests as $request)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        @if($request->requester->getFirstMediaUrl('avatar'))
                                            <img class="h-10 w-10 rounded-full object-cover" src="{{ $request->requester->getFirstMediaUrl('avatar') }}" alt="{{ $request->requester->name }}">
                                        @else
                                            <div class="h-10 w-10 rounded-full bg-slate-200 dark:bg-slate-600 flex items-center justify-center">
                                                <span class="text-sm font-medium text-slate-600 dark:text-slate-300">
                                                    {{ strtoupper(substr($request->requester->name, 0, 2)) }}
                                                </span>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-slate-900 dark:text-white">{{ $request->requester->name }}</div>
                                        <div class="text-sm text-slate-500 dark:text-slate-300">{{ $request->requester->email }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        @if($request->target->getFirstMediaUrl('avatar'))
                                            <img class="h-10 w-10 rounded-full object-cover" src="{{ $request->target->getFirstMediaUrl('avatar') }}" alt="{{ $request->target->name }}">
                                        @else
                                            <div class="h-10 w-10 rounded-full bg-slate-200 dark:bg-slate-600 flex items-center justify-center">
                                                <span class="text-sm font-medium text-slate-600 dark:text-slate-300">
                                                    {{ strtoupper(substr($request->target->name, 0, 2)) }}
                                                </span>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-slate-900 dark:text-white">{{ $request->target->name }}</div>
                                        <div class="text-sm text-slate-500 dark:text-slate-300">{{ $request->target->email }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                    {{ ucfirst(str_replace('_', ' ', $request->type)) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @php
                                    $statusClasses = [
                                        'pending' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
                                        'approved' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
                                        'rejected' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                                    ];
                                    $statusClass = $statusClasses[$request->status] ?? 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
                                @endphp
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $statusClass }}">
                                    {{ ucfirst($request->status) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500 dark:text-slate-300">
                                {{ $request->created_at->format('M d, Y') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                @if($request->target->role === 'startup')
                                    <a href="{{ route('startup-request.show', $request) }}" class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300">
                                        View Details
                                    </a>
                                @else
                                    <a href="{{ route('investor-request.show', $request) }}" class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300">
                                        View Details
                                    </a>
                                @endif
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-sm text-slate-500 dark:text-slate-300">
                                No interest requests found.
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        // Monthly Requests Chart
        const monthlyRequestsOptions = {
            series: [{
                name: 'Interest Requests',
                data: @json($chartData['monthlyRequests']['data'])
            }],
            chart: {
                type: 'line',
                height: 350,
                toolbar: {
                    show: false
                }
            },
            xaxis: {
                categories: @json($chartData['monthlyRequests']['labels'])
            },
            colors: ['#3B82F6'],
            stroke: {
                curve: 'smooth',
                width: 3
            },
            markers: {
                size: 6
            }
        };
        const monthlyRequestsChart = new ApexCharts(document.querySelector("#monthlyRequestsChart"), monthlyRequestsOptions);
        monthlyRequestsChart.render();

        // Request Status Chart
        const requestStatusOptions = {
            series: Object.values(@json($chartData['requestStats'])),
            chart: {
                type: 'donut',
                height: 350
            },
            labels: Object.keys(@json($chartData['requestStats'])).map(status => status.charAt(0).toUpperCase() + status.slice(1)),
            colors: ['#F59E0B', '#10B981', '#EF4444'],
            legend: {
                position: 'bottom'
            }
        };
        const requestStatusChart = new ApexCharts(document.querySelector("#requestStatusChart"), requestStatusOptions);
        requestStatusChart.render();
    </script>
    @endpush
</x-app-layout>
