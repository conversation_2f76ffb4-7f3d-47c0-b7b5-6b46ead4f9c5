<x-app-layout>
    <div class="space-y-8">
        <!-- <PERSON> Header -->
        <div class="md:flex md:items-center md:justify-between">
            <div class="min-w-0 flex-1">
                <x-breadcrumb :page-title="$pageTitle" :breadcrumb-items="$breadcrumbItems" />
                <h2 class="text-2xl font-bold leading-7 text-slate-900 dark:text-white sm:truncate sm:text-3xl sm:tracking-tight">
                    Interest Request Details
                </h2>
                <p class="mt-1 text-sm text-slate-500 dark:text-slate-400">
                    Review complete information about this investment interest request
                </p>
            </div>
            <div class="mt-4 flex md:mt-0 md:ml-4">
                @if($interestRequest->status === 'pending')
                    <div class="flex space-x-3">
                        <form method="POST" action="{{ route('admin.investment.interest-requests.approve', $interestRequest) }}" class="inline">
                            @csrf
                            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Approve Request
                            </button>
                        </form>
                        <button type="button" onclick="openRejectModal()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Reject Request
                        </button>
                    </div>
                @endif
            </div>
        </div>

        <!-- Request Status Card -->
        <div class="bg-white dark:bg-slate-800 overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg leading-6 font-medium text-slate-900 dark:text-white">Request Status</h3>
                        <p class="mt-1 text-sm text-slate-500 dark:text-slate-400">Current status and timeline</p>
                    </div>
                    <div class="flex items-center">
                        <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full
                            @if($interestRequest->status === 'pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100
                            @elseif($interestRequest->status === 'approved') bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100
                            @elseif($interestRequest->status === 'rejected') bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100
                            @endif">
                            {{ ucfirst($interestRequest->status) }}
                        </span>
                    </div>
                </div>
                <div class="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-3">
                    <div class="bg-slate-50 dark:bg-slate-700 rounded-lg p-4">
                        <div class="text-sm font-medium text-slate-500 dark:text-slate-400">Submitted</div>
                        <div class="mt-1 text-lg font-semibold text-slate-900 dark:text-white">
                            {{ $interestRequest->created_at->format('M d, Y') }}
                        </div>
                        <div class="text-sm text-slate-500 dark:text-slate-400">
                            {{ $interestRequest->created_at->format('h:i A') }}
                        </div>
                    </div>
                    @if($interestRequest->approved_at)
                    <div class="bg-green-50 dark:bg-green-900 rounded-lg p-4">
                        <div class="text-sm font-medium text-green-600 dark:text-green-400">Approved</div>
                        <div class="mt-1 text-lg font-semibold text-slate-900 dark:text-white">
                            {{ $interestRequest->approved_at->format('M d, Y') }}
                        </div>
                        <div class="text-sm text-green-600 dark:text-green-400">
                            {{ $interestRequest->approved_at->format('h:i A') }}
                        </div>
                    </div>
                    @elseif($interestRequest->rejected_at)
                    <div class="bg-red-50 dark:bg-red-900 rounded-lg p-4">
                        <div class="text-sm font-medium text-red-600 dark:text-red-400">Rejected</div>
                        <div class="mt-1 text-lg font-semibold text-slate-900 dark:text-white">
                            {{ $interestRequest->rejected_at->format('M d, Y') }}
                        </div>
                        <div class="text-sm text-red-600 dark:text-red-400">
                            {{ $interestRequest->rejected_at->format('h:i A') }}
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
            <!-- Requester Information -->
            <div class="bg-white dark:bg-slate-800 overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-slate-900 dark:text-white mb-4">
                        Requester Information
                    </h3>
                    <div class="flex items-center space-x-4 mb-6">
                        <div class="flex-shrink-0">
                            @if($interestRequest->requester->getFirstMediaUrl('avatar'))
                                <img class="h-16 w-16 rounded-full object-cover" src="{{ $interestRequest->requester->getFirstMediaUrl('avatar') }}" alt="{{ $interestRequest->requester->name }}">
                            @else
                                <div class="h-16 w-16 rounded-full bg-slate-200 dark:bg-slate-600 flex items-center justify-center">
                                    <span class="text-xl font-medium text-slate-700 dark:text-slate-300">
                                        {{ strtoupper(substr($interestRequest->requester->name, 0, 2)) }}
                                    </span>
                                </div>
                            @endif
                        </div>
                        <div class="flex-1">
                            <h4 class="text-lg font-medium text-slate-900 dark:text-white">{{ $interestRequest->requester->name }}</h4>
                            <p class="text-sm text-slate-500 dark:text-slate-400">{{ $interestRequest->requester->email }}</p>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                                {{ ucfirst($interestRequest->requester->role) }}
                            </span>
                        </div>
                    </div>

                    @if($interestRequest->requester->role === 'investor' && $interestRequest->requester->investorProfile)
                        <div class="space-y-3">
                            <h5 class="font-medium text-slate-900 dark:text-white">Investor Profile</h5>
                            @if($interestRequest->requester->investorProfile->bio)
                                <div>
                                    <span class="text-sm font-medium text-slate-500 dark:text-slate-400">Bio:</span>
                                    <p class="text-sm text-slate-700 dark:text-slate-300 mt-1">{{ $interestRequest->requester->investorProfile->bio }}</p>
                                </div>
                            @endif
                            @if($interestRequest->requester->investorProfile->website)
                                <div>
                                    <span class="text-sm font-medium text-slate-500 dark:text-slate-400">Website:</span>
                                    <a href="{{ $interestRequest->requester->investorProfile->website }}" target="_blank" class="text-sm text-blue-600 dark:text-blue-400 hover:underline ml-2">
                                        {{ $interestRequest->requester->investorProfile->website }}
                                    </a>
                                </div>
                            @endif
                        </div>
                    @elseif($interestRequest->requester->role === 'startup' && $interestRequest->requester->startupProfile)
                        <div class="space-y-3">
                            <h5 class="font-medium text-slate-900 dark:text-white">Startup Profile</h5>
                            @if($interestRequest->requester->startupProfile->company_name)
                                <div>
                                    <span class="text-sm font-medium text-slate-500 dark:text-slate-400">Company:</span>
                                    <span class="text-sm text-slate-700 dark:text-slate-300 ml-2">{{ $interestRequest->requester->startupProfile->company_name }}</span>
                                </div>
                            @endif
                            @if($interestRequest->requester->startupProfile->company_description)
                                <div>
                                    <span class="text-sm font-medium text-slate-500 dark:text-slate-400">Description:</span>
                                    <p class="text-sm text-slate-700 dark:text-slate-300 mt-1">{{ $interestRequest->requester->startupProfile->company_description }}</p>
                                </div>
                            @endif
                            @if($interestRequest->requester->startupProfile->funding_stage)
                                <div>
                                    <span class="text-sm font-medium text-slate-500 dark:text-slate-400">Funding Stage:</span>
                                    <span class="text-sm text-slate-700 dark:text-slate-300 ml-2">{{ $interestRequest->requester->startupProfile->funding_stage }}</span>
                                </div>
                            @endif
                        </div>
                    @endif
                </div>
            </div>

            <!-- Target Information -->
            <div class="bg-white dark:bg-slate-800 overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-slate-900 dark:text-white mb-4">
                        Target Information
                    </h3>
                    <div class="flex items-center space-x-4 mb-6">
                        <div class="flex-shrink-0">
                            @if($interestRequest->target->getFirstMediaUrl('avatar'))
                                <img class="h-16 w-16 rounded-full object-cover" src="{{ $interestRequest->target->getFirstMediaUrl('avatar') }}" alt="{{ $interestRequest->target->name }}">
                            @else
                                <div class="h-16 w-16 rounded-full bg-slate-200 dark:bg-slate-600 flex items-center justify-center">
                                    <span class="text-xl font-medium text-slate-700 dark:text-slate-300">
                                        {{ strtoupper(substr($interestRequest->target->name, 0, 2)) }}
                                    </span>
                                </div>
                            @endif
                        </div>
                        <div class="flex-1">
                            <h4 class="text-lg font-medium text-slate-900 dark:text-white">{{ $interestRequest->target->name }}</h4>
                            <p class="text-sm text-slate-500 dark:text-slate-400">{{ $interestRequest->target->email }}</p>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100">
                                {{ ucfirst($interestRequest->target->role) }}
                            </span>
                        </div>
                    </div>

                    @if($interestRequest->target->role === 'investor' && $interestRequest->target->investorProfile)
                        <div class="space-y-3">
                            <h5 class="font-medium text-slate-900 dark:text-white">Investor Profile</h5>
                            @if($interestRequest->target->investorProfile->bio)
                                <div>
                                    <span class="text-sm font-medium text-slate-500 dark:text-slate-400">Bio:</span>
                                    <p class="text-sm text-slate-700 dark:text-slate-300 mt-1">{{ $interestRequest->target->investorProfile->bio }}</p>
                                </div>
                            @endif
                            @if($interestRequest->target->investorProfile->website)
                                <div>
                                    <span class="text-sm font-medium text-slate-500 dark:text-slate-400">Website:</span>
                                    <a href="{{ $interestRequest->target->investorProfile->website }}" target="_blank" class="text-sm text-blue-600 dark:text-blue-400 hover:underline ml-2">
                                        {{ $interestRequest->target->investorProfile->website }}
                                    </a>
                                </div>
                            @endif
                        </div>
                    @elseif($interestRequest->target->role === 'startup' && $interestRequest->target->startupProfile)
                        <div class="space-y-3">
                            <h5 class="font-medium text-slate-900 dark:text-white">Startup Profile</h5>
                            @if($interestRequest->target->startupProfile->company_name)
                                <div>
                                    <span class="text-sm font-medium text-slate-500 dark:text-slate-400">Company:</span>
                                    <span class="text-sm text-slate-700 dark:text-slate-300 ml-2">{{ $interestRequest->target->startupProfile->company_name }}</span>
                                </div>
                            @endif
                            @if($interestRequest->target->startupProfile->company_description)
                                <div>
                                    <span class="text-sm font-medium text-slate-500 dark:text-slate-400">Description:</span>
                                    <p class="text-sm text-slate-700 dark:text-slate-300 mt-1">{{ $interestRequest->target->startupProfile->company_description }}</p>
                                </div>
                            @endif
                            @if($interestRequest->target->startupProfile->funding_stage)
                                <div>
                                    <span class="text-sm font-medium text-slate-500 dark:text-slate-400">Funding Stage:</span>
                                    <span class="text-sm text-slate-700 dark:text-slate-300 ml-2">{{ $interestRequest->target->startupProfile->funding_stage }}</span>
                                </div>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Request Details -->
        <div class="bg-white dark:bg-slate-800 overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-slate-900 dark:text-white mb-4">Request Details</h3>
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                        <span class="text-sm font-medium text-slate-500 dark:text-slate-400">Request ID:</span>
                        <p class="text-sm text-slate-700 dark:text-slate-300 mt-1">#{{ $interestRequest->id }}</p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-slate-500 dark:text-slate-400">Submitted:</span>
                        <p class="text-sm text-slate-700 dark:text-slate-300 mt-1">{{ $interestRequest->created_at->format('M d, Y h:i A') }}</p>
                    </div>
                    @if($interestRequest->message)
                    <div class="sm:col-span-2">
                        <span class="text-sm font-medium text-slate-500 dark:text-slate-400">Message:</span>
                        <p class="text-sm text-slate-700 dark:text-slate-300 mt-1 p-3 bg-slate-50 dark:bg-slate-700 rounded-md">{{ $interestRequest->message }}</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Approval History -->
        @if($approvalHistory->isNotEmpty())
        <div class="bg-white dark:bg-slate-800 overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-slate-900 dark:text-white mb-4">Approval History</h3>
                <div class="space-y-4">
                    @foreach($approvalHistory as $history)
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 rounded-full flex items-center justify-center
                                @if($history['action'] === 'approved') bg-green-100 text-green-600 dark:bg-green-800 dark:text-green-400
                                @else bg-red-100 text-red-600 dark:bg-red-800 dark:text-red-400 @endif">
                                @if($history['action'] === 'approved')
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                @else
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                @endif
                            </div>
                        </div>
                        <div class="flex-1">
                            <div class="text-sm font-medium text-slate-900 dark:text-white">
                                Request {{ $history['action'] }} by {{ $history['user']->name ?? 'System' }}
                            </div>
                            <div class="text-sm text-slate-500 dark:text-slate-400">
                                {{ $history['timestamp']->format('M d, Y h:i A') }}
                            </div>
                            @if($history['reason'])
                            <div class="text-sm text-slate-600 dark:text-slate-300 mt-1 p-2 bg-slate-50 dark:bg-slate-700 rounded">
                                <strong>Reason:</strong> {{ $history['reason'] }}
                            </div>
                            @endif
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
        @endif
    </div>

    <!-- Reject Modal -->
    <div id="rejectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-slate-800">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-slate-900 dark:text-white">Reject Interest Request</h3>
                <form id="rejectForm" method="POST" action="{{ route('admin.investment.interest-requests.reject', $interestRequest) }}" class="mt-4">
                    @csrf
                    <div class="mb-4">
                        <label for="reason" class="block text-sm font-medium text-slate-700 dark:text-slate-300">Reason (Optional)</label>
                        <textarea name="reason" id="reason" rows="3"
                                  class="mt-1 block w-full rounded-md border-slate-300 dark:border-slate-600 dark:bg-slate-700 shadow-sm focus:border-red-500 focus:ring-red-500"
                                  placeholder="Enter reason for rejection..."></textarea>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeRejectModal()"
                                class="px-4 py-2 text-sm font-medium text-slate-700 dark:text-slate-300 bg-slate-100 dark:bg-slate-600 rounded-md hover:bg-slate-200 dark:hover:bg-slate-500">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700">
                            Reject
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
    function openRejectModal() {
        document.getElementById('rejectModal').classList.remove('hidden');
    }

    function closeRejectModal() {
        document.getElementById('rejectModal').classList.add('hidden');
    }

    // Close modal when clicking outside
    document.getElementById('rejectModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeRejectModal();
        }
    });
    </script>
</x-app-layout>
