<x-app-layout>

    <!-- START:: Breadcrumbs -->
    <div class="flex justify-between flex-wrap items-center mb-6">
        <h4 class="font-medium lg:text-2xl text-xl capitalize text-slate-900 ltr:pr-4 rtl:pl-4 mb-4 sm:mb-0">{{ __('Admin Dashboard') }}</h4>
        <div class="flex sm:space-x-4 space-x-2 sm:justify-end items-center rtl:space-x-reverse">
            <button class="btn leading-0 inline-flex justify-center bg-white text-slate-700 dark:bg-slate-800 dark:text-slate-300 !font-normal">
                <span class="flex items-center">
                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2 font-light" icon="heroicons-outline:calendar"></iconify-icon>
                    <span>This Month</span>
                </span>
            </button>
            <button class="btn leading-0 inline-flex justify-center bg-white text-slate-700 dark:bg-slate-800 dark:text-slate-300 !font-normal">
                <span class="flex items-center">
                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2 font-light" icon="heroicons-outline:filter"></iconify-icon>
                    <span>Filter</span>
                </span>
            </button>
        </div>
    </div>
    <!-- END:: Breadcrumbs -->

    <!-- System Status Banner -->
    <div class="grid grid-cols-12 gap-5 mb-5">
        <div class="2xl:col-span-3 lg:col-span-4 col-span-12">
            <div class="bg-gradient-to-r from-blue-500 to-purple-600 p-4 rounded-[6px] relative text-white">
                <div class="max-w-[180px]">
                    <div class="text-xl font-medium mb-2">
                        Investment Platform
                    </div>
                    <p class="text-sm text-blue-100">
                        System running smoothly
                    </p>
                </div>
                <div class="absolute top-1/2 -translate-y-1/2 ltr:right-6 rtl:left-6 mt-2 h-12 w-12 bg-white bg-opacity-20 rounded-full text-xs font-medium
                        flex flex-col items-center justify-center text-white">
                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse mb-1"></div>
                    Live
                </div>
            </div>
        </div>
        <div class="2xl:col-span-9 lg:col-span-8 col-span-12">
            <div class="p-4 card">
                <div class="grid md:grid-cols-4 col-span-1 gap-4">

                    <!-- Total Users -->
                    <div class="py-[18px] px-4 rounded-[6px] bg-[#E5F9FF] dark:bg-slate-900">
                        <div class="flex items-center space-x-6 rtl:space-x-reverse">
                            <div class="flex-none">
                                <div class="w-12 h-12 rounded-full bg-primary-500 bg-opacity-10 flex items-center justify-center">
                                    <iconify-icon class="text-2xl text-primary-500" icon="heroicons-outline:users"></iconify-icon>
                                </div>
                            </div>
                            <div class="flex-1">
                                <div class="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                                    {{ __('Total Users') }}
                                </div>
                                <div class="text-slate-900 dark:text-white text-lg font-medium">
                                    {{ number_format($metrics['totalUsers']) }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Active Subscriptions -->
                    <div class="py-[18px] px-4 rounded-[6px] bg-[#FFEDE5] dark:bg-slate-900">
                        <div class="flex items-center space-x-6 rtl:space-x-reverse">
                            <div class="flex-none">
                                <div class="w-12 h-12 rounded-full bg-success-500 bg-opacity-10 flex items-center justify-center">
                                    <iconify-icon class="text-2xl text-success-500" icon="heroicons-outline:credit-card"></iconify-icon>
                                </div>
                            </div>
                            <div class="flex-1">
                                <div class="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                                    {{ __('Active Subscriptions') }}
                                </div>
                                <div class="text-slate-900 dark:text-white text-lg font-medium">
                                    {{ number_format($metrics['activeSubscriptions']) }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- User Growth -->
                    <div class="py-[18px] px-4 rounded-[6px] bg-[#EAE5FF] dark:bg-slate-900">
                        <div class="flex items-center space-x-6 rtl:space-x-reverse">
                            <div class="flex-none">
                                <div class="w-12 h-12 rounded-full bg-warning-500 bg-opacity-10 flex items-center justify-center">
                                    <iconify-icon class="text-2xl text-warning-500" icon="heroicons-outline:user-plus"></iconify-icon>
                                </div>
                            </div>
                            <div class="flex-1">
                                <div class="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                                    {{ __('User Growth') }}
                                </div>
                                <div class="text-slate-900 dark:text-white text-lg font-medium">
                                    {{ number_format($metrics['userGrowthRate'], 1) }}%
                                </div>
                                <div class="text-xs text-slate-500 dark:text-slate-400 mt-1">
                                    @if($metrics['userGrowthRate'] > 0)
                                        <span class="text-green-500">↗ +{{ number_format($metrics['userGrowthRate'], 1) }}%</span>
                                    @elseif($metrics['userGrowthRate'] < 0)
                                        <span class="text-red-500">↘ {{ number_format($metrics['userGrowthRate'], 1) }}%</span>
                                    @else
                                        <span class="text-slate-500">→ 0%</span>
                                    @endif
                                    vs last month
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Churn Rate -->
                    <div class="py-[18px] px-4 rounded-[6px] bg-[#F0FDF4] dark:bg-slate-900">
                        <div class="flex items-center space-x-6 rtl:space-x-reverse">
                            <div class="flex-none">
                                <div class="w-12 h-12 rounded-full bg-info-500 bg-opacity-10 flex items-center justify-center">
                                    <iconify-icon class="text-2xl text-info-500" icon="heroicons-outline:chart-bar"></iconify-icon>
                                </div>
                            </div>
                            <div class="flex-1">
                                <div class="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                                    {{ __('Churn Rate') }}
                                </div>
                                <div class="text-slate-900 dark:text-white text-lg font-medium">
                                    {{ number_format($metrics['churnRate'], 1) }}%
                                </div>
                                <div class="text-xs text-slate-500 dark:text-slate-400 mt-1">
                                    Monthly cancellation rate
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <!-- Investment Platform Metrics -->
    <div class="grid grid-cols-12 gap-5 mb-5">
        <div class="col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">{{ __('Investment Platform Overview') }}</h4>
                </header>
                <div class="card-body p-6">
                    <div class="grid md:grid-cols-6 grid-cols-2 gap-4">
                        <!-- Total Investors -->
                        <div class="text-center p-4 bg-slate-50 dark:bg-slate-900 rounded-lg">
                            <div class="w-12 h-12 mx-auto rounded-full bg-success-500 bg-opacity-10 flex items-center justify-center mb-3">
                                <iconify-icon class="text-2xl text-success-500" icon="heroicons-outline:user-group"></iconify-icon>
                            </div>
                            <div class="text-2xl font-bold text-slate-900 dark:text-white">{{ number_format($metrics['totalInvestors']) }}</div>
                            <div class="text-xs text-slate-500 dark:text-slate-400">Total Investors</div>
                        </div>

                        <!-- Total Startups -->
                        <div class="text-center p-4 bg-slate-50 dark:bg-slate-900 rounded-lg">
                            <div class="w-12 h-12 mx-auto rounded-full bg-info-500 bg-opacity-10 flex items-center justify-center mb-3">
                                <iconify-icon class="text-2xl text-info-500" icon="heroicons-outline:office-building"></iconify-icon>
                            </div>
                            <div class="text-2xl font-bold text-slate-900 dark:text-white">{{ number_format($metrics['totalStartups']) }}</div>
                            <div class="text-xs text-slate-500 dark:text-slate-400">Total Startups</div>
                        </div>

                        <!-- Total Interests -->
                        <div class="text-center p-4 bg-slate-50 dark:bg-slate-900 rounded-lg">
                            <div class="w-12 h-12 mx-auto rounded-full bg-warning-500 bg-opacity-10 flex items-center justify-center mb-3">
                                <iconify-icon class="text-2xl text-warning-500" icon="heroicons-outline:heart"></iconify-icon>
                            </div>
                            <div class="text-2xl font-bold text-slate-900 dark:text-white">{{ number_format($metrics['totalInterests']) }}</div>
                            <div class="text-xs text-slate-500 dark:text-slate-400">Total Interests</div>
                        </div>

                        <!-- Active Interests -->
                        <div class="text-center p-4 bg-slate-50 dark:bg-slate-900 rounded-lg">
                            <div class="w-12 h-12 mx-auto rounded-full bg-primary-500 bg-opacity-10 flex items-center justify-center mb-3">
                                <iconify-icon class="text-2xl text-primary-500" icon="heroicons-outline:clock"></iconify-icon>
                            </div>
                            <div class="text-2xl font-bold text-slate-900 dark:text-white">{{ number_format($metrics['activeInterests']) }}</div>
                            <div class="text-xs text-slate-500 dark:text-slate-400">Active Interests</div>
                        </div>

                        <!-- ESG Completion Rate -->
                        <div class="text-center p-4 bg-slate-50 dark:bg-slate-900 rounded-lg">
                            <div class="w-12 h-12 mx-auto rounded-full bg-purple-500 bg-opacity-10 flex items-center justify-center mb-3">
                                <iconify-icon class="text-2xl text-purple-500" icon="heroicons-outline:clipboard-check"></iconify-icon>
                            </div>
                            <div class="text-2xl font-bold text-slate-900 dark:text-white">{{ $metrics['esgCompletionRate'] }}%</div>
                            <div class="text-xs text-slate-500 dark:text-slate-400">ESG Completion</div>
                        </div>

                        <!-- Document Upload Rate -->
                        <div class="text-center p-4 bg-slate-50 dark:bg-slate-900 rounded-lg">
                            <div class="w-12 h-12 mx-auto rounded-full bg-indigo-500 bg-opacity-10 flex items-center justify-center mb-3">
                                <iconify-icon class="text-2xl text-indigo-500" icon="heroicons-outline:document"></iconify-icon>
                            </div>
                            <div class="text-2xl font-bold text-slate-900 dark:text-white">{{ $metrics['documentUploadRate'] }}%</div>
                            <div class="text-xs text-slate-500 dark:text-slate-400">Document Upload</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions & Today's Activity -->
    <div class="grid grid-cols-12 gap-5 mb-5">
        <div class="lg:col-span-6 col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">{{ __('Quick Actions') }}</h4>
                    <div class="text-sm text-slate-500">Items requiring attention</div>
                </header>
                <div class="card-body p-6">
                    <div class="space-y-4">
                        @if($quickActions['pending_verifications'] > 0)
                        <div class="flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 rounded-full bg-yellow-500 bg-opacity-20 flex items-center justify-center">
                                    <iconify-icon class="text-yellow-600" icon="heroicons-outline:exclamation"></iconify-icon>
                                </div>
                                <div>
                                    <div class="font-medium text-slate-900 dark:text-white">Pending Email Verifications</div>
                                    <div class="text-sm text-slate-500">{{ $quickActions['pending_verifications'] }} users need verification</div>
                                </div>
                            </div>
                            <a href="{{ route('admin.users.index', ['filter' => 'unverified']) }}" class="btn btn-sm btn-outline-warning">
                                View
                            </a>
                        </div>
                        @endif

                        @if($quickActions['locked_accounts'] > 0)
                        <div class="flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 rounded-full bg-red-500 bg-opacity-20 flex items-center justify-center">
                                    <iconify-icon class="text-red-600" icon="heroicons-outline:lock-closed"></iconify-icon>
                                </div>
                                <div>
                                    <div class="font-medium text-slate-900 dark:text-white">Locked Accounts</div>
                                    <div class="text-sm text-slate-500">{{ $quickActions['locked_accounts'] }} accounts are locked</div>
                                </div>
                            </div>
                            <a href="{{ route('admin.users.index', ['filter' => 'locked']) }}" class="btn btn-sm btn-outline-danger">
                                Review
                            </a>
                        </div>
                        @endif

                        @if($quickActions['pending_refunds'] > 0)
                        <div class="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 rounded-full bg-blue-500 bg-opacity-20 flex items-center justify-center">
                                    <iconify-icon class="text-blue-600" icon="heroicons-outline:currency-dollar"></iconify-icon>
                                </div>
                                <div>
                                    <div class="font-medium text-slate-900 dark:text-white">Pending Refunds</div>
                                    <div class="text-sm text-slate-500">{{ $quickActions['pending_refunds'] }} refunds to process</div>
                                </div>
                            </div>
                            <a href="{{ route('admin.financial.refunds') }}" class="btn btn-sm btn-outline-info">
                                Process
                            </a>
                        </div>
                        @endif

                        @if($quickActions['failed_payments_week'] > 0)
                        <div class="flex items-center justify-between p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 rounded-full bg-orange-500 bg-opacity-20 flex items-center justify-center">
                                    <iconify-icon class="text-orange-600" icon="heroicons-outline:x-circle"></iconify-icon>
                                </div>
                                <div>
                                    <div class="font-medium text-slate-900 dark:text-white">Failed Payments (7 days)</div>
                                    <div class="text-sm text-slate-500">{{ $quickActions['failed_payments_week'] }} failed payments</div>
                                </div>
                            </div>
                            <a href="{{ route('admin.financial.payments', ['status' => 'failed']) }}" class="btn btn-sm btn-outline-warning">
                                Review
                            </a>
                        </div>
                        @endif

                        @if(array_sum($quickActions) == 0)
                        <div class="text-center py-8">
                            <div class="w-16 h-16 mx-auto rounded-full bg-green-500 bg-opacity-10 flex items-center justify-center mb-4">
                                <iconify-icon class="text-3xl text-green-500" icon="heroicons-outline:check-circle"></iconify-icon>
                            </div>
                            <div class="text-slate-600 dark:text-slate-400">All caught up! No urgent actions needed.</div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <div class="lg:col-span-6 col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">{{ __('Today\'s Activity') }}</h4>
                    <div class="text-sm text-slate-500">{{ now()->format('M d, Y') }}</div>
                </header>
                <div class="card-body p-6">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg">
                            <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ $platformActivity['new_registrations_today'] }}</div>
                            <div class="text-sm text-blue-600 dark:text-blue-400">New Registrations</div>
                        </div>
                        <div class="text-center p-4 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg">
                            <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ $platformActivity['new_subscriptions_today'] }}</div>
                            <div class="text-sm text-green-600 dark:text-green-400">New Subscriptions</div>
                        </div>
                        <div class="text-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg">
                            <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ $platformActivity['payments_today'] }}</div>
                            <div class="text-sm text-purple-600 dark:text-purple-400">Payments Today</div>
                        </div>
                        <div class="text-center p-4 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-lg">
                            <div class="text-2xl font-bold text-orange-600 dark:text-orange-400">{{ $platformActivity['interests_today'] }}</div>
                            <div class="text-sm text-orange-600 dark:text-orange-400">New Interests</div>
                        </div>
                    </div>

                    @if($metrics['recentRegistrations'] > 0)
                    <div class="mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                            <div class="text-sm text-green-700 dark:text-green-400">
                                <strong>{{ $metrics['recentRegistrations'] }}</strong> new registrations in the last 24 hours
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- System Health & Additional Metrics -->
    <div class="grid grid-cols-12 gap-5 mb-5">
        <div class="lg:col-span-8 col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">{{ __('Key Performance Indicators') }}</h4>
                </header>
                <div class="card-body p-6">
                    <div class="grid md:grid-cols-4 grid-cols-2 gap-4">
                        <!-- Churn Rate -->
                        <div class="text-center p-4 bg-slate-50 dark:bg-slate-900 rounded-lg">
                            <div class="w-12 h-12 mx-auto rounded-full bg-danger-500 bg-opacity-10 flex items-center justify-center mb-3">
                                <iconify-icon class="text-2xl text-danger-500" icon="heroicons-outline:trending-down"></iconify-icon>
                            </div>
                            <div class="text-2xl font-bold text-slate-900 dark:text-white">{{ $metrics['churnRate'] }}%</div>
                            <div class="text-sm text-slate-500 dark:text-slate-400">Churn Rate</div>
                        </div>

                        <!-- Failed Payments -->
                        <div class="text-center p-4 bg-slate-50 dark:bg-slate-900 rounded-lg">
                            <div class="w-12 h-12 mx-auto rounded-full bg-warning-500 bg-opacity-10 flex items-center justify-center mb-3">
                                <iconify-icon class="text-2xl text-warning-500" icon="heroicons-outline:exclamation-triangle"></iconify-icon>
                            </div>
                            <div class="text-2xl font-bold text-slate-900 dark:text-white">{{ $metrics['failedPayments'] }}</div>
                            <div class="text-sm text-slate-500 dark:text-slate-400">Failed Payments</div>
                        </div>

                        <!-- Pending Refunds -->
                        <div class="text-center p-4 bg-slate-50 dark:bg-slate-900 rounded-lg">
                            <div class="w-12 h-12 mx-auto rounded-full bg-info-500 bg-opacity-10 flex items-center justify-center mb-3">
                                <iconify-icon class="text-2xl text-info-500" icon="heroicons-outline:arrow-circle-left"></iconify-icon>
                            </div>
                            <div class="text-2xl font-bold text-slate-900 dark:text-white">{{ $metrics['pendingRefunds'] }}</div>
                            <div class="text-sm text-slate-500 dark:text-slate-400">Pending Refunds</div>
                        </div>

                        <!-- User Growth -->
                        <div class="text-center p-4 bg-slate-50 dark:bg-slate-900 rounded-lg">
                            <div class="w-12 h-12 mx-auto rounded-full bg-success-500 bg-opacity-10 flex items-center justify-center mb-3">
                                <iconify-icon class="text-2xl text-success-500" icon="heroicons-outline:trending-up"></iconify-icon>
                            </div>
                            <div class="text-2xl font-bold text-slate-900 dark:text-white">
                                @if($metrics['userGrowthRate'] > 0)
                                    +{{ number_format($metrics['userGrowthRate'], 1) }}%
                                @else
                                    {{ number_format($metrics['userGrowthRate'], 1) }}%
                                @endif
                            </div>
                            <div class="text-sm text-slate-500 dark:text-slate-400">User Growth</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="lg:col-span-4 col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">{{ __('System Health') }}</h4>
                </header>
                <div class="card-body p-6">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                <span class="text-sm text-slate-600 dark:text-slate-300">Database</span>
                            </div>
                            <span class="text-sm font-medium text-slate-900 dark:text-white">{{ $systemHealth['database_size'] }} MB</span>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                                <span class="text-sm text-slate-600 dark:text-slate-300">Active Sessions</span>
                            </div>
                            <span class="text-sm font-medium text-slate-900 dark:text-white">{{ $systemHealth['active_sessions'] }}</span>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                <span class="text-sm text-slate-600 dark:text-slate-300">Uptime</span>
                            </div>
                            <span class="text-sm font-medium text-slate-900 dark:text-white">{{ $systemHealth['server_uptime'] }}</span>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 {{ $systemHealth['error_rate'] < 1 ? 'bg-green-500' : 'bg-yellow-500' }} rounded-full"></div>
                                <span class="text-sm text-slate-600 dark:text-slate-300">Error Rate</span>
                            </div>
                            <span class="text-sm font-medium text-slate-900 dark:text-white">{{ $systemHealth['error_rate'] }}%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-12 gap-5">
        <div class="lg:col-span-8 col-span-12">
            <div class="card">
                <div class="card-body p-6">
                    <div class="legend-ring">
                        <div id="subscription-barchart"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="lg:col-span-4 col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">User Distribution</h4>
                    <div class="relative">
                        <div class="dropdown relative">
                            <button class="text-xl text-center block w-full " type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <span class="text-lg inline-flex h-6 w-6 flex-col items-center justify-center border border-slate-200 dark:border-slate-700
                                    rounded dark:text-slate-400">
                                    <iconify-icon icon="heroicons-outline:dots-horizontal"></iconify-icon>
                                </span>
                            </button>
                            <ul class=" dropdown-menu min-w-[120px] absolute text-sm text-slate-700 dark:text-white hidden bg-white dark:bg-slate-700
                                shadow z-[2] overflow-hidden list-none text-left rounded-lg mt-1 m-0 bg-clip-padding border-none">
                                <li>
                                    <a href="#" class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600
                                        dark:hover:text-white">
                                        Last 28 Days</a>
                                </li>
                                <li>
                                    <a href="#" class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600
                                        dark:hover:text-white">
                                        Last Month</a>
                                </li>
                                <li>
                                    <a href="#" class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600
                                        dark:hover:text-white">
                                        Last Year</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </header>
                <div class="card-body p-6">
                    <div id="user-roles-chart"></div>
                </div>
            </div>
        </div>

        <div class="lg:col-span-8 col-span-12">
            <div class="card">
                <header class="card-header noborder">
                    <h4 class="card-title">Recent Users</h4>
                    <div class="relative">
                        <div class="dropdown relative">
                            <button class="text-xl text-center block w-full " type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <span class="text-lg inline-flex h-6 w-6 flex-col items-center justify-center border border-slate-200 dark:border-slate-700
                                    rounded dark:text-slate-400">
                                    <iconify-icon icon="heroicons-outline:dots-horizontal"></iconify-icon>
                                </span>
                            </button>
                            <ul class=" dropdown-menu min-w-[120px] absolute text-sm text-slate-700 dark:text-white hidden bg-white dark:bg-slate-700
                                shadow z-[2] overflow-hidden list-none text-left rounded-lg mt-1 m-0 bg-clip-padding border-none">
                                <li>
                                    <a href="{{ route('admin.users.index') }}" class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600
                                        dark:hover:text-white">
                                        View All Users</a>
                                </li>
                                <li>
                                    <a href="#" class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600
                                        dark:hover:text-white">
                                        Export Users</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </header>
                <div class="card-body p-6">
                    <div class="overflow-x-auto -mx-6">
                        <div class="inline-block min-w-full align-middle">
                            <div class="overflow-hidden ">
                                <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
                                    <thead class="  bg-slate-200 dark:bg-slate-700">
                                        <tr>
                                            <th scope="col" class="table-th">
                                                {{ __('NAME') }}
                                            </th>
                                            <th scope="col" class="table-th">
                                                {{ __('EMAIL') }}
                                            </th>
                                            <th scope="col" class="table-th">
                                                {{ __('MEMBER SINCE') }}
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                                        @forelse($recentActivity['users'] as $user)
                                        <tr>
                                            <td class="table-td">
                                                <div class="flex items-center">
                                                    <div class="flex-none">
                                                        <div class="w-8 h-8 rounded-[100%] ltr:mr-3 rtl:ml-3">
                                                            <div class="w-full h-full rounded-[100%] bg-slate-100 dark:bg-slate-700 flex items-center justify-center">
                                                                <span class="text-xs font-medium text-slate-600 dark:text-slate-300">
                                                                    {{ strtoupper(substr($user->name, 0, 2)) }}
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="flex-1 text-start">
                                                        <h4 class="text-sm font-medium text-slate-600 whitespace-nowrap">
                                                            {{ $user->name }}
                                                        </h4>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="table-td">{{ $user->email }}</td>
                                            <td class="table-td ">{{ $user->created_at->diffForHumans() }}</td>
                                        </tr>
                                        @empty
                                        <tr>
                                            <td colspan="3" class="table-td text-center">{{ __('No recent users') }}</td>
                                        </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="lg:col-span-4 col-span-12">
            <div class="card ">
                <div class="card-header ">
                    <h4 class="card-title">Recent Activity</h4>
                </div>
                <div class="card-body p-6">
                    <!-- BEGIN: Activity Card -->
                    <div>
                        <ul class="list-item space-y-3 h-full overflow-x-auto">
                            @forelse($topProducts as $product)
                            <li class="flex items-center space-x-3 rtl:space-x-reverse border-b border-slate-100 dark:border-slate-700 last:border-b-0 pb-3 last:pb-0">
                                <div>
                                    <div class="w-8 h-8 rounded-[100%] bg-primary-500 bg-opacity-10 flex items-center justify-center">
                                        <iconify-icon class="text-primary-500" icon="heroicons-outline:cube"></iconify-icon>
                                    </div>
                                </div>
                                <div class="text-start overflow-hidden text-ellipsis whitespace-nowrap max-w-[63%]">
                                    <div class="text-sm text-slate-600 dark:text-slate-300 overflow-hidden text-ellipsis whitespace-nowrap">
                                        {{ $product->name }} - {{ $product->subscriptions_count }} subscribers
                                    </div>
                                </div>
                                <div class="flex-1 ltr:text-right rtl:text-left">
                                    <div class="text-sm font-light text-slate-400 dark:text-slate-400">
                                        ${{ number_format($product->price, 2) }}
                                    </div>
                                </div>
                            </li>
                            @empty
                            <li class="flex items-center space-x-3 rtl:space-x-reverse border-b border-slate-100 dark:border-slate-700 last:border-b-0 pb-3 last:pb-0">
                                <div class="text-start overflow-hidden text-ellipsis whitespace-nowrap max-w-[100%]">
                                    <div class="text-sm text-slate-600 dark:text-slate-300 overflow-hidden text-ellipsis whitespace-nowrap">
                                        No products available
                                    </div>
                                </div>
                            </li>
                            @endforelse
                        </ul>
                    </div>
                    <!-- END: Activity Card -->
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Recent Activity Feed -->
    <div class="grid grid-cols-12 gap-5 mb-5">
        <div class="col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">{{ __('Recent Activity Feed') }}</h4>
                    <div class="text-sm text-slate-500">Latest platform activity across all modules</div>
                </header>
                <div class="card-body p-6">
                    <div class="space-y-4 max-h-96 overflow-y-auto">
                        <!-- Recent Subscriptions -->
                        @forelse($recentActivity['subscriptions'] as $subscription)
                        <div class="flex items-start space-x-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                            <div class="w-8 h-8 rounded-full bg-green-500 bg-opacity-20 flex items-center justify-center flex-shrink-0">
                                <iconify-icon class="text-green-600 text-sm" icon="heroicons-outline:credit-card"></iconify-icon>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="text-sm font-medium text-slate-900 dark:text-white">
                                    New Subscription
                                </div>
                                <div class="text-xs text-slate-600 dark:text-slate-400">
                                    <strong>{{ $subscription->user->name }}</strong> subscribed to
                                    {{ $subscription->subscriptionProduct->name ?? 'a plan' }}
                                </div>
                                <div class="text-xs text-slate-500 mt-1">
                                    {{ $subscription->created_at->diffForHumans() }}
                                </div>
                            </div>
                            <div class="text-right">
                                <a href="{{ route('admin.users.show', $subscription->user->id) }}" class="text-xs text-blue-600 hover:text-blue-800">
                                    View User
                                </a>
                            </div>
                        </div>
                        @empty
                        @endforelse

                        <!-- Recent Payments -->
                        @forelse($recentActivity['payments'] as $payment)
                        <div class="flex items-start space-x-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                            <div class="w-8 h-8 rounded-full bg-blue-500 bg-opacity-20 flex items-center justify-center flex-shrink-0">
                                <iconify-icon class="text-blue-600 text-sm" icon="heroicons-outline:currency-dollar"></iconify-icon>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="text-sm font-medium text-slate-900 dark:text-white">
                                    Payment Received
                                </div>
                                <div class="text-xs text-slate-600 dark:text-slate-400">
                                    <strong>${{ number_format($payment->amount / 100, 2) }}</strong> from {{ $payment->user->name }}
                                    @if($payment->userSubscription && $payment->userSubscription->subscriptionProduct)
                                        for {{ $payment->userSubscription->subscriptionProduct->name }}
                                    @endif
                                </div>
                                <div class="text-xs text-slate-500 mt-1">
                                    {{ $payment->created_at->diffForHumans() }}
                                </div>
                            </div>
                            <div class="text-right">
                                <a href="{{ route('admin.financial.payments') }}" class="text-xs text-blue-600 hover:text-blue-800">
                                    View Payment
                                </a>
                            </div>
                        </div>
                        @empty
                        @endforelse

                        <!-- Recent Failed Payments -->
                        @forelse($recentActivity['failedPayments'] as $failedPayment)
                        <div class="flex items-start space-x-3 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                            <div class="w-8 h-8 rounded-full bg-red-500 bg-opacity-20 flex items-center justify-center flex-shrink-0">
                                <iconify-icon class="text-red-600 text-sm" icon="heroicons-outline:x-circle"></iconify-icon>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="text-sm font-medium text-slate-900 dark:text-white">
                                    Payment Failed
                                </div>
                                <div class="text-xs text-slate-600 dark:text-slate-400">
                                    <strong>${{ number_format($failedPayment->amount / 100, 2) }}</strong> from {{ $failedPayment->user->name }}
                                </div>
                                <div class="text-xs text-slate-500 mt-1">
                                    {{ $failedPayment->created_at->diffForHumans() }}
                                </div>
                            </div>
                            <div class="text-right">
                                <a href="{{ route('admin.users.show', $failedPayment->user->id) }}" class="text-xs text-red-600 hover:text-red-800">
                                    Review
                                </a>
                            </div>
                        </div>
                        @empty
                        @endforelse

                        <!-- Recent Users -->
                        @forelse($recentActivity['users'] as $user)
                        <div class="flex items-start space-x-3 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
                            <div class="w-8 h-8 rounded-full bg-purple-500 bg-opacity-20 flex items-center justify-center flex-shrink-0">
                                <iconify-icon class="text-purple-600 text-sm" icon="heroicons-outline:user-add"></iconify-icon>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="text-sm font-medium text-slate-900 dark:text-white">
                                    New Registration
                                </div>
                                <div class="text-xs text-slate-600 dark:text-slate-400">
                                    <strong>{{ $user->name }}</strong> ({{ ucfirst($user->role) }}) joined the platform
                                </div>
                                <div class="text-xs text-slate-500 mt-1">
                                    {{ $user->created_at->diffForHumans() }}
                                </div>
                            </div>
                            <div class="text-right">
                                <a href="{{ route('admin.users.show', $user->id) }}" class="text-xs text-purple-600 hover:text-purple-800">
                                    View Profile
                                </a>
                            </div>
                        </div>
                        @empty
                        @endforelse

                        @if(
                            $recentActivity['subscriptions']->isEmpty() &&
                            $recentActivity['payments']->isEmpty() &&
                            $recentActivity['users']->isEmpty() &&
                            $recentActivity['failedPayments']->isEmpty()
                        )
                        <div class="text-center py-8">
                            <div class="w-16 h-16 mx-auto rounded-full bg-slate-100 dark:bg-slate-800 flex items-center justify-center mb-4">
                                <iconify-icon class="text-3xl text-slate-400" icon="heroicons-outline:clock"></iconify-icon>
                            </div>
                            <div class="text-slate-500 dark:text-slate-400">No recent activity</div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>



</x-app-layout>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Subscription Bar Chart
    var options = {
        series: [{
            name: 'Subscriptions',
            data: @json($chartData['monthlySubscriptions']['data'])
        }],
        chart: {
            type: 'bar',
            height: 350,
            toolbar: {
                show: false
            }
        },
        plotOptions: {
            bar: {
                horizontal: false,
                columnWidth: '55%',
                endingShape: 'rounded'
            },
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            show: true,
            width: 2,
            colors: ['transparent']
        },
        xaxis: {
            categories: @json($chartData['monthlySubscriptions']['labels']),
        },
        yaxis: {
            title: {
                text: 'Subscriptions'
            }
        },
        fill: {
            opacity: 1
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return val + " subscriptions"
                }
            }
        },
        colors: ['#4669FA']
    };

    var chart = new ApexCharts(document.querySelector("#subscription-barchart"), options);
    chart.render();

    // User Roles Pie Chart
    const userRolesData = @json($chartData['userRoleStats']);
    const rolesChart = new ApexCharts(document.querySelector("#user-roles-chart"), {
        series: Object.values(userRolesData),
        chart: {
            type: 'donut',
            height: 350
        },
        labels: Object.keys(userRolesData).map(role => role.charAt(0).toUpperCase() + role.slice(1)),
        colors: ['#4F46E5', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'],
        legend: {
            position: 'bottom'
        },
        plotOptions: {
            pie: {
                donut: {
                    labels: {
                        show: true,
                        total: {
                            show: true,
                            label: 'Total Users',
                            formatter: function (w) {
                                return w.globals.seriesTotals.reduce((a, b) => a + b, 0);
                            }
                        }
                    }
                }
            }
        }
    });
    rolesChart.render();
});
</script>
@endpush
