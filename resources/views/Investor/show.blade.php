<x-app-layout>
    <div class="space-y-8">
        <!-- Breadcrumb -->
        <div>
            <x-breadcrumb :page-title="$pageTitle" :breadcrumb-items="$breadcrumbItems" />
        </div>

        <!-- User Profile Header -->
        <div class="card">
            <div class="card-body p-6">
                <div class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-6">
                    <div class="flex-none">
                        <img src="{{ $investor->getFirstMediaUrl('profile-image') ?: asset('images/all-img/customer_1.png') }}" 
                             alt="{{ $investor->name }}" 
                             class="w-24 h-24 rounded-full object-cover">
                    </div>
                    <div class="flex-1">
                        <h2 class="text-2xl font-bold text-slate-900 dark:text-white mb-2">{{ $investor->name }}</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-slate-600 dark:text-slate-300">Email:</span>
                                <span class="font-medium ml-2">{{ $investor->email }}</span>
                            </div>
                            <div>
                                <span class="text-slate-600 dark:text-slate-300">Role:</span>
                                <span class="badge badge-primary-light ml-2">{{ ucfirst($investor->role) }}</span>
                            </div>
                            <div>
                                <span class="text-slate-600 dark:text-slate-300">Account Status:</span>
                                <span class="badge {{ $investor->account_status === 'active' ? 'badge-success-light' : 'badge-warning-light' }} ml-2">
                                    {{ ucfirst($investor->account_status) }}
                                </span>
                            </div>
                            <div>
                                <span class="text-slate-600 dark:text-slate-300">Joined:</span>
                                <span class="font-medium ml-2">{{ $investor->created_at->format('M d, Y') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Methods Section -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Payment Methods</h4>
            </div>
            <div class="card-body">
                @if($investor->paymentMethods->count() > 0)
                    <div class="space-y-4">
                        @foreach($investor->paymentMethods as $method)
                            <div class="flex items-center justify-between p-4 border border-slate-200 dark:border-slate-700 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                                        <iconify-icon icon="heroicons:credit-card" class="text-blue-600 dark:text-blue-400"></iconify-icon>
                                    </div>
                                    <div>
                                        <div class="font-medium">**** **** **** {{ $method->card_last_four }}</div>
                                        <div class="text-sm text-slate-500">{{ ucfirst($method->card_brand) }} • Expires {{ $method->card_exp_month }}/{{ $method->card_exp_year }}</div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    @if($method->is_default)
                                        <span class="badge badge-success-light">Default</span>
                                    @endif
                                    <span class="badge badge-primary-light">{{ ucfirst($method->type) }}</span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <iconify-icon icon="heroicons:credit-card" class="text-4xl text-slate-400 mb-2"></iconify-icon>
                        <p class="text-slate-500">No payment methods found</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Subscription History Section -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Subscription History</h4>
            </div>
            <div class="card-body">
                @if($subscriptionHistory->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
                            <thead>
                                <tr>
                                    <th class="table-th">Product</th>
                                    <th class="table-th">Status</th>
                                    <th class="table-th">Amount</th>
                                    <th class="table-th">Period</th>
                                    <th class="table-th">Created</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-slate-200 dark:divide-slate-700">
                                @foreach($subscriptionHistory as $subscription)
                                    <tr>
                                        <td class="table-td">{{ $subscription->product->name }}</td>
                                        <td class="table-td">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                                @if($subscription->status === 'active') bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100
                                                @elseif($subscription->status === 'canceled') bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100
                                                @elseif($subscription->status === 'past_due') bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100
                                                @else bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100 @endif">
                                                {{ ucfirst($subscription->status) }}
                                            </span>
                                        </td>
                                        <td class="table-td">${{ number_format($subscription->amount, 2) }}</td>
                                        <td class="table-td">
                                            @if($subscription->current_period_start && $subscription->current_period_end)
                                                {{ $subscription->current_period_start->format('M d') }} - {{ $subscription->current_period_end->format('M d, Y') }}
                                            @else
                                                N/A
                                            @endif
                                        </td>
                                        <td class="table-td">{{ $subscription->created_at->format('M d, Y') }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-8">
                        <iconify-icon icon="heroicons:document-text" class="text-4xl text-slate-400 mb-2"></iconify-icon>
                        <p class="text-slate-500">No subscription history found</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Invoices Section -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Invoices</h4>
            </div>
            <div class="card-body">
                @if($invoices->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
                            <thead>
                                <tr>
                                    <th class="table-th">Invoice #</th>
                                    <th class="table-th">Status</th>
                                    <th class="table-th">Amount</th>
                                    <th class="table-th">Date</th>
                                    <th class="table-th">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-slate-200 dark:divide-slate-700">
                                @foreach($invoices as $invoice)
                                    <tr>
                                        <td class="table-td">{{ $invoice->invoice_number }}</td>
                                        <td class="table-td">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                                @if($invoice->status === 'paid') bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100
                                                @elseif($invoice->status === 'open') bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100
                                                @elseif($invoice->status === 'draft') bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100
                                                @else bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100 @endif">
                                                {{ ucfirst($invoice->status) }}
                                            </span>
                                        </td>
                                        <td class="table-td">${{ number_format($invoice->total, 2) }}</td>
                                        <td class="table-td">{{ $invoice->created_at->format('M d, Y') }}</td>
                                        <td class="table-td">
                                            @if($invoice->pdf_url)
                                                <a href="{{ $invoice->pdf_url }}" target="_blank" class="btn btn-sm btn-primary">
                                                    <iconify-icon icon="heroicons:arrow-down-tray" class="mr-1"></iconify-icon>
                                                    Download
                                                </a>
                                            @else
                                                <span class="text-slate-400">N/A</span>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-8">
                        <iconify-icon icon="heroicons:document-text" class="text-4xl text-slate-400 mb-2"></iconify-icon>
                        <p class="text-slate-500">No invoices found</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Interested Startups Section -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Startups of Interest</h4>
            </div>
            <div class="card-body">
                @if($interestedStartups->count() > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @foreach($interestedStartups as $request)
                            <div class="border border-slate-200 dark:border-slate-700 rounded-lg p-4">
                                <div class="flex items-center space-x-3 mb-3">
                                    <img src="{{ $request->target->getFirstMediaUrl('profile-image') ?: asset('images/all-img/customer_1.png') }}" 
                                         alt="{{ $request->target->name }}" 
                                         class="w-10 h-10 rounded-full object-cover">
                                    <div>
                                        <div class="font-medium">{{ $request->target->name }}</div>
                                        <div class="text-sm text-slate-500">{{ $request->target->email }}</div>
                                    </div>
                                </div>
                                @if($request->message)
                                    <p class="text-sm text-slate-600 dark:text-slate-300 mb-2">{{ Str::limit($request->message, 100) }}</p>
                                @endif
                                @if($request->proposed_amount)
                                    <div class="text-sm">
                                        <span class="text-slate-500">Proposed Amount:</span>
                                        <span class="font-medium">${{ number_format($request->proposed_amount, 2) }}</span>
                                    </div>
                                @endif
                                <div class="text-xs text-slate-400 mt-2">
                                    Interested on {{ $request->created_at->format('M d, Y') }}
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <iconify-icon icon="heroicons:building-office" class="text-4xl text-slate-400 mb-2"></iconify-icon>
                        <p class="text-slate-500">No startup interests yet</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Investor Profile & Categories -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Investor Profile -->
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Investor Profile</h4>
                </div>
                <div class="card-body">
                    @if($investor->investorProfile)
                        <div class="space-y-4">
                            @if($investor->investorProfile->bio)
                                <div>
                                    <label class="form-label">Bio</label>
                                    <p class="text-slate-600 dark:text-slate-300">{{ $investor->investorProfile->bio }}</p>
                                </div>
                            @endif
                            @if($investor->investorProfile->investment_range_min || $investor->investorProfile->investment_range_max)
                                <div>
                                    <label class="form-label">Investment Range</label>
                                    <p class="text-slate-600 dark:text-slate-300">
                                        ${{ number_format($investor->investorProfile->investment_range_min ?? 0) }} - 
                                        ${{ number_format($investor->investorProfile->investment_range_max ?? 0) }}
                                    </p>
                                </div>
                            @endif
                            @if($investor->investorProfile->preferred_sectors)
                                <div>
                                    <label class="form-label">Preferred Sectors</label>
                                    <div class="flex flex-wrap gap-2">
                                        @foreach($investor->investorProfile->preferred_sectors as $sector)
                                            <span class="badge badge-primary-light">{{ $sector }}</span>
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                        </div>
                    @else
                        <div class="text-center py-8">
                            <iconify-icon icon="heroicons:user" class="text-4xl text-slate-400 mb-2"></iconify-icon>
                            <p class="text-slate-500">No profile information available</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Categories -->
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Investment Categories</h4>
                </div>
                <div class="card-body">
                    @if($investor->investorProfile && $investor->investorProfile->categories->count() > 0)
                        <div class="space-y-3">
                            @foreach($investor->investorProfile->categories as $category)
                                <div class="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                                    <div>
                                        <div class="font-medium">{{ $category->name }}</div>
                                        @if($category->description)
                                            <div class="text-sm text-slate-500">{{ $category->description }}</div>
                                        @endif
                                    </div>
                                    <span class="badge badge-primary-light">{{ ucfirst($category->type) }}</span>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8">
                            <iconify-icon icon="heroicons:tag" class="text-4xl text-slate-400 mb-2"></iconify-icon>
                            <p class="text-slate-500">No categories selected</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
