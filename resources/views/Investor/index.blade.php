<x-app-layout>
    <div class="space-y-8">
        <div class="block sm:flex items-center justify-between mb-6">
            <x-breadcrumb :pageTitle="$pageTitle" :breadcrumbItems="$breadcrumbItems"/>
        </div>

    <div class="card">
        <header class=" card-header noborder">
            <h4 class="card-title">All Investors
            </h4>
        </header>
        <div class="card-body px-6 pb-6">
            <div class="overflow-x-auto -mx-6 dashcode-data-table">
                <span class=" col-span-8  hidden"></span>
                <span class="  col-span-4 hidden"></span>
                <div class="inline-block min-w-full align-middle">
                    <div class="overflow-hidden ">
                        <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700"
                               id="data-table">
                            <thead class=" border-t border-slate-100 dark:border-slate-800">
                            <tr>

                                <th scope="col" class=" table-th ">
                                    SL
                                </th>

                                <th scope="col" class=" table-th ">
                                    Investor Name
                                </th>

                                <th scope="col" class=" table-th ">
                                    Investor Type
                                </th>

                                <th scope="col" class=" table-th ">
                                   E-Mail
                                </th>

                                <th scope="col" class=" table-th ">
                                   Website URL
                                </th>

                                <th scope="col" class=" table-th ">
                                    Founded
                                </th>

                                <th scope="col" class=" table-th ">
                                    Country
                                </th>

                                <th scope="col" class=" table-th ">
                                    Action
                                </th>

                            </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                            <!-- Data will be populated by DataTable via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

        @push('scripts')
            <script type="module">
                // DataTable initialization with server-side processing
                $("#data-table").DataTable({
                    processing: true,
                    serverSide: true,
                    ajax: {
                        url: "{{ route('investors.datatable') }}",
                        type: "GET"
                    },
                    columns: [
                        { "data": 0, "name": "id", "orderable": false, "searchable": false }, // SL
                        { "data": 1, "name": "name", "orderable": true, "searchable": true }, // Investor Name
                        { "data": 2, "name": "investor_type", "orderable": false, "searchable": false }, // Investor Type
                        { "data": 3, "name": "email", "orderable": true, "searchable": true }, // E-Mail
                        { "data": 4, "name": "website", "orderable": false, "searchable": false }, // Website URL
                        { "data": 5, "name": "created_at", "orderable": true, "searchable": false }, // Founded
                        { "data": 6, "name": "country", "orderable": false, "searchable": false }, // Country
                        { "data": 7, "name": "action", "orderable": false, "searchable": false } // Action
                    ],
                    dom: "<'grid grid-cols-12 gap-5 px-6 mt-6'<'col-span-4'l><'col-span-8 flex justify-end'f><'#pagination.flex items-center'>><'min-w-full't><'flex justify-end items-center'p>",
                    paging: true,
                    ordering: true,
                    info: false,
                    searching: true,
                    lengthChange: true,
                    lengthMenu: [10, 25, 50, 100],
                    language: {
                        lengthMenu: "Show _MENU_ entries",
                        paginate: {
                            previous: `<iconify-icon icon="ic:round-keyboard-arrow-left"></iconify-icon>`,
                            next: `<iconify-icon icon="ic:round-keyboard-arrow-right"></iconify-icon>`,
                        },
                        search: "Search:",
                        processing: "Loading..."
                    },
                    drawCallback: function() {
                        // Reinitialize dropdown functionality after table redraw
                        initializeDropdowns();
                    }
                });

                // Function to initialize dropdown functionality
                function initializeDropdowns() {
                    // Handle dropdown toggles
                    document.querySelectorAll('[data-bs-toggle="dropdown"]').forEach(function(button) {
                        button.removeEventListener('click', handleDropdownClick); // Remove existing listeners
                        button.addEventListener('click', handleDropdownClick);
                    });
                }

                // Dropdown click handler
                function handleDropdownClick(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown-menu').forEach(function(menu) {
                        if (menu !== this.nextElementSibling) {
                            menu.classList.add('hidden');
                        }
                    }.bind(this));

                    // Toggle current dropdown
                    const menu = this.nextElementSibling;
                    if (menu) {
                        menu.classList.toggle('hidden');
                    }
                }

                // Close dropdowns when clicking outside
                document.addEventListener('click', function(e) {
                    if (!e.target.closest('.dropdown')) {
                        document.querySelectorAll('.dropdown-menu').forEach(function(menu) {
                            menu.classList.add('hidden');
                        });
                    }
                });

                // Initialize dropdowns on page load
                initializeDropdowns();
            </script>
        @endpush
    </div>
</x-app-layout>
