<x-app-layout>
    <div class="space-y-8">
        <div>
            <x-breadcrumb :page-title="$pageTitle" :breadcrumb-items="$breadcrumbItems" />
        </div>

        <!-- Header -->
        <div class="text-center">
            <h1 class="font-bold lg:text-4xl text-3xl text-slate-900 dark:text-slate-100 mb-4">
                Frequently Asked Questions
            </h1>
            <p class="text-slate-600 dark:text-slate-400 text-lg max-w-2xl mx-auto">
                Find answers to common questions about our platform. 
                @if($userRole === 'investor')
                    These FAQs are specifically for investors.
                @elseif($userRole === 'startup')
                    These FAQs are specifically for startups.
                @else
                    These FAQs cover general platform information.
                @endif
            </p>
        </div>

        <!-- Search and Filters -->
        <div class="card">
            <div class="card-body">
                <form method="GET" action="{{ route('faqs.index') }}" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Search -->
                    <div class="md:col-span-2 input-area">
                        <label for="search" class="form-label">Search FAQs</label>
                        <div class="relative">
                            <input type="text" id="search" name="search" class="form-control pl-10" 
                                   placeholder="Search questions and answers..." value="{{ $searchQuery }}">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <iconify-icon icon="heroicons-outline:search" class="text-slate-400"></iconify-icon>
                            </div>
                        </div>
                    </div>

                    <!-- Category Filter -->
                    <div class="input-area">
                        <label for="category" class="form-label">Category</label>
                        <select name="category" id="category" class="form-control">
                            <option value="">All Categories</option>
                            @foreach($availableCategories as $value => $label)
                                <option value="{{ $value }}" {{ $currentCategory === $value ? 'selected' : '' }}>
                                    {{ $label }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Hidden submit button for form submission -->
                    <button type="submit" class="hidden"></button>
                </form>
            </div>
        </div>

        @if($faqs->count() > 0)
            <!-- FAQ Categories -->
            @if($faqsByCategory->count() > 1)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    @foreach($faqsByCategory as $category => $categoryFaqs)
                        <a href="{{ route('faqs.index', ['category' => $category]) }}" 
                           class="card hover:shadow-lg transition-shadow {{ $currentCategory === $category ? 'ring-2 ring-primary-500' : '' }}">
                            <div class="card-body text-center">
                                <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <iconify-icon icon="heroicons-outline:question-mark-circle" class="text-2xl text-primary-600 dark:text-primary-300"></iconify-icon>
                                </div>
                                <h3 class="font-medium text-slate-900 dark:text-slate-100 mb-2">
                                    {{ $allCategories[$category] ?? ucfirst($category ?? 'General') }}
                                </h3>
                                <p class="text-sm text-slate-600 dark:text-slate-400">
                                    {{ $categoryFaqs->count() }} {{ Str::plural('question', $categoryFaqs->count()) }}
                                </p>
                            </div>
                        </a>
                    @endforeach
                </div>
            @endif

            <!-- FAQ List -->
            <div class="space-y-6" id="faq-list">
                @if($currentCategory)
                    <div class="flex items-center justify-between">
                        <h2 class="text-xl font-semibold text-slate-900 dark:text-slate-100">
                            {{ $allCategories[$currentCategory] ?? ucfirst($currentCategory) }}
                        </h2>
                        <a href="{{ route('faqs.index') }}" class="text-primary-600 hover:text-primary-700 text-sm">
                            View all FAQs
                        </a>
                    </div>
                @endif

                <div class="space-y-4">
                    @foreach($faqs as $faq)
                        <div class="card faq-item">
                            <div class="card-body p-0">
                                <button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors faq-toggle"
                                        data-faq-id="{{ $faq->id }}">
                                    <span class="font-medium text-slate-900 dark:text-slate-100 pr-4">
                                        {{ $faq->question }}
                                    </span>
                                    <iconify-icon icon="heroicons:chevron-down" class="text-slate-500 transform transition-transform faq-chevron"></iconify-icon>
                                </button>
                                <div class="faq-answer hidden px-6 pb-4">
                                    <div class="text-slate-600 dark:text-slate-400 leading-relaxed">
                                        {!! nl2br(e($faq->answer)) !!}
                                    </div>
                                    @if($faq->category)
                                        <div class="mt-3 pt-3 border-t border-slate-200 dark:border-slate-700">
                                            <span class="inline-block px-2 py-1 bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-400 text-xs rounded">
                                                {{ $allCategories[$faq->category] ?? ucfirst($faq->category) }}
                                            </span>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>

            <!-- Quick Links -->
            @if(!$currentCategory && $availableCategories)
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Browse by Category</h4>
                    </div>
                    <div class="card-body">
                        <div class="flex flex-wrap gap-2">
                            @foreach($availableCategories as $value => $label)
                                <a href="{{ route('faqs.index', ['category' => $value]) }}" 
                                   class="inline-block px-3 py-1 bg-slate-100 hover:bg-slate-200 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-300 text-sm rounded transition-colors">
                                    {{ $label }}
                                </a>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif
        @else
            <!-- No FAQs Found -->
            <div class="card">
                <div class="card-body text-center py-12">
                    <iconify-icon icon="heroicons-outline:question-mark-circle" class="text-6xl text-slate-300 dark:text-slate-600 mb-4"></iconify-icon>
                    <h3 class="text-lg font-medium text-slate-600 dark:text-slate-300 mb-2">
                        @if($searchQuery || $currentCategory)
                            No FAQs found
                        @else
                            No FAQs available
                        @endif
                    </h3>
                    <p class="text-slate-500 dark:text-slate-400 mb-4">
                        @if($searchQuery || $currentCategory)
                            Try adjusting your search or filter criteria.
                        @else
                            FAQs for your role haven't been added yet. Check back later!
                        @endif
                    </p>
                    @if($searchQuery || $currentCategory)
                        <a href="{{ route('faqs.index') }}" class="btn inline-flex justify-center btn-outline-dark">
                            View All FAQs
                        </a>
                    @endif
                </div>
            </div>
        @endif

        <!-- Contact Support -->
        <div class="card bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900 dark:to-primary-800">
            <div class="card-body text-center">
                <h3 class="text-lg font-medium text-slate-900 dark:text-slate-100 mb-2">
                    Still have questions?
                </h3>
                <p class="text-slate-600 dark:text-slate-400 mb-4">
                    Can't find what you're looking for? Our support team is here to help.
                </p>
                <div class="flex flex-col sm:flex-row gap-3 justify-center">
                    <a href="mailto:<EMAIL>" class="btn inline-flex justify-center btn-primary">
                        <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="heroicons-outline:mail"></iconify-icon>
                        Contact Support
                    </a>
                    <a href="#" class="btn inline-flex justify-center btn-outline-primary">
                        <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="heroicons-outline:chat-bubble-left-right"></iconify-icon>
                        Live Chat
                    </a>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            $(document).ready(function() {
                // FAQ Toggle functionality
                $('.faq-toggle').on('click', function() {
                    const $this = $(this);
                    const $answer = $this.siblings('.faq-answer');
                    const $chevron = $this.find('.faq-chevron');
                    
                    // Toggle answer visibility
                    $answer.slideToggle(300);
                    
                    // Rotate chevron
                    $chevron.toggleClass('rotate-180');
                });

                // Auto-submit form on category change
                $('#category').on('change', function() {
                    $(this).closest('form').submit();
                });

                // Live search functionality
                let searchTimeout;
                $('#search').on('input', function() {
                    clearTimeout(searchTimeout);
                    const query = $(this).val();
                    
                    searchTimeout = setTimeout(function() {
                        if (query.length >= 2 || query.length === 0) {
                            // Auto-submit form for search
                            $('#search').closest('form').submit();
                        }
                    }, 500);
                });

                // Open FAQ if URL has hash
                if (window.location.hash) {
                    const faqId = window.location.hash.substring(1);
                    const $faqToggle = $(`[data-faq-id="${faqId}"]`);
                    if ($faqToggle.length) {
                        $faqToggle.click();
                        $faqToggle[0].scrollIntoView({ behavior: 'smooth' });
                    }
                }
            });
        </script>
    @endpush
</x-app-layout>
