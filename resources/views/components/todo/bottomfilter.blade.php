<!-- BEGIN: Bottom Filter -->
<li data-status="team" class="flex space-x-2 text-sm capitalize dark:text-slate-300 py-2 cursor-pointer items-center rtl:space-x-reverse email-categorie-list">
    <span class="bar-c  bg-danger-500 ring-danger-500  inline-block h-2
    w-2 rounded-full ring-opacity-30 transition-all duration-150 "></span>
    <span class="transition duration-150">Team</span>
</li>

<li data-status="low" class="flex space-x-2 text-sm capitalize dark:text-slate-300 py-2 cursor-pointer items-center rtl:space-x-reverse email-categorie-list">
    <span class="bar-c  bg-success-500
    ring-success-500  inline-block h-2
    w-2 rounded-full ring-opacity-30 transition-all duration-150 "></span>
    <span class="transition duration-150">low</span>
</li>

<li data-status="medium" class="flex space-x-2 text-sm capitalize dark:text-slate-300 py-2 cursor-pointer items-center rtl:space-x-reverse email-categorie-list">
    <span class="bar-c  bg-warning-500 ring-warning-500  inline-block h-2
    w-2 rounded-full ring-opacity-30 transition-all duration-150 "></span>
    <span class="transition duration-150">medium</span>
</li>

<li data-status="high" class="flex space-x-2 text-sm capitalize dark:text-slate-300 py-2 cursor-pointer items-center rtl:space-x-reverse email-categorie-list">
    <span class="bar-c
    bg-primary-500 ring-primary-500  inline-block h-2
    w-2 rounded-full ring-opacity-30 transition-all duration-150 "></span>
    <span class="transition duration-150">high</span>
</li>

<li data-status="update" class="flex space-x-2 text-sm capitalize dark:text-slate-300 py-2 cursor-pointer items-center rtl:space-x-reverse email-categorie-list">
    <span class="bar-c  bg-info-500 ring-info-500  inline-block h-2
    w-2 rounded-full ring-opacity-30 transition-all duration-150 "></span>
    <span class="transition duration-150">update</span>
</li>
<!-- END: Bottom Filter -->
