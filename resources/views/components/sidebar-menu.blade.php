<!-- BEGIN: Sidebar -->
<div class="sidebar-wrapper group w-0 hidden xl:w-[248px] xl:block">
    <div id="bodyOverlay" class="w-screen h-screen fixed top-0 bg-slate-900 bg-opacity-50 backdrop-blur-sm z-10 hidden">
    </div>
    <div class="logo-segment">

        <!-- Application Logo -->
        <x-application-logo />

        <!-- Sidebar Type Button -->
        <div id="sidebar_type" class="cursor-pointer text-slate-900 dark:text-white text-lg">
            <iconify-icon class="sidebarDotIcon extend-icon text-slate-900 dark:text-slate-200" icon="fa-regular:dot-circle"></iconify-icon>
            <iconify-icon class="sidebarDotIcon collapsed-icon text-slate-900 dark:text-slate-200" icon="material-symbols:circle-outline"></iconify-icon>
        </div>
        <button class="sidebarCloseIcon text-2xl inline-block md:hidden">
            <iconify-icon class="text-slate-900 dark:text-slate-200" icon="clarity:window-close-line"></iconify-icon>
        </button>
    </div>
    <div id="nav_shadow" class="nav_shadow h-[60px] absolute top-[80px] nav-shadow z-[1] w-full transition-all duration-200 pointer-events-none
      opacity-0"></div>
    <div class="sidebar-menus bg-white dark:bg-slate-800 py-2 px-4 h-[calc(100%-80px)] z-50" id="sidebar_menus">
        <ul class="sidebar-menu">
            <!-- MAIN NAVIGATION -->
            <li class="sidebar-menu-title">{{ __('MAIN') }}</li>

            <!-- Dashboard -->
            <li class="{{ (request()->is('admin/dashboard*') || request()->is('dashboard*')) ? 'active' : '' }}">
                <a href="{{ route('admin.dashboard') }}" class="navItem">
                    <span class="flex items-center">
                        <iconify-icon class="nav-icon" icon="heroicons-outline:home"></iconify-icon>
                        <span>{{ __('Dashboard') }}</span>
                    </span>
                </a>
            </li>

            <!-- INVESTMENT PLATFORM SECTION -->
            @if(in_array(auth()->user()->role, ['admin', 'super-admin', 'analyst']))
                <li class="sidebar-menu-title">{{ __('INVESTMENT PLATFORM') }}</li>

                <!-- Investment Overview -->
                <li class="{{ (request()->is('admin/investment*')) ? 'active' : '' }}">
                    <a href="javascript:void(0)" class="navItem">
                        <span class="flex items-center">
                            <iconify-icon class="nav-icon" icon="heroicons-outline:trending-up"></iconify-icon>
                            <span>{{ __('Platform Overview') }}</span>
                        </span>
                        <iconify-icon class="icon-arrow" icon="heroicons-outline:chevron-right"></iconify-icon>
                    </a>
                    <ul class="sidebar-submenu">
                        <li>
                            <a href="{{ route('admin.investment.index') }}" class="navItem {{ (request()->is('admin/investment') && !request()->is('admin/investment/*')) ? 'active' : '' }}">
                                {{ __('Dashboard') }}
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('admin.investment.interest-requests') }}" class="navItem {{ (request()->is('admin/investment/interest-requests*')) ? 'active' : '' }}">
                                {{ __('Interest Requests') }}
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('admin.investment.users') }}" class="navItem {{ (request()->is('admin/investment/users*')) ? 'active' : '' }}">
                                {{ __('Platform Users') }}
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('admin.investment.esg-analytics') }}" class="navItem {{ (request()->is('admin/investment/esg-analytics*')) ? 'active' : '' }}">
                                {{ __('ESG Analytics') }}
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Investors Management -->
                <li class="{{ (request()->is('investors*') || request()->is('investor-request*')) ? 'active' : '' }}">
                    <a href="javascript:void(0)" class="navItem">
                        <span class="flex items-center">
                            <iconify-icon class="nav-icon" icon="heroicons-outline:user-group"></iconify-icon>
                            <span>{{ __('Investors') }}</span>
                        </span>
                        <iconify-icon class="icon-arrow" icon="heroicons-outline:chevron-right"></iconify-icon>
                    </a>
                    <ul class="sidebar-submenu">
                        <li>
                            <a href="{{ route('investors.index') }}" class="navItem {{ (request()->is('investors*') && !request()->is('investor-request*')) ? 'active' : '' }}">
                                {{ __('All Investors') }}
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('investor-request.index') }}" class="navItem {{ (request()->is('investor-request*')) ? 'active' : '' }}">
                                {{ __('Interest Requests') }}
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Startups Management -->
                <li class="{{ (request()->is('startups*') || request()->is('startup-request*')) ? 'active' : '' }}">
                    <a href="javascript:void(0)" class="navItem">
                        <span class="flex items-center">
                            <iconify-icon class="nav-icon" icon="heroicons-outline:building-office"></iconify-icon>
                            <span>{{ __('Startups') }}</span>
                        </span>
                        <iconify-icon class="icon-arrow" icon="heroicons-outline:chevron-right"></iconify-icon>
                    </a>
                    <ul class="sidebar-submenu">
                        <li>
                            <a href="{{ route('startups.index') }}" class="navItem {{ (request()->is('startups*') && !request()->is('startup-request*')) ? 'active' : '' }}">
                                {{ __('All Startups') }}
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('startup-request.index') }}" class="navItem {{ (request()->is('startup-request*')) ? 'active' : '' }}">
                                {{ __('Funding Requests') }}
                            </a>
                        </li>
                    </ul>
                </li>
            @endif

            <!-- USER MANAGEMENT SECTION -->
            @if(in_array(auth()->user()->role, ['admin', 'super-admin']))
                <li class="sidebar-menu-title">{{ __('USER MANAGEMENT') }}</li>

                <!-- All Users -->
                <li class="{{ (request()->is('admin/users*') || request()->is('admin/profiles*')) ? 'active' : '' }}">
                    <a href="javascript:void(0)" class="navItem">
                        <span class="flex items-center">
                            <iconify-icon class="nav-icon" icon="heroicons-outline:users"></iconify-icon>
                            <span>{{ __('All Users') }}</span>
                        </span>
                        <iconify-icon class="icon-arrow" icon="heroicons-outline:chevron-right"></iconify-icon>
                    </a>
                    <ul class="sidebar-submenu">
                        <li>
                            <a href="{{ route('admin.users.index') }}" class="navItem {{ (request()->is('admin/users*')) ? 'active' : '' }}">
                                {{ __('User Management') }}
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('admin.profiles.index') }}" class="navItem {{ (request()->is('admin/profiles*')) ? 'active' : '' }}">
                                {{ __('Profile Management') }}
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Subscription Users -->
                <li class="{{ (request()->is('admin/subscription-users*')) ? 'active' : '' }}">
                    <a href="{{ route('admin.subscription-users.index') }}" class="navItem">
                        <span class="flex items-center">
                            <iconify-icon class="nav-icon" icon="heroicons-outline:user-circle"></iconify-icon>
                            <span>{{ __('Subscription Users') }}</span>
                        </span>
                    </a>
                </li>
            @endif

            <!-- SUBSCRIPTION & BILLING SECTION -->
            <li class="sidebar-menu-title">{{ __('SUBSCRIPTION & BILLING') }}</li>

            <!-- Subscription Management -->
            <li class="{{ (request()->is('admin/products*') || request()->is('admin/subscriptions*')) ? 'active' : '' }}">
                <a href="javascript:void(0)" class="navItem">
                    <span class="flex items-center">
                        <iconify-icon class="nav-icon" icon="heroicons-outline:credit-card"></iconify-icon>
                        <span>{{ __('Subscriptions') }}</span>
                    </span>
                    <iconify-icon class="icon-arrow" icon="heroicons-outline:chevron-right"></iconify-icon>
                </a>
                <ul class="sidebar-submenu">
                    @if(in_array(auth()->user()->role, ['admin', 'super-admin']))
                        <li>
                            <a href="{{ route('admin.products.index') }}" class="navItem {{ (request()->is('admin/products*')) ? 'active' : '' }}">
                                {{ __('Products & Plans') }}
                            </a>
                        </li>
                    @endif
                    <li>
                        <a href="{{ route('admin.subscriptions.index') }}" class="navItem {{ (request()->is('admin/subscriptions*')) ? 'active' : '' }}">
                            {{ __('Active Subscriptions') }}
                        </a>
                    </li>
                </ul>
            </li>

            <!-- Financial Management -->
            @if(in_array(auth()->user()->role, ['admin', 'super-admin']))
                <li class="{{ (request()->is('admin/financial*') || request()->is('admin/payment-methods*')) ? 'active' : '' }}">
                    <a href="javascript:void(0)" class="navItem">
                        <span class="flex items-center">
                            <iconify-icon class="nav-icon" icon="heroicons-outline:banknotes"></iconify-icon>
                            <span>{{ __('Financial Management') }}</span>
                        </span>
                        <iconify-icon class="icon-arrow" icon="heroicons-outline:chevron-right"></iconify-icon>
                    </a>
                    <ul class="sidebar-submenu">
                        <li>
                            <a href="{{ route('admin.financial.index') }}" class="navItem {{ (request()->is('admin/financial*') && !request()->is('admin/financial/revenue-analytics*')) ? 'active' : '' }}">
                                {{ __('Overview') }}
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('admin.financial.payments') }}" class="navItem {{ (request()->is('admin/financial/payments*')) ? 'active' : '' }}">
                                {{ __('Payments') }}
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('admin.financial.invoices') }}" class="navItem {{ (request()->is('admin/financial/invoices*')) ? 'active' : '' }}">
                                {{ __('Invoices') }}
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('admin.financial.refunds') }}" class="navItem {{ (request()->is('admin/financial/refunds*')) ? 'active' : '' }}">
                                {{ __('Refunds') }}
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('admin.payment-methods.index') }}" class="navItem {{ (request()->is('admin/payment-methods*')) ? 'active' : '' }}">
                                {{ __('Payment Methods') }}
                            </a>
                        </li>
                    </ul>
                </li>
            @endif

            <!-- ANALYTICS & REPORTS SECTION -->
            <li class="sidebar-menu-title">{{ __('ANALYTICS & REPORTS') }}</li>

            <!-- Reports -->
            <li class="{{ (request()->is('admin/reports*')) ? 'active' : '' }}">
                <a href="javascript:void(0)" class="navItem">
                    <span class="flex items-center">
                        <iconify-icon class="nav-icon" icon="heroicons-outline:chart-bar"></iconify-icon>
                        <span>{{ __('Reports') }}</span>
                    </span>
                    <iconify-icon class="icon-arrow" icon="heroicons-outline:chevron-right"></iconify-icon>
                </a>
                <ul class="sidebar-submenu">
                    <li>
                        <a href="{{ route('admin.reports.index') }}" class="navItem {{ (request()->is('admin/reports') && !request()->is('admin/reports/*')) ? 'active' : '' }}">
                            {{ __('Overview') }}
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('admin.reports.subscriptions') }}" class="navItem {{ (request()->is('admin/reports/subscriptions*')) ? 'active' : '' }}">
                            {{ __('Subscriptions') }}
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('admin.reports.revenue') }}" class="navItem {{ (request()->is('admin/reports/revenue*')) ? 'active' : '' }}">
                            {{ __('Revenue') }}
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('admin.reports.users') }}" class="navItem {{ (request()->is('admin/reports/users*')) ? 'active' : '' }}">
                            {{ __('Users') }}
                        </a>
                    </li>
                </ul>
            </li>

            @if(in_array(auth()->user()->role, ['admin', 'super-admin']))
                <!-- Revenue Analytics -->
                <li class="{{ (request()->is('admin/financial/revenue-analytics*')) ? 'active' : '' }}">
                    <a href="{{ route('admin.financial.revenue-analytics') }}" class="navItem">
                        <span class="flex items-center">
                            <iconify-icon class="nav-icon" icon="heroicons-outline:chart-pie"></iconify-icon>
                            <span>{{ __('Revenue Analytics') }}</span>
                        </span>
                    </a>
                </li>

                <!-- Payment Analytics -->
                <li class="{{ (request()->is('admin/payment-methods/analytics*')) ? 'active' : '' }}">
                    <a href="{{ route('admin.payment-methods.analytics') }}" class="navItem">
                        <span class="flex items-center">
                            <iconify-icon class="nav-icon" icon="heroicons-outline:credit-card"></iconify-icon>
                            <span>{{ __('Payment Analytics') }}</span>
                        </span>
                    </a>
                </li>
            @endif

            <!-- CONTENT MANAGEMENT SECTION -->
            @if(in_array(auth()->user()->role, ['admin', 'super-admin']))
                <li class="sidebar-menu-title">{{ __('CONTENT MANAGEMENT') }}</li>

                <!-- Blog Management -->
                <li class="{{ (request()->is('admin/blogs*')) ? 'active' : '' }}">
                    <a href="{{ route('admin.blogs.index') }}" class="navItem">
                        <span class="flex items-center">
                            <iconify-icon class="nav-icon" icon="heroicons-outline:document-text"></iconify-icon>
                            <span>{{ __('Blog Management') }}</span>
                        </span>
                    </a>
                </li>

                <!-- FAQ Management -->
                <li class="{{ (request()->is('admin/faqs*')) ? 'active' : '' }}">
                    <a href="{{ route('admin.faqs.index') }}" class="navItem">
                        <span class="flex items-center">
                            <iconify-icon class="nav-icon" icon="heroicons-outline:question-mark-circle"></iconify-icon>
                            <span>{{ __('FAQ Management') }}</span>
                        </span>
                    </a>
                </li>
            @endif

            <!-- SYSTEM MANAGEMENT SECTION -->
            @if(in_array(auth()->user()->role, ['admin', 'super-admin']))
                <li class="sidebar-menu-title">{{ __('SYSTEM MANAGEMENT') }}</li>

                <!-- Categories Management -->
                <li class="{{ (request()->is('admin/categories*')) ? 'active' : '' }}">
                    <a href="{{ route('admin.categories.index') }}" class="navItem">
                        <span class="flex items-center">
                            <iconify-icon class="nav-icon" icon="heroicons-outline:tag"></iconify-icon>
                            <span>{{ __('Categories') }}</span>
                        </span>
                    </a>
                </li>

                @if(auth()->user()->role === 'super-admin')
                    <!-- Admin Role Management (Super-Admin only) -->
                    <li class="{{ (request()->is('admin/admin-roles*')) ? 'active' : '' }}">
                        <a href="{{ route('admin.roles.index') }}" class="navItem">
                            <span class="flex items-center">
                                <iconify-icon class="nav-icon" icon="heroicons-outline:shield-check"></iconify-icon>
                                <span>{{ __('Admin & Analyst Users') }}</span>
                            </span>
                        </a>
                    </li>
                @endif

                <!-- System Settings -->
                <li class="{{ (request()->is('admin/general-settings*')) ? 'active' : '' }}">
                    <a href="{{ route('admin.general-settings.show') }}" class="navItem">
                        <span class="flex items-center">
                            <iconify-icon class="nav-icon" icon="heroicons-outline:cog-6-tooth"></iconify-icon>
                            <span>{{ __('System Settings') }}</span>
                        </span>
                    </a>
                </li>
            @endif
        </ul>
        <!-- Upgrade Your Business Plan Card Start -->
        <div class="bg-slate-900 mb-10 mt-24 p-4 relative text-center rounded-2xl text-white" id="sidebar_bottom_wizard">
            <img src="{{ asset('images/svg/rabit.svg') }}" alt="" class="mx-auto relative -mt-[73px]">
        </div>
        <!-- Upgrade Your Business Plan Card Start -->
    </div>
</div>
<!-- End: Sidebar -->
