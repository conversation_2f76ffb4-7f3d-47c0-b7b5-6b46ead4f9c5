<div>
    <button
        id="themeMood"
        class="h-[28px] w-[28px] lg:h-[32px] lg:w-[32px] lg:bg-gray-500-f7 bg-slate-50 dark:bg-slate-900 lg:dark:bg-slate-900 dark:text-white text-slate-900 cursor-pointer rounded-full text-[20px] flex flex-col items-center justify-center">
        <iconify-icon
            class="text-slate-800 dark:text-white text-xl dark:block hidden"
            id="moonIcon"
            icon="line-md:sunny-outline-to-moon-alt-loop-transition"></iconify-icon>
        <iconify-icon
            class="text-slate-800 dark:text-white text-xl dark:hidden block"
            id="sunIcon"
            icon="line-md:moon-filled-to-sunny-filled-loop-transition"></iconify-icon>
    </button>
</div>
