<x-app-layout>
    <div class="space-y-8">
        <!-- Breadcrumb -->
        <div>
            <x-breadcrumb :page-title="$pageTitle" :breadcrumb-items="$breadcrumbItems" />
        </div>

        <!-- Profile Overview Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="card">
                <div class="card-body p-6">
                    <div class="flex items-center">
                        <div class="flex-none h-12 w-12 rounded-full bg-primary-500 text-white flex items-center justify-center">
                            <iconify-icon icon="heroicons:user-circle" class="text-xl"></iconify-icon>
                        </div>
                        <div class="flex-1 ltr:ml-4 rtl:mr-4">
                            <div class="text-slate-600 dark:text-slate-300 text-sm font-medium mb-1">Profile Status</div>
                            <div class="text-slate-900 dark:text-white text-xl font-semibold">
                                {{ $stats['profile_completed'] ? 'Complete' : 'Incomplete' }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body p-6">
                    <div class="flex items-center">
                        <div class="flex-none h-12 w-12 rounded-full bg-success-500 text-white flex items-center justify-center">
                            <iconify-icon icon="heroicons:chart-bar" class="text-xl"></iconify-icon>
                        </div>
                        <div class="flex-1 ltr:ml-4 rtl:mr-4">
                            <div class="text-slate-600 dark:text-slate-300 text-sm font-medium mb-1">ESG Score</div>
                            <div class="text-slate-900 dark:text-white text-xl font-semibold">
                                {{ $stats['esg_score'] ? number_format($stats['esg_score'], 1) : 'N/A' }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body p-6">
                    <div class="flex items-center">
                        <div class="flex-none h-12 w-12 rounded-full bg-warning-500 text-white flex items-center justify-center">
                            <iconify-icon icon="heroicons:banknotes" class="text-xl"></iconify-icon>
                        </div>
                        <div class="flex-1 ltr:ml-4 rtl:mr-4">
                            <div class="text-slate-600 dark:text-slate-300 text-sm font-medium mb-1">Funding Sought</div>
                            <div class="text-slate-900 dark:text-white text-xl font-semibold">
                                ${{ $stats['funding_amount_sought'] ? number_format($stats['funding_amount_sought']) : '0' }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body p-6">
                    <div class="flex items-center">
                        <div class="flex-none h-12 w-12 rounded-full bg-info-500 text-white flex items-center justify-center">
                            <iconify-icon icon="heroicons:check-circle" class="text-xl"></iconify-icon>
                        </div>
                        <div class="flex-1 ltr:ml-4 rtl:mr-4">
                            <div class="text-slate-600 dark:text-slate-300 text-sm font-medium mb-1">ESG Status</div>
                            <div class="text-slate-900 dark:text-white text-xl font-semibold">
                                {{ $stats['esg_completed'] ? 'Complete' : 'Pending' }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Management -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Company Profile -->
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Company Profile</h4>
                </div>
                <div class="card-body p-6">
                    @if($startupProfile)
                        <div class="space-y-4">
                            <div class="flex justify-between">
                                <span class="text-slate-600 dark:text-slate-300">Company Name:</span>
                                <span class="font-medium">{{ $startupProfile->company_name ?: 'Not set' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-slate-600 dark:text-slate-300">Business Model:</span>
                                <span class="font-medium">{{ $startupProfile->business_model ? implode(', ', $startupProfile->business_model) : 'Not set' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-slate-600 dark:text-slate-300">Funding Stage:</span>
                                <span class="font-medium">{{ $startupProfile->funding_stage ? ucfirst(str_replace('_', ' ', $startupProfile->funding_stage)) : 'Not set' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-slate-600 dark:text-slate-300">Employee Count:</span>
                                <span class="font-medium">{{ $startupProfile->employee_count ?: 'Not set' }}</span>
                            </div>
                        </div>
                        <div class="mt-6">
                            <a href="/app/startup/profile" class="btn btn-primary w-full">
                                <iconify-icon icon="heroicons:pencil-square" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                                Edit Profile
                            </a>
                        </div>
                    @else
                        <div class="text-center py-8">
                            <iconify-icon icon="heroicons:building-office" class="text-6xl text-slate-400 mb-4"></iconify-icon>
                            <h5 class="text-lg font-semibold mb-2">No Profile Created</h5>
                            <p class="text-slate-600 dark:text-slate-300 mb-6">Create your startup profile to get started with the investment platform.</p>
                            <a href="/app/startup/profile" class="btn btn-primary">
                                <iconify-icon icon="heroicons:plus" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                                Create Profile
                            </a>
                        </div>
                    @endif
                </div>
            </div>

            <!-- ESG Details -->
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">ESG Details</h4>
                </div>
                <div class="card-body p-6">
                    @if($stats['esg_completed'])
                        <div class="space-y-4">
                            <div class="text-center mb-6">
                                <div class="inline-flex items-center justify-center w-20 h-20 rounded-full bg-success-100 text-success-600 mb-4">
                                    <iconify-icon icon="heroicons:check-circle" class="text-3xl"></iconify-icon>
                                </div>
                                <h5 class="text-lg font-semibold">ESG Assessment Complete</h5>
                                <p class="text-slate-600 dark:text-slate-300">Your ESG score: {{ number_format($stats['esg_score'], 1) }}/100</p>
                            </div>
                            
                            @if($startupProfile && $startupProfile->esg_breakdown)
                                <div class="space-y-3">
                                    @foreach($startupProfile->esg_breakdown as $category => $score)
                                        <div class="flex justify-between items-center">
                                            <span class="text-slate-600 dark:text-slate-300 capitalize">{{ $category }}:</span>
                                            <div class="flex items-center space-x-2">
                                                <div class="w-20 bg-slate-200 rounded-full h-2">
                                                    <div class="bg-success-500 h-2 rounded-full" style="width: {{ $score }}%"></div>
                                                </div>
                                                <span class="text-sm font-medium">{{ number_format($score, 1) }}</span>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                            
                            <div class="flex space-x-3 mt-6">
                                <a href="{{ route('startup-profile.esg-results') }}" class="btn btn-outline-primary flex-1">
                                    <iconify-icon icon="heroicons:chart-bar" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                                    View Results
                                </a>
                                <a href="{{ route('startup-profile.esg-questionnaire') }}" class="btn btn-primary flex-1">
                                    <iconify-icon icon="heroicons:arrow-path" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                                    Retake
                                </a>
                            </div>
                        </div>
                    @else
                        <div class="text-center py-8">
                            <iconify-icon icon="heroicons:clipboard-document-list" class="text-6xl text-slate-400 mb-4"></iconify-icon>
                            <h5 class="text-lg font-semibold mb-2">ESG Assessment Pending</h5>
                            <p class="text-slate-600 dark:text-slate-300 mb-6">Complete your ESG questionnaire to get your sustainability score and attract ESG-focused investors.</p>
                            @if($startupProfile)
                                <a href="{{ route('startup-profile.esg-questionnaire') }}" class="btn btn-primary">
                                    <iconify-icon icon="heroicons:clipboard-document-check" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                                    Start ESG Assessment
                                </a>
                            @else
                                <div class="text-sm text-slate-500">Complete your company profile first</div>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Quick Actions</h4>
            </div>
            <div class="card-body p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <a href="/app/startup/discovery" class="flex items-center p-4 bg-slate-50 dark:bg-slate-700 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-600 transition-colors">
                        <div class="flex-none w-10 h-10 rounded-full bg-primary-500 text-white flex items-center justify-center ltr:mr-4 rtl:ml-4">
                            <iconify-icon icon="heroicons:magnifying-glass"></iconify-icon>
                        </div>
                        <div>
                            <div class="font-medium">Discover Investors</div>
                            <div class="text-sm text-slate-600 dark:text-slate-300">Find potential investors</div>
                        </div>
                    </a>
                    
                    <a href="/app/startup/requests" class="flex items-center p-4 bg-slate-50 dark:bg-slate-700 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-600 transition-colors">
                        <div class="flex-none w-10 h-10 rounded-full bg-success-500 text-white flex items-center justify-center ltr:mr-4 rtl:ml-4">
                            <iconify-icon icon="heroicons:envelope"></iconify-icon>
                        </div>
                        <div>
                            <div class="font-medium">Interest Requests</div>
                            <div class="text-sm text-slate-600 dark:text-slate-300">Manage investor requests</div>
                        </div>
                    </a>
                    
                    <a href="/app/startup/analytics" class="flex items-center p-4 bg-slate-50 dark:bg-slate-700 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-600 transition-colors">
                        <div class="flex-none w-10 h-10 rounded-full bg-warning-500 text-white flex items-center justify-center ltr:mr-4 rtl:ml-4">
                            <iconify-icon icon="heroicons:chart-pie"></iconify-icon>
                        </div>
                        <div>
                            <div class="font-medium">Analytics</div>
                            <div class="text-sm text-slate-600 dark:text-slate-300">View performance metrics</div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
