<x-app-layout>
    <div class="space-y-8">
        <!-- Breadcrumb -->
        <div>
            <x-breadcrumb :page-title="$pageTitle" :breadcrumb-items="$breadcrumbItems" />
        </div>

        <!-- ESG Score Overview -->
        <div class="card">
            <div class="card-body p-6">
                <div class="text-center">
                    <div class="inline-flex items-center justify-center w-32 h-32 rounded-full bg-gradient-to-br from-success-400 to-success-600 text-white mb-6">
                        <div class="text-center">
                            <div class="text-3xl font-bold">{{ number_format($startupProfile->esg_score, 1) }}</div>
                            <div class="text-sm opacity-90">out of 100</div>
                        </div>
                    </div>
                    <h3 class="text-2xl font-bold mb-2">Your ESG Score</h3>
                    <p class="text-slate-600 dark:text-slate-300 mb-4">
                        @if($startupProfile->esg_score >= 80)
                            Excellent! Your company demonstrates outstanding ESG practices.
                        @elseif($startupProfile->esg_score >= 60)
                            Good work! Your company shows strong commitment to ESG principles.
                        @elseif($startupProfile->esg_score >= 40)
                            Fair performance. There's room for improvement in your ESG practices.
                        @else
                            Your ESG practices need significant improvement to attract ESG-focused investors.
                        @endif
                    </p>
                    <div class="flex justify-center space-x-3">
                        <a href="{{ route('startup-profile.esg-questionnaire') }}" class="btn btn-primary">
                            <iconify-icon icon="heroicons:arrow-path" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                            Retake Assessment
                        </a>
                        <a href="{{ route('startup-profile.index') }}" class="btn btn-outline-primary">
                            <iconify-icon icon="heroicons:arrow-left" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                            Back to Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Category Breakdown -->
        @if($startupProfile->esg_breakdown)
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">ESG Category Breakdown</h4>
                </div>
                <div class="card-body p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        @foreach($startupProfile->esg_breakdown as $category => $score)
                            <div class="text-center">
                                <div class="inline-flex items-center justify-center w-20 h-20 rounded-full 
                                    @if($category === 'environmental') bg-green-100 text-green-600
                                    @elseif($category === 'social') bg-blue-100 text-blue-600
                                    @else bg-purple-100 text-purple-600
                                    @endif mb-4">
                                    @if($category === 'environmental')
                                        <iconify-icon icon="heroicons:leaf" class="text-2xl"></iconify-icon>
                                    @elseif($category === 'social')
                                        <iconify-icon icon="heroicons:users" class="text-2xl"></iconify-icon>
                                    @else
                                        <iconify-icon icon="heroicons:scale" class="text-2xl"></iconify-icon>
                                    @endif
                                </div>
                                <h5 class="text-lg font-semibold capitalize mb-2">{{ $category }}</h5>
                                <div class="text-2xl font-bold mb-2">{{ number_format($score, 1) }}</div>
                                <div class="w-full bg-slate-200 rounded-full h-2 mb-2">
                                    <div class="
                                        @if($category === 'environmental') bg-green-500
                                        @elseif($category === 'social') bg-blue-500
                                        @else bg-purple-500
                                        @endif h-2 rounded-full transition-all duration-300" 
                                        style="width: {{ $score }}%"></div>
                                </div>
                                <div class="text-sm text-slate-600 dark:text-slate-300">
                                    @if($score >= 80) Excellent
                                    @elseif($score >= 60) Good
                                    @elseif($score >= 40) Fair
                                    @else Needs Improvement
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @endif

        <!-- Detailed Responses -->
        <div class="space-y-6">
            @foreach($responses as $category => $categoryResponses)
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title capitalize">{{ $category }} Responses</h4>
                    </div>
                    <div class="card-body p-6">
                        <div class="space-y-6">
                            @foreach($categoryResponses as $response)
                                <div class="border-b border-slate-200 dark:border-slate-600 pb-4 last:border-b-0 last:pb-0">
                                    <h6 class="font-medium mb-2">{{ $response->esgQuestion->question_text }}</h6>
                                    <div class="flex items-center justify-between">
                                        <div class="text-slate-600 dark:text-slate-300">
                                            <strong>Your Answer:</strong> 
                                            @if($response->esgQuestion->type === 'scale')
                                                {{ $response->response_value }}/5
                                                @switch($response->response_value)
                                                    @case('1') (Poor) @break
                                                    @case('2') (Fair) @break
                                                    @case('3') (Good) @break
                                                    @case('4') (Very Good) @break
                                                    @case('5') (Excellent) @break
                                                @endswitch
                                            @elseif($response->esgQuestion->type === 'yes_no')
                                                {{ ucfirst($response->response_value) }}
                                            @else
                                                {{ $response->response_value }}
                                            @endif
                                        </div>
                                        @if($response->score)
                                            <div class="text-sm">
                                                <span class="badge badge-success-light">
                                                    Score: {{ number_format($response->score, 1) }}
                                                </span>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Recommendations -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Recommendations for Improvement</h4>
            </div>
            <div class="card-body p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    @if(($startupProfile->esg_breakdown['environmental'] ?? 0) < 60)
                        <div class="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                            <div class="flex items-center mb-3">
                                <iconify-icon icon="heroicons:leaf" class="text-green-600 text-xl ltr:mr-2 rtl:ml-2"></iconify-icon>
                                <h6 class="font-semibold text-green-800 dark:text-green-200">Environmental</h6>
                            </div>
                            <ul class="text-sm text-green-700 dark:text-green-300 space-y-1">
                                <li>• Implement energy-efficient practices</li>
                                <li>• Develop waste reduction programs</li>
                                <li>• Consider renewable energy sources</li>
                                <li>• Track and report carbon footprint</li>
                            </ul>
                        </div>
                    @endif

                    @if(($startupProfile->esg_breakdown['social'] ?? 0) < 60)
                        <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                            <div class="flex items-center mb-3">
                                <iconify-icon icon="heroicons:users" class="text-blue-600 text-xl ltr:mr-2 rtl:ml-2"></iconify-icon>
                                <h6 class="font-semibold text-blue-800 dark:text-blue-200">Social</h6>
                            </div>
                            <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                                <li>• Enhance employee benefits</li>
                                <li>• Promote diversity and inclusion</li>
                                <li>• Engage with local communities</li>
                                <li>• Ensure fair labor practices</li>
                            </ul>
                        </div>
                    @endif

                    @if(($startupProfile->esg_breakdown['governance'] ?? 0) < 60)
                        <div class="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                            <div class="flex items-center mb-3">
                                <iconify-icon icon="heroicons:scale" class="text-purple-600 text-xl ltr:mr-2 rtl:ml-2"></iconify-icon>
                                <h6 class="font-semibold text-purple-800 dark:text-purple-200">Governance</h6>
                            </div>
                            <ul class="text-sm text-purple-700 dark:text-purple-300 space-y-1">
                                <li>• Establish clear governance policies</li>
                                <li>• Implement transparency measures</li>
                                <li>• Create ethics and compliance programs</li>
                                <li>• Ensure board diversity</li>
                            </ul>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
