<x-app-layout>
    <div class="space-y-8">
        <!-- Breadcrumb -->
        <div>
            <x-breadcrumb :page-title="$pageTitle" :breadcrumb-items="$breadcrumbItems" />
        </div>

        <!-- ESG Questionnaire Form -->
        <form method="POST" action="{{ route('startup-profile.esg-submit') }}" id="esgForm">
            @csrf
            
            <!-- Progress Indicator -->
            <div class="card mb-6">
                <div class="card-body p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="text-lg font-semibold">ESG Assessment Progress</h4>
                        <span class="text-sm text-slate-600 dark:text-slate-300" id="progressText">0% Complete</span>
                    </div>
                    <div class="w-full bg-slate-200 rounded-full h-2">
                        <div class="bg-primary-500 h-2 rounded-full transition-all duration-300" style="width: 0%" id="progressBar"></div>
                    </div>
                </div>
            </div>

            @foreach($questions as $category => $categoryQuestions)
                <div class="card mb-6">
                    <div class="card-header">
                        <h4 class="card-title capitalize">{{ $category }} Questions</h4>
                        <p class="text-sm text-slate-600 dark:text-slate-300 mt-1">
                            @if($category === 'environmental')
                                Questions about your company's environmental impact and sustainability practices.
                            @elseif($category === 'social')
                                Questions about your company's social responsibility and community impact.
                            @else
                                Questions about your company's governance structure and ethical practices.
                            @endif
                        </p>
                    </div>
                    <div class="card-body p-6">
                        <div class="space-y-6">
                            @foreach($categoryQuestions as $question)
                                <div class="question-item" data-question-id="{{ $question->id }}">
                                    <label class="form-label font-medium">
                                        {{ $question->question_text }}
                                        @if($question->is_required)
                                            <span class="text-danger-500">*</span>
                                        @endif
                                    </label>
                                    
                                    @if($question->help_text)
                                        <p class="text-sm text-slate-600 dark:text-slate-300 mb-3">{{ $question->help_text }}</p>
                                    @endif

                                    @php
                                        $existingResponse = $existingResponses->get($question->id);
                                        $currentValue = $existingResponse ? $existingResponse->response_value : '';
                                    @endphp

                                    @if($question->type === 'yes_no')
                                        <div class="flex space-x-4">
                                            <label class="inline-flex items-center">
                                                <input type="radio" name="responses[{{ $question->id }}][response_value]" 
                                                       value="yes" class="form-radio question-input" 
                                                       {{ $currentValue === 'yes' ? 'checked' : '' }}
                                                       {{ $question->is_required ? 'required' : '' }}>
                                                <span class="ltr:ml-2 rtl:mr-2">Yes</span>
                                            </label>
                                            <label class="inline-flex items-center">
                                                <input type="radio" name="responses[{{ $question->id }}][response_value]" 
                                                       value="no" class="form-radio question-input"
                                                       {{ $currentValue === 'no' ? 'checked' : '' }}
                                                       {{ $question->is_required ? 'required' : '' }}>
                                                <span class="ltr:ml-2 rtl:mr-2">No</span>
                                            </label>
                                        </div>

                                    @elseif($question->type === 'scale')
                                        <div class="space-y-2">
                                            <input type="range" name="responses[{{ $question->id }}][response_value]" 
                                                   min="1" max="5" value="{{ $currentValue ?: '3' }}" 
                                                   class="w-full h-2 bg-slate-200 rounded-lg appearance-none cursor-pointer question-input"
                                                   id="scale_{{ $question->id }}"
                                                   {{ $question->is_required ? 'required' : '' }}>
                                            <div class="flex justify-between text-sm text-slate-600 dark:text-slate-300">
                                                <span>1 - Poor</span>
                                                <span>2 - Fair</span>
                                                <span>3 - Good</span>
                                                <span>4 - Very Good</span>
                                                <span>5 - Excellent</span>
                                            </div>
                                            <div class="text-center">
                                                <span class="text-sm font-medium">Current: <span id="scale_value_{{ $question->id }}">{{ $currentValue ?: '3' }}</span></span>
                                            </div>
                                        </div>

                                    @elseif($question->type === 'multiple_choice')
                                        <div class="space-y-2">
                                            @foreach($question->options as $option)
                                                <label class="inline-flex items-center">
                                                    <input type="radio" name="responses[{{ $question->id }}][response_value]" 
                                                           value="{{ $option }}" class="form-radio question-input"
                                                           {{ $currentValue === $option ? 'checked' : '' }}
                                                           {{ $question->is_required ? 'required' : '' }}>
                                                    <span class="ltr:ml-2 rtl:mr-2">{{ $option }}</span>
                                                </label>
                                            @endforeach
                                        </div>

                                    @elseif($question->type === 'text')
                                        <textarea name="responses[{{ $question->id }}][response_value]" 
                                                  class="form-control question-input" rows="3"
                                                  placeholder="Please provide your answer..."
                                                  {{ $question->is_required ? 'required' : '' }}>{{ $currentValue }}</textarea>
                                    @endif

                                    <input type="hidden" name="responses[{{ $question->id }}][question_id]" value="{{ $question->id }}">
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endforeach

            <!-- Submit Section -->
            <div class="card">
                <div class="card-body p-6">
                    <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                        <div>
                            <h5 class="text-lg font-semibold">Ready to Submit?</h5>
                            <p class="text-slate-600 dark:text-slate-300">
                                Please review your answers before submitting. Your ESG score will be calculated automatically.
                            </p>
                        </div>
                        <div class="flex space-x-3">
                            <a href="{{ route('startup-profile.index') }}" class="btn btn-secondary">
                                <iconify-icon icon="heroicons:arrow-left" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                                Back to Profile
                            </a>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <iconify-icon icon="heroicons:check" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                                Submit ESG Assessment
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('esgForm');
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            const submitBtn = document.getElementById('submitBtn');
            
            // Get all questions
            const questions = document.querySelectorAll('.question-item');
            const totalQuestions = questions.length;
            
            // Update progress
            function updateProgress() {
                let answeredQuestions = 0;
                
                questions.forEach(question => {
                    const inputs = question.querySelectorAll('.question-input');
                    let hasAnswer = false;
                    
                    inputs.forEach(input => {
                        if (input.type === 'radio' && input.checked) {
                            hasAnswer = true;
                        } else if (input.type === 'range' && input.value) {
                            hasAnswer = true;
                        } else if (input.type === 'textarea' && input.value.trim()) {
                            hasAnswer = true;
                        }
                    });
                    
                    if (hasAnswer) {
                        answeredQuestions++;
                    }
                });
                
                const progress = Math.round((answeredQuestions / totalQuestions) * 100);
                progressBar.style.width = progress + '%';
                progressText.textContent = progress + '% Complete';
                
                // Enable submit button when all questions are answered
                submitBtn.disabled = progress < 100;
            }
            
            // Handle scale inputs
            document.querySelectorAll('input[type="range"]').forEach(slider => {
                const questionId = slider.id.replace('scale_', '');
                const valueDisplay = document.getElementById('scale_value_' + questionId);
                
                slider.addEventListener('input', function() {
                    valueDisplay.textContent = this.value;
                    updateProgress();
                });
            });
            
            // Handle other inputs
            document.querySelectorAll('.question-input').forEach(input => {
                input.addEventListener('change', updateProgress);
                input.addEventListener('input', updateProgress);
            });
            
            // Initial progress calculation
            updateProgress();
            
            // Form submission
            form.addEventListener('submit', function(e) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<iconify-icon icon="heroicons:arrow-path" class="text-lg ltr:mr-2 rtl:ml-2 animate-spin"></iconify-icon>Submitting...';
            });
        });
    </script>
    @endpush
</x-app-layout>
