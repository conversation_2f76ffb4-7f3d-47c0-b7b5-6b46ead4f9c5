<form method="POST" action="{{ route('register') }}" class="space-y-4">
    @csrf

    {{-- Name --}}
    <div class="fromGroup">
        <label for="name" class="block capitalize form-label">
            {{ __('Name') }}
        </label>
        <input type="text" name="name" id="name"
            class="form-control py-2 @error('name') !border !border-red-500 @enderror"
            placeholder="{{ __('Type your full name') }}" required autofocus value="{{ old('name') }}">
        <x-input-error :messages="$errors->get('name')" class="mt-2" />
    </div>

    {{-- Email --}}
    <div class="fromGroup">
        <label for="email" class="block capitalize form-label">
            {{ __('Email') }}
        </label>
        <input type="email" name="email" id="email"
            class="form-control py-2 @error('email') !border !border-red-500 @enderror"
            placeholder="{{ __('Type your email') }}" required value="{{ old('email') }}">
        <x-input-error :messages="$errors->get('email')" class="mt-2" />
    </div>

    {{-- Password --}}
    <div class="fromGroup">
        <label for="password" class="block capitalize form-label">
            {{ __('Password') }}
        </label>
        <input type="password" name="password" id="password"
            class="form-control py-2 @error('password') !border !border-red-500 @enderror"
            placeholder="{{ __('Password') }}" required autocomplete="new-password">
        <x-input-error :messages="$errors->get('password')" class="mt-2" />
    </div>

    {{-- Confirm Password --}}
    <div class="fromGroup">
        <label for="password_confirmation" class="block capitalize form-label">
            {{ __('Confirm Password') }}
        </label>
        <input type="password" name="password_confirmation"
            id="password_confirmation"
            class="form-control py-2 @error('password_confirmation') !border !border-red-500 @enderror"
            placeholder="{{ __('Confirm Password') }}" required autocomplete="password_confirmation">
        <x-input-error :messages="$errors->get('password_confirmation')" class="mt-2" />
    </div>

    <button type="submit" class="btn btn-dark block w-full text-center">
        {{ __('Create an account') }}
    </button>
</form>
