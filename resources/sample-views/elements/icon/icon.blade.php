<x-app-layout>
    <div class="space-y-8">
        <div>
          <x-breadcrumb :page-title="$pageTitle" :breadcrumb-items="$breadcrumbItems" />
        </div>

        <div>
        <ul class="flex icon-lists flex-wrap">

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:academic-cap">
                <iconify-icon icon="heroicons:academic-cap"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:adjustments-horizontal">
                <iconify-icon icon="heroicons:adjustments-horizontal"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:adjustments-vertical">
                <iconify-icon icon="heroicons:adjustments-vertical"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:archive-box">
                <iconify-icon icon="heroicons:archive-box"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:archive-box-arrow-down">
                <iconify-icon icon="heroicons:archive-box-arrow-down"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:archive-box-x-mark">
                <iconify-icon icon="heroicons:archive-box-x-mark"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:arrow-down">
                <iconify-icon icon="heroicons:arrow-down"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:arrow-down-circle">
                <iconify-icon icon="heroicons:arrow-down-circle"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:arrow-down-left">
                <iconify-icon icon="heroicons:arrow-down-left"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:arrow-down-on-square">
                <iconify-icon icon="heroicons:arrow-down-on-square"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:arrow-up-tray">
                <iconify-icon icon="heroicons:arrow-up-tray"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:arrows-pointing-in">
                <iconify-icon icon="heroicons:arrows-pointing-in"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:cloud">
                <iconify-icon icon="heroicons:cloud"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:cog">
                <iconify-icon icon="heroicons:cog"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:command-line">
                <iconify-icon icon="heroicons:command-line"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:computer-desktop">
                <iconify-icon icon="heroicons:computer-desktop"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:cpu-chip">
                <iconify-icon icon="heroicons:cpu-chip"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:document-arrow-down">
                <iconify-icon icon="heroicons:document-arrow-down"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:envelope">
                <iconify-icon icon="heroicons:envelope"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:envelope-open">
                <iconify-icon icon="heroicons:envelope-open"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:exclamation-circle">
                <iconify-icon icon="heroicons:exclamation-circle"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:exclamation-triangle">
                <iconify-icon icon="heroicons:exclamation-triangle"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:eye">
                <iconify-icon icon="heroicons:eye"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:eye-dropper">
                <iconify-icon icon="heroicons:eye-dropper"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:film">
                <iconify-icon icon="heroicons:film"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:heart">
                <iconify-icon icon="heroicons:heart"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:inbox">
                <iconify-icon icon="heroicons:inbox"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:inbox">
                <iconify-icon icon="heroicons:inbox"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:information-circle">
                <iconify-icon icon="heroicons:information-circle"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:lifebuoy">
                <iconify-icon icon="heroicons:lifebuoy"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:identification">
                <iconify-icon icon="heroicons:identification"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:key">
                <iconify-icon icon="heroicons:key"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:link">
                <iconify-icon icon="heroicons:link"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:pencil-square">
                <iconify-icon icon="heroicons:pencil-square"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:rectangle-stack">
                <iconify-icon icon="heroicons:rectangle-stack"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:rocket-launch">
                <iconify-icon icon="heroicons:rocket-launch"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:window">
                <iconify-icon icon="heroicons:window"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:wifi">
                <iconify-icon icon="heroicons:wifi"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:wallet">
                <iconify-icon icon="heroicons:wallet"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:variable">
                <iconify-icon icon="heroicons:variable"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:users">
                <iconify-icon icon="heroicons:users"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:user-plus">
                <iconify-icon icon="heroicons:user-plus"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:user-minus">
                <iconify-icon icon="heroicons:user-minus"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:user-group">
                <iconify-icon icon="heroicons:user-group"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:user-circle">
                <iconify-icon icon="heroicons:user-circle"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:user">
                <iconify-icon icon="heroicons:user"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:square-2-stack">
                <iconify-icon icon="heroicons:square-2-stack"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:shopping-bag">
                <iconify-icon icon="heroicons:shopping-bag"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:shield-check">
                <iconify-icon icon="heroicons:shield-check"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:share">
                <iconify-icon icon="heroicons:share"></iconify-icon>
            </button>
            </li>

            <li>
            <button class="toolTip onTop rounded-md bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 text-xl py-3 px-4" data-tippy-content="heroicons:wrench">
                <iconify-icon icon="heroicons:wrench"></iconify-icon>
            </button>
            </li>

            <li class="text-center block w-full mt-6">
            <a href="https://icon-sets.iconify.design" target="_blank" class="btn btn-dark dark:bg-slate-700">
                View All iconify Icons
            </a>
            </li>
        </ul>
        </div>
    </div>
</x-app-layout>
