// app todo css
.app_height {
  height: calc(var(--vh, 1vh) * 100 - 12.1rem);
}
.email-categorie-list {
  label {
    @apply flex cursor-pointer items-center rounded px-2 py-2  text-slate-600 dark:text-slate-300;
  }
  .bar-c {
    @apply ring-0;
  }
  &.active {
    label {
      @apply bg-slate-100 text-slate-900 dark:bg-slate-700 dark:text-slate-200;
    }
    .bar-c {
      @apply ring-4;
    }
  }
}
.email-list {
  li:not(.opened) {
    .read-unread-name {
      @apply font-medium text-slate-900 dark:text-slate-300;
    }
  }
  li.opened {
    @apply dark:text-slate-300;
  }
}

// email
.email-icon {
  @apply flex h-8 w-8 cursor-pointer flex-col items-center justify-center rounded-full bg-slate-100 text-base text-slate-600 dark:bg-slate-900 dark:text-slate-200;
}
[data-stared="true"] {
  .email-fav {
    @apply text-[#FFCE30];
  }
}
[data-stared="false"] {
  .email-fav {
    @apply text-slate-400;
  }
}

.email-fav {
  @apply cursor-pointer;
}

.email-sidebar {
  @apply w-[200px] flex-none transition-all duration-150 lg:w-[260px];
  &.enter-lg {
    @apply absolute top-0 -left-full z-[999] h-full w-[200px] md:w-[260px];
    &.active {
      @apply left-0;
    }
  }
}

.email-overlay {
  @apply invisible absolute  inset-0 z-[-99] w-full  flex-1 rounded-md bg-slate-900 bg-opacity-60 opacity-0 backdrop-blur-sm backdrop-filter dark:bg-slate-900 dark:bg-opacity-60;
  &.active {
    @apply visible z-[99] opacity-100;
  }
}
