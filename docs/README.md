# Subscription System with Pro-rated Refunds

## Overview

A comprehensive Laravel 11 subscription system with Stripe integration, featuring automated billing, pro-rated refunds, account management, and a complete admin dashboard.

## Features

### Core Functionality
- **Subscription Management**: Multiple plans, billing cycles, upgrades/downgrades
- **Payment Processing**: Stripe integration with 3D Secure support
- **Pro-rated Refunds**: Automatic calculation and processing
- **Account Status Management**: Automatic locking based on payment failures
- **Invoice Generation**: Automated invoice creation and management
- **Admin Dashboard**: Comprehensive management interface

### Refund System
- **Full Refunds**: Complete payment refunds
- **Partial Refunds**: Custom amount refunds
- **Pro-rated Refunds**: Time-based refund calculations
- **Audit Trail**: Complete refund history and processing logs
- **Stripe Integration**: Automatic refund processing through Stripe

### Admin Features
- **User Management**: Account status control, subscription management
- **Financial Overview**: Revenue analytics, payment monitoring
- **Refund Processing**: Admin refund tools with approval workflows
- **Product Management**: Subscription plan configuration
- **Reporting**: Comprehensive analytics and reporting

## Installation

### Requirements
- PHP 8.2+
- Laravel 11.x
- MySQL 8.0+
- Stripe Account
- Composer

### Setup Steps

1. **Clone Repository**
   ```bash
   git clone <repository-url>
   cd impactintels
   ```

2. **Install Dependencies**
   ```bash
   composer install
   npm install
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

4. **Database Setup**
   ```bash
   php artisan migrate
   php artisan db:seed
   ```

5. **Stripe Configuration**
   ```bash
   # Add to .env
   STRIPE_KEY=pk_test_...
   STRIPE_SECRET=sk_test_...
   STRIPE_WEBHOOK_SECRET=whsec_...
   ```

6. **Build Assets**
   ```bash
   npm run build
   ```

## API Documentation

### Authentication
All API endpoints require authentication using Laravel Sanctum tokens.

### User Endpoints
- `GET /api/refunds` - Get user refunds
- `GET /api/refunds/statistics` - Get refund statistics
- `POST /api/refunds/request` - Request a refund
- `POST /api/refunds/calculate-prorated` - Calculate prorated refund

### Admin Endpoints
- `GET /api/admin/refunds` - Get all refunds (admin)
- `GET /api/admin/refunds/statistics` - Get admin refund statistics
- `POST /api/admin/refunds/process` - Process refund (admin)
- `POST /api/admin/refunds/process-prorated` - Process prorated refund (admin)

## Database Schema

### Key Tables
- **users**: User accounts with status management
- **subscription_products**: Available subscription plans
- **user_subscriptions**: Active user subscriptions
- **payments**: Payment transactions
- **refunds**: Refund records with audit trail
- **invoices**: Generated invoices

### Relationships
- Users have many subscriptions and payments
- Subscriptions belong to products and users
- Payments belong to users and subscriptions
- Refunds belong to payments and users

## Admin Dashboard

### Access
Navigate to `/admin/dashboard` with admin credentials.

### Key Features
- **Dashboard Overview**: Key metrics and quick actions
- **User Management**: Account control and subscription management
- **Financial Management**: Revenue analytics and payment monitoring
- **Refund Management**: Process and track refunds
- **Product Management**: Configure subscription plans

### Navigation
- **Users**: `/admin/users` - User management
- **Subscriptions**: `/admin/subscriptions` - Subscription overview
- **Financial**: `/admin/financial` - Financial analytics
- **Products**: `/admin/products` - Product management

## Refund System

### Pro-rated Calculation
```php
$refundAmount = ($unusedDays / $totalDays) * $originalAmount;
```

### Refund Types
1. **Full**: Complete payment refund
2. **Partial**: Custom amount refund
3. **Prorated**: Time-based calculation

### Processing Flow
1. Calculate refund amount
2. Create refund record
3. Process through Stripe
4. Update audit trail
5. Send notifications

## Testing

### Running Tests
```bash
# All tests
php artisan test

# Specific test suite
php artisan test tests/Unit/RefundServiceTest.php
php artisan test tests/Feature/RefundApiTest.php
```

### Test Coverage
- Unit tests for RefundService calculations
- Feature tests for API endpoints
- Integration tests for Stripe processing

## Security

### Payment Security
- PCI compliance through Stripe
- Secure token storage
- Encrypted data transmission

### Access Control
- Role-based permissions
- API authentication
- Admin access controls

### Data Protection
- Encrypted sensitive data
- Audit logging
- Secure payment processing

## Deployment

### Production Setup
1. Configure production environment
2. Set up SSL certificates
3. Configure Stripe webhooks
4. Set up monitoring and logging
5. Configure backup systems

### Environment Variables
```bash
APP_ENV=production
STRIPE_KEY=pk_live_...
STRIPE_SECRET=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
```

## Monitoring

### Key Metrics
- Payment success rates
- Refund rates and amounts
- User churn and retention
- System performance

### Alerts
- Failed payment notifications
- High refund rate alerts
- System error notifications
- Security incident alerts

## Support

### Documentation
- [API Documentation](docs/api/refunds.md)
- [User Guide](docs/user-guide/subscription-system.md)
- [Admin Guide](docs/admin-guide/dashboard-management.md)

### Troubleshooting
- Check Stripe webhook delivery
- Verify database migrations
- Monitor application logs
- Review payment processing status

## Contributing

### Development Setup
1. Fork repository
2. Create feature branch
3. Implement changes
4. Add tests
5. Submit pull request

### Code Standards
- Follow PSR-12 coding standards
- Write comprehensive tests
- Document API changes
- Update user documentation

## License

This project is licensed under the MIT License.

## Changelog

### Version 1.0.0
- Initial release with complete subscription system
- Pro-rated refund functionality
- Admin dashboard with DashCode Tailwind CSS
- Comprehensive API documentation
- Full test suite

### Recent Updates
- Laravel 11 upgrade completed
- Enhanced refund system with audit trails
- Improved admin dashboard interface
- Comprehensive documentation added
- Security enhancements implemented
