# Refund System API Documentation

## Overview

The Refund System provides comprehensive functionality for processing, tracking, and managing refunds with support for full, partial, and pro-rated refunds. It integrates with Stripe for payment processing and includes detailed audit trails.

## Authentication

All endpoints require authentication using Laravel Sanctum. Include the bearer token in the Authorization header:

```
Authorization: Bearer {token}
```

Admin endpoints require additional role-based permissions.

## User Refund Endpoints

### Get User Refund Statistics

**GET** `/api/refunds/statistics`

Returns refund statistics for the authenticated user.

**Response:**
```json
{
  "status": "success",
  "data": {
    "total_refunds": 5,
    "total_amount": 15000,
    "successful_refunds": 4,
    "pending_refunds": 1,
    "full_refunds": 2,
    "partial_refunds": 1,
    "prorated_refunds": 2,
    "formatted_total_amount": "$150.00"
  },
  "message": "Refund statistics retrieved successfully"
}
```

### Get User Refunds

**GET** `/api/refunds`

Returns paginated list of refunds for the authenticated user.

**Query Parameters:**
- `page` (optional): Page number for pagination
- `per_page` (optional): Number of items per page (default: 15)

**Response:**
```json
{
  "status": "success",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "amount": 2500,
        "type": "prorated",
        "reason": "subscription_cancellation",
        "status": "succeeded",
        "description": "Prorated refund for early cancellation",
        "created_at": "2025-07-04T08:30:00.000000Z",
        "formatted_amount": "$25.00",
        "proration_percentage": 83.33
      }
    ],
    "total": 1,
    "per_page": 15,
    "last_page": 1
  },
  "message": "Refunds retrieved successfully"
}
```

### Request Refund

**POST** `/api/refunds/request`

Allows users to request a refund for a payment.

**Request Body:**
```json
{
  "payment_id": 123,
  "reason": "requested_by_customer",
  "description": "Product not as expected"
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "amount": 2500,
    "type": "full",
    "reason": "requested_by_customer",
    "status": "pending",
    "description": "Product not as expected"
  },
  "message": "Refund request submitted successfully"
}
```

### Calculate Prorated Refund

**POST** `/api/refunds/calculate-prorated`

Calculates potential prorated refund amount for a subscription.

**Request Body:**
```json
{
  "subscription_id": 456,
  "cancellation_date": "2025-07-15"
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "original_amount": 3000,
    "prorated_amount": 2000,
    "total_days": 30,
    "days_used": 10,
    "unused_days": 20,
    "proration_percentage": 66.67,
    "service_start_date": "2025-07-01",
    "service_end_date": "2025-07-31",
    "cancellation_date": "2025-07-15"
  },
  "message": "Prorated refund calculated successfully"
}
```

## Admin Refund Endpoints

### Get All Refunds (Admin)

**GET** `/api/admin/refunds`

Returns paginated list of all refunds with advanced filtering options.

**Query Parameters:**
- `user_id` (optional): Filter by user ID
- `status` (optional): Filter by status (pending, succeeded, failed)
- `type` (optional): Filter by type (full, partial, prorated)
- `reason` (optional): Filter by reason
- `processed_by` (optional): Filter by admin who processed
- `from_date` (optional): Filter from date (YYYY-MM-DD)
- `to_date` (optional): Filter to date (YYYY-MM-DD)
- `min_amount` (optional): Minimum amount filter (in dollars)
- `max_amount` (optional): Maximum amount filter (in dollars)
- `per_page` (optional): Items per page (default: 20)

### Get Refund Details (Admin)

**GET** `/api/admin/refunds/{refund}`

Returns detailed refund information with audit trail.

**Response:**
```json
{
  "status": "success",
  "data": {
    "refund": {
      "id": 1,
      "user": {...},
      "payment": {...},
      "amount": 2500,
      "type": "prorated",
      "reason": "subscription_cancellation",
      "status": "succeeded",
      "proration_details": {...},
      "internal_notes": "Processed due to early cancellation",
      "processed_by": {...}
    },
    "audit_trail": {
      "refund_id": 1,
      "stripe_refund_id": "re_1234567890",
      "processing_history": [...],
      "status_changes": [...]
    }
  },
  "message": "Refund details retrieved successfully"
}
```

### Process Refund (Admin)

**POST** `/api/admin/refunds/process`

Process a full or partial refund for a payment.

**Request Body:**
```json
{
  "payment_id": 123,
  "type": "partial",
  "amount": 15.50,
  "reason": "duplicate",
  "description": "Duplicate charge identified",
  "internal_notes": "Customer contacted support"
}
```

### Process Prorated Refund (Admin)

**POST** `/api/admin/refunds/process-prorated`

Process a prorated refund for subscription cancellation.

**Request Body:**
```json
{
  "subscription_id": 456,
  "cancellation_date": "2025-07-15",
  "reason": "subscription_cancellation",
  "description": "Early cancellation request",
  "internal_notes": "Customer moving to competitor"
}
```

### Get Refund Statistics (Admin)

**GET** `/api/admin/refunds/statistics`

Returns comprehensive refund statistics for admin dashboard.

**Query Parameters:**
- `from_date` (optional): Filter from date
- `to_date` (optional): Filter to date

**Response:**
```json
{
  "status": "success",
  "data": {
    "total_refunds": 150,
    "total_amount": 45000,
    "successful_refunds": 140,
    "pending_refunds": 8,
    "failed_refunds": 2,
    "by_type": {
      "full": 80,
      "partial": 35,
      "prorated": 35
    },
    "by_reason": {
      "requested_by_customer": 90,
      "subscription_cancellation": 35,
      "duplicate": 15,
      "fraudulent": 5,
      "other": 5
    },
    "recent_refunds": [...],
    "formatted_total_amount": "$450.00"
  },
  "message": "Refund statistics retrieved successfully"
}
```

## Data Models

### Refund Types
- `full`: Complete refund of the payment amount
- `partial`: Partial refund of a specific amount
- `prorated`: Calculated refund based on unused service time

### Refund Reasons
- `requested_by_customer`: Customer-initiated refund request
- `duplicate`: Duplicate payment identified
- `fraudulent`: Fraudulent transaction
- `subscription_cancellation`: Refund due to subscription cancellation
- `prorated_downgrade`: Refund for subscription downgrade
- `prorated_cancellation`: Prorated refund for early cancellation
- `other`: Other reasons

### Refund Status
- `pending`: Refund request submitted but not processed
- `succeeded`: Refund successfully processed
- `failed`: Refund processing failed

## Error Responses

All endpoints return standardized error responses:

```json
{
  "status": "error",
  "message": "Error description",
  "errors": {
    "field_name": ["Validation error message"]
  }
}
```

Common HTTP status codes:
- `400`: Bad Request (validation errors)
- `401`: Unauthorized (authentication required)
- `403`: Forbidden (insufficient permissions)
- `404`: Not Found (resource not found)
- `500`: Internal Server Error (server error)
