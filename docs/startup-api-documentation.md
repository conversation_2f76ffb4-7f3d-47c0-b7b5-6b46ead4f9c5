# Startup API Documentation

## Overview

This document provides comprehensive API documentation for the startup user functionality in the Laravel + React investment platform. The API follows RESTful principles and uses JSON for data exchange.

## Base URL
```
https://impactintels.test/api
```

## Authentication

All API endpoints require authentication using Laravel Sanctum tokens. Include the token in the Authorization header:

```
Authorization: Bearer {your-token}
```

## Content Type

All requests should include the following headers:
```
Content-Type: application/json
Accept: application/json
```

---

## Startup Registration

### Enhanced Startup Registration

**Endpoint:** `POST /api/startup/register`

**Description:** Register a new startup user with enhanced social media integration.

**Request Body:**
```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "password_confirmation": "SecurePass123!",
  "linkedin_url": "https://linkedin.com/in/johndoe",
  "twitter_handle": "@johndoe",
  "facebook_url": "https://facebook.com/johndoe",
  "personal_website": "https://johndoe.com",
  "phone": "+1234567890",
  "city": "San Francisco",
  "country": "United States"
}
```

**Validation Rules:**
- `name`: required, string, max:255, min:2
- `email`: required, email:rfc,dns, unique:users, max:255
- `password`: required, confirmed, min:8 with mixed case, numbers, symbols
- `linkedin_url`: nullable, url, LinkedIn format validation
- `twitter_handle`: nullable, string, max:50, Twitter handle format
- `facebook_url`: nullable, url, Facebook format validation
- `personal_website`: nullable, url, max:255

**Response (201 Created):**
```json
{
  "success": true,
  "message": "Startup registration successful. Please verify your email address.",
  "data": {
    "user": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "startup",
      "email_verified_at": null,
      "phone": "+1234567890",
      "city": "San Francisco",
      "country": "United States"
    },
    "token": "1|abc123...",
    "next_steps": {
      "email_verification": true,
      "company_information": true,
      "funding_information": true,
      "category_selection": true,
      "subscription_required": true,
      "esg_questionnaire": true
    }
  }
}
```

### Get Registration Progress

**Endpoint:** `GET /api/startup/registration-progress`

**Description:** Get the current registration progress for the authenticated startup user.

**Headers:** `Authorization: Bearer {token}`

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "overall_completion_percentage": 42.86,
    "steps": {
      "email_verification": {
        "completed": true,
        "required": true,
        "step": 1
      },
      "company_information": {
        "completed": false,
        "required": true,
        "step": 2,
        "completion_percentage": 0
      },
      "funding_information": {
        "completed": false,
        "required": true,
        "step": 3
      },
      "category_selection": {
        "completed": false,
        "required": true,
        "step": 4,
        "selected_count": 0,
        "minimum_required": 3,
        "maximum_allowed": 5
      },
      "subscription": {
        "completed": false,
        "required": true,
        "step": 5
      },
      "esg_questionnaire": {
        "completed": false,
        "required": true,
        "step": 6,
        "esg_score": null
      },
      "social_media_links": {
        "completed": true,
        "required": false,
        "step": 7,
        "links_count": 3
      }
    },
    "can_access_platform": false
  }
}
```

---

## Social Media Links Management

### Get Social Media Links

**Endpoint:** `GET /api/social-media-links`

**Description:** Retrieve all social media links for the authenticated user.

**Headers:** `Authorization: Bearer {token}`

**Response (200 OK):**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "platform": "linkedin",
      "platform_name": "LinkedIn",
      "platform_icon": "fab fa-linkedin",
      "url": "https://linkedin.com/in/johndoe",
      "formatted_url": "https://linkedin.com/in/johndoe",
      "username": null,
      "is_primary": true,
      "is_public": true,
      "created_at": "2025-07-10T19:30:00.000000Z"
    }
  ]
}
```

### Add Social Media Link

**Endpoint:** `POST /api/social-media-links`

**Description:** Add a new social media link for the authenticated user.

**Headers:** `Authorization: Bearer {token}`

**Request Body:**
```json
{
  "platform": "linkedin",
  "url": "https://linkedin.com/in/johndoe",
  "username": "johndoe",
  "is_primary": true,
  "is_public": true
}
```

**Validation Rules:**
- `platform`: required, enum: ['linkedin', 'twitter', 'facebook', 'instagram', 'website', 'github', 'youtube', 'tiktok']
- `url`: required, url, max:255
- `username`: nullable, string, max:100
- `is_primary`: boolean
- `is_public`: boolean

**Response (201 Created):**
```json
{
  "success": true,
  "message": "Social media link added successfully.",
  "data": {
    "id": 1,
    "platform": "linkedin",
    "platform_name": "LinkedIn",
    "platform_icon": "fab fa-linkedin",
    "url": "https://linkedin.com/in/johndoe",
    "formatted_url": "https://linkedin.com/in/johndoe",
    "username": "johndoe",
    "is_primary": true,
    "is_public": true,
    "created_at": "2025-07-10T19:30:00.000000Z"
  }
}
```

### Update Social Media Link

**Endpoint:** `PUT /api/social-media-links/{id}`

**Description:** Update an existing social media link.

**Headers:** `Authorization: Bearer {token}`

**Request Body:**
```json
{
  "url": "https://linkedin.com/in/johndoe-updated",
  "username": "johndoe-updated",
  "is_primary": true,
  "is_public": false
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Social media link updated successfully.",
  "data": {
    "id": 1,
    "platform": "linkedin",
    "platform_name": "LinkedIn",
    "platform_icon": "fab fa-linkedin",
    "url": "https://linkedin.com/in/johndoe-updated",
    "formatted_url": "https://linkedin.com/in/johndoe-updated",
    "username": "johndoe-updated",
    "is_primary": true,
    "is_public": false,
    "updated_at": "2025-07-10T19:35:00.000000Z"
  }
}
```

### Delete Social Media Link

**Endpoint:** `DELETE /api/social-media-links/{id}`

**Description:** Delete a social media link.

**Headers:** `Authorization: Bearer {token}`

**Response (200 OK):**
```json
{
  "success": true,
  "message": "LinkedIn link deleted successfully."
}
```

### Get Available Platforms

**Endpoint:** `GET /api/social-media-links/platforms`

**Description:** Get list of available social media platforms with validation patterns.

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "linkedin": {
      "name": "LinkedIn",
      "icon": "fab fa-linkedin",
      "url_pattern": "https://linkedin.com/in/{username}",
      "validation_regex": "/^https?:\\/\\/(www\\.)?linkedin\\.com\\/.*$/"
    },
    "twitter": {
      "name": "Twitter",
      "icon": "fab fa-twitter",
      "url_pattern": "https://twitter.com/{username}",
      "validation_regex": "/^https?:\\/\\/(www\\.)?twitter\\.com\\/.*$/"
    }
  }
}
```

---

## Taxonomy Management

### Get Taxonomies by Type

**Endpoint:** `GET /api/taxonomies`

**Description:** Retrieve taxonomies by type with optional search functionality.

**Headers:** `Authorization: Bearer {token}`

**Query Parameters:**
- `type` (required): keyword, tag, category, brand, type, model, industry, technology, market, stage
- `search` (optional): Search term for name or description
- `limit` (optional): Maximum number of results (1-100, default: 50)

**Example:** `GET /api/taxonomies?type=industry&search=tech&limit=20`

**Response (200 OK):**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "FinTech",
      "slug": "fintech",
      "description": "Financial Technology and Services",
      "type": "industry",
      "sort_order": 0
    }
  ],
  "meta": {
    "type": "industry",
    "search": "tech",
    "count": 1
  }
}
```

### Get Startup Profile's Selected Taxonomies

**Endpoint:** `GET /api/taxonomies/user`

**Description:** Get taxonomies selected by the authenticated startup user's profile for a specific type.

**Headers:** `Authorization: Bearer {token}`

**Query Parameters:**
- `type` (required): taxonomy type

**Example:** `GET /api/taxonomies/user?type=category`

**Note:** This endpoint requires startup role and an existing startup profile.

**Response (200 OK):**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Software",
      "slug": "software",
      "description": "Software Development and Technology",
      "type": "category",
      "sort_order": 0
    }
  ],
  "meta": {
    "type": "category",
    "count": 1
  }
}
```

**Error Response (403 Forbidden):**
```json
{
  "success": false,
  "message": "Access denied. Startup role required."
}
```

**Error Response (404 Not Found):**
```json
{
  "success": false,
  "message": "Startup profile not found. Please create your profile first."
}
```

### Attach Taxonomies to Startup Profile

**Endpoint:** `POST /api/taxonomies/attach`

**Description:** Attach taxonomies to the authenticated startup user's profile with business rule validation.

**Headers:** `Authorization: Bearer {token}`

**Request Body:**
```json
{
  "taxonomy_ids": [1, 2, 3],
  "type": "category"
}
```

**Validation Rules:**
- `taxonomy_ids`: required, array, min:1, max:10
- `taxonomy_ids.*`: required, integer, exists:taxonomies,id
- `type`: required, enum

**Business Rules:**
- Categories: max 5
- Keywords: max 10
- Industries: max 3
- Technologies: max 8
- Markets: max 5
- Stages: max 1

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Category selection updated successfully.",
  "data": [
    {
      "id": 1,
      "name": "Software",
      "slug": "software",
      "description": "Software Development and Technology",
      "type": "category"
    }
  ],
  "meta": {
    "type": "category",
    "count": 1,
    "limit": 5
  }
}
```

### Get Taxonomy Suggestions

**Endpoint:** `GET /api/taxonomies/suggestions`

**Description:** Get personalized taxonomy suggestions based on user's existing selections.

**Headers:** `Authorization: Bearer {token}`

**Query Parameters:**
- `type` (required): taxonomy type
- `limit` (optional): max results (1-20, default: 10)

**Example:** `GET /api/taxonomies/suggestions?type=keyword&limit=5`

**Response (200 OK):**
```json
{
  "success": true,
  "data": [
    {
      "id": 15,
      "name": "Machine Learning",
      "slug": "machine-learning",
      "description": "ML Algorithms and Data Science",
      "type": "keyword",
      "relevance_score": 8
    }
  ],
  "meta": {
    "type": "keyword",
    "count": 1,
    "limit": 5
  }
}
```

---

## Company Information Management

### Store Enhanced Company Information

**Endpoint:** `POST /api/startup/company-information`

**Description:** Submit comprehensive company information with taxonomy integration.

**Headers:** `Authorization: Bearer {token}`

**Request Body:**
```json
{
  "company_name": "TechStartup Inc.",
  "company_website": "https://techstartup.com",
  "company_description": "We are building the next generation of AI-powered solutions for small businesses. Our platform helps companies automate their workflows and increase productivity through intelligent automation.",
  "incorporation_status": "incorporated",
  "incorporation_date": "2023-01-15",
  "incorporation_place": "Delaware, USA",
  "company_headquarters": "San Francisco, CA",
  "current_stage": "mvp",
  "employee_count": 12,
  "founding_date": "2022-06-01",
  "company_linkedin": "https://linkedin.com/company/techstartup",
  "industry_keywords": [1, 2, 3],
  "business_model_tags": [4, 5],
  "technology_stack": [6, 7, 8],
  "target_market": [9, 10]
}
```

**Validation Rules:**
- `company_name`: required, string, max:255, unique:startup_profiles
- `company_website`: required, url, max:255
- `company_description`: required, string, min:100, max:2000
- `incorporation_status`: required, enum: ['incorporated', 'not_incorporated', 'in_process']
- `company_headquarters`: required, string, max:255
- `current_stage`: required, enum: ['idea', 'prototype', 'mvp', 'early_revenue', 'growth', 'expansion']
- `employee_count`: required, integer, min:1, max:10000
- `founding_date`: required, date, before_or_equal:today, after:1900-01-01
- `industry_keywords`: required, array, min:3, max:6
- `business_model_tags`: required, array, min:1, max:3
- `target_market`: required, array, min:1, max:5

**Response (201 Created):**
```json
{
  "success": true,
  "message": "Company information saved successfully.",
  "data": {
    "profile": {
      "id": 1,
      "company_name": "TechStartup Inc.",
      "website": "https://techstartup.com",
      "company_description": "We are building the next generation...",
      "incorporation_status": "incorporated",
      "incorporation_date": "2023-01-15",
      "incorporation_place": "Delaware, USA",
      "company_headquarters": "San Francisco, CA",
      "current_stage": "mvp",
      "employee_count": 12,
      "founding_date": "2022-06-01",
      "company_linkedin": "https://linkedin.com/company/techstartup",
      "profile_completion_percentage": 85.5,
      "profile_completed": true,
      "created_at": "2025-07-10T19:30:00.000000Z",
      "updated_at": "2025-07-10T19:30:00.000000Z"
    },
    "completion_percentage": 85.5,
    "next_steps": {
      "funding_information": true,
      "document_uploads": true,
      "category_selection": false,
      "subscription_required": true,
      "esg_questionnaire": true
    }
  }
}
```

---

## Error Responses

### Validation Error (422 Unprocessable Entity)
```json
{
  "success": false,
  "message": "The given data was invalid.",
  "errors": {
    "email": ["The email has already been taken."],
    "password": ["The password must contain at least one uppercase letter."]
  }
}
```

### Authentication Error (401 Unauthorized)
```json
{
  "success": false,
  "message": "Unauthenticated."
}
```

### Authorization Error (403 Forbidden)
```json
{
  "success": false,
  "message": "Access denied. Startup role required."
}
```

### Not Found Error (404 Not Found)
```json
{
  "success": false,
  "message": "Social media link not found."
}
```

### Server Error (500 Internal Server Error)
```json
{
  "success": false,
  "message": "Registration failed. Please try again.",
  "error": "Internal server error"
}
```

---

## Rate Limiting

API endpoints are rate limited to prevent abuse:

- **Registration endpoints**: 5 requests per hour per IP
- **General API endpoints**: 60 requests per minute per user
- **File upload endpoints**: 10 requests per minute per user

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: 1625097600
```

---

## File Upload Guidelines

When implementing file upload functionality:

1. **Company Logo**:
   - Max size: 2MB
   - Formats: JPG, PNG, SVG
   - Collection: 'company_logo'

2. **Pitch Deck**:
   - Max size: 10MB
   - Formats: PDF, PPT, PPTX
   - Collection: 'pitch_deck'

3. **Business Plan**:
   - Max size: 15MB
   - Formats: PDF, DOC, DOCX
   - Collection: 'business_plan'

4. **Financial Projections**:
   - Max size: 5MB
   - Formats: PDF, XLS, XLSX, CSV
   - Collection: 'financial_projections'

---

## Testing

### Test Environment
- **Base URL**: `https://impactintels.test/api`
- **Test Credentials**:
  - Email: `<EMAIL>`
  - Password: `investor credential password`

### Test Cards (Stripe Sandbox)
- **Success**: `************** 4242`
- **Declined**: `************** 0002`
- **3D Secure**: `************** 3155`

### Sample Test Data

**Sample Taxonomy IDs by Type:**
- Industries: 1-15 (FinTech, HealthTech, EdTech, etc.)
- Technologies: 16-30 (AI, ML, Blockchain, etc.)
- Business Models: 31-45 (B2B, B2C, SaaS, etc.)
- Markets: 46-60 (Global, North America, Enterprise, etc.)
- Stages: 61-75 (Pre-Seed, Seed, Series A, etc.)
- Categories: 76-90 (Software, Hardware, Services, etc.)

---

## Changelog

### Version 1.0.0 (2025-07-10)
- Initial API documentation
- Enhanced startup registration with social media integration
- Taxonomy system integration
- Company information management
- Social media links management
- Comprehensive validation and error handling
```
