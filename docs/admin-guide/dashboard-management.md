# Admin Dashboard Management Guide

## Overview

The Admin Dashboard provides comprehensive tools for managing users, subscriptions, payments, and refunds. Built with Laravel Blade templates and DashCode Tailwind CSS, it offers a modern, responsive interface for administrative tasks.

## Dashboard Access

### Authentication Requirements
- Admin role required (`role = 'admin'`)
- Valid authentication session
- Proper permissions via Spatie Permission system

### Navigation
Access the admin dashboard at: `/admin/dashboard`

## Main Dashboard

### Key Metrics Overview
- **Total Users**: Active user count with growth indicators
- **Active Subscriptions**: Current subscription count and trends
- **Monthly Revenue**: Current month revenue with comparison
- **Failed Payments**: Recent payment failures requiring attention

### Quick Actions
- **User Management**: Direct access to user listing
- **Subscription Overview**: View active subscriptions
- **Financial Reports**: Access revenue analytics
- **System Health**: Monitor payment processing status

## User Management

### User Listing (`/admin/users`)

#### Features
- **Paginated User List**: 20 users per page with search functionality
- **Account Status Indicators**: Visual status badges (Active, Locked, Suspended)
- **Subscription Information**: Current plan and billing status
- **Quick Actions**: Lock/unlock, view details, manage subscriptions

#### Search and Filtering
- **Search by**: Name, email, user ID
- **Filter by**: Account status, subscription status, registration date
- **Sort by**: Registration date, last login, subscription value

### User Details (`/admin/users/{user}`)

#### User Information
- **Profile Details**: Name, email, registration date, last login
- **Account Status**: Current status with lock/unlock controls
- **Subscription History**: Complete subscription timeline
- **Payment History**: All payments and refunds

#### Available Actions
- **Lock Account**: Immediately lock user account with reason
- **Unlock Account**: Restore account access
- **Manage Subscription**: Modify current subscription
- **Process Refund**: Initiate refund for payments
- **View Audit Trail**: Complete activity history

### Account Status Management

#### Locking Accounts
1. **Select User**: Navigate to user details page
2. **Lock Account**: Click "Lock Account" button
3. **Provide Reason**: Enter detailed reason for locking
4. **Confirm Action**: Account immediately locked
5. **User Notification**: Automatic email notification sent

#### Unlocking Accounts
1. **Review Lock Reason**: Check why account was locked
2. **Verify Resolution**: Ensure underlying issue resolved
3. **Unlock Account**: Click "Unlock Account" button
4. **Add Notes**: Document reason for unlocking
5. **User Notification**: Automatic unlock notification

## Subscription Management

### Subscription Overview (`/admin/subscriptions`)

#### Active Subscriptions
- **Subscription List**: All active subscriptions with details
- **Revenue Tracking**: Monthly recurring revenue calculations
- **Churn Analysis**: Cancellation rates and trends
- **Upgrade/Downgrade Tracking**: Plan change analytics

#### Subscription Actions
- **View Details**: Complete subscription information
- **Modify Plan**: Change subscription plan or billing frequency
- **Cancel Subscription**: Process cancellation with refund options
- **Pause Subscription**: Temporarily suspend billing

### Product Management (`/admin/products`)

#### Subscription Products
- **Product Listing**: All available subscription plans
- **Pricing Management**: Update prices and billing frequencies
- **Feature Configuration**: Define plan features and limits
- **Product Analytics**: Track adoption and performance

#### Creating New Products
1. **Product Details**: Name, description, features
2. **Pricing Structure**: Set prices for different billing cycles
3. **Stripe Integration**: Configure Stripe product and prices
4. **Feature Limits**: Define usage limits and restrictions
5. **Activation**: Make product available to users

## Financial Management

### Financial Overview (`/admin/financial`)

#### Revenue Analytics
- **Monthly Revenue Chart**: Interactive revenue trends
- **Payment Status Distribution**: Success vs. failure rates
- **Subscription Metrics**: MRR, ARR, churn rate calculations
- **Refund Analytics**: Refund rates and impact on revenue

#### Key Performance Indicators
- **Monthly Recurring Revenue (MRR)**: Current month recurring revenue
- **Annual Recurring Revenue (ARR)**: Projected annual revenue
- **Customer Lifetime Value (CLV)**: Average customer value
- **Churn Rate**: Monthly subscription cancellation rate

### Payment Management (`/admin/financial/payments`)

#### Payment Monitoring
- **Recent Payments**: Latest payment transactions
- **Failed Payments**: Payments requiring attention
- **Payment Methods**: User payment method overview
- **Retry Management**: Failed payment retry status

#### Payment Actions
- **Retry Failed Payment**: Manually retry failed payments
- **Update Payment Method**: Assist users with payment updates
- **Process Refund**: Initiate refund for successful payments
- **View Payment Details**: Complete payment information

### Invoice Management (`/admin/financial/invoices`)

#### Invoice Overview
- **Invoice Listing**: All generated invoices
- **Payment Status**: Paid, pending, overdue invoices
- **Overdue Management**: Track and manage overdue accounts
- **Invoice Generation**: Manual invoice creation if needed

#### Invoice Actions
- **View Invoice**: Complete invoice details
- **Download PDF**: Generate PDF for customer
- **Mark as Paid**: Manual payment recording
- **Send Reminder**: Email payment reminders

## Refund Management

### Refund Overview (`/admin/financial/refunds`)

#### Refund Dashboard
- **Refund Statistics**: Total refunds, amounts, success rates
- **Recent Refunds**: Latest refund transactions
- **Pending Requests**: User-initiated refund requests
- **Refund Analytics**: Trends and reason analysis

#### Refund Processing
1. **Review Request**: Evaluate refund request details
2. **Verify Eligibility**: Check payment status and refund policy
3. **Calculate Amount**: Use pro-rated calculator for subscriptions
4. **Process Refund**: Execute through Stripe integration
5. **Update Records**: Maintain audit trail and notifications

### Pro-rated Refund Calculator

#### Automatic Calculation
- **Service Period**: Identifies current billing cycle
- **Usage Calculation**: Days used vs. total billing period
- **Refund Amount**: Precise pro-rated calculation
- **Stripe Processing**: Automatic refund execution

#### Manual Override
- **Custom Amounts**: Override calculated amounts if needed
- **Reason Documentation**: Detailed reason for manual adjustments
- **Approval Workflow**: Additional approval for large refunds
- **Audit Trail**: Complete record of all refund decisions

## System Administration

### User Permissions

#### Role Management
- **Admin Roles**: Full system access
- **Manager Roles**: Limited administrative access
- **Support Roles**: User assistance capabilities
- **Custom Permissions**: Granular permission control

#### Permission Categories
- **User Management**: Create, edit, lock/unlock users
- **Financial Access**: View and process payments/refunds
- **Subscription Control**: Modify subscriptions and products
- **System Configuration**: Access system settings

### Audit Trail

#### Activity Logging
- **User Actions**: All user account modifications
- **Payment Processing**: Complete payment and refund history
- **Subscription Changes**: Plan modifications and cancellations
- **Admin Actions**: All administrative activities

#### Compliance Reporting
- **Financial Reports**: Revenue and refund summaries
- **User Activity**: Account status changes and access logs
- **Payment Compliance**: PCI and regulatory compliance data
- **Audit Exports**: CSV/PDF exports for external auditing

## Security and Compliance

### Data Protection
- **Encryption**: All sensitive data encrypted at rest and in transit
- **Access Control**: Role-based access with audit logging
- **Payment Security**: PCI compliance through Stripe integration
- **Data Retention**: Configurable data retention policies

### Monitoring and Alerts
- **Failed Payment Alerts**: Immediate notification of payment issues
- **Security Alerts**: Suspicious activity notifications
- **System Health**: Performance and uptime monitoring
- **Compliance Alerts**: Regulatory compliance notifications

## Best Practices

### Daily Operations
1. **Monitor Failed Payments**: Check and retry failed payments
2. **Review Refund Requests**: Process pending refund requests
3. **Check Account Locks**: Review and resolve locked accounts
4. **Verify System Health**: Ensure all systems operational

### Weekly Reviews
1. **Revenue Analysis**: Review weekly revenue trends
2. **User Growth**: Monitor user acquisition and churn
3. **Refund Analysis**: Analyze refund patterns and reasons
4. **Performance Metrics**: Review system performance indicators

### Monthly Tasks
1. **Financial Reporting**: Generate monthly financial reports
2. **Compliance Review**: Ensure regulatory compliance
3. **Security Audit**: Review security logs and access patterns
4. **System Updates**: Apply security patches and updates

## Troubleshooting

### Common Issues

#### Payment Processing
- **Stripe Connection**: Verify Stripe API connectivity
- **Webhook Processing**: Check webhook delivery status
- **3D Secure Issues**: Monitor authentication failures
- **Currency Conversion**: Verify multi-currency handling

#### User Account Issues
- **Login Problems**: Check authentication system status
- **Permission Errors**: Verify role and permission assignments
- **Account Locks**: Review automatic locking triggers
- **Data Synchronization**: Ensure data consistency

#### System Performance
- **Database Performance**: Monitor query performance
- **API Response Times**: Check endpoint response times
- **Memory Usage**: Monitor system resource utilization
- **Error Rates**: Track application error frequencies

### Support Escalation

#### Internal Support
- **Technical Issues**: Development team escalation
- **Financial Disputes**: Finance team involvement
- **Legal Compliance**: Legal team consultation
- **Security Incidents**: Security team immediate notification

#### External Support
- **Stripe Issues**: Direct Stripe support contact
- **Infrastructure**: Hosting provider support
- **Third-party Services**: Vendor-specific support channels
- **Regulatory Questions**: Compliance consultant contact
