<?php

namespace App\Notifications;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AccountStatusChangedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected string $newStatus;
    protected string $reason;
    protected ?string $actionUrl;

    /**
     * Create a new notification instance.
     */
    public function __construct(string $newStatus, string $reason = '', ?string $actionUrl = null)
    {
        $this->newStatus = $newStatus;
        $this->reason = $reason;
        $this->actionUrl = $actionUrl;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $message = (new MailMessage)
            ->greeting('Hello ' . $notifiable->name . ',');

        switch ($this->newStatus) {
            case 'locked':
                $message->subject('Account Locked - Action Required')
                    ->line('Your account has been temporarily locked.')
                    ->when($this->reason, function ($msg) {
                        return $msg->line('Reason: ' . $this->reason);
                    })
                    ->line('This may be due to failed payments, security concerns, or policy violations.')
                    ->line('Please contact our support team to resolve this issue and restore access to your account.');
                break;

            case 'unlocked':
                $message->subject('Account Unlocked - Welcome Back!')
                    ->line('Great news! Your account has been unlocked and is now active.')
                    ->when($this->reason, function ($msg) {
                        return $msg->line('Resolution: ' . $this->reason);
                    })
                    ->line('You can now access all features and services normally.');
                break;

            case 'suspended':
                $message->subject('Account Suspended')
                    ->line('Your account has been suspended.')
                    ->when($this->reason, function ($msg) {
                        return $msg->line('Reason: ' . $this->reason);
                    })
                    ->line('During suspension, access to services may be limited.')
                    ->line('Please contact support for more information.');
                break;

            case 'active':
                $message->subject('Account Activated')
                    ->line('Your account is now active and fully operational.')
                    ->line('You have access to all features and services.');
                break;

            default:
                $message->subject('Account Status Updated')
                    ->line('Your account status has been updated to: ' . ucfirst($this->newStatus))
                    ->when($this->reason, function ($msg) {
                        return $msg->line('Details: ' . $this->reason);
                    });
        }

        if ($this->actionUrl) {
            $message->action('Take Action', $this->actionUrl);
        } else {
            $message->action('Visit Dashboard', url('/dashboard'));
        }

        return $message->salutation('Best regards, ' . config('app.name') . ' Team');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'account_status_changed',
            'new_status' => $this->newStatus,
            'reason' => $this->reason,
            'action_url' => $this->actionUrl ?: url('/dashboard'),
            'changed_at' => now()->toISOString(),
            'message' => $this->getStatusMessage(),
        ];
    }

    /**
     * Get the notification's database type.
     */
    public function databaseType(object $notifiable): string
    {
        return 'account_status_changed';
    }

    /**
     * Get a user-friendly status message.
     */
    protected function getStatusMessage(): string
    {
        switch ($this->newStatus) {
            case 'locked':
                return 'Your account has been locked. Please contact support.';
            case 'unlocked':
                return 'Your account has been unlocked. Welcome back!';
            case 'suspended':
                return 'Your account has been suspended. Contact support for details.';
            case 'active':
                return 'Your account is now active and fully operational.';
            default:
                return 'Your account status has been updated to: ' . ucfirst($this->newStatus);
        }
    }
}
