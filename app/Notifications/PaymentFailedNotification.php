<?php

namespace App\Notifications;

use App\Models\Payment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class PaymentFailedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected Payment $payment;
    protected string $failureReason;

    /**
     * Create a new notification instance.
     */
    public function __construct(Payment $payment, string $failureReason = '')
    {
        $this->payment = $payment;
        $this->failureReason = $failureReason;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $subscriptionName = $this->payment->userSubscription?->subscriptionProduct?->name ?? 'Subscription';
        
        return (new MailMessage)
            ->subject('Payment Failed - Action Required')
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('We were unable to process your payment for ' . $subscriptionName . '.')
            ->line('Amount: $' . number_format($this->payment->amount, 2))
            ->line('Payment Date: ' . $this->payment->created_at->format('M j, Y'))
            ->when($this->failureReason, function ($message) {
                return $message->line('Reason: ' . $this->failureReason);
            })
            ->line('To avoid any interruption to your service, please update your payment method or retry the payment.')
            ->action('Update Payment Method', url('/dashboard/payment-methods'))
            ->line('If you continue to experience issues, please contact our support team.')
            ->salutation('Best regards, ' . config('app.name') . ' Team');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'payment_failed',
            'payment_id' => $this->payment->id,
            'amount' => $this->payment->amount,
            'currency' => $this->payment->currency,
            'subscription_name' => $this->payment->userSubscription?->subscriptionProduct?->name,
            'failure_reason' => $this->failureReason,
            'payment_date' => $this->payment->created_at->toISOString(),
            'action_url' => url('/dashboard/payment-methods'),
            'message' => 'Payment failed for ' . ($this->payment->userSubscription?->subscriptionProduct?->name ?? 'subscription') . '. Please update your payment method.',
        ];
    }

    /**
     * Get the notification's database type.
     */
    public function databaseType(object $notifiable): string
    {
        return 'payment_failed';
    }
}
