<?php

namespace App\Notifications;

use App\Models\Payment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class PaymentRequiresActionNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected Payment $payment;
    protected string $actionUrl;

    /**
     * Create a new notification instance.
     */
    public function __construct(Payment $payment, string $actionUrl)
    {
        $this->payment = $payment;
        $this->actionUrl = $actionUrl;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $subscriptionName = $this->payment->userSubscription?->subscriptionProduct?->name ?? 'Subscription';
        
        return (new MailMessage)
            ->subject('Payment Authentication Required - 3D Secure')
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('Your payment for ' . $subscriptionName . ' requires additional authentication.')
            ->line('Amount: $' . number_format($this->payment->amount, 2))
            ->line('This is a security measure required by your bank to protect your payment.')
            ->line('Please complete the authentication process to finalize your payment.')
            ->action('Complete Payment Authentication', $this->actionUrl)
            ->line('This link will expire in 24 hours. If you do not complete the authentication, your payment will be cancelled.')
            ->line('If you did not initiate this payment, please contact us immediately.')
            ->salutation('Best regards, ' . config('app.name') . ' Team');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'payment_requires_action',
            'payment_id' => $this->payment->id,
            'amount' => $this->payment->amount,
            'currency' => $this->payment->currency,
            'subscription_name' => $this->payment->userSubscription?->subscriptionProduct?->name,
            'action_url' => $this->actionUrl,
            'payment_date' => $this->payment->created_at->toISOString(),
            'expires_at' => now()->addHours(24)->toISOString(),
            'message' => 'Payment authentication required for ' . ($this->payment->userSubscription?->subscriptionProduct?->name ?? 'subscription') . '. Complete 3D Secure verification.',
        ];
    }

    /**
     * Get the notification's database type.
     */
    public function databaseType(object $notifiable): string
    {
        return 'payment_requires_action';
    }
}
