<?php

namespace App\Notifications;

use App\Models\Payment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class PaymentSucceededNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected Payment $payment;

    /**
     * Create a new notification instance.
     */
    public function __construct(Payment $payment)
    {
        $this->payment = $payment;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $subscriptionName = $this->payment->userSubscription?->subscriptionProduct?->name ?? 'Subscription';
        
        return (new MailMessage)
            ->subject('Payment Successful - Thank You!')
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('Thank you! Your payment for ' . $subscriptionName . ' has been processed successfully.')
            ->line('Amount: $' . number_format($this->payment->amount, 2))
            ->line('Payment Date: ' . $this->payment->created_at->format('M j, Y'))
            ->line('Transaction ID: ' . $this->payment->stripe_payment_intent_id)
            ->line('Your subscription is now active and you can continue enjoying our services.')
            ->action('View Payment Details', url('/dashboard/payments/' . $this->payment->id))
            ->line('If you have any questions about this payment, please contact our support team.')
            ->salutation('Best regards, ' . config('app.name') . ' Team');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'payment_succeeded',
            'payment_id' => $this->payment->id,
            'amount' => $this->payment->amount,
            'currency' => $this->payment->currency,
            'subscription_name' => $this->payment->userSubscription?->subscriptionProduct?->name,
            'transaction_id' => $this->payment->stripe_payment_intent_id,
            'payment_date' => $this->payment->created_at->toISOString(),
            'action_url' => url('/dashboard/payments/' . $this->payment->id),
            'message' => 'Payment successful for ' . ($this->payment->userSubscription?->subscriptionProduct?->name ?? 'subscription') . '. Thank you!',
        ];
    }

    /**
     * Get the notification's database type.
     */
    public function databaseType(object $notifiable): string
    {
        return 'payment_succeeded';
    }
}
