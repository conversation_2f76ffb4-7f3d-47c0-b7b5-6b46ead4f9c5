<?php

namespace App\Traits;

use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Builder;

trait DataTableTrait
{
    /**
     * Handle DataTable server-side processing
     */
    protected function processDataTable(Request $request, Builder $query, array $columns, callable $dataFormatter)
    {
        $draw = $request->get('draw');
        $start = $request->get('start', 0);
        $length = $request->get('length', 10);
        $search = $request->get('search')['value'] ?? '';
        $orderColumn = $request->get('order')[0]['column'] ?? 0;
        $orderDir = $request->get('order')[0]['dir'] ?? 'desc';

        // Clone query for total count
        $totalQuery = clone $query;
        $totalRecords = $totalQuery->count();

        // Apply search if provided
        if (!empty($search)) {
            $query = $this->applySearch($query, $search);
        }

        // Get filtered count
        $filteredRecords = $query->count();

        // Apply sorting
        $query = $this->applySorting($query, $columns, $orderColumn, $orderDir);

        // Apply pagination
        $results = $query->skip($start)->take($length)->get();

        // Format data using the provided formatter
        $data = [];
        foreach ($results as $index => $item) {
            $data[] = $dataFormatter($item, $start + $index + 1);
        }

        return response()->json([
            'draw' => intval($draw),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ]);
    }

    /**
     * Apply search filtering to query
     */
    protected function applySearch(Builder $query, string $search)
    {
        // This method should be overridden in each controller
        // to implement specific search logic
        return $query;
    }

    /**
     * Apply sorting to query
     */
    protected function applySorting(Builder $query, array $columns, int $orderColumn, string $orderDir)
    {
        // Validate order direction
        $orderDir = in_array(strtolower($orderDir), ['asc', 'desc']) ? strtolower($orderDir) : 'desc';

        // Get column name from mapping
        $sortColumn = $columns[$orderColumn] ?? null;

        if (!$sortColumn) {
            // Default sorting if column not found or null
            return $query->orderBy('created_at', $orderDir);
        }

        // Handle different types of sorting
        if (str_contains($sortColumn, '.')) {
            // Related model sorting (e.g., 'startup_profile.company_name')
            [$relation, $field] = explode('.', $sortColumn, 2);
            $tableName = $this->getTableName($relation);

            // Check if join already exists to avoid duplicate joins
            $joins = $query->getQuery()->joins ?? [];
            $joinExists = false;
            foreach ($joins as $join) {
                if ($join->table === $tableName) {
                    $joinExists = true;
                    break;
                }
            }

            if (!$joinExists) {
                $query->leftJoin($tableName, function($join) use ($tableName) {
                    $join->on('users.id', '=', $tableName . '.user_id');
                });
            }

            return $query->orderBy($tableName . '.' . $field, $orderDir)
                         ->select('users.*');
        } else {
            // Direct column sorting
            return $query->orderBy($sortColumn, $orderDir);
        }
    }

    /**
     * Get table name for relation
     */
    protected function getTableName(string $relation): string
    {
        return match($relation) {
            'startup_profile' => 'startup_profiles',
            'investor_profile' => 'investor_profiles',
            default => $relation
        };
    }

    /**
     * Generate status badge HTML
     */
    protected function getStatusBadge(string $status): string
    {
        $badgeClass = match($status) {
            'pending' => 'bg-warning-500',
            'approved' => 'bg-success-500',
            'rejected' => 'bg-danger-500',
            default => 'bg-slate-500'
        };

        return '<span class="badge ' . $badgeClass . ' text-white capitalize">' . $status . '</span>';
    }

    /**
     * Generate dropdown action buttons HTML
     */
    protected function getActionButtons(string $routePrefix, $model, array $actions = ['show', 'edit']): string
    {
        $buttons = '<div class="relative">
                        <div class="dropdown relative">
                            <button class="text-xl text-center block w-full" type="button"
                                    id="tableDropdownMenuButton' . $model->id . '" data-bs-toggle="dropdown"
                                    aria-expanded="false">
                                <iconify-icon icon="heroicons-outline:dots-vertical"></iconify-icon>
                            </button>
                            <ul class="dropdown-menu min-w-[120px] absolute text-sm text-slate-700 dark:text-white hidden bg-white dark:bg-slate-700
                              shadow z-[2] float-left overflow-hidden list-none text-left rounded-lg mt-1 m-0 bg-clip-padding border-none">';

        foreach ($actions as $action) {
            $actionLabel = $action === 'show' ? 'View' : ucfirst($action);
            $routeName = $routePrefix . '.' . $action;
            $buttons .= '<li>
                            <a href="' . route($routeName, $model) . '"
                               class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600 dark:hover:text-white">
                                ' . $actionLabel . '
                            </a>
                        </li>';
        }

        $buttons .= '</ul>
                    </div>
                </div>';

        return $buttons;
    }

    /**
     * Generate company name cell with avatar
     */
    protected function getCompanyNameCell($user, string $fallbackImage = 'images/all-img/customer_1.png'): string
    {
        $avatarUrl = $user->getFirstMediaUrl('profile-image') ?: asset($fallbackImage);
        $companyName = $user->startupProfile->company_name ?? $user->name;
        
        return '<span class="flex">
                    <span class="w-7 h-7 rounded-full ltr:mr-3 rtl:ml-3 flex-none">
                        <img src="' . $avatarUrl . '" alt="' . $user->name . '" class="object-cover w-full h-full rounded-full">
                    </span>
                    <span class="text-sm text-slate-600 dark:text-slate-300 capitalize">' . $companyName . '</span>
                </span>';
    }
}
