<?php

namespace App\Policies;

use App\Models\User;
use App\Models\UserSubscription;

class UserSubscriptionPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        // Users can only view their own subscriptions
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, UserSubscription $userSubscription): bool
    {
        // Users can only view their own subscriptions
        return $user->id === $userSubscription->user_id;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        // All authenticated users can create subscriptions
        return true;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, UserSubscription $userSubscription): bool
    {
        // Users can only update their own active subscriptions
        return $user->id === $userSubscription->user_id && 
               $userSubscription->isActive();
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, UserSubscription $userSubscription): bool
    {
        // Users can only cancel their own active subscriptions
        return $user->id === $userSubscription->user_id && 
               $userSubscription->isActive();
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, UserSubscription $userSubscription): bool
    {
        // Only admins can restore subscriptions
        return $user->hasPermissionTo('admin subscription management') || 
               $user->hasRole(['super-admin', 'admin']);
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, UserSubscription $userSubscription): bool
    {
        // Only super-admin can permanently delete subscriptions
        return $user->hasRole('super-admin');
    }
}
