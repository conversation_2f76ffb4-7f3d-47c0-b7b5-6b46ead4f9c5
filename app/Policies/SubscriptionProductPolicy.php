<?php

namespace App\Policies;

use App\Models\SubscriptionProduct;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class SubscriptionProductPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        // Allow all authenticated users to view subscription products
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, SubscriptionProduct $subscriptionProduct): bool
    {
        // Allow all authenticated users to view individual subscription products
        return true;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        // Only allow users with admin permissions to create subscription products
        return in_array($user->role, ['super-admin', 'admin']);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, SubscriptionProduct $subscriptionProduct): bool
    {
        // Only allow users with admin permissions to update subscription products
        return $user->hasPermissionTo('admin subscription management') || 
               $user->hasRole(['super-admin', 'admin']);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, SubscriptionProduct $subscriptionProduct): bool
    {
        // Only allow users with admin permissions to delete subscription products
        return $user->hasPermissionTo('admin subscription management') || 
               $user->hasRole(['super-admin', 'admin']);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, SubscriptionProduct $subscriptionProduct): bool
    {
        // Only allow users with admin permissions to restore subscription products
        return in_array($user->role, ['super-admin', 'admin']);
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, SubscriptionProduct $subscriptionProduct): bool
    {
        // Only allow super-admin to permanently delete subscription products
        return $user->role === 'super-admin';
    }
}
