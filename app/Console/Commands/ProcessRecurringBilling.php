<?php

namespace App\Console\Commands;

use App\Models\UserSubscription;
use App\Models\Payment;
use App\Services\StripeService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ProcessRecurringBilling extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'billing:process-recurring 
                            {--dry-run : Run without making actual charges}
                            {--limit=50 : Maximum number of subscriptions to process}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process recurring billing for active subscriptions';

    protected StripeService $stripeService;

    /**
     * Create a new command instance.
     */
    public function __construct(StripeService $stripeService)
    {
        parent::__construct();
        $this->stripeService = $stripeService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $isDryRun = $this->option('dry-run');
        $limit = (int) $this->option('limit');

        $this->info('Starting recurring billing process...');
        
        if ($isDryRun) {
            $this->warn('DRY RUN MODE - No actual charges will be made');
        }

        // Get subscriptions that need billing
        $subscriptions = $this->getSubscriptionsForBilling($limit);

        if ($subscriptions->isEmpty()) {
            $this->info('No subscriptions require billing at this time.');
            return Command::SUCCESS;
        }

        $this->info("Found {$subscriptions->count()} subscriptions to process");

        $processed = 0;
        $successful = 0;
        $failed = 0;

        foreach ($subscriptions as $subscription) {
            $this->processSubscription($subscription, $isDryRun, $processed, $successful, $failed);
            $processed++;
        }

        $this->info("\nBilling process completed:");
        $this->info("- Processed: {$processed}");
        $this->info("- Successful: {$successful}");
        $this->info("- Failed: {$failed}");

        return Command::SUCCESS;
    }

    /**
     * Get subscriptions that need billing
     */
    protected function getSubscriptionsForBilling(int $limit): \Illuminate\Database\Eloquent\Collection
    {
        return UserSubscription::with(['user', 'subscriptionProduct'])
            ->where('status', 'active')
            ->where('current_period_end', '<=', Carbon::now()->addHours(1)) // Process subscriptions ending within 1 hour
            ->whereNotNull('stripe_subscription_id')
            ->limit($limit)
            ->get();
    }

    /**
     * Process individual subscription billing
     */
    protected function processSubscription(
        UserSubscription $subscription, 
        bool $isDryRun, 
        int &$processed, 
        int &$successful, 
        int &$failed
    ): void {
        $this->line("Processing subscription {$subscription->id} for user {$subscription->user->email}");

        try {
            if ($isDryRun) {
                $this->info("  [DRY RUN] Would process billing for subscription {$subscription->id}");
                $successful++;
                return;
            }

            // Retrieve the subscription from Stripe to get the latest status
            $stripeSubscription = $this->stripeService->getSubscription($subscription->stripe_subscription_id);

            // Check if subscription is still active in Stripe
            if ($stripeSubscription->status !== 'active') {
                $this->warn("  Subscription {$subscription->id} is not active in Stripe (status: {$stripeSubscription->status})");
                
                // Update local status to match Stripe
                $subscription->update(['status' => $stripeSubscription->status]);
                return;
            }

            // Get the latest invoice for this subscription
            $invoices = $this->stripeService->getSubscriptionInvoices($subscription->stripe_subscription_id, 1);
            
            if (empty($invoices)) {
                $this->warn("  No invoices found for subscription {$subscription->id}");
                return;
            }

            $latestInvoice = $invoices[0];

            // Check if the invoice is paid
            if ($latestInvoice->status === 'paid') {
                $this->info("  Latest invoice is already paid for subscription {$subscription->id}");
                
                // Update subscription period
                $subscription->update([
                    'current_period_start' => Carbon::createFromTimestamp($stripeSubscription->current_period_start),
                    'current_period_end' => Carbon::createFromTimestamp($stripeSubscription->current_period_end),
                ]);
                
                $successful++;
                return;
            }

            // If invoice is not paid, attempt to collect payment
            if ($latestInvoice->status === 'open') {
                $this->info("  Attempting to collect payment for subscription {$subscription->id}");
                
                $paidInvoice = $this->stripeService->payInvoice($latestInvoice->id);
                
                if ($paidInvoice->status === 'paid') {
                    $this->info("  ✓ Payment collected successfully for subscription {$subscription->id}");
                    
                    // Update subscription
                    $subscription->update([
                        'status' => 'active',
                        'current_period_start' => Carbon::createFromTimestamp($stripeSubscription->current_period_start),
                        'current_period_end' => Carbon::createFromTimestamp($stripeSubscription->current_period_end),
                    ]);
                    
                    // Create payment record
                    $this->createPaymentRecord($subscription, $paidInvoice);
                    
                    $successful++;
                } else {
                    $this->error("  ✗ Payment failed for subscription {$subscription->id}");
                    
                    // Update subscription status
                    $subscription->update(['status' => 'past_due']);
                    
                    $failed++;
                }
            }

        } catch (\Exception $e) {
            $this->error("  ✗ Error processing subscription {$subscription->id}: {$e->getMessage()}");
            
            Log::error('Recurring billing error', [
                'subscription_id' => $subscription->id,
                'user_id' => $subscription->user_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            $failed++;
        }
    }

    /**
     * Create payment record for successful billing
     */
    protected function createPaymentRecord(UserSubscription $subscription, $invoice): void
    {
        try {
            Payment::create([
                'user_id' => $subscription->user_id,
                'user_subscription_id' => $subscription->id,
                'stripe_payment_intent_id' => $invoice->payment_intent,
                'amount' => $invoice->amount_paid / 100, // Convert from cents
                'currency' => $invoice->currency,
                'status' => 'succeeded',
                'type' => 'subscription',
                'paid_at' => now(),
                'metadata' => [
                    'stripe_invoice_id' => $invoice->id,
                    'billing_reason' => 'subscription_cycle',
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to create payment record', [
                'subscription_id' => $subscription->id,
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
