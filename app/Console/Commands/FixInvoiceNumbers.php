<?php

namespace App\Console\Commands;

use App\Models\Invoice;
use App\Services\StripeService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class FixInvoiceNumbers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'invoice:fix-numbers {--dry-run : Show what would be fixed without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix missing or inconsistent invoice numbers and sync with Stripe';

    protected StripeService $stripeService;

    public function __construct(StripeService $stripeService)
    {
        parent::__construct();
        $this->stripeService = $stripeService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->info('Running in dry-run mode. No changes will be made.');
        }

        // Find invoices with missing invoice numbers
        $invoicesWithoutNumbers = Invoice::whereNull('invoice_number')
            ->orWhere('invoice_number', '')
            ->get();

        $this->info("Found {$invoicesWithoutNumbers->count()} invoices without custom invoice numbers.");

        $fixed = 0;
        $errors = 0;

        foreach ($invoicesWithoutNumbers as $invoice) {
            try {
                $customInvoiceNumber = Invoice::generateInvoiceNumber();

                $this->line("Processing Invoice ID {$invoice->id} (Stripe: {$invoice->stripe_invoice_id})");
                $this->line("  Generated number: {$customInvoiceNumber}");

                if (!$dryRun) {
                    // Update local invoice
                    $invoice->update(['invoice_number' => $customInvoiceNumber]);

                    // Update Stripe invoice metadata
                    try {
                        $this->stripeService->updateInvoice($invoice->stripe_invoice_id, [
                            'metadata' => [
                                'custom_invoice_number' => $customInvoiceNumber,
                                'application_invoice_id' => $invoice->id,
                                'fixed_by_command' => 'true',
                            ]
                        ]);
                        $this->line("  ✓ Updated Stripe metadata");
                    } catch (\Exception $e) {
                        $this->warn("  ⚠ Failed to update Stripe metadata: {$e->getMessage()}");
                    }
                }

                $fixed++;
            } catch (\Exception $e) {
                $this->error("  ✗ Failed to fix invoice {$invoice->id}: {$e->getMessage()}");
                $errors++;
            }
        }

        // Check for duplicate invoice numbers
        $duplicates = Invoice::select('invoice_number')
            ->whereNotNull('invoice_number')
            ->where('invoice_number', '!=', '')
            ->groupBy('invoice_number')
            ->havingRaw('COUNT(*) > 1')
            ->pluck('invoice_number');

        if ($duplicates->count() > 0) {
            $this->warn("Found {$duplicates->count()} duplicate invoice numbers:");
            foreach ($duplicates as $duplicateNumber) {
                $this->line("  - {$duplicateNumber}");
            }
        }

        $this->newLine();
        if ($dryRun) {
            $this->info("Dry run completed. Would have fixed {$fixed} invoices.");
        } else {
            $this->info("Fixed {$fixed} invoices successfully.");
        }

        if ($errors > 0) {
            $this->warn("Encountered {$errors} errors.");
        }

        return $errors > 0 ? 1 : 0;
    }
}
