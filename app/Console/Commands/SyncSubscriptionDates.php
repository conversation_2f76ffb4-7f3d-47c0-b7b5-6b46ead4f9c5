<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\UserSubscription;
use App\Services\StripeService;
use Carbon\Carbon;

class SyncSubscriptionDates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscription:sync-dates {--subscription-id= : Specific subscription ID to sync}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync subscription billing dates from Stripe';

    protected StripeService $stripeService;

    public function __construct(StripeService $stripeService)
    {
        parent::__construct();
        $this->stripeService = $stripeService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $subscriptionId = $this->option('subscription-id');

        if ($subscriptionId) {
            $subscriptions = UserSubscription::where('id', $subscriptionId)->get();
        } else {
            $subscriptions = UserSubscription::whereNotNull('stripe_subscription_id')
                ->where('status', 'active')
                ->get();
        }

        if ($subscriptions->isEmpty()) {
            $this->error('No subscriptions found to sync.');
            return 1;
        }

        $this->info("Syncing {$subscriptions->count()} subscription(s)...");

        foreach ($subscriptions as $subscription) {
            try {
                $this->syncSubscription($subscription);
                $this->info("✓ Synced subscription ID {$subscription->id}");
            } catch (\Exception $e) {
                $this->error("✗ Failed to sync subscription ID {$subscription->id}: {$e->getMessage()}");
            }
        }

        $this->info('Sync completed!');
        return 0;
    }

    protected function syncSubscription(UserSubscription $subscription)
    {
        if (!$subscription->stripe_subscription_id) {
            throw new \Exception('No Stripe subscription ID found');
        }

        // Get subscription from Stripe
        $stripeSubscription = $this->stripeService->getSubscription($subscription->stripe_subscription_id);

        // Update local subscription with Stripe data
        $updateData = [
            'status' => $stripeSubscription->status,
        ];

        // Get billing dates from subscription items (Stripe stores them there)
        $subscriptionItem = $stripeSubscription->items->data[0] ?? null;

        if ($subscriptionItem) {
            if ($timestamp = UserSubscription::safeTimestamp($subscriptionItem->current_period_start)) {
                $updateData['current_period_start'] = $timestamp;
            }

            if ($timestamp = UserSubscription::safeTimestamp($subscriptionItem->current_period_end)) {
                $updateData['current_period_end'] = $timestamp;
            }
        }

        if ($timestamp = UserSubscription::safeTimestamp($stripeSubscription->trial_start)) {
            $updateData['trial_start'] = $timestamp;
        }

        if ($timestamp = UserSubscription::safeTimestamp($stripeSubscription->trial_end)) {
            $updateData['trial_end'] = $timestamp;
        }

        if ($timestamp = UserSubscription::safeTimestamp($stripeSubscription->canceled_at)) {
            $updateData['canceled_at'] = $timestamp;
        }

        $subscription->update($updateData);

        $this->line("  - Status: {$stripeSubscription->status}");

        $subscriptionItem = $stripeSubscription->items->data[0] ?? null;
        $periodStart = $subscriptionItem ? UserSubscription::safeTimestamp($subscriptionItem->current_period_start) : null;
        $periodEnd = $subscriptionItem ? UserSubscription::safeTimestamp($subscriptionItem->current_period_end) : null;

        if ($periodStart && $periodEnd) {
            $this->line("  - Current period: " .
                $periodStart->format('Y-m-d') .
                " to " .
                $periodEnd->format('Y-m-d')
            );
        } else {
            $this->line("  - Current period: Not available");
        }
    }
}
