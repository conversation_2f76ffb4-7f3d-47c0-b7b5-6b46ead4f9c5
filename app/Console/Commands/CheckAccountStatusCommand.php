<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Services\AccountStatusService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CheckAccountStatusCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'accounts:check-status 
                            {--dry-run : Show what would be done without making changes}
                            {--user= : Check specific user by ID or email}
                            {--force : Force check even if recently checked}';

    /**
     * The console command description.
     */
    protected $description = 'Check and update account statuses based on payment failures and overdue invoices';

    protected AccountStatusService $accountStatusService;

    public function __construct(AccountStatusService $accountStatusService)
    {
        parent::__construct();
        $this->accountStatusService = $accountStatusService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Starting account status check...');

        $dryRun = $this->option('dry-run');
        $userFilter = $this->option('user');
        $force = $this->option('force');

        if ($dryRun) {
            $this->warn('DRY RUN MODE - No changes will be made');
        }

        try {
            $users = $this->getUsers($userFilter);
            $stats = [
                'checked' => 0,
                'locked_for_payments' => 0,
                'locked_for_invoices' => 0,
                'already_locked' => 0,
                'errors' => 0,
            ];

            $this->info("Found {$users->count()} users to check");

            foreach ($users as $user) {
                $stats['checked']++;
                
                $this->line("Checking user: {$user->email} (ID: {$user->id})");

                // Skip if already locked/suspended
                if (in_array($user->account_status, ['locked', 'suspended'])) {
                    $stats['already_locked']++;
                    $this->line("  → Already {$user->account_status}, skipping");
                    continue;
                }

                try {
                    // Check for failed payments
                    $shouldLockForPayments = $this->shouldLockForFailedPayments($user);
                    if ($shouldLockForPayments) {
                        if (!$dryRun) {
                            $success = $this->accountStatusService->checkAndLockForFailedPayments($user);
                            if ($success) {
                                $stats['locked_for_payments']++;
                                $this->warn("  → Locked due to failed payments");
                            } else {
                                $stats['errors']++;
                                $this->error("  → Failed to lock for payments");
                            }
                        } else {
                            $stats['locked_for_payments']++;
                            $this->warn("  → Would lock due to failed payments");
                        }
                        continue;
                    }

                    // Check for overdue invoices
                    $shouldLockForInvoices = $this->shouldLockForOverdueInvoices($user);
                    if ($shouldLockForInvoices) {
                        if (!$dryRun) {
                            $success = $this->accountStatusService->checkAndLockForOverdueInvoices($user);
                            if ($success) {
                                $stats['locked_for_invoices']++;
                                $this->warn("  → Locked due to overdue invoices");
                            } else {
                                $stats['errors']++;
                                $this->error("  → Failed to lock for invoices");
                            }
                        } else {
                            $stats['locked_for_invoices']++;
                            $this->warn("  → Would lock due to overdue invoices");
                        }
                        continue;
                    }

                    $this->line("  → Account status OK");

                } catch (\Exception $e) {
                    $stats['errors']++;
                    $this->error("  → Error checking user: " . $e->getMessage());
                    
                    Log::error('Error in account status check command', [
                        'user_id' => $user->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                    ]);
                }
            }

            // Display summary
            $this->newLine();
            $this->info('Account Status Check Summary:');
            $this->table(
                ['Metric', 'Count'],
                [
                    ['Users Checked', $stats['checked']],
                    ['Locked for Failed Payments', $stats['locked_for_payments']],
                    ['Locked for Overdue Invoices', $stats['locked_for_invoices']],
                    ['Already Locked/Suspended', $stats['already_locked']],
                    ['Errors', $stats['errors']],
                ]
            );

            if ($dryRun) {
                $this->warn('This was a dry run - no actual changes were made');
            }

            Log::info('Account status check completed', $stats);

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('Fatal error during account status check: ' . $e->getMessage());
            
            Log::error('Fatal error in account status check command', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return Command::FAILURE;
        }
    }

    /**
     * Get users to check
     */
    protected function getUsers(?string $userFilter): \Illuminate\Database\Eloquent\Collection
    {
        if ($userFilter) {
            // Check if it's an email or ID
            if (filter_var($userFilter, FILTER_VALIDATE_EMAIL)) {
                $user = User::where('email', $userFilter)->first();
            } else {
                $user = User::find($userFilter);
            }

            if (!$user) {
                throw new \Exception("User not found: {$userFilter}");
            }

            return collect([$user]);
        }

        // Get all active users with subscriptions or recent payments
        return User::where('account_status', '!=', 'locked')
            ->where('account_status', '!=', 'suspended')
            ->where(function ($query) {
                $query->whereHas('subscriptions')
                      ->orWhereHas('payments', function ($q) {
                          $q->where('created_at', '>=', now()->subDays(60));
                      });
            })
            ->get();
    }

    /**
     * Check if user should be locked for failed payments
     */
    protected function shouldLockForFailedPayments(User $user): bool
    {
        $failedPaymentsCount = $user->payments()
            ->where('status', 'failed')
            ->where('created_at', '>=', now()->subDays(30))
            ->count();

        $maxFailedPayments = config('subscription.max_failed_payments_before_lock', 3);

        return $failedPaymentsCount >= $maxFailedPayments;
    }

    /**
     * Check if user should be locked for overdue invoices
     */
    protected function shouldLockForOverdueInvoices(User $user): bool
    {
        $overdueInvoicesCount = $user->invoices()
            ->where('status', 'open')
            ->where('due_date', '<', now())
            ->count();

        $maxOverdueInvoices = config('subscription.max_overdue_invoices_before_lock', 2);

        return $overdueInvoicesCount >= $maxOverdueInvoices;
    }
}
