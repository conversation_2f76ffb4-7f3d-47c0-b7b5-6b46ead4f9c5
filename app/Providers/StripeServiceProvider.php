<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Stripe\Stripe;

class StripeServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton('stripe', function ($app) {
            $secretKey = config('services.stripe.secret');

            if (empty($secretKey)) {
                throw new \Exception('Stripe secret key is not configured. Please set STRIPE_SECRET in your .env file.');
            }

            Stripe::setApiKey($secretKey);
            return new \Stripe\StripeClient($secretKey);
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
