<?php

namespace App\Providers;

// use Illuminate\Support\Facades\Gate;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
        // 'Spatie\Permission\Models\Role' => 'App\Policies\RolePolicy',
        // 'Spatie\Permission\Models\Permission' => 'App\Policies\PermissionPolicy',
        'App\Models\Payment' => 'App\Policies\PaymentPolicy',
        'App\Models\PaymentMethod' => 'App\Policies\PaymentMethodPolicy',
        'App\Models\Invoice' => 'App\Policies\InvoicePolicy',
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        //
    }
}
