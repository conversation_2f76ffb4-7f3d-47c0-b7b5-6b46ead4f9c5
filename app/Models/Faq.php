<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Faq extends Model
{
    use HasFactory;

    protected $fillable = [
        'question',
        'answer',
        'target_role',
        'status',
        'sort_order',
        'category',
        'created_by',
    ];

    protected $casts = [
        'sort_order' => 'integer',
    ];

    /**
     * Get the user who created this FAQ
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope for active FAQs
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for FAQs by target role
     */
    public function scopeForRole($query, string $role)
    {
        return $query->where(function ($q) use ($role) {
            $q->where('target_role', $role)
              ->orWhere('target_role', 'both');
        });
    }

    /**
     * Scope for FAQs by category
     */
    public function scopeByCategory($query, ?string $category = null)
    {
        if ($category) {
            return $query->where('category', $category);
        }

        return $query;
    }

    /**
     * Scope for ordered FAQs
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('created_at', 'asc');
    }

    /**
     * Check if FAQ is active
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if FAQ is for specific role
     */
    public function isForRole(string $role): bool
    {
        return $this->target_role === $role || $this->target_role === 'both';
    }

    /**
     * Get available target roles
     */
    public static function getTargetRoles(): array
    {
        return [
            'investor' => 'Investor',
            'startup' => 'Startup',
            'both' => 'Both Roles',
        ];
    }

    /**
     * Get available categories
     */
    public static function getCategories(): array
    {
        return [
            'general' => 'General',
            'account' => 'Account Management',
            'subscription' => 'Subscription & Billing',
            'investment' => 'Investment Process',
            'profile' => 'Profile Setup',
            'matching' => 'Matching & Discovery',
            'support' => 'Support & Contact',
        ];
    }
}
