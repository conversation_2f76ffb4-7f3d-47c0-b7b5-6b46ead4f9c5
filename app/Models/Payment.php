<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Payment extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'user_subscription_id',
        'stripe_payment_intent_id',
        'stripe_invoice_id',
        'amount',
        'currency',
        'status',
        'type',
        'payment_method_type',
        'failure_code',
        'failure_message',
        'requires_3d_secure',
        '3d_secure_url',
        'metadata',
        'paid_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'requires_3d_secure' => 'boolean',
        'metadata' => 'array',
        'paid_at' => 'datetime',
    ];

    /**
     * Get the user that owns the payment
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the subscription associated with this payment
     */
    public function userSubscription(): BelongsTo
    {
        return $this->belongsTo(UserSubscription::class);
    }

    /**
     * Alias for userSubscription relationship (for backward compatibility)
     */
    public function subscription(): BelongsTo
    {
        return $this->userSubscription();
    }

    /**
     * Get the invoice for this payment
     */
    public function invoice(): HasOne
    {
        return $this->hasOne(Invoice::class);
    }

    /**
     * Get the refunds for this payment
     */
    public function refunds(): HasMany
    {
        return $this->hasMany(Refund::class);
    }

    /**
     * Scope to get successful payments
     */
    public function scopeSuccessful($query)
    {
        return $query->where('status', 'succeeded');
    }

    /**
     * Scope to get failed payments
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope to get payments requiring action
     */
    public function scopeRequiresAction($query)
    {
        return $query->where('status', 'requires_action');
    }

    /**
     * Check if payment was successful
     */
    public function isSuccessful(): bool
    {
        return $this->status === 'succeeded';
    }

    /**
     * Check if payment failed
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if payment requires action (3D Secure)
     */
    public function requiresAction(): bool
    {
        return $this->status === 'requires_action';
    }

    /**
     * Get formatted amount with currency
     */
    public function getFormattedAmountAttribute(): string
    {
        return '$' . number_format($this->amount, 2);
    }

    /**
     * Get status display name
     */
    public function getStatusDisplayAttribute(): string
    {
        return match($this->status) {
            'succeeded' => 'Successful',
            'failed' => 'Failed',
            'pending' => 'Pending',
            'canceled' => 'Canceled',
            'requires_action' => 'Requires Action',
            default => ucfirst($this->status),
        };
    }

    /**
     * Get total refunded amount
     */
    public function getTotalRefundedAttribute(): float
    {
        return $this->refunds()->where('status', 'succeeded')->sum('amount');
    }

    /**
     * Check if payment is fully refunded
     */
    public function isFullyRefunded(): bool
    {
        return $this->total_refunded >= $this->amount;
    }
}
