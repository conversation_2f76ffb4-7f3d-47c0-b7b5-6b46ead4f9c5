<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserAccountStatus extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'status',
        'stripe_customer_id',
        'is_locked',
        'locked_at',
        'locked_reason',
        'locked_by',
        'suspended_at',
        'suspended_reason',
        'suspended_by',
        'activated_at',
        'activated_by',
        'unlocked_at',
        'unlocked_by',
    ];

    protected $casts = [
        'is_locked' => 'boolean',
        'locked_at' => 'datetime',
        'suspended_at' => 'datetime',
        'activated_at' => 'datetime',
        'unlocked_at' => 'datetime',
    ];

    /**
     * Get the user that owns this account status
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who locked this account
     */
    public function lockedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'locked_by');
    }

    /**
     * Get the user who suspended this account
     */
    public function suspendedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'suspended_by');
    }

    /**
     * Get the user who activated this account
     */
    public function activatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'activated_by');
    }

    /**
     * Get the user who unlocked this account
     */
    public function unlockedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'unlocked_by');
    }

    /**
     * Scope for active accounts
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for locked accounts
     */
    public function scopeLocked($query)
    {
        return $query->where('is_locked', true);
    }

    /**
     * Scope for suspended accounts
     */
    public function scopeSuspended($query)
    {
        return $query->where('status', 'suspended');
    }

    /**
     * Check if account is active and not locked
     */
    public function isAccessible(): bool
    {
        return $this->status === 'active' && !$this->is_locked;
    }

    /**
     * Lock the account
     */
    public function lock(string $reason, ?int $lockedBy = null): void
    {
        $this->update([
            'is_locked' => true,
            'locked_at' => now(),
            'locked_reason' => $reason,
            'locked_by' => $lockedBy,
        ]);
    }

    /**
     * Unlock the account
     */
    public function unlock(?int $unlockedBy = null): void
    {
        $this->update([
            'is_locked' => false,
            'locked_at' => null,
            'locked_reason' => null,
            'unlocked_at' => now(),
            'unlocked_by' => $unlockedBy,
        ]);
    }

    /**
     * Suspend the account
     */
    public function suspend(string $reason, ?int $suspendedBy = null): void
    {
        $this->update([
            'status' => 'suspended',
            'suspended_at' => now(),
            'suspended_reason' => $reason,
            'suspended_by' => $suspendedBy,
        ]);
    }

    /**
     * Activate the account
     */
    public function activate(?int $activatedBy = null): void
    {
        $this->update([
            'status' => 'active',
            'suspended_at' => null,
            'suspended_reason' => null,
            'activated_at' => now(),
            'activated_by' => $activatedBy,
        ]);
    }
}
