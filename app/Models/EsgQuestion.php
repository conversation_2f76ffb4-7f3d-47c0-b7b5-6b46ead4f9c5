<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class EsgQuestion extends Model
{
    use HasFactory;

    protected $fillable = [
        'question_text',
        'category',
        'type',
        'options',
        'weight',
        'sort_order',
        'is_required',
        'is_active',
        'help_text',
    ];

    protected $casts = [
        'options' => 'array',
        'weight' => 'integer',
        'sort_order' => 'integer',
        'is_required' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get responses for this question
     */
    public function esgResponses(): HasMany
    {
        return $this->hasMany(EsgResponse::class);
    }

    /**
     * Scope for active questions
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for questions by category
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope for required questions
     */
    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }

    /**
     * Scope for ordered questions
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('id');
    }

    /**
     * Get questions grouped by category
     */
    public static function getQuestionsByCategory(): array
    {
        return self::active()
            ->ordered()
            ->get()
            ->groupBy('category')
            ->toArray();
    }
}
