<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InterestRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'requester_id',
        'target_id',
        'type',
        'status',
        'message',
        'proposed_amount',
        'terms',
        'approved_by',
        'approved_at',
        'rejection_reason',
    ];

    protected $casts = [
        'proposed_amount' => 'decimal:2',
        'terms' => 'array',
        'approved_at' => 'datetime',
    ];

    /**
     * Get the user who made the request
     */
    public function requester(): BelongsTo
    {
        return $this->belongsTo(User::class, 'requester_id');
    }

    /**
     * Get the user who is the target of the request
     */
    public function target(): BelongsTo
    {
        return $this->belongsTo(User::class, 'target_id');
    }

    /**
     * Get the user who approved the request
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Scope for pending requests
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for approved requests
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for rejected requests
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    /**
     * Scope for requests by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for requests by requester
     */
    public function scopeByRequester($query, $userId)
    {
        return $query->where('requester_id', $userId);
    }

    /**
     * Scope for requests by target
     */
    public function scopeByTarget($query, $userId)
    {
        return $query->where('target_id', $userId);
    }

    /**
     * Scope for investment interest requests
     */
    public function scopeInvestmentInterest($query)
    {
        return $query->where('type', 'investment_interest');
    }

    /**
     * Scope for funding requests
     */
    public function scopeFundingRequest($query)
    {
        return $query->where('type', 'funding_request');
    }

    /**
     * Approve the request
     */
    public function approve(User $approver): bool
    {
        return $this->update([
            'status' => 'approved',
            'approved_by' => $approver->id,
            'approved_at' => now(),
        ]);
    }

    /**
     * Reject the request
     */
    public function reject(string $reason = null): bool
    {
        return $this->update([
            'status' => 'rejected',
            'rejection_reason' => $reason,
        ]);
    }

    /**
     * Check if request can be approved
     */
    public function canBeApproved(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if request can be rejected
     */
    public function canBeRejected(): bool
    {
        return $this->status === 'pending';
    }
}
