<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Spatie\Image\Manipulations;
use Aliziodev\LaravelTaxonomy\Traits\HasTaxonomy;

class StartupProfile extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia, HasTaxonomy;

    protected $fillable = [
        'user_id',
        'company_name',
        'company_description',
        'founding_date',
        'employee_count',
        'website',
        'linkedin',
        'funding_stage',
        'funding_amount_sought',
        'current_valuation',
        'business_model',
        'esg_score',
        'esg_breakdown',
        'esg_completed',
        'profile_completed',
    ];

    protected $casts = [
        'founding_date' => 'date',
        'employee_count' => 'integer',
        'funding_amount_sought' => 'decimal:2',
        'current_valuation' => 'decimal:2',
        'business_model' => 'array',
        'esg_score' => 'decimal:2',
        'esg_breakdown' => 'array',
        'esg_completed' => 'boolean',
        'profile_completed' => 'boolean',
    ];

    /**
     * Get the user that owns the startup profile
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get ESG responses for this startup
     */
    public function esgResponses(): HasMany
    {
        return $this->hasMany(EsgResponse::class);
    }

    /**
     * Get interest requests sent by this startup
     */
    public function sentInterestRequests(): HasMany
    {
        return $this->hasMany(InterestRequest::class, 'requester_id', 'user_id');
    }

    /**
     * Get interest requests received by this startup
     */
    public function receivedInterestRequests(): HasMany
    {
        return $this->hasMany(InterestRequest::class, 'target_id', 'user_id');
    }

    /**
     * Get social media links for this startup profile
     */
    public function socialMediaLinks(): MorphMany
    {
        return $this->morphMany(SocialMediaLink::class, 'linkable');
    }

    /**
     * Get categories associated with this startup (using taxonomy system)
     */
    public function categories()
    {
        return $this->taxonomies()->where('type', 'category');
    }

    /**
     * Get keywords associated with this startup
     */
    public function keywords()
    {
        return $this->taxonomies()->where('type', 'keyword');
    }

    /**
     * Get industry tags associated with this startup
     */
    public function industryTags()
    {
        return $this->taxonomies()->where('type', 'industry');
    }

    /**
     * Get technology tags associated with this startup
     */
    public function technologyTags()
    {
        return $this->taxonomies()->where('type', 'technology');
    }

    /**
     * Define media collections for different document types
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('company_logo')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/svg+xml'])
            ->singleFile();

        $this->addMediaCollection('pitch_deck')
            ->acceptsMimeTypes(['application/pdf', 'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation'])
            ->singleFile();

        $this->addMediaCollection('business_plan')
            ->acceptsMimeTypes(['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'])
            ->singleFile();

        $this->addMediaCollection('financial_projections')
            ->acceptsMimeTypes(['application/pdf', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/csv'])
            ->singleFile();
    }

    /**
     * Define media conversions for images
     */
    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->fit(Manipulations::FIT_CROP, 300, 300)
            ->nonQueued();

        $this->addMediaConversion('preview')
            ->fit(Manipulations::FIT_CROP, 500, 300)
            ->nonQueued();
    }

    /**
     * Scope for completed profiles
     */
    public function scopeCompleted($query)
    {
        return $query->where('profile_completed', true);
    }

    /**
     * Scope for profiles with completed ESG
     */
    public function scopeEsgCompleted($query)
    {
        return $query->where('esg_completed', true);
    }

    /**
     * Scope for profiles by funding stage
     */
    public function scopeByFundingStage($query, $stage)
    {
        return $query->where('funding_stage', $stage);
    }

    /**
     * Scope for profiles by ESG score range
     */
    public function scopeByEsgScore($query, $minScore, $maxScore = null)
    {
        $query->where('esg_score', '>=', $minScore);

        if ($maxScore !== null) {
            $query->where('esg_score', '<=', $maxScore);
        }

        return $query;
    }

    /**
     * Scope for profiles seeking funding within range
     */
    public function scopeSeekingFunding($query, $minAmount, $maxAmount = null)
    {
        $query->whereNotNull('funding_amount_sought')
              ->where('funding_amount_sought', '>=', $minAmount);

        if ($maxAmount !== null) {
            $query->where('funding_amount_sought', '<=', $maxAmount);
        }

        return $query;
    }

    /**
     * Calculate and update ESG score based on responses
     */
    public function calculateEsgScore(): void
    {
        $esgService = app(\App\Services\EsgScoringService::class);
        $scoreData = $esgService->calculateScore($this);

        $this->update([
            'esg_score' => $scoreData['overall_score'],
            'esg_breakdown' => $scoreData['category_scores'],
        ]);
    }
}
