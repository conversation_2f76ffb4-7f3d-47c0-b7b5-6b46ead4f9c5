<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Interest extends Model
{
    protected $fillable = [
        'investor_id',
        'startup_id',
        'status',
        'message',
        'metadata'
    ];

    protected $casts = [
        'metadata' => 'array'
    ];

    /**
     * Get the investor who expressed interest
     */
    public function investor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'investor_id');
    }

    /**
     * Get the startup that received interest
     */
    public function startup(): BelongsTo
    {
        return $this->belongsTo(User::class, 'startup_id');
    }
}
