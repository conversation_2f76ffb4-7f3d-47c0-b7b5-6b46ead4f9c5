<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EsgResponse extends Model
{
    use HasFactory;

    protected $fillable = [
        'startup_profile_id',
        'esg_question_id',
        'response_value',
        'score',
    ];

    protected $casts = [
        'score' => 'decimal:2',
    ];

    /**
     * Get the startup profile that owns this response
     */
    public function startupProfile(): BelongsTo
    {
        return $this->belongsTo(StartupProfile::class);
    }

    /**
     * Get the ESG question for this response
     */
    public function esgQuestion(): BelongsTo
    {
        return $this->belongsTo(EsgQuestion::class);
    }

    /**
     * Scope for responses by startup
     */
    public function scopeByStartup($query, $startupProfileId)
    {
        return $query->where('startup_profile_id', $startupProfileId);
    }

    /**
     * Scope for responses by question
     */
    public function scopeByQuestion($query, $questionId)
    {
        return $query->where('esg_question_id', $questionId);
    }

    /**
     * Scope for responses by category
     */
    public function scopeByCategory($query, $category)
    {
        return $query->whereHas('esgQuestion', function ($q) use ($category) {
            $q->where('category', $category);
        });
    }
}
