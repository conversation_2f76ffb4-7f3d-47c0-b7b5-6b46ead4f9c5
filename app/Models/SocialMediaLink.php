<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class SocialMediaLink extends Model
{
    use HasFactory;

    protected $fillable = [
        'linkable_type',
        'linkable_id',
        'platform',
        'url',
        'username',
        'is_primary',
        'is_public',
    ];

    protected $casts = [
        'is_primary' => 'boolean',
        'is_public' => 'boolean',
    ];

    /**
     * Get the owning linkable model (User, StartupProfile, etc.)
     */
    public function linkable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope for public links
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope for primary links
     */
    public function scopePrimary($query)
    {
        return $query->where('is_primary', true);
    }

    /**
     * Scope for specific platform
     */
    public function scopePlatform($query, $platform)
    {
        return $query->where('platform', $platform);
    }

    /**
     * Get formatted URL with protocol
     */
    public function getFormattedUrlAttribute(): string
    {
        $url = $this->url;

        if (!str_starts_with($url, 'http://') && !str_starts_with($url, 'https://')) {
            $url = 'https://' . $url;
        }

        return $url;
    }

    /**
     * Get platform icon class for UI
     */
    public function getPlatformIconAttribute(): string
    {
        return match($this->platform) {
            'linkedin' => 'fab fa-linkedin',
            'twitter' => 'fab fa-twitter',
            'facebook' => 'fab fa-facebook',
            'instagram' => 'fab fa-instagram',
            'github' => 'fab fa-github',
            'youtube' => 'fab fa-youtube',
            'tiktok' => 'fab fa-tiktok',
            'website' => 'fas fa-globe',
            default => 'fas fa-link',
        };
    }

    /**
     * Get platform display name
     */
    public function getPlatformNameAttribute(): string
    {
        return match($this->platform) {
            'linkedin' => 'LinkedIn',
            'twitter' => 'Twitter',
            'facebook' => 'Facebook',
            'instagram' => 'Instagram',
            'github' => 'GitHub',
            'youtube' => 'YouTube',
            'tiktok' => 'TikTok',
            'website' => 'Website',
            default => ucfirst($this->platform),
        };
    }
}
