<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SubscriptionProduct extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'price',
        'billing_cycle',
        'features',
        'limits',
        'is_active',
        'stripe_product_id',
        'stripe_price_id',
        'sort_order',
        'target_role',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'features' => 'array',
        'limits' => 'array',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the user subscriptions for this product
     */
    public function userSubscriptions(): Has<PERSON>any
    {
        return $this->hasMany(UserSubscription::class);
    }

    /**
     * Alias for userSubscriptions for backward compatibility
     */
    public function subscriptions(): HasMany
    {
        return $this->userSubscriptions();
    }

    /**
     * Scope to get only active products
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to order by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('price');
    }

    /**
     * Scope to filter by target role
     */
    public function scopeForRole($query, string $role)
    {
        return $query->where(function($q) use ($role) {
            $q->where('target_role', $role)
              ->orWhere('target_role', 'all');
        });
    }

    /**
     * Get formatted price with currency
     */
    public function getFormattedPriceAttribute(): string
    {
        return '$' . number_format($this->price, 2);
    }

    /**
     * Get billing cycle display name
     */
    public function getBillingCycleDisplayAttribute(): string
    {
        return match($this->billing_cycle) {
            'monthly' => 'Monthly',
            'yearly' => 'Yearly',
            'quarterly' => 'Quarterly',
            default => ucfirst($this->billing_cycle),
        };
    }
}
