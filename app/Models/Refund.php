<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Refund extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'payment_id',
        'user_subscription_id',
        'invoice_id',
        'stripe_refund_id',
        'stripe_payment_intent_id',
        'stripe_charge_id',
        'amount',
        'original_amount',
        'prorated_amount',
        'currency',
        'type',
        'reason',
        'status',
        'proration_details',
        'service_start_date',
        'service_end_date',
        'cancellation_date',
        'days_used',
        'total_days',
        'description',
        'internal_notes',
        'processed_by',
        'processed_at',
        'stripe_created_at',
        'metadata',
    ];

    protected $casts = [
        'amount' => 'integer', // Store in cents
        'original_amount' => 'integer',
        'prorated_amount' => 'integer',
        'proration_details' => 'array',
        'metadata' => 'array',
        'service_start_date' => 'date',
        'service_end_date' => 'date',
        'cancellation_date' => 'date',
        'processed_at' => 'datetime',
        'stripe_created_at' => 'datetime',
    ];

    /**
     * Get the user that owns the refund
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the payment that was refunded
     */
    public function payment(): BelongsTo
    {
        return $this->belongsTo(Payment::class);
    }

    /**
     * Get the subscription associated with this refund
     */
    public function userSubscription(): BelongsTo
    {
        return $this->belongsTo(UserSubscription::class);
    }

    /**
     * Get the invoice associated with this refund
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * Get the admin who processed this refund
     */
    public function processedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'processed_by');
    }

    /**
     * Scope to get successful refunds
     */
    public function scopeSuccessful($query)
    {
        return $query->where('status', 'succeeded');
    }

    /**
     * Scope to get pending refunds
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Check if refund was successful
     */
    public function isSuccessful(): bool
    {
        return $this->status === 'succeeded';
    }

    /**
     * Check if refund is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Get formatted amount with currency
     */
    public function getFormattedAmountAttribute(): string
    {
        return '$' . number_format($this->amount / 100, 2);
    }

    /**
     * Get formatted original amount
     */
    public function getFormattedOriginalAmountAttribute(): string
    {
        return '$' . number_format($this->original_amount / 100, 2);
    }

    /**
     * Get formatted prorated amount
     */
    public function getFormattedProratedAmountAttribute(): string
    {
        if (!$this->prorated_amount) {
            return 'N/A';
        }
        return '$' . number_format($this->prorated_amount / 100, 2);
    }

    /**
     * Check if this is a prorated refund
     */
    public function isProrated(): bool
    {
        return $this->type === 'prorated';
    }

    /**
     * Get proration percentage
     */
    public function getProrationPercentageAttribute(): ?float
    {
        if (!$this->total_days || !$this->days_used) {
            return null;
        }

        $unusedDays = $this->total_days - $this->days_used;
        return ($unusedDays / $this->total_days) * 100;
    }

    /**
     * Get status display name
     */
    public function getStatusDisplayAttribute(): string
    {
        return match($this->status) {
            'succeeded' => 'Successful',
            'pending' => 'Pending',
            'failed' => 'Failed',
            'canceled' => 'Canceled',
            default => ucfirst($this->status),
        };
    }

    /**
     * Get reason display name
     */
    public function getReasonDisplayAttribute(): string
    {
        return match($this->reason) {
            'duplicate' => 'Duplicate Payment',
            'fraudulent' => 'Fraudulent',
            'requested_by_customer' => 'Customer Request',
            'expired_uncaptured_charge' => 'Expired Charge',
            default => ucfirst(str_replace('_', ' ', $this->reason)),
        };
    }
}
