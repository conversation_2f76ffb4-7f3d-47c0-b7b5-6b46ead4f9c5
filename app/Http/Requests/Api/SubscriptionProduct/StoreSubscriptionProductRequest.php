<?php

namespace App\Http\Requests\Api\SubscriptionProduct;

use App\Http\Requests\Api\ApiBaseRequest;
use Illuminate\Validation\Rule;

class StoreSubscriptionProductRequest extends ApiBaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization is handled by the policy
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'price' => 'required|numeric|min:0.01|max:999999.99',
            'billing_cycle' => ['required', Rule::in(['monthly', 'yearly', 'quarterly'])],
            'features' => 'nullable|array',
            'features.*' => 'string|max:255',
            'limits' => 'nullable|array',
            'limits.*' => 'nullable',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0|max:999',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'The product name is required.',
            'name.max' => 'The product name may not be greater than 255 characters.',
            'description.max' => 'The description may not be greater than 1000 characters.',
            'price.required' => 'The price is required.',
            'price.numeric' => 'The price must be a valid number.',
            'price.min' => 'The price must be at least $0.01.',
            'price.max' => 'The price may not be greater than $999,999.99.',
            'billing_cycle.required' => 'The billing cycle is required.',
            'billing_cycle.in' => 'The billing cycle must be monthly, yearly, or quarterly.',
            'features.array' => 'Features must be provided as an array.',
            'features.*.string' => 'Each feature must be a string.',
            'features.*.max' => 'Each feature may not be greater than 255 characters.',
            'limits.array' => 'Limits must be provided as an array.',
            'is_active.boolean' => 'The active status must be true or false.',
            'sort_order.integer' => 'The sort order must be an integer.',
            'sort_order.min' => 'The sort order must be at least 0.',
            'sort_order.max' => 'The sort order may not be greater than 999.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'name' => 'product name',
            'billing_cycle' => 'billing cycle',
            'is_active' => 'active status',
            'sort_order' => 'sort order',
        ];
    }
}
