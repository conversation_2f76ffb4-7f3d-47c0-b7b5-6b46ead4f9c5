<?php

namespace App\Http\Requests\Api\Payment;

use App\Http\Requests\Api\ApiBaseRequest;

class CreatePaymentIntentRequest extends ApiBaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization is handled by the policy
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'amount' => 'required|numeric|min:0.50|max:999999.99',
            'currency' => 'nullable|string|size:3|in:usd,eur,gbp,cad,aud',
            'description' => 'nullable|string|max:500',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'amount.required' => 'Payment amount is required.',
            'amount.numeric' => 'Payment amount must be a valid number.',
            'amount.min' => 'Payment amount must be at least $0.50.',
            'amount.max' => 'Payment amount cannot exceed $999,999.99.',
            'currency.size' => 'Currency code must be exactly 3 characters.',
            'currency.in' => 'Currency must be one of: USD, EUR, GBP, CAD, AUD.',
            'description.string' => 'Description must be a valid string.',
            'description.max' => 'Description cannot exceed 500 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'amount' => 'payment amount',
            'currency' => 'currency',
            'description' => 'payment description',
        ];
    }
}
