<?php

namespace App\Http\Requests\Api\Subscription;

use App\Http\Requests\Api\ApiBaseRequest;

class UpdateSubscriptionRequest extends ApiBaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization is handled by the policy
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'subscription_product_id' => 'required|exists:subscription_products,id',
            'proration_behavior' => 'nullable|in:create_prorations,none,always_invoice',
            'billing_cycle_anchor' => 'nullable|in:now,unchanged',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'subscription_product_id.required' => 'Please select a new subscription product.',
            'subscription_product_id.exists' => 'The selected subscription product is invalid.',
            'proration_behavior.in' => 'Invalid proration behavior. Must be create_prorations, none, or always_invoice.',
            'billing_cycle_anchor.in' => 'Invalid billing cycle anchor. Must be now or unchanged.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'subscription_product_id' => 'subscription product',
            'proration_behavior' => 'proration behavior',
            'billing_cycle_anchor' => 'billing cycle anchor',
        ];
    }
}
