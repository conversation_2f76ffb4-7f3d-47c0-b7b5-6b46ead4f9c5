<?php

namespace App\Http\Requests\Api\Subscription;

use App\Http\Requests\Api\ApiBaseRequest;

class SubscribeRequest extends ApiBaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization is handled by the policy
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'subscription_product_id' => 'required|exists:subscription_products,id',
            'payment_method_id' => [
                'nullable',
                'integer',
                'exists:payment_methods,id,user_id,' . auth()->id() . ',is_active,1'
            ],
            'trial_days' => 'nullable|integer|min:0|max:365',
            'coupon_code' => 'nullable|string|max:100',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'subscription_product_id.required' => 'Please select a subscription product.',
            'subscription_product_id.exists' => 'The selected subscription product is invalid.',
            'payment_method_id.string' => 'The payment method ID must be a valid string.',
            'payment_method_id.min' => 'The payment method ID is too short.',
            'payment_method_id.max' => 'The payment method ID is too long.',
            'trial_days.integer' => 'Trial days must be a valid number.',
            'trial_days.min' => 'Trial days cannot be negative.',
            'trial_days.max' => 'Trial days cannot exceed 365 days.',
            'coupon_code.string' => 'The coupon code must be a valid string.',
            'coupon_code.max' => 'The coupon code is too long.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'subscription_product_id' => 'subscription product',
            'payment_method_id' => 'payment method',
            'trial_days' => 'trial period',
            'coupon_code' => 'coupon code',
        ];
    }
}
