<?php

namespace App\Http\Requests\Api\PaymentMethod;

use App\Http\Requests\Api\ApiBaseRequest;

class StorePaymentMethodRequest extends ApiBaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization is handled by the policy
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'stripe_payment_method_id' => 'required|string|starts_with:pm_',
            'is_default' => 'nullable|boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'stripe_payment_method_id.required' => 'Payment method ID is required.',
            'stripe_payment_method_id.string' => 'Payment method ID must be a valid string.',
            'stripe_payment_method_id.starts_with' => 'Payment method ID must be a valid Stripe payment method ID.',
            'is_default.boolean' => 'Default flag must be true or false.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'stripe_payment_method_id' => 'payment method ID',
            'is_default' => 'default setting',
        ];
    }
}
