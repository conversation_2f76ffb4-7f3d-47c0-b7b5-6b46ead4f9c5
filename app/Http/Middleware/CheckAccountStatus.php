<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckAccountStatus
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();

        if (!$user) {
            return $next($request);
        }

        // Check if account is locked
        if ($user->account_status === 'locked') {
            return $this->handleLockedAccount($request);
        }

        // Check if account is suspended
        if ($user->account_status === 'suspended') {
            return $this->handleSuspendedAccount($request);
        }

        // Check if account is inactive
        if ($user->account_status === 'inactive') {
            return $this->handleInactiveAccount($request);
        }

        return $next($request);
    }

    /**
     * Handle locked account
     */
    protected function handleLockedAccount(Request $request): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Your account has been locked. Please contact support to restore access.',
                'error_code' => 'ACCOUNT_LOCKED',
                'support_email' => config('app.support_email', '<EMAIL>'),
                'support_phone' => config('app.support_phone', null),
            ], 423); // 423 Locked
        }

        return redirect()->route('account.locked')
            ->with('error', 'Your account has been locked. Please contact support to restore access.');
    }

    /**
     * Handle suspended account
     */
    protected function handleSuspendedAccount(Request $request): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Your account has been suspended. Some features may be limited.',
                'error_code' => 'ACCOUNT_SUSPENDED',
                'support_email' => config('app.support_email', '<EMAIL>'),
                'limited_access' => true,
            ], 403); // 403 Forbidden
        }

        return redirect()->route('account.suspended')
            ->with('warning', 'Your account has been suspended. Some features may be limited.');
    }

    /**
     * Handle inactive account
     */
    protected function handleInactiveAccount(Request $request): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Your account is inactive. Please activate your account to continue.',
                'error_code' => 'ACCOUNT_INACTIVE',
                'activation_url' => route('account.activate'),
            ], 403); // 403 Forbidden
        }

        return redirect()->route('account.activate')
            ->with('info', 'Your account is inactive. Please activate your account to continue.');
    }
}
