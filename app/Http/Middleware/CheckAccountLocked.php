<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckAccountLocked
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        if ($user && $user->isAccountLocked()) {
            return response()->json([
                'success' => false,
                'message' => 'Your account has been locked due to payment issues. Please contact support or update your payment method.',
                'data' => [
                    'account_locked' => true,
                    'account_locked_at' => $user->account_locked_at,
                ]
            ], 423); // 423 Locked
        }

        return $next($request);
    }
}
