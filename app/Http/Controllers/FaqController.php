<?php

namespace App\Http\Controllers;

use App\Models\Faq;
use Illuminate\Http\Request;

class FaqController extends Controller
{
    /**
     * Display FAQs for the current user's role
     */
    public function index(Request $request)
    {
        $breadcrumbItems = [
            [
                'name' => 'FAQ',
                'url' => route('faqs.index'),
                'active' => true
            ],
        ];

        $user = auth()->user();
        $userRole = $user ? $user->role : 'investor'; // Default to investor for guests

        // Get FAQs for the user's role
        $query = Faq::active()->forRole($userRole)->ordered();

        // Filter by category if requested
        if ($request->has('category') && $request->category !== '') {
            $query->byCategory($request->category);
        }

        // Search functionality
        if ($request->has('search') && $request->search !== '') {
            $query->where('question', 'like', '%' . $request->search . '%');
        }

        $faqs = $query->get();

        // Group FAQs by category
        $faqsByCategory = $faqs->groupBy('category');

        // Get available categories for this user's role
        $availableCategories = Faq::active()
            ->forRole($userRole)
            ->whereNotNull('category')
            ->distinct()
            ->pluck('category')
            ->mapWithKeys(function ($category) {
                return [$category => Faq::getCategories()[$category] ?? ucfirst($category)];
            })
            ->toArray();

        return view('faqs.index', [
            'pageTitle' => 'Frequently Asked Questions',
            'breadcrumbItems' => $breadcrumbItems,
            'faqs' => $faqs,
            'faqsByCategory' => $faqsByCategory,
            'availableCategories' => $availableCategories,
            'userRole' => $userRole,
            'currentCategory' => $request->category,
            'searchQuery' => $request->search,
            'allCategories' => Faq::getCategories()
        ]);
    }

    /**
     * Search FAQs via AJAX
     */
    public function search(Request $request)
    {
        $user = auth()->user();
        $userRole = $user ? $user->role : 'investor';

        $query = Faq::active()->forRole($userRole)->ordered();

        if ($request->has('q') && $request->q !== '') {
            $searchTerm = $request->q;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('question', 'like', '%' . $searchTerm . '%')
                  ->orWhere('answer', 'like', '%' . $searchTerm . '%');
            });
        }

        if ($request->has('category') && $request->category !== '') {
            $query->byCategory($request->category);
        }

        $faqs = $query->get();

        return response()->json([
            'faqs' => $faqs->map(function ($faq) {
                return [
                    'id' => $faq->id,
                    'question' => $faq->question,
                    'answer' => $faq->answer,
                    'category' => $faq->category,
                    'category_label' => Faq::getCategories()[$faq->category] ?? ucfirst($faq->category ?? 'general'),
                ];
            })
        ]);
    }
}
