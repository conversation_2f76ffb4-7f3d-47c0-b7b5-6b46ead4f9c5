<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class AdminRoleController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin|super-admin']);
    }

    /**
     * Display a listing of admin users
     */
    public function index(Request $request): View
    {
        $breadcrumbsItems = [
            [
                'name' => 'Dashboard',
                'url' => route('admin.dashboard'),
                'active' => false,
            ],
            [
                'name' => 'Admin & Analyst Management',
                'url' => null,
                'active' => true,
            ],
        ];

        $query = User::with(['accountStatus'])->whereIn('role', ['admin', 'analyst'])->with(['media']);

        // Search functionality
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by role
        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('account_status', 'active');
            } else {
                $query->where('account_status', '!=', 'active');
            }
        }

        $adminUsers = $query->latest()->paginate(15);

        $stats = [
            'total_admins' => User::where('role', 'admin')->count(),
            'total_analysts' => User::where('role', 'analyst')->count(),
            'active_admins' => User::where('role', 'admin')->whereHas('accountStatus',fn($q)=>$q->where('status', 'active'))->count(),
            'active_analysts' => User::where('role', 'admin')->whereHas('accountStatus',fn($q)=>$q->where('status', 'analyst'))->count(),
            'active_users' => User::where('role', 'admin')->whereHas('accountStatus',fn($q)=>$q->whereIn('status',  ['admin', 'analyst']))->count(),
        ];

        return view('admin.roles.index', [
            'pageTitle' => 'Admin & Analyst Management',
            'breadcrumbItems' => $breadcrumbsItems,
            'users' => $adminUsers,
            'stats' => $stats,
            'filters' => $request->only(['search', 'role', 'status']),
        ]);
    }

    /**
     * Show the form for creating a new admin/analyst
     */
    public function create(): View
    {
        $breadcrumbsItems = [
            [
                'name' => 'Dashboard',
                'url' => route('admin.dashboard'),
                'active' => false,
            ],
            [
                'name' => 'Admin & Analyst Management',
                'url' => route('admin.roles.index'),
                'active' => false,
            ],
            [
                'name' => 'Create Admin/Analyst',
                'url' => null,
                'active' => true,
            ],
        ];

        return view('admin.roles.create', [
            'pageTitle' => 'Create Admin/Analyst',
            'breadcrumbItems' => $breadcrumbsItems,
        ]);
    }

    /**
     * Store a newly created admin/analyst
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|in:admin,analyst',
            'phone' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'city' => 'nullable|string|max:100',
        ]);

        $validated['password'] = Hash::make($validated['password']);
        $validated['email_verified_at'] = now();
        $validated['account_status'] = 'active';

        $user = User::create($validated);

        return redirect()->route('admin.roles.index')
            ->with('message', ucfirst($validated['role']) . ' created successfully.');
    }

    /**
     * Display the specified admin/analyst
     */
    public function show(User $user): View
    {
        // Ensure user is admin or analyst
        if (!in_array($user->role, ['admin', 'analyst'])) {
            abort(404);
        }

        $breadcrumbsItems = [
            [
                'name' => 'Dashboard',
                'url' => route('admin.dashboard'),
                'active' => false,
            ],
            [
                'name' => 'Admin & Analyst Management',
                'url' => route('admin.roles.index'),
                'active' => false,
            ],
            [
                'name' => $user->name,
                'url' => null,
                'active' => true,
            ],
        ];

        // Get activity stats for analysts
        $activityStats = [];
        if ($user->role === 'analyst') {
            $activityStats = [
                'approved_requests' => $user->approvedInterestRequests()->count(),
                'recent_activity' => $user->approvedInterestRequests()
                    ->with(['requester', 'target'])
                    ->latest()
                    ->take(5)
                    ->get(),
            ];
        }

        return view('admin.roles.show', [
            'pageTitle' => ucfirst($user->role) . ' Details',
            'breadcrumbItems' => $breadcrumbsItems,
            'user' => $user,
            'activityStats' => $activityStats,
        ]);
    }

    /**
     * Show the form for editing the specified admin/analyst
     */
    public function edit(User $user): View
    {
        // Ensure user is admin or analyst
        if (!in_array($user->role, ['admin', 'analyst'])) {
            abort(404);
        }

        $breadcrumbsItems = [
            [
                'name' => 'Dashboard',
                'url' => route('admin.dashboard'),
                'active' => false,
            ],
            [
                'name' => 'Admin & Analyst Management',
                'url' => route('admin.roles.index'),
                'active' => false,
            ],
            [
                'name' => 'Edit ' . $user->name,
                'url' => null,
                'active' => true,
            ],
        ];

        return view('admin.roles.edit', [
            'pageTitle' => 'Edit ' . ucfirst($user->role),
            'breadcrumbItems' => $breadcrumbsItems,
            'user' => $user,
        ]);
    }

    /**
     * Update the specified admin/analyst
     */
    public function update(Request $request, User $user): RedirectResponse
    {
        // Ensure user is admin or analyst
        if (!in_array($user->role, ['admin', 'analyst'])) {
            abort(404);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'password' => 'nullable|string|min:8|confirmed',
            'role' => 'required|in:admin,analyst',
            'phone' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'city' => 'nullable|string|max:100',
            'account_status' => 'required|in:active,locked,suspended,inactive',
        ]);

        if (!empty($validated['password'])) {
            $validated['password'] = Hash::make($validated['password']);
        } else {
            unset($validated['password']);
        }

        $user->update($validated);

        return redirect()->route('admin.roles.index')
            ->with('message', ucfirst($user->role) . ' updated successfully.');
    }

    /**
     * Remove the specified admin/analyst
     */
    public function destroy(User $user): RedirectResponse
    {
        // Ensure user is admin or analyst
        if (!in_array($user->role, ['admin', 'analyst'])) {
            abort(404);
        }

        // Prevent deletion of super-admin or self
        if ($user->role === 'super-admin' || $user->id === auth()->id()) {
            return redirect()->route('admin.roles.index')
                ->with('error', 'Cannot delete this user.');
        }

        $role = $user->role;
        $user->delete();

        return redirect()->route('admin.roles.index')
            ->with('message', ucfirst($role) . ' deleted successfully.');
    }

    /**
     * Toggle user account status
     */
    public function toggleStatus(User $user): RedirectResponse
    {
        // Ensure user is admin or analyst
        if (!in_array($user->role, ['admin', 'analyst'])) {
            abort(404);
        }

        // Get or create account status
        if (!$user->accountStatus) {
            $user->accountStatus()->create(['user_id' => $user->id]);
            $user->load('accountStatus');
        }

        $isCurrentlyLocked = $user->accountStatus->is_locked;

        if ($isCurrentlyLocked) {
            // Unlock the account
            $user->accountStatus->unlock(auth()->id());
            $action = 'activated';
        } else {
            // Lock the account
            $user->accountStatus->lock('Account locked by admin', auth()->id());
            $action = 'locked';
        }

        return redirect()->route('admin.roles.index')
            ->with('message', ucfirst($user->role) . " account {$action} successfully.");
    }
}
