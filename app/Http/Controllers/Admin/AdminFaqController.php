<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Faq;
use Illuminate\Http\Request;

class AdminFaqController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $breadcrumbItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'FAQ Management',
                'url' => route('admin.faqs.index'),
                'active' => true
            ],
        ];

        $query = Faq::with('creator');

        // Filter by target role
        if ($request->has('target_role') && $request->target_role !== '') {
            $query->where('target_role', $request->target_role);
        }

        // Filter by status
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        // Filter by category
        if ($request->has('category') && $request->category !== '') {
            $query->where('category', $request->category);
        }

        // Search by question
        if ($request->has('search') && $request->search !== '') {
            $query->where('question', 'like', '%' . $request->search . '%');
        }

        $faqs = $query->ordered()->paginate(15);

        return view('admin.faqs.index', [
            'pageTitle' => 'FAQ Management',
            'breadcrumbItems' => $breadcrumbItems,
            'faqs' => $faqs,
            'filters' => $request->only(['target_role', 'status', 'category', 'search']),
            'targetRoles' => Faq::getTargetRoles(),
            'categories' => Faq::getCategories()
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $breadcrumbItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'FAQ Management',
                'url' => route('admin.faqs.index'),
                'active' => false
            ],
            [
                'name' => 'Create FAQ',
                'url' => route('admin.faqs.create'),
                'active' => true
            ],
        ];

        return view('admin.faqs.create', [
            'pageTitle' => 'Create FAQ',
            'breadcrumbItems' => $breadcrumbItems,
            'targetRoles' => Faq::getTargetRoles(),
            'categories' => Faq::getCategories()
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'question' => 'required|string|max:500',
            'answer' => 'required|string',
            'target_role' => 'required|in:investor,startup,both',
            'status' => 'required|in:active,inactive',
            'category' => 'nullable|string|max:100',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        // Set creator
        $validated['created_by'] = auth()->id();

        // Set default sort order if not provided
        if (!isset($validated['sort_order'])) {
            $maxOrder = Faq::max('sort_order') ?? 0;
            $validated['sort_order'] = $maxOrder + 1;
        }

        Faq::create($validated);

        return redirect()->route('admin.faqs.index')
                        ->with('success', 'FAQ created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Faq $faq)
    {
        $breadcrumbItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'FAQ Management',
                'url' => route('admin.faqs.index'),
                'active' => false
            ],
            [
                'name' => 'FAQ Details',
                'url' => route('admin.faqs.show', $faq),
                'active' => true
            ],
        ];

        $faq->load('creator');

        return view('admin.faqs.show', [
            'pageTitle' => 'FAQ Details',
            'breadcrumbItems' => $breadcrumbItems,
            'faq' => $faq
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Faq $faq)
    {
        $breadcrumbItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'FAQ Management',
                'url' => route('admin.faqs.index'),
                'active' => false
            ],
            [
                'name' => 'Edit FAQ',
                'url' => route('admin.faqs.edit', $faq),
                'active' => true
            ],
        ];

        return view('admin.faqs.edit', [
            'pageTitle' => 'Edit FAQ',
            'breadcrumbItems' => $breadcrumbItems,
            'faq' => $faq,
            'targetRoles' => Faq::getTargetRoles(),
            'categories' => Faq::getCategories()
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Faq $faq)
    {
        $validated = $request->validate([
            'question' => 'required|string|max:500',
            'answer' => 'required|string',
            'target_role' => 'required|in:investor,startup,both',
            'status' => 'required|in:active,inactive',
            'category' => 'nullable|string|max:100',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        // Set default sort order if not provided
        if (!isset($validated['sort_order'])) {
            $validated['sort_order'] = $faq->sort_order;
        }

        $faq->update($validated);

        return redirect()->route('admin.faqs.index')
                        ->with('success', 'FAQ updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Faq $faq)
    {
        $faq->delete();

        return redirect()->route('admin.faqs.index')
                        ->with('success', 'FAQ deleted successfully!');
    }

    /**
     * Update the sort order of FAQs
     */
    public function updateOrder(Request $request)
    {
        $request->validate([
            'faqs' => 'required|array',
            'faqs.*.id' => 'required|exists:faqs,id',
            'faqs.*.sort_order' => 'required|integer|min:0',
        ]);

        foreach ($request->faqs as $faqData) {
            Faq::where('id', $faqData['id'])->update(['sort_order' => $faqData['sort_order']]);
        }

        return response()->json(['success' => true, 'message' => 'FAQ order updated successfully!']);
    }

    /**
     * Toggle FAQ status
     */
    public function toggleStatus(Faq $faq)
    {
        $faq->update([
            'status' => $faq->status === 'active' ? 'inactive' : 'active'
        ]);

        return redirect()->route('admin.faqs.index')
                        ->with('success', 'FAQ status updated successfully!');
    }
}
