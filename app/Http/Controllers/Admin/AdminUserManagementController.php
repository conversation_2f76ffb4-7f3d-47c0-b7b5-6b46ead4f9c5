<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserSubscription;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Spatie\QueryBuilder\QueryBuilder;
use Spatie\Permission\Models\Role;

class AdminUserManagementController extends Controller
{
    /**
     * Display a listing of users with subscription information
     */
    public function index(Request $request)
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'User Management',
                'url' => route('admin.users.index'),
                'active' => true
            ],
        ];

        $q = $request->get('q');
        $perPage = $request->get('per_page', 10);
        $status = $request->get('status');

        $users = QueryBuilder::for(User::class)
            ->allowedFilters(['name', 'email'])
            ->with(['activeSubscription.subscriptionProduct', 'accountStatus'])
            ->when($q, function ($query) use ($q) {
                $query->where(function ($subQuery) use ($q) {
                    $subQuery->where('name', 'like', "%{$q}%")
                        ->orWhere('email', 'like', "%{$q}%");
                });
            })
            ->when($status, function ($query) use ($status) {
                if ($status === 'locked') {
                    $query->where('account_locked', true);
                } elseif ($status === 'subscribed') {
                    $query->whereHas('activeSubscription');
                } elseif ($status === 'unsubscribed') {
                    $query->whereDoesntHave('activeSubscription');
                }
            })
            ->latest()
            ->paginate($perPage);

        return view('admin.users.index', [
            'pageTitle' => 'User Management',
            'breadcrumbItems' => $breadcrumbsItems,
            'users' => $users,
            'filters' => [
                'q' => $q,
                'status' => $status,
                'per_page' => $perPage
            ]
        ]);
    }

    /**
     * Display the specified user with detailed information
     */
    public function show(User $user)
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'User Management',
                'url' => route('admin.users.index'),
                'active' => false
            ],
            [
                'name' => $user->name,
                'url' => route('admin.users.show', $user),
                'active' => true
            ],
        ];

        $user->load([
            'subscriptions.subscriptionProduct',
            'activeSubscription.subscriptionProduct',
            'payments' => function ($query) {
                $query->latest()->take(10);
            },
            'invoices' => function ($query) {
                $query->latest()->take(10);
            },
            'paymentMethods' => function ($query) {
                $query->where('is_active', true);
            },
            'refunds' => function ($query) {
                $query->latest()->take(5);
            },
            'accountStatus',
            'investorProfile',
            'startupProfile'
        ]);

        // Investment Activity (for startup users)
        $investmentActivity = [];
        if ($user->role === 'startup') {
            $investmentActivity = [
                'interests_received' => \App\Models\Interest::where('startup_id', $user->id)->count(),
                'active_interests' => \App\Models\Interest::where('startup_id', $user->id)
                    ->where('status', 'pending')
                    ->count(),
                'recent_interests' => \App\Models\Interest::where('startup_id', $user->id)
                    ->with('investor')
                    ->latest()
                    ->take(5)
                    ->get(),
                'investors_list' => \App\Models\Interest::where('startup_id', $user->id)
                    ->with('investor')
                    ->get()
                    ->pluck('investor')
                    ->unique('id')
            ];
        } elseif ($user->role === 'investor') {
            $investmentActivity = [
                'interests_sent' => \App\Models\Interest::where('investor_id', $user->id)->count(),
                'active_interests' => \App\Models\Interest::where('investor_id', $user->id)
                    ->where('status', 'pending')
                    ->count(),
                'recent_interests' => \App\Models\Interest::where('investor_id', $user->id)
                    ->with('startup')
                    ->latest()
                    ->take(5)
                    ->get(),
                'startups_list' => \App\Models\Interest::where('investor_id', $user->id)
                    ->with('startup')
                    ->get()
                    ->pluck('startup')
                    ->unique('id')
            ];
        }

        // Financial Information - Use invoices if payments don't exist
        $totalPayments = $user->payments()->where('status', 'succeeded')->sum('amount');
        $totalFromInvoices = $user->invoices()->where('status', 'paid')->sum('total');

        // If no payment records exist but invoices do, use invoice data
        if ($totalPayments == 0 && $totalFromInvoices > 0) {
            $totalSpent = $totalFromInvoices;
            $lifetimeValue = $totalFromInvoices;
        } else {
            // Use payment data (convert from cents if needed)
            $totalSpent = $totalPayments > 100 ? $totalPayments / 100 : $totalPayments;
            $lifetimeValue = $totalSpent;
        }

        $financialInfo = [
            'total_payments' => $totalSpent,
            'total_refunds' => $user->refunds()->where('status', 'succeeded')->sum('amount'),
            'failed_payments_count' => $user->payments()->where('status', 'failed')->count(),
            'payment_methods_count' => $user->paymentMethods()->where('is_active', true)->count(),
            'next_billing_date' => $user->activeSubscription ? $user->activeSubscription->current_period_end : null,
            'subscription_value' => $user->activeSubscription ? $user->activeSubscription->subscriptionProduct->price : 0,
            'lifetime_value' => $lifetimeValue,
            'total_invoices' => $user->invoices()->count(),
            'paid_invoices' => $user->invoices()->where('status', 'paid')->count(),
            'pending_invoices' => $user->invoices()->where('status', 'open')->count(),
        ];

        // Account Details
        $accountDetails = [
            'account_age_days' => $user->created_at->diffInDays(now()),
            'last_login' => null, // Placeholder - would need login tracking
            'email_verified' => $user->email_verified_at !== null,
            'account_locked' => $user->account_locked,
            'login_count' => 0, // Placeholder - would need login tracking
            'esg_completed' => false, // Placeholder - would need ESG assessment model
            'documents_uploaded' => false, // Placeholder - would need document model
            'profile_completed' => $user->hasCompletedProfile(),
            'categories_count' => $user->categories->count(),
            'phone' => $user->phone,
            'city' => $user->city,
            'country' => $user->country,
        ];

        // User Categories
        $userCategories = [
            'selected_categories' => $user->categories,
            'categories_count' => $user->categories->count(),
            'max_categories' => 5, // Based on platform rules
        ];

        // Role-specific Information
        $roleSpecificInfo = [];
        if ($user->role === 'startup' && $user->startupProfile) {
            $roleSpecificInfo = [
                'company_name' => $user->startupProfile->company_name,
                'company_description' => $user->startupProfile->company_description,
                'funding_stage' => $user->startupProfile->funding_stage,
                'funding_amount_sought' => $user->startupProfile->funding_amount_sought,
                'employee_count' => $user->startupProfile->employee_count,
                'website' => $user->startupProfile->website,
                'linkedin' => $user->startupProfile->linkedin,
                'profile_completed' => $user->startupProfile->profile_completed,
                'esg_completed' => $user->startupProfile->esg_completed,
            ];
        } elseif ($user->role === 'investor' && $user->investorProfile) {
            $roleSpecificInfo = [
                'investment_budget_min' => $user->investorProfile->investment_budget_min,
                'investment_budget_max' => $user->investorProfile->investment_budget_max,
                'risk_tolerance' => $user->investorProfile->risk_tolerance,
                'investment_experience' => $user->investorProfile->investment_experience,
                'website' => $user->investorProfile->website,
                'linkedin' => $user->investorProfile->linkedin,
                'profile_completed' => $user->investorProfile->profile_completed,
                'investment_preferences' => $user->investorProfile->investment_preferences,
            ];
        }

        // Subscription Management
        $subscriptionManagement = [
            'current_subscription' => $user->activeSubscription,
            'subscription_history' => $user->subscriptions()->with('subscriptionProduct')->latest()->get(),
            'trial_used' => false, // Placeholder - would need trial tracking
            'subscription_changes_count' => $user->subscriptions()->count(),
            'cancellation_history' => $user->subscriptions()->where('status', 'canceled')->count(),
        ];

        return view('admin.users.show', [
            'pageTitle' => 'User Details - ' . $user->name,
            'breadcrumbItems' => $breadcrumbsItems,
            'user' => $user,
            'investmentActivity' => $investmentActivity,
            'financialInfo' => $financialInfo,
            'accountDetails' => $accountDetails,
            'subscriptionManagement' => $subscriptionManagement,
            'userCategories' => $userCategories,
            'roleSpecificInfo' => $roleSpecificInfo
        ]);
    }

    /**
     * Show the form for editing the specified user
     */
    public function edit(User $user)
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'User Management',
                'url' => route('admin.users.index'),
                'active' => false
            ],
            [
                'name' => 'Edit ' . $user->name,
                'url' => route('admin.users.edit', $user),
                'active' => true
            ],
        ];

        $roles = Role::with(['permissions'])->get();

        return view('admin.users.edit', [
            'pageTitle' => 'Edit User - ' . $user->name,
            'breadcrumbItems' => $breadcrumbsItems,
            'user' => $user,
            'roles' => $roles
        ]);
    }

    /**
     * Update the specified user
     */
    public function update(Request $request, User $user): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'roles' => 'array',
            'roles.*' => 'exists:roles,name'
        ]);

        $user->update([
            'name' => $request->name,
            'email' => $request->email,
        ]);

        if ($request->has('roles')) {
            $user->syncRoles($request->roles);
        }

        return redirect()->route('admin.users.show', $user)
            ->with('message', 'User updated successfully.');
    }

    /**
     * Update user account status (lock/unlock)
     */
    public function updateAccountStatus(Request $request, User $user): RedirectResponse
    {
        $request->validate([
            'action' => 'required|in:lock,unlock',
            'reason' => 'required_if:action,lock|string|max:500'
        ]);

        if ($request->action === 'lock') {
            $user->lockAccount($request->reason);
            $message = 'User account has been locked.';
        } else {
            $user->unlockAccount();
            $message = 'User account has been unlocked.';
        }

        return redirect()->route('admin.users.show', $user)
            ->with('message', $message);
    }

    /**
     * Get subscription analytics for a user
     */
    public function subscriptionAnalytics(User $user)
    {
        $subscriptions = $user->subscriptions()
            ->with('subscriptionProduct')
            ->orderBy('created_at', 'desc')
            ->get();

        $totalSpent = $user->payments()
            ->where('status', 'succeeded')
            ->sum('amount') / 100; // Convert from cents

        $analytics = [
            'total_subscriptions' => $subscriptions->count(),
            'active_subscriptions' => $subscriptions->where('status', 'active')->count(),
            'total_spent' => $totalSpent,
            'subscription_history' => $subscriptions
        ];

        return response()->json($analytics);
    }

    /**
     * Get payment history for a user
     */
    public function paymentHistory(User $user)
    {
        $payments = $user->payments()
            ->with('subscription.product')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('admin.users.payment-history', [
            'user' => $user,
            'payments' => $payments
        ]);
    }
}
