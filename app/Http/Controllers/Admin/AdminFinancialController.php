<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\Invoice;
use App\Models\Refund;
use App\Models\UserSubscription;
use App\Services\StripeService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Spatie\QueryBuilder\QueryBuilder;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AdminFinancialController extends Controller
{
    protected StripeService $stripeService;

    public function __construct(StripeService $stripeService)
    {
        $this->stripeService = $stripeService;
    }

    /**
     * Display financial overview dashboard
     */
    public function index()
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Financial Overview',
                'url' => route('admin.financial.index'),
                'active' => true
            ],
        ];

        // Key Financial Metrics
        $totalRefunds = Refund::where('status', 'succeeded')->sum('amount') / 100;
        $failedPayments = Payment::where('status', 'failed')
            ->whereMonth('created_at', Carbon::now()->month)
            ->count();

        // Monthly Payment Trend
        $monthlyPaymentData = [];
        $monthLabels = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $monthLabels[] = $date->format('M Y');
            $payments = Payment::where('status', 'succeeded')
                ->whereMonth('created_at', $date->month)
                ->whereYear('created_at', $date->year)
                ->count();
            $monthlyPaymentData[] = $payments;
        }

        // Payment Status Distribution
        $paymentStats = Payment::select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        // Ensure we have default data for empty payment stats
        if (empty($paymentStats)) {
            $paymentStats = [
                'succeeded' => 0,
                'failed' => 0,
                'pending' => 0
            ];
        }

        // Recent Transactions
        $recentPayments = Payment::with(['user', 'subscription.product'])
            ->latest()
            ->take(10)
            ->get();

        $recentRefunds = Refund::with(['payment.user'])
            ->latest()
            ->take(5)
            ->get();

        return view('admin.financial.index', [
            'pageTitle' => 'Financial Overview',
            'breadcrumbItems' => $breadcrumbsItems,
            'metrics' => [
                'totalRefunds' => $totalRefunds,
                'failedPayments' => $failedPayments
            ],
            'chartData' => [
                'monthlyPayments' => [
                    'labels' => $monthLabels,
                    'data' => $monthlyPaymentData
                ],
                'paymentStats' => $paymentStats
            ],
            'recentTransactions' => [
                'payments' => $recentPayments,
                'refunds' => $recentRefunds
            ]
        ]);
    }

    /**
     * Display payments listing
     */
    public function payments(Request $request)
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Financial',
                'url' => route('admin.financial.index'),
                'active' => false
            ],
            [
                'name' => 'Payments',
                'url' => route('admin.financial.payments'),
                'active' => true
            ],
        ];

        $q = $request->get('q');
        $status = $request->get('status');
        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');
        $perPage = $request->get('per_page', 10);

        $payments = QueryBuilder::for(Payment::class)
            ->allowedFilters(['status'])
            ->with(['user', 'subscription.product'])
            ->when($q, function ($query) use ($q) {
                $query->whereHas('user', function ($userQuery) use ($q) {
                    $userQuery->where('name', 'like', "%{$q}%")
                        ->orWhere('email', 'like', "%{$q}%");
                });
            })
            ->when($status, function ($query) use ($status) {
                $query->where('status', $status);
            })
            ->when($dateFrom, function ($query) use ($dateFrom) {
                $query->whereDate('created_at', '>=', $dateFrom);
            })
            ->when($dateTo, function ($query) use ($dateTo) {
                $query->whereDate('created_at', '<=', $dateTo);
            })
            ->latest()
            ->paginate($perPage);

        $statusOptions = ['succeeded', 'failed', 'pending', 'cancelled'];

        return view('admin.financial.payments', [
            'pageTitle' => 'Payment Management',
            'breadcrumbItems' => $breadcrumbsItems,
            'payments' => $payments,
            'statusOptions' => $statusOptions,
            'filters' => [
                'q' => $q,
                'status' => $status,
                'date_from' => $dateFrom,
                'date_to' => $dateTo,
                'per_page' => $perPage
            ]
        ]);
    }

    /**
     * Display specific payment details
     */
    public function showPayment(Payment $payment)
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Financial',
                'url' => route('admin.financial.index'),
                'active' => false
            ],
            [
                'name' => 'Payments',
                'url' => route('admin.financial.payments'),
                'active' => false
            ],
            [
                'name' => 'Payment #' . $payment->id,
                'url' => route('admin.financial.payments.show', $payment),
                'active' => true
            ],
        ];

        $payment->load(['user', 'subscription.product', 'refunds']);

        return view('admin.financial.payment-details', [
            'pageTitle' => 'Payment Details #' . $payment->id,
            'breadcrumbItems' => $breadcrumbsItems,
            'payment' => $payment
        ]);
    }

    /**
     * Display invoices listing
     */
    public function invoices(Request $request)
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Financial',
                'url' => route('admin.financial.index'),
                'active' => false
            ],
            [
                'name' => 'Invoices',
                'url' => route('admin.financial.invoices'),
                'active' => true
            ],
        ];

        $q = $request->get('q');
        $status = $request->get('status');
        $perPage = $request->get('per_page', 10);

        $invoices = QueryBuilder::for(Invoice::class)
            ->allowedFilters(['status'])
            ->with(['user', 'userSubscription.product'])
            ->when($q, function ($query) use ($q) {
                $query->where('invoice_number', 'like', "%{$q}%")
                    ->orWhereHas('user', function ($userQuery) use ($q) {
                        $userQuery->where('name', 'like', "%{$q}%")
                            ->orWhere('email', 'like', "%{$q}%");
                    });
            })
            ->when($status, function ($query) use ($status) {
                $query->where('status', $status);
            })
            ->latest()
            ->paginate($perPage);

        $statusOptions = ['draft', 'open', 'paid', 'void', 'uncollectible'];

        return view('admin.financial.invoices', [
            'pageTitle' => 'Invoice Management',
            'breadcrumbItems' => $breadcrumbsItems,
            'invoices' => $invoices,
            'statusOptions' => $statusOptions,
            'filters' => [
                'q' => $q,
                'status' => $status,
                'per_page' => $perPage
            ]
        ]);
    }

    /**
     * Display specific invoice details
     */
    public function showInvoice(Invoice $invoice)
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Financial',
                'url' => route('admin.financial.index'),
                'active' => false
            ],
            [
                'name' => 'Invoices',
                'url' => route('admin.financial.invoices'),
                'active' => false
            ],
            [
                'name' => 'Invoice #' . $invoice->invoice_number,
                'url' => route('admin.financial.invoices.show', $invoice),
                'active' => true
            ],
        ];

        $invoice->load(['user', 'subscription.product']);

        return view('admin.financial.invoice-details', [
            'pageTitle' => 'Invoice Details #' . $invoice->invoice_number,
            'breadcrumbItems' => $breadcrumbsItems,
            'invoice' => $invoice
        ]);
    }

    /**
     * Display refunds listing
     */
    public function refunds(Request $request)
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Financial',
                'url' => route('admin.financial.index'),
                'active' => false
            ],
            [
                'name' => 'Refunds',
                'url' => route('admin.financial.refunds'),
                'active' => true
            ],
        ];

        $q = $request->get('q');
        $status = $request->get('status');
        $perPage = $request->get('per_page', 10);
        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');

        $refunds = QueryBuilder::for(Refund::class)
            ->allowedFilters(['status'])
            ->with(['payment.user', 'payment.subscription.product'])
            ->when($q, function ($query) use ($q) {
                $query->whereHas('payment.user', function ($userQuery) use ($q) {
                    $userQuery->where('name', 'like', "%{$q}%")
                        ->orWhere('email', 'like', "%{$q}%");
                });
            })
            ->when($status, function ($query) use ($status) {
                $query->where('status', $status);
            })
            ->when($dateFrom, function ($query) use ($dateFrom) {
                $query->whereDate('created_at', '>=', $dateFrom);
            })
            ->when($dateTo, function ($query) use ($dateTo) {
                $query->whereDate('created_at', '<=', $dateTo);
            })
            ->latest()
            ->paginate($perPage);

        $statusOptions = ['pending', 'succeeded', 'failed', 'cancelled'];

        return view('admin.financial.refunds', [
            'pageTitle' => 'Refund Management',
            'breadcrumbItems' => $breadcrumbsItems,
            'refunds' => $refunds,
            'statusOptions' => $statusOptions,
            'filters' => [
                'q' => $q,
                'status' => $status,
                'per_page' => $perPage,
                'date_from' => $dateFrom,
                'date_to' => $dateTo
            ]
        ]);
    }

    /**
     * Process a refund
     */
    public function processRefund(Request $request): RedirectResponse
    {
        $request->validate([
            'payment_id' => 'required|exists:payments,id',
            'amount' => 'required|numeric|min:0.01',
            'reason' => 'required|string|max:500'
        ]);

        try {
            $payment = Payment::findOrFail($request->payment_id);

            // Convert amount to cents
            $amountCents = $request->amount * 100;

            // Validate refund amount
            if ($amountCents > $payment->amount) {
                return redirect()->back()
                    ->with('error', 'Refund amount cannot exceed the original payment amount.');
            }

            // Process refund through Stripe
            $stripeRefund = $this->stripeService->createRefund(
                $payment->stripe_payment_intent_id,
                $amountCents,
                $request->reason
            );

            // Create local refund record
            Refund::create([
                'payment_id' => $payment->id,
                'stripe_refund_id' => $stripeRefund->id,
                'amount' => $amountCents,
                'reason' => $request->reason,
                'status' => 'succeeded'
            ]);

            return redirect()->route('admin.financial.refunds')
                ->with('message', 'Refund processed successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to process refund: ' . $e->getMessage());
        }
    }

    /**
     * Display revenue analytics
     */
    public function revenueAnalytics()
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Revenue Analytics',
                'url' => route('admin.financial.revenue-analytics'),
                'active' => true
            ],
        ];

        // Monthly revenue for the last 12 months
        $monthlyData = [];
        $monthLabels = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $monthLabels[] = $date->format('M Y');

            $revenue = Payment::where('status', 'succeeded')
                ->whereMonth('created_at', $date->month)
                ->whereYear('created_at', $date->year)
                ->sum('amount') / 100;

            $refunds = Refund::where('status', 'succeeded')
                ->whereMonth('created_at', $date->month)
                ->whereYear('created_at', $date->year)
                ->sum('amount') / 100;

            $monthlyData['revenue'][] = $revenue;
            $monthlyData['refunds'][] = $refunds;
            $monthlyData['net'][] = $revenue - $refunds;
        }

        // Revenue by product
        $productRevenue = DB::table('payments')
            ->join('user_subscriptions', 'payments.user_subscription_id', '=', 'user_subscriptions.id')
            ->join('subscription_products', 'user_subscriptions.subscription_product_id', '=', 'subscription_products.id')
            ->where('payments.status', 'succeeded')
            ->select('subscription_products.name', DB::raw('SUM(payments.amount) as total_revenue'))
            ->groupBy('subscription_products.id', 'subscription_products.name')
            ->orderBy('total_revenue', 'desc')
            ->get()
            ->map(function ($item) {
                $item->total_revenue = $item->total_revenue / 100; // Convert from cents
                return $item;
            });

        // Calculate analytics data that the view expects
        $totalRevenue = Payment::where('status', 'succeeded')->sum('amount') / 100;
        $previousRevenue = Payment::where('status', 'succeeded')
            ->whereMonth('created_at', Carbon::now()->subMonth()->month)
            ->whereYear('created_at', Carbon::now()->subMonth()->year)
            ->sum('amount') / 100;

        $totalRefunds = Refund::where('status', 'succeeded')->sum('amount') / 100;
        $previousRefunds = Refund::where('status', 'succeeded')
            ->whereMonth('created_at', Carbon::now()->subMonth()->month)
            ->whereYear('created_at', Carbon::now()->subMonth()->year)
            ->sum('amount') / 100;

        $netRevenue = $totalRevenue - $totalRefunds;

        $revenueChange = $totalRevenue - $previousRevenue;
        $revenueGrowth = $previousRevenue > 0 ? (($totalRevenue - $previousRevenue) / $previousRevenue) * 100 : 0;

        $refundsChange = $totalRefunds - $previousRefunds;
        $refundsGrowth = $previousRefunds > 0 ? (($totalRefunds - $previousRefunds) / $previousRefunds) * 100 : 0;

        // Payment counts
        $totalPayments = Payment::where('status', 'succeeded')->count();
        $previousPayments = Payment::where('status', 'succeeded')
            ->whereMonth('created_at', Carbon::now()->subMonth()->month)
            ->whereYear('created_at', Carbon::now()->subMonth()->year)
            ->count();

        $paymentsChange = $totalPayments - $previousPayments;
        $paymentsGrowth = $previousPayments > 0 ? (($totalPayments - $previousPayments) / $previousPayments) * 100 : 0;

        // Average order value
        $avgOrderValue = $totalPayments > 0 ? $totalRevenue / $totalPayments : 0;
        $previousAvgOrderValue = $previousPayments > 0 ? $previousRevenue / $previousPayments : 0;
        $avgOrderValueChange = $avgOrderValue - $previousAvgOrderValue;
        $avgOrderValueGrowth = $previousAvgOrderValue > 0 ? (($avgOrderValue - $previousAvgOrderValue) / $previousAvgOrderValue) * 100 : 0;

        // Payment methods breakdown
        $paymentMethods = Payment::where('status', 'succeeded')
            ->select('payment_method_type', DB::raw('count(*) as count'))
            ->groupBy('payment_method_type')
            ->pluck('count', 'payment_method_type')
            ->toArray();

        // Ensure we have default data for empty payment methods
        if (empty($paymentMethods)) {
            $paymentMethods = ['card' => 0];
        }

        // Product revenue breakdown
        $productRevenueArray = [];
        foreach ($productRevenue as $product) {
            $productRevenueArray[$product->name] = $product->total_revenue;
        }

        // Ensure we have default data for empty product revenue
        if (empty($productRevenueArray)) {
            $productRevenueArray = ['No Products' => 0];
        }

        return view('admin.financial.revenue-analytics', [
            'pageTitle' => 'Revenue Analytics',
            'breadcrumbItems' => $breadcrumbsItems,
            'analytics' => [
                'totalRevenue' => $totalRevenue,
                'previousRevenue' => $previousRevenue,
                'netRevenue' => $netRevenue,
                'totalRefunds' => $totalRefunds,
                'previousRefunds' => $previousRefunds,
                'revenueChange' => $revenueChange,
                'revenueGrowth' => $revenueGrowth,
                'refundsChange' => $refundsChange,
                'refundsGrowth' => $refundsGrowth,
                'totalPayments' => $totalPayments,
                'previousPayments' => $previousPayments,
                'paymentsChange' => $paymentsChange,
                'paymentsGrowth' => $paymentsGrowth,
                'avgOrderValue' => $avgOrderValue,
                'previousAvgOrderValue' => $previousAvgOrderValue,
                'avgOrderValueChange' => $avgOrderValueChange,
                'avgOrderValueGrowth' => $avgOrderValueGrowth,
                'paymentMethods' => $paymentMethods,
                'productRevenue' => $productRevenueArray
            ],
            'chartData' => [
                'revenueTrend' => [
                    'labels' => $monthLabels,
                    'revenue' => $monthlyData['revenue'],
                    'netRevenue' => $monthlyData['net']
                ],
                'monthlyComparison' => [
                    'labels' => $monthLabels,
                    'revenue' => $monthlyData['revenue'],
                    'refunds' => $monthlyData['refunds']
                ]
            ]
        ]);
    }
}
