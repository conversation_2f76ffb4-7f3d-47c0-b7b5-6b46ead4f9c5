<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Blog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;

class AdminBlogController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $breadcrumbItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Blog Management',
                'url' => route('admin.blogs.index'),
                'active' => true
            ],
        ];

        $query = Blog::with(['author', 'media']);

        // Filter by status
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        // Search by title
        if ($request->has('search') && $request->search !== '') {
            $query->where('title', 'like', '%' . $request->search . '%');
        }

        // Filter by featured
        if ($request->has('featured') && $request->featured !== '') {
            $query->where('featured', $request->boolean('featured'));
        }

        $blogs = $query->latest()->paginate(10);

        return view('admin.blogs.index', [
            'pageTitle' => 'Blog Management',
            'breadcrumbItems' => $breadcrumbItems,
            'blogs' => $blogs,
            'filters' => $request->only(['status', 'search', 'featured'])
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $breadcrumbItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Blog Management',
                'url' => route('admin.blogs.index'),
                'active' => false
            ],
            [
                'name' => 'Create Blog Post',
                'url' => route('admin.blogs.create'),
                'active' => true
            ],
        ];

        // Get available tags and categories from taxonomy
        $tags = Taxonomy::where('type', 'tag')->get();
        $categories = Taxonomy::where('type', 'category')->get();

        return view('admin.blogs.create', [
            'pageTitle' => 'Create Blog Post',
            'breadcrumbItems' => $breadcrumbItems,
            'tags' => $tags,
            'categories' => $categories
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'excerpt' => 'nullable|string|max:500',
            'content' => 'required|string',
            'status' => 'required|in:draft,published,archived',
            'featured' => 'boolean',
            'published_at' => 'nullable|date',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'tags' => 'nullable|array',
            'tags.*' => 'exists:taxonomies,id',
            'categories' => 'nullable|array',
            'categories.*' => 'exists:taxonomies,id',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
        ]);

        // Set published_at if status is published and no date is set
        if ($validated['status'] === 'published' && empty($validated['published_at'])) {
            $validated['published_at'] = now();
        }

        // Set author
        $validated['author_id'] = auth()->id();

        // Generate unique slug
        $validated['slug'] = $this->generateUniqueSlug($validated['title']);

        // Handle meta data
        $validated['meta_data'] = [
            'meta_title' => $request->meta_title,
            'meta_description' => $request->meta_description,
        ];

        $blog = Blog::create($validated);

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            $blog->addMediaFromRequest('featured_image')
                  ->toMediaCollection('featured_images');
        }

        // Attach taxonomies
        if ($request->has('tags')) {
            $blog->attachTaxonomies($request->tags);
        }
        if ($request->has('categories')) {
            $blog->attachTaxonomies($request->categories);
        }

        return redirect()->route('admin.blogs.index')
                        ->with('success', 'Blog post created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Blog $blog)
    {
        $breadcrumbItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Blog Management',
                'url' => route('admin.blogs.index'),
                'active' => false
            ],
            [
                'name' => $blog->title,
                'url' => route('admin.blogs.show', $blog),
                'active' => true
            ],
        ];

        $blog->load(['author', 'media', 'taxonomies']);

        return view('admin.blogs.show', [
            'pageTitle' => $blog->title,
            'breadcrumbItems' => $breadcrumbItems,
            'blog' => $blog
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Blog $blog)
    {
        $breadcrumbItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Blog Management',
                'url' => route('admin.blogs.index'),
                'active' => false
            ],
            [
                'name' => 'Edit: ' . $blog->title,
                'url' => route('admin.blogs.edit', $blog),
                'active' => true
            ],
        ];

        // Get available tags and categories from taxonomy
        $tags = Taxonomy::where('type', 'tag')->get();
        $categories = Taxonomy::where('type', 'category')->get();

        // Get currently attached taxonomies
        $blog->load('taxonomies');
        $selectedTags = $blog->taxonomies->where('type', 'tag')->pluck('id')->toArray();
        $selectedCategories = $blog->taxonomies->where('type', 'category')->pluck('id')->toArray();

        return view('admin.blogs.edit', [
            'pageTitle' => 'Edit Blog Post',
            'breadcrumbItems' => $breadcrumbItems,
            'blog' => $blog,
            'tags' => $tags,
            'categories' => $categories,
            'selectedTags' => $selectedTags,
            'selectedCategories' => $selectedCategories
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Blog $blog)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'excerpt' => 'nullable|string|max:500',
            'content' => 'required|string',
            'status' => 'required|in:draft,published,archived',
            'featured' => 'boolean',
            'published_at' => 'nullable|date',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'tags' => 'nullable|array',
            'tags.*' => 'exists:taxonomies,id',
            'categories' => 'nullable|array',
            'categories.*' => 'exists:taxonomies,id',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
        ]);

        // Set published_at if status is published and no date is set
        if ($validated['status'] === 'published' && empty($validated['published_at'])) {
            $validated['published_at'] = now();
        }

        // Update slug if title changed
        if ($blog->title !== $validated['title']) {
            $validated['slug'] = $this->generateUniqueSlug($validated['title'], $blog->id);
        }

        // Handle meta data
        $validated['meta_data'] = [
            'meta_title' => $request->meta_title,
            'meta_description' => $request->meta_description,
        ];

        $blog->update($validated);

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            // Remove old featured image
            $blog->clearMediaCollection('featured_images');

            $blog->addMediaFromRequest('featured_image')
                  ->toMediaCollection('featured_images');
        }

        // Sync taxonomies
        $blog->detachTaxonomies();
        if ($request->has('tags')) {
            $blog->attachTaxonomies($request->tags);
        }
        if ($request->has('categories')) {
            $blog->attachTaxonomies($request->categories);
        }

        return redirect()->route('admin.blogs.index')
                        ->with('success', 'Blog post updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Blog $blog)
    {
        // Remove associated media
        $blog->clearMediaCollection('featured_images');

        // Detach taxonomies
        $blog->detachTaxonomies();

        // Delete the blog
        $blog->delete();

        return redirect()->route('admin.blogs.index')
                        ->with('success', 'Blog post deleted successfully!');
    }

    /**
     * Generate a unique slug for the blog post
     */
    private function generateUniqueSlug(string $title, ?int $excludeId = null): string
    {
        $slug = Str::slug($title);
        $originalSlug = $slug;
        $counter = 1;

        while (true) {
            $query = Blog::where('slug', $slug);

            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }

            if (!$query->exists()) {
                break;
            }

            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }
}
