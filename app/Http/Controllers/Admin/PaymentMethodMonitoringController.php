<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PaymentMethod;
use App\Models\User;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PaymentMethodMonitoringController extends Controller
{
    /**
     * Display payment method monitoring dashboard
     */
    public function index(Request $request)
    {
        $breadcrumbItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Payment Method Monitoring',
                'url' => route('admin.payment-methods.index'),
                'active' => true
            ],
        ];

        // Get filters
        $filters = [
            'role' => $request->get('role', ''),
            'card_brand' => $request->get('card_brand', ''),
            'status' => $request->get('status', ''),
            'search' => $request->get('search', ''),
            'per_page' => $request->get('per_page', 25),
        ];

        // Build query for payment methods with user data
        $query = PaymentMethod::with(['user'])
            ->select('payment_methods.*')
            ->join('users', 'payment_methods.user_id', '=', 'users.id')
            ->whereIn('users.role', ['investor', 'startup']);

        // Apply filters
        if ($filters['role']) {
            $query->where('users.role', $filters['role']);
        }

        if ($filters['card_brand']) {
            $query->where('payment_methods.card_brand', $filters['card_brand']);
        }

        if ($filters['status']) {
            if ($filters['status'] === 'active') {
                $query->where('payment_methods.is_active', true);
            } elseif ($filters['status'] === 'inactive') {
                $query->where('payment_methods.is_active', false);
            }
        }

        if ($filters['search']) {
            $query->where(function($q) use ($filters) {
                $q->where('users.name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('users.email', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('payment_methods.card_last_four', 'like', '%' . $filters['search'] . '%');
            });
        }

        $paymentMethods = $query->orderBy('payment_methods.created_at', 'desc')
                               ->paginate($filters['per_page']);

        // Get statistics
        $stats = $this->getPaymentMethodStats();

        // Get card brand options
        $cardBrands = PaymentMethod::distinct()
                                  ->pluck('card_brand')
                                  ->filter()
                                  ->sort()
                                  ->values();

        return view('admin.payment-methods.index', [
            'pageTitle' => 'Payment Method Monitoring',
            'breadcrumbItems' => $breadcrumbItems,
            'paymentMethods' => $paymentMethods,
            'filters' => $filters,
            'stats' => $stats,
            'cardBrands' => $cardBrands,
        ]);
    }

    /**
     * Display 3D Secure monitoring dashboard
     */
    public function threeDSecure(Request $request)
    {
        $breadcrumbItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Payment Method Monitoring',
                'url' => route('admin.payment-methods.index'),
                'active' => false
            ],
            [
                'name' => '3D Secure Events',
                'url' => route('admin.payment-methods.three-d-secure'),
                'active' => true
            ],
        ];

        // Get filters
        $filters = [
            'status' => $request->get('status', ''),
            'date_from' => $request->get('date_from', ''),
            'date_to' => $request->get('date_to', ''),
            'search' => $request->get('search', ''),
            'per_page' => $request->get('per_page', 25),
        ];

        // Build query for 3D Secure events (payments that required authentication)
        $query = Payment::with(['user', 'paymentMethod'])
            ->where(function($q) {
                $q->where('status', 'requires_action')
                  ->orWhere('metadata->three_d_secure_required', true)
                  ->orWhereNotNull('metadata->three_d_secure_url');
            });

        // Apply filters
        if ($filters['status']) {
            $query->where('status', $filters['status']);
        }

        if ($filters['date_from']) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if ($filters['date_to']) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        if ($filters['search']) {
            $query->whereHas('user', function($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('email', 'like', '%' . $filters['search'] . '%');
            });
        }

        $threeDSecureEvents = $query->orderBy('created_at', 'desc')
                                   ->paginate($filters['per_page']);

        // Get 3D Secure statistics
        $threeDStats = $this->getThreeDSecureStats();

        return view('admin.payment-methods.three-d-secure', [
            'pageTitle' => '3D Secure Monitoring',
            'breadcrumbItems' => $breadcrumbItems,
            'threeDSecureEvents' => $threeDSecureEvents,
            'filters' => $filters,
            'stats' => $threeDStats,
        ]);
    }

    /**
     * Display payment method analytics
     */
    public function analytics(Request $request)
    {
        $breadcrumbItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Payment Method Monitoring',
                'url' => route('admin.payment-methods.index'),
                'active' => false
            ],
            [
                'name' => 'Analytics',
                'url' => route('admin.payment-methods.analytics'),
                'active' => true
            ],
        ];

        $period = $request->get('period', '30'); // days

        // Get analytics data
        $analytics = $this->getPaymentMethodAnalytics($period);

        return view('admin.payment-methods.analytics', [
            'pageTitle' => 'Payment Method Analytics',
            'breadcrumbItems' => $breadcrumbItems,
            'analytics' => $analytics,
            'period' => $period,
        ]);
    }

    /**
     * Get payment method statistics
     */
    private function getPaymentMethodStats()
    {
        $totalPaymentMethods = PaymentMethod::count();
        $activePaymentMethods = PaymentMethod::where('is_active', true)->count();
        
        $investorPaymentMethods = PaymentMethod::whereHas('user', function($q) {
            $q->where('role', 'investor');
        })->count();
        
        $startupPaymentMethods = PaymentMethod::whereHas('user', function($q) {
            $q->where('role', 'startup');
        })->count();

        $defaultPaymentMethods = PaymentMethod::where('is_default', true)->count();

        // Card brand distribution
        $cardBrandStats = PaymentMethod::select('card_brand', DB::raw('count(*) as count'))
                                      ->groupBy('card_brand')
                                      ->orderBy('count', 'desc')
                                      ->get();

        return [
            'total' => $totalPaymentMethods,
            'active' => $activePaymentMethods,
            'inactive' => $totalPaymentMethods - $activePaymentMethods,
            'investor' => $investorPaymentMethods,
            'startup' => $startupPaymentMethods,
            'default' => $defaultPaymentMethods,
            'card_brands' => $cardBrandStats,
        ];
    }

    /**
     * Get 3D Secure statistics
     */
    private function getThreeDSecureStats()
    {
        $total3DSecure = Payment::where(function($q) {
            $q->where('status', 'requires_action')
              ->orWhere('metadata->three_d_secure_required', true);
        })->count();

        $successful3DSecure = Payment::where('metadata->three_d_secure_required', true)
                                    ->where('status', 'succeeded')
                                    ->count();

        $failed3DSecure = Payment::where('metadata->three_d_secure_required', true)
                                ->where('status', 'failed')
                                ->count();

        $pending3DSecure = Payment::where('status', 'requires_action')->count();

        // Recent 3D Secure events (last 30 days)
        $recent3DSecure = Payment::where(function($q) {
            $q->where('status', 'requires_action')
              ->orWhere('metadata->three_d_secure_required', true);
        })->where('created_at', '>=', Carbon::now()->subDays(30))->count();

        return [
            'total' => $total3DSecure,
            'successful' => $successful3DSecure,
            'failed' => $failed3DSecure,
            'pending' => $pending3DSecure,
            'recent' => $recent3DSecure,
            'success_rate' => $total3DSecure > 0 ? round(($successful3DSecure / $total3DSecure) * 100, 2) : 0,
        ];
    }

    /**
     * Get payment method analytics data
     */
    private function getPaymentMethodAnalytics($days)
    {
        $startDate = Carbon::now()->subDays($days);

        // Payment methods added over time
        $paymentMethodsOverTime = PaymentMethod::select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as count')
            )
            ->where('created_at', '>=', $startDate)
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Usage analytics (payments per payment method)
        // Note: Since payments table doesn't have payment_method_id, we join by user_id
        $usageAnalytics = PaymentMethod::select(
                'payment_methods.id',
                'payment_methods.card_brand',
                'payment_methods.card_last_four',
                'users.name as user_name',
                'users.role as user_role',
                DB::raw('COUNT(payments.id) as payment_count'),
                DB::raw('SUM(CASE WHEN payments.status = "succeeded" THEN payments.amount ELSE 0 END) as total_amount')
            )
            ->leftJoin('payments', 'payment_methods.user_id', '=', 'payments.user_id')
            ->join('users', 'payment_methods.user_id', '=', 'users.id')
            ->where('payment_methods.created_at', '>=', $startDate)
            ->groupBy('payment_methods.id', 'payment_methods.card_brand', 'payment_methods.card_last_four', 'users.name', 'users.role')
            ->orderBy('payment_count', 'desc')
            ->limit(20)
            ->get();

        return [
            'payment_methods_over_time' => $paymentMethodsOverTime,
            'usage_analytics' => $usageAnalytics,
        ];
    }
}
