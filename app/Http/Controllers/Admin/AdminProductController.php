<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SubscriptionProduct;
use App\Services\StripeService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Spatie\QueryBuilder\QueryBuilder;

class AdminProductController extends Controller
{
    protected StripeService $stripeService;

    public function __construct(StripeService $stripeService)
    {
        $this->stripeService = $stripeService;
    }

    /**
     * Display a listing of subscription products
     */
    public function index(Request $request)
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Subscription Products',
                'url' => route('admin.products.index'),
                'active' => true
            ],
        ];

        $q = $request->get('q');
        $status = $request->get('status');
        $perPage = $request->get('per_page', 10);

        $products = QueryBuilder::for(SubscriptionProduct::class)
            ->allowedFilters(['name', 'billing_cycle'])
            ->withCount('subscriptions')
            ->when($q, function ($query) use ($q) {
                $query->where('name', 'like', "%{$q}%")
                    ->orWhere('description', 'like', "%{$q}%");
            })
            ->when($status !== null, function ($query) use ($status) {
                $query->where('is_active', $status === 'active');
            })
            ->latest()
            ->paginate($perPage);

        return view('admin.products.index', [
            'pageTitle' => 'Subscription Products',
            'breadcrumbItems' => $breadcrumbsItems,
            'products' => $products,
            'filters' => [
                'q' => $q,
                'status' => $status,
                'per_page' => $perPage
            ]
        ]);
    }

    /**
     * Show the form for creating a new subscription product
     */
    public function create()
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Subscription Products',
                'url' => route('admin.products.index'),
                'active' => false
            ],
            [
                'name' => 'Create Product',
                'url' => route('admin.products.create'),
                'active' => true
            ],
        ];

        return view('admin.products.create', [
            'pageTitle' => 'Create Subscription Product',
            'breadcrumbItems' => $breadcrumbsItems
        ]);
    }

    /**
     * Store a newly created subscription product
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0.01',
            'billing_cycle' => 'required|in:monthly,yearly',
            'features' => 'nullable|array',
            'features.*' => 'string|max:255',
            'is_active' => 'boolean'
        ]);

        try {
            // Create product in Stripe
            $stripeProduct = $this->stripeService->createProduct([
                'name' => $request->name,
                'description' => $request->description,
            ]);

            // Create price in Stripe
            $stripePrice = $this->stripeService->createPrice([
                'product' => $stripeProduct->id,
                'unit_amount' => $request->price * 100, // Convert to cents
                'currency' => 'usd',
                'recurring' => [
                    'interval' => $request->billing_cycle === 'yearly' ? 'year' : 'month'
                ]
            ]);

            // Create local product
           SubscriptionProduct::create([
                'name' => $request->name,
                'description' => $request->description,
                'price' => $request->price, // Store in dollars (Stripe handles cents conversion)
                'billing_cycle' => $request->billing_cycle,
                'features' => $request->features ?? [],
                'is_active' => $request->boolean('is_active', true),
                'stripe_product_id' => $stripeProduct->id,
                'stripe_price_id' => $stripePrice->id,
            ]);

            return redirect()->route('admin.products.index')
                ->with('message', 'Subscription product created successfully.');

        } catch (\Exception $e) {
            Log::error("Failed to create product: " . $e->getMessage());
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to create product: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for editing the specified subscription product
     */
    public function edit(SubscriptionProduct $product)
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Subscription Products',
                'url' => route('admin.products.index'),
                'active' => false
            ],
            [
                'name' => 'Edit ' . $product->name,
                'url' => route('admin.products.edit', $product),
                'active' => true
            ],
        ];

        return view('admin.products.edit', [
            'pageTitle' => 'Edit Subscription Product',
            'breadcrumbItems' => $breadcrumbsItems,
            'product' => $product
        ]);
    }

    /**
     * Update the specified subscription product
     */
    public function update(Request $request, SubscriptionProduct $product): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'features' => 'nullable|array',
            'features.*' => 'string|max:255',
            'is_active' => 'boolean'
        ]);

        try {
            // Update product in Stripe (only name and description can be updated)
            if ($product->stripe_product_id) {
                $this->stripeService->updateProduct($product->stripe_product_id, [
                    'name' => $request->name,
                    'description' => $request->description,
                ]);
            }

            // Update local product
            $product->update([
                'name' => $request->name,
                'description' => $request->description,
                'features' => $request->features ?? [],
                'is_active' => $request->boolean('is_active'),
            ]);

            return redirect()->route('admin.products.index')
                ->with('message', 'Subscription product updated successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to update product: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified subscription product
     */
    public function destroy(SubscriptionProduct $product): RedirectResponse
    {
        try {
            // Check if product has active subscriptions
            if ($product->subscriptions()->where('status', 'active')->exists()) {
                return redirect()->back()
                    ->with('error', 'Cannot delete product with active subscriptions.');
            }

            // Archive product in Stripe instead of deleting
            if ($product->stripe_product_id) {
                $this->stripeService->updateProduct($product->stripe_product_id, [
                    'active' => false
                ]);
            }

            // Soft delete or mark as inactive
            $product->update(['is_active' => false]);

            return redirect()->route('admin.products.index')
                ->with('message', 'Subscription product deactivated successfully.');

        } catch (\Exception $e) {
            Log::error('Failed to deactivate product', [
                'product_id' => $product->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()
                ->with('error', 'Failed to deactivate product: ' . $e->getMessage());
        }
    }

    /**
     * Toggle product status (active/inactive)
     */
    public function toggleStatus(SubscriptionProduct $product): RedirectResponse
    {
        try {
            $newStatus = !$product->is_active;

            // Update in Stripe
            if ($product->stripe_product_id) {
                $this->stripeService->updateProduct($product->stripe_product_id, [
                    'active' => $newStatus
                ]);
            }

            // Update local product
            $product->update(['is_active' => $newStatus]);

            $message = $newStatus ? 'Product activated successfully.' : 'Product deactivated successfully.';

            return redirect()->route('admin.products.index')
                ->with('message', $message);

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update product status: ' . $e->getMessage());
        }
    }
}
