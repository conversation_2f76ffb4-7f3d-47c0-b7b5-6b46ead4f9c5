<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\UserSubscription;
use App\Models\SubscriptionProduct;
use App\Services\StripeService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Spatie\QueryBuilder\QueryBuilder;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AdminSubscriptionManagementController extends Controller
{
    protected StripeService $stripeService;

    public function __construct(StripeService $stripeService)
    {
        $this->stripeService = $stripeService;
    }

    /**
     * Display a listing of subscriptions
     */
    public function index(Request $request)
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Subscription Management',
                'url' => route('admin.subscriptions.index'),
                'active' => true
            ],
        ];

        $q = $request->get('q');
        $status = $request->get('status');
        $product = $request->get('product');
        $perPage = $request->get('per_page', 10);

        $subscriptions = QueryBuilder::for(UserSubscription::class)
            ->allowedFilters(['status'])
            ->with(['user', 'subscriptionProduct'])
            ->when($q, function ($query) use ($q) {
                $query->whereHas('user', function ($userQuery) use ($q) {
                    $userQuery->where('name', 'like', "%{$q}%")
                        ->orWhere('email', 'like', "%{$q}%");
                });
            })
            ->when($status, function ($query) use ($status) {
                $query->where('status', $status);
            })
            ->when($product, function ($query) use ($product) {
                $query->where('subscription_product_id', $product);
            })
            ->latest()
            ->paginate($perPage);

        $products = SubscriptionProduct::all();
        $statusOptions = ['active', 'cancelled', 'paused', 'past_due', 'trialing'];

        return view('admin.subscriptions.index', [
            'pageTitle' => 'Subscription Management',
            'breadcrumbItems' => $breadcrumbsItems,
            'subscriptions' => $subscriptions,
            'products' => $products,
            'statusOptions' => $statusOptions,
            'filters' => [
                'q' => $q,
                'status' => $status,
                'product' => $product,
                'per_page' => $perPage
            ]
        ]);
    }

    /**
     * Display the specified subscription
     */
    public function show(UserSubscription $subscription)
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Subscription Management',
                'url' => route('admin.subscriptions.index'),
                'active' => false
            ],
            [
                'name' => 'Subscription #' . $subscription->id,
                'url' => route('admin.subscriptions.show', $subscription),
                'active' => true
            ],
        ];

        $subscription->load(['user', 'subscriptionProduct', 'payments' => function ($query) {
            $query->latest()->take(10);
        }]);

        return view('admin.subscriptions.show', [
            'pageTitle' => 'Subscription Details #' . $subscription->id,
            'breadcrumbItems' => $breadcrumbsItems,
            'subscription' => $subscription
        ]);
    }

    /**
     * Cancel a subscription
     */
    public function cancel(Request $request, UserSubscription $subscription): RedirectResponse
    {
        $request->validate([
            'reason' => 'required|string|max:500',
            'immediate' => 'boolean'
        ]);

        try {
            if ($subscription->stripe_subscription_id) {
                // Cancel in Stripe
                $this->stripeService->cancelSubscription(
                    $subscription->stripe_subscription_id,
                    $request->boolean('immediate')
                );
            }

            // Update local subscription
            $subscription->update([
                'status' => 'canceled',
                'canceled_at' => now(),
                'cancellation_reason' => $request->reason
            ]);

            return redirect()->route('admin.subscriptions.show', $subscription)
                ->with('message', 'Subscription cancelled successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to cancel subscription: ' . $e->getMessage());
        }
    }

    /**
     * Pause a subscription
     */
    public function pause(Request $request, UserSubscription $subscription): RedirectResponse
    {
        $request->validate([
            'reason' => 'required|string|max:500'
        ]);

        try {
            // Update subscription status
            $subscription->update([
                'status' => 'paused',
                'paused_at' => now(),
                'pause_reason' => $request->reason
            ]);

            return redirect()->route('admin.subscriptions.show', $subscription)
                ->with('message', 'Subscription paused successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to pause subscription: ' . $e->getMessage());
        }
    }

    /**
     * Resume a paused subscription
     */
    public function resume(UserSubscription $subscription): RedirectResponse
    {
        try {
            $subscription->update([
                'status' => 'active',
                'paused_at' => null,
                'pause_reason' => null
            ]);

            return redirect()->route('admin.subscriptions.show', $subscription)
                ->with('message', 'Subscription resumed successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to resume subscription: ' . $e->getMessage());
        }
    }

    /**
     * Display subscription analytics
     */
    public function analytics()
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Subscription Analytics',
                'url' => route('admin.subscriptions.analytics'),
                'active' => true
            ],
        ];

        // Monthly subscription trends
        $monthlyData = [];
        $monthLabels = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $monthLabels[] = $date->format('M Y');
            
            $newSubscriptions = UserSubscription::whereMonth('created_at', $date->month)
                ->whereYear('created_at', $date->year)
                ->count();
            
            $cancelledSubscriptions = UserSubscription::where('status', 'cancelled')
                ->whereMonth('updated_at', $date->month)
                ->whereYear('updated_at', $date->year)
                ->count();
            
            $monthlyData['new'][] = $newSubscriptions;
            $monthlyData['cancelled'][] = $cancelledSubscriptions;
        }

        // Product performance
        $productStats = SubscriptionProduct::withCount([
            'subscriptions',
            'subscriptions as active_subscriptions_count' => function ($query) {
                $query->where('status', 'active');
            }
        ])->get();

        // Churn analysis
        $churnData = UserSubscription::select(
            DB::raw('YEAR(updated_at) as year'),
            DB::raw('MONTH(updated_at) as month'),
            DB::raw('COUNT(*) as cancelled_count')
        )
        ->where('status', 'cancelled')
        ->where('updated_at', '>=', Carbon::now()->subMonths(12))
        ->groupBy('year', 'month')
        ->orderBy('year', 'desc')
        ->orderBy('month', 'desc')
        ->get();

        return view('admin.subscriptions.analytics', [
            'pageTitle' => 'Subscription Analytics',
            'breadcrumbItems' => $breadcrumbsItems,
            'chartData' => [
                'monthly' => [
                    'labels' => $monthLabels,
                    'new' => $monthlyData['new'],
                    'cancelled' => $monthlyData['cancelled']
                ]
            ],
            'productStats' => $productStats,
            'churnData' => $churnData
        ]);
    }
}
