<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\InvestorProfile;
use App\Models\StartupProfile;
use App\Models\InterestRequest;
use App\Models\EsgResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AdminInvestmentController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin|analyst|super-admin']);
    }

    /**
     * Investment platform dashboard
     */
    public function index()
    {
        $breadcrumbsItems = [
            [
                'name' => 'Dashboard',
                'url' => route('admin.dashboard'),
            ],
            [
                'name' => 'Investment Platform',
                'url' => null,
            ],
        ];

        // Calculate statistics
        $stats = [
            'pendingApprovals' => InterestRequest::where('status', 'pending')->count(),
            'totalInvestors' => User::where('role', 'investor')->count(),
            'totalStartups' => User::where('role', 'startup')->count(),
            'averageEsgScore' => StartupProfile::whereNotNull('esg_score')->avg('esg_score') ?? 0,
        ];

        // Recent activity
        $recentActivity = collect([
            // Recent interest requests
            ...InterestRequest::with(['requester', 'target'])
                ->latest()
                ->take(3)
                ->get()
                ->map(function ($request) {
                    return [
                        'description' => "Interest request from {$request->requester->name} to {$request->target->name}",
                        'created_at' => $request->created_at,
                        'type' => 'interest_request'
                    ];
                }),

            // Recent ESG completions
            ...StartupProfile::whereNotNull('esg_score')
                ->with('user')
                ->latest('updated_at')
                ->take(2)
                ->get()
                ->map(function ($profile) {
                    return [
                        'description' => "{$profile->user->name} completed ESG questionnaire (Score: {$profile->esg_score})",
                        'created_at' => $profile->updated_at,
                        'type' => 'esg_completion'
                    ];
                }),
        ])->sortByDesc('created_at')->take(5);

        return view('admin.investment.index', [
            'pageTitle' => 'Investment Platform Management',
            'breadcrumbItems' => $breadcrumbsItems,
            'stats' => $stats,
            'recentActivity' => $recentActivity,
        ]);
    }

    /**
     * Interest requests management
     */
    public function interestRequests(Request $request)
    {
        $breadcrumbsItems = [
            [
                'name' => 'Dashboard',
                'url' => route('admin.dashboard'),
            ],
            [
                'name' => 'Investment Platform',
                'url' => route('admin.investment.index'),
            ],
            [
                'name' => 'Interest Requests',
                'url' => null,
            ],
        ];

        $query = InterestRequest::with(['requester', 'target', 'approver']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $interestRequests = $query->latest()->paginate(10);

        $stats = [
            'total' => InterestRequest::count(),
            'pending' => InterestRequest::where('status', 'pending')->count(),
            'approved' => InterestRequest::where('status', 'approved')->count(),
            'rejected' => InterestRequest::where('status', 'rejected')->count(),
        ];
        #if page is 2 or more, and it has no data, redirect to page 1
        if ($interestRequests->currentPage() > 1 && $interestRequests->isEmpty()) {
            return redirect()->route('admin.investment.interest-requests');
        }

        return view('admin.investment.interest-requests', [
            'pageTitle' => 'Interest Requests Management',
            'breadcrumbItems' => $breadcrumbsItems,
            'interestRequests' => $interestRequests,
            'stats' => $stats,
            'filters' => $request->only(['status', 'date_from', 'date_to']),
        ]);
    }

    /**
     * Show interest request detail
     */
    public function showInterestRequest(InterestRequest $interestRequest)
    {
        $breadcrumbsItems = [
            [
                'name' => 'Dashboard',
                'url' => route('admin.dashboard'),
                'active' => false,
            ],
            [
                'name' => 'Investment Platform',
                'url' => route('admin.investment.index'),
                'active' => false,
            ],
            [
                'name' => 'Interest Requests',
                'url' => route('admin.investment.interest-requests'),
                'active' => false,
            ],
            [
                'name' => 'Request #' . $interestRequest->id,
                'url' => null,
                'active' => true,
            ],
        ];

        // Load comprehensive data for the interest request
        $interestRequest->load([
            'requester.investorProfile',
            'requester.startupProfile',
            'requester.media',
            'target.investorProfile',
            'target.startupProfile',
            'target.media',
            'approver'
        ]);

        // Get approval history (if any)
        $approvalHistory = collect();
        if ($interestRequest->approved_at) {
            $approvalHistory->push([
                'action' => 'approved',
                'user' => $interestRequest->approver,
                'timestamp' => $interestRequest->approved_at,
                'reason' => null // InterestRequest model doesn't have approval_reason field
            ]);
        }
        if ($interestRequest->status === 'rejected') {
            $approvalHistory->push([
                'action' => 'rejected',
                'user' => $interestRequest->approver,
                'timestamp' => $interestRequest->updated_at, // Use updated_at since there's no rejected_at field
                'reason' => $interestRequest->rejection_reason
            ]);
        }

        // Determine request type for visual highlighting based on requester and target roles
        $requesterRole = $interestRequest->requester->role;
        $targetRole = $interestRequest->target->role;

        $requestTypeInfo = [
            'type' => $interestRequest->type,
            'direction' => $requesterRole === 'investor' ? 'Investor → Startup' : 'Startup → Investor',
            'color_class' => $requesterRole === 'investor' ? 'blue' : 'green',
            'description' => $requesterRole === 'investor'
                ? 'An investor is expressing interest in this startup'
                : 'A startup is requesting investment from this investor',
            'requester_role' => $requesterRole,
            'target_role' => $targetRole
        ];

        return view('admin.investment.interest-request-detail', [
            'pageTitle' => 'Interest Request #' . $interestRequest->id,
            'breadcrumbItems' => $breadcrumbsItems,
            'interestRequest' => $interestRequest,
            'approvalHistory' => $approvalHistory->sortByDesc('timestamp'),
            'requestTypeInfo' => $requestTypeInfo,
        ]);
    }

    /**
     * Approve interest request
     */
    public function approveInterestRequest(InterestRequest $interestRequest)
    {
        if (!$interestRequest->canBeApproved()) {
            return back()->with('error', 'Interest request cannot be approved.');
        }

        $interestRequest->approve(auth()->user());

        return back()->with('success', 'Interest request approved successfully.');
    }

    /**
     * Reject interest request
     */
    public function rejectInterestRequest(Request $request, InterestRequest $interestRequest)
    {
        $request->validate([
            'reason' => 'nullable|string|max:500',
        ]);

        if (!$interestRequest->canBeRejected()) {
            return back()->with('error', 'Interest request cannot be rejected.');
        }

        $interestRequest->reject($request->reason);

        return back()->with('success', 'Interest request rejected successfully.');
    }

    /**
     * Investment platform users management
     */
    public function users(Request $request)
    {
        $breadcrumbsItems = [
            [
                'name' => 'Dashboard',
                'url' => route('admin.dashboard'),
            ],
            [
                'name' => 'Investment Platform',
                'url' => route('admin.investment.index'),
            ],
            [
                'name' => 'Users',
                'url' => null,
            ],
        ];

        $query = User::whereIn('role', ['investor', 'startup'])
                     ->with(['investorProfile', 'startupProfile', 'media']);

        // Filter by role
        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        // Search by name or email
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $users = $query->latest()->paginate(20);

        $stats = [
            'totalInvestors' => User::where('role', 'investor')->count(),
            'totalStartups' => User::where('role', 'startup')->count(),
            'completedProfiles' => User::whereIn('role', ['investor', 'startup'])
                ->where(function ($q) {
                    $q->whereHas('investorProfile', function ($iq) {
                        $iq->where('profile_completed', true);
                    })->orWhereHas('startupProfile', function ($sq) {
                        $sq->where('profile_completed', true);
                    });
                })->count(),
        ];

        return view('admin.investment.users', [
            'pageTitle' => 'Investment Platform Users',
            'breadcrumbItems' => $breadcrumbsItems,
            'users' => $users,
            'stats' => $stats,
            'filters' => $request->only(['role', 'search']),
        ]);
    }

    /**
     * ESG Analytics
     */
    public function esgAnalytics()
    {
        $breadcrumbsItems = [
            [
                'name' => 'Dashboard',
                'url' => route('admin.dashboard'),
            ],
            [
                'name' => 'Investment Platform',
                'url' => route('admin.investment.index'),
            ],
            [
                'name' => 'ESG Analytics',
                'url' => null,
            ],
        ];

        $stats = [
            'totalCompleted' => StartupProfile::whereNotNull('esg_score')->count(),
            'averageScore' => StartupProfile::whereNotNull('esg_score')->avg('esg_score') ?? 0,
            'scoreDistribution' => [
                'excellent' => StartupProfile::whereBetween('esg_score', [80, 100])->count(),
                'good' => StartupProfile::whereBetween('esg_score', [60, 79.99])->count(),
                'fair' => StartupProfile::whereBetween('esg_score', [40, 59.99])->count(),
                'poor' => StartupProfile::whereBetween('esg_score', [0, 39.99])->count(),
            ],
        ];

        // Monthly ESG completion trend
        $monthlyData = StartupProfile::whereNotNull('esg_score')
            ->where('updated_at', '>=', Carbon::now()->subMonths(12))
            ->selectRaw('YEAR(updated_at) as year, MONTH(updated_at) as month, COUNT(*) as count, AVG(esg_score) as avg_score')
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get();

        $chartData = [
            'labels' => $monthlyData->map(function ($item) {
                return Carbon::createFromDate($item->year, $item->month, 1)->format('M Y');
            })->reverse()->values(),
            'completions' => $monthlyData->pluck('count')->reverse()->values(),
            'averageScores' => $monthlyData->pluck('avg_score')->reverse()->values(),
        ];

        return view('admin.investment.esg-analytics', [
            'pageTitle' => 'ESG Analytics',
            'breadcrumbItems' => $breadcrumbsItems,
            'stats' => $stats,
            'chartData' => $chartData,
        ]);
    }
}
