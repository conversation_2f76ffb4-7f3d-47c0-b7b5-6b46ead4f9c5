<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\SubscriptionProduct;
use App\Models\Payment;
use App\Models\Refund;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AdminReportsController extends Controller
{

    /**
     * Display the main reports dashboard
     */
    public function index()
    {
        $data = [
            'totalUsers' => User::count(),
            'activeSubscriptions' => User::whereHas('subscriptions', function($query) {
                $query->where('status', 'active');
            })->count(),
            'totalRefunds' => Refund::where('status', 'succeeded')->sum('amount') / 100,
            'subscriptionsByProduct' => $this->getSubscriptionsByProduct(),
            'userGrowth' => $this->getUserGrowth(),
        ];

        return view('admin.reports.index', compact('data'));
    }

    /**
     * Display subscription reports
     */
    public function subscriptions()
    {
        $data = [
            'subscriptionsByStatus' => $this->getSubscriptionsByStatus(),
            'subscriptionsByProduct' => $this->getSubscriptionsByProduct(),
            'churnRate' => $this->getChurnRate(),
            'averageLifetime' => $this->getAverageSubscriptionLifetime(),
            'monthlySubscriptions' => $this->getMonthlySubscriptions(),
        ];

        return view('admin.reports.user', compact('data'));
    }



    /**
     * Display user reports
     */
    public function users()
    {
        $data = [
            'userGrowth' => $this->getUserGrowth(),
            'usersBySubscriptionStatus' => $this->getUsersBySubscriptionStatus(),
            'topUsers' => $this->getTopUsers(),
            'userRetention' => $this->getUserRetention(),
        ];

        return view('admin.reports.users', compact('data'));
    }

    /**
     * Display refund requests pending list
     */
    public function refundRequests()
    {
        $data = [
            'pendingRefunds' => $this->getPendingRefunds(),
            'refundStats' => $this->getRefundStats(),
            'recentRefunds' => $this->getRecentRefunds(),
            'refundsByMonth' => $this->getRefundsByMonth(),
        ];

        return view('admin.reports.refund-requests', compact('data'));
    }



    /**
     * Get subscriptions grouped by product
     */
    private function getSubscriptionsByProduct()
    {
        return DB::table('user_subscriptions')
            ->join('subscription_products', 'user_subscriptions.subscription_product_id', '=', 'subscription_products.id')
            ->select('subscription_products.name', DB::raw('count(*) as count'))
            ->where('user_subscriptions.status', 'active')
            ->groupBy('subscription_products.name')
            ->get();
    }

    /**
     * Get user growth data for the last 12 months
     */
    private function getUserGrowth()
    {
        return User::where('created_at', '>=', Carbon::now()->subMonths(12))
            ->selectRaw('MONTH(created_at) as month, YEAR(created_at) as year, COUNT(*) as count')
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get()
            ->map(function ($item) {
                return [
                    'month' => Carbon::createFromDate($item->year, $item->month, 1)->format('M Y'),
                    'count' => $item->count,
                ];
            });
    }



    /**
     * Get subscriptions grouped by status
     */
    private function getSubscriptionsByStatus()
    {
        return DB::table('user_subscriptions')
            ->select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->get();
    }

    /**
     * Get churn rate
     */
    private function getChurnRate()
    {
        $totalSubscriptions = DB::table('user_subscriptions')->count();
        $cancelledSubscriptions = DB::table('user_subscriptions')
            ->where('status', 'canceled')
            ->count();

        return $totalSubscriptions > 0 ? round(($cancelledSubscriptions / $totalSubscriptions) * 100, 2) : 0;
    }

    /**
     * Get average subscription lifetime in days
     */
    private function getAverageSubscriptionLifetime()
    {
        $cancelledSubscriptions = DB::table('user_subscriptions')
            ->where('status', 'canceled')
            ->whereNotNull('ends_at')
            ->selectRaw('AVG(DATEDIFF(ends_at, created_at)) as avg_lifetime')
            ->first();

        return round($cancelledSubscriptions->avg_lifetime ?? 0, 0);
    }

    /**
     * Get monthly subscription data
     */
    private function getMonthlySubscriptions()
    {
        return DB::table('user_subscriptions')
            ->where('created_at', '>=', Carbon::now()->subMonths(12))
            ->selectRaw('MONTH(created_at) as month, YEAR(created_at) as year, COUNT(*) as count')
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get()
            ->map(function ($item) {
                return [
                    'month' => Carbon::createFromDate($item->year, $item->month, 1)->format('M Y'),
                    'count' => $item->count,
                ];
            });
    }







    /**
     * Get users by subscription status
     */
    private function getUsersBySubscriptionStatus()
    {
        return [
            'active' => User::whereHas('subscriptions', function($query) {
                $query->where('status', 'active');
            })->count(),
            'canceled' => User::whereHas('subscriptions', function($query) {
                $query->where('status', 'canceled');
            })->count(),
            'no_subscription' => User::whereDoesntHave('subscriptions')->count(),
        ];
    }

    /**
     * Get top users by subscription activity
     */
    private function getTopUsers()
    {
        return User::select('users.id', 'users.name', 'users.email', 'users.role', 'users.created_at', DB::raw('COUNT(user_subscriptions.id) as subscription_count'))
            ->join('user_subscriptions', 'users.id', '=', 'user_subscriptions.user_id')
            ->groupBy('users.id', 'users.name', 'users.email', 'users.role', 'users.created_at')
            ->orderBy('subscription_count', 'desc')
            ->limit(10)
            ->get();
    }

    /**
     * Get user retention rate
     */
    private function getUserRetention()
    {
        $totalUsers = User::count();
        $activeUsers = User::whereHas('subscriptions', function($query) {
            $query->where('status', 'active');
        })->count();

        return $totalUsers > 0 ? round(($activeUsers / $totalUsers) * 100, 2) : 0;
    }

    /**
     * Get pending refund requests
     */
    private function getPendingRefunds()
    {
        return Refund::with(['user', 'payment'])
            ->where('status', 'pending')
            ->latest()
            ->get();
    }

    /**
     * Get refund statistics
     */
    private function getRefundStats()
    {
        return [
            'pending' => Refund::where('status', 'pending')->count(),
            'approved' => Refund::where('status', 'succeeded')->count(),
            'rejected' => Refund::where('status', 'failed')->count(),
            'total_amount' => Refund::where('status', 'succeeded')->sum('amount') / 100,
        ];
    }

    /**
     * Get recent refunds
     */
    private function getRecentRefunds()
    {
        return Refund::with(['user', 'payment'])
            ->whereIn('status', ['succeeded', 'failed'])
            ->latest()
            ->take(10)
            ->get();
    }

    /**
     * Get refunds by month
     */
    private function getRefundsByMonth()
    {
        return Refund::where('status', 'succeeded')
            ->where('created_at', '>=', Carbon::now()->subMonths(12))
            ->selectRaw('MONTH(created_at) as month, YEAR(created_at) as year, COUNT(*) as count, SUM(amount) as total')
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get()
            ->map(function ($item) {
                return [
                    'month' => Carbon::createFromDate($item->year, $item->month, 1)->format('M Y'),
                    'count' => $item->count,
                    'total' => $item->total / 100,
                ];
            });
    }
}
