<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class AdminCategoryController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin|super-admin']);

        // Bind the category parameter to only resolve taxonomies of type 'category'
        $this->middleware(function ($request, $next) {
            if ($request->route('category')) {
                $taxonomy = Taxonomy::where('type', 'category')
                    ->where('id', $request->route('category'))
                    ->firstOrFail();
                $request->route()->setParameter('category', $taxonomy);
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of categories
     */
    public function index(Request $request): View
    {
        $breadcrumbsItems = [
            [
                'name' => 'Dashboard',
                'url' => route('admin.dashboard'),
                'active' => false,
            ],
            [
                'name' => 'Categories',
                'url' => null,
                'active' => true,
            ],
        ];

        $query = Taxonomy::where('type', 'category');

        // Search functionality
        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }

        // Additional filtering by status if needed
        if ($request->filled('status')) {
            // Note: Taxonomy model doesn't have is_active by default, but we can add custom filtering
            // For now, we'll skip this filter or add it if the taxonomy model supports it
        }

        // No hierarchical filtering needed for flat taxonomy structure

        $categories = $query->orderBy('name')->paginate(15);

        // Get all categories for parent selection (taxonomy doesn't have hierarchical structure by default)
        $allCategories = Taxonomy::where('type', 'category')->orderBy('name')->get();

        // Get category types for filtering (we'll use different taxonomy types)
        $categoryTypes = ['category', 'industry', 'technology', 'keyword'];

        $stats = [
            'total' => Taxonomy::where('type', 'category')->count(),
            'categories' => Taxonomy::where('type', 'category')->count(),
            'industries' => Taxonomy::where('type', 'industry')->count(),
            'technologies' => Taxonomy::where('type', 'technology')->count(),
            'keywords' => Taxonomy::where('type', 'keyword')->count(),
        ];

        return view('admin.categories.index', [
            'pageTitle' => 'Categories Management',
            'breadcrumbItems' => $breadcrumbsItems,
            'categories' => $categories,
            'allCategories' => $allCategories,
            'categoryTypes' => $categoryTypes,
            'currentParentId' => $request->parent_id,
            'stats' => $stats,
            'filters' => $request->only(['search', 'type', 'parent_id']),
        ]);
    }

    /**
     * Show the form for creating a new category
     */
    public function create(): View
    {
        $breadcrumbsItems = [
            [
                'name' => 'Dashboard',
                'url' => route('admin.dashboard'),
                'active' => false,
            ],
            [
                'name' => 'Categories',
                'url' => route('admin.categories.index'),
                'active' => false,
            ],
            [
                'name' => 'Create Category',
                'url' => null,
                'active' => true,
            ],
        ];

        // Get all categories for parent selection
        $parentCategories = Taxonomy::where('type', 'category')->orderBy('name')->get();
        $categoryTypes = ['category', 'industry', 'technology', 'keyword'];

        return view('admin.categories.create', [
            'pageTitle' => 'Create Category',
            'breadcrumbItems' => $breadcrumbsItems,
            'parentCategories' => $parentCategories,
            'categoryTypes' => $categoryTypes,
        ]);
    }

    /**
     * Store a newly created category
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:taxonomies,name',
            'description' => 'nullable|string|max:1000',
            'type' => 'required|in:category,industry,technology,keyword',
        ]);

        // Create the taxonomy
        Taxonomy::create($validated);

        return redirect()->route('admin.categories.index')
            ->with('message', 'Category created successfully.');
    }

    /**
     * Display the specified category
     */
    public function show(Taxonomy $category): View
    {
        $breadcrumbsItems = [
            [
                'name' => 'Dashboard',
                'url' => route('admin.dashboard'),
                'active' => false,
            ],
            [
                'name' => 'Categories',
                'url' => route('admin.categories.index'),
                'active' => false,
            ],
            [
                'name' => $category->name,
                'url' => null,
                'active' => true,
            ],
        ];

        // Get users associated with this category (through the taxonomable relationship)
        $users = $category->taxonomables()
            ->where('taxonomable_type', 'App\\Models\\User')
            ->with(['taxonomable.investorProfile', 'taxonomable.startupProfile'])
            ->paginate(10);

        return view('admin.categories.show', [
            'pageTitle' => 'Category Details',
            'breadcrumbItems' => $breadcrumbsItems,
            'category' => $category,
            'users' => $users,
        ]);
    }

    /**
     * Show the form for editing the specified category
     */
    public function edit(Taxonomy $category): View
    {
        $breadcrumbsItems = [
            [
                'name' => 'Dashboard',
                'url' => route('admin.dashboard'),
                'active' => false,
            ],
            [
                'name' => 'Categories',
                'url' => route('admin.categories.index'),
                'active' => false,
            ],
            [
                'name' => 'Edit ' . $category->name,
                'url' => null,
                'active' => true,
            ],
        ];

        // Get all categories for parent selection (excluding current category)
        $parentCategories = Taxonomy::where('type', 'category')
            ->where('id', '!=', $category->id)
            ->orderBy('name')
            ->get();

        $categoryTypes = ['category', 'industry', 'technology', 'keyword'];

        return view('admin.categories.edit', [
            'pageTitle' => 'Edit Category',
            'breadcrumbItems' => $breadcrumbsItems,
            'category' => $category,
            'parentCategories' => $parentCategories,
            'categoryTypes' => $categoryTypes,
        ]);
    }

    /**
     * Update the specified category
     */
    public function update(Request $request, Taxonomy $category): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:taxonomies,name,' . $category->id,
            'description' => 'nullable|string|max:1000',
            'type' => 'required|in:category,industry,technology,keyword',
        ]);

        $validated['is_active'] = $request->boolean('is_active', true);
        $validated['sort_order'] = $validated['sort_order'] ?? $category->sort_order;

        $category->update($validated);

        return redirect()->route('admin.categories.index')
            ->with('message', 'Category updated successfully.');
    }

    /**
     * Remove the specified category
     */
    public function destroy(Taxonomy $category): RedirectResponse
    {
        // Check if category has associated users
        $associatedCount = $category->taxonomables()
            ->where('taxonomable_type', 'App\\Models\\User')
            ->count();

        if ($associatedCount > 0) {
            return redirect()->route('admin.categories.index')
                ->with('error', 'Cannot delete category that has associated users.');
        }

        $category->delete();

        return redirect()->route('admin.categories.index')
            ->with('message', 'Category deleted successfully.');
    }

    /**
     * Toggle category status (Note: Taxonomy model doesn't have is_active by default)
     */
    public function toggleStatus(Taxonomy $category): RedirectResponse
    {
        // Since the taxonomy model doesn't have is_active field by default,
        // we'll just return a message for now
        return redirect()->route('admin.categories.index')
            ->with('message', "Category status toggle not available for taxonomy model.");
    }
}
