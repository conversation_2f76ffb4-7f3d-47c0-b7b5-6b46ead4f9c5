<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Traits\DataTableTrait;
use Illuminate\Http\Request;
use Illuminate\Pagination\Paginator;

class StartupController extends Controller
{
    use DataTableTrait;
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $breadcrumbsItems = [
            [
                'name' => 'Startup',
                'url' => 'startups',
                'active' => true
            ],
        ];

        return view('startup.index', [
            'pageTitle' => 'Startup Company',
            'breadcrumbItems' => $breadcrumbsItems
        ]);
    }

    /**
     * Handle DataTable server-side processing for startups
     */
    public function datatable(Request $request)
    {
        $columns = [
            0 => null, // SL (not sortable)
            1 => 'name', // Company Name
            2 => 'startup_profile.esg_score', // ESG Avg Score
            3 => null, // X-Corn Status (not sortable)
            4 => 'startup_profile.location', // Location
            5 => 'startup_profile.funding_amount', // Total Equity Funding
            6 => 'created_at', // Founded
            7 => 'startup_profile.funding_stage', // Company Stage
            8 => null // Action (not sortable)
        ];

        $query = User::where('role', 'startup')
            ->with(['startupProfile', 'media']);

        return $this->processDataTable($request, $query, $columns, function ($startup, $index) {
            return [
                $index, // SL
                $this->getCompanyNameCell($startup), // Company Name with avatar
                $startup->startupProfile->esg_score ?? 'N/A', // ESG Avg Score
                'N/A', // X-Corn Status
                '$' . number_format($startup->startupProfile->funding_amount ?? 0, 2), // Location (showing funding amount)
                '$' . number_format($startup->startupProfile->valuation ?? 0, 2), // Total Equity Funding (showing valuation)
                $startup->created_at->format('m-d-Y'), // Founded
                $startup->startupProfile->funding_stage ?? 'N/A', // Company Stage
                $this->getActionButtons('startups', $startup) // Action
            ];
        });
    }

    /**
     * Apply search filtering for startups
     */
    protected function applySearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'LIKE', "%{$search}%")
              ->orWhere('email', 'LIKE', "%{$search}%")
              ->orWhereHas('startupProfile', function ($profile) use ($search) {
                  $profile->where('company_name', 'LIKE', "%{$search}%")
                          ->orWhere('funding_stage', 'LIKE', "%{$search}%")
                          ->orWhere('location', 'LIKE', "%{$search}%");
              });
        });
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(User $startup)
    {
        // Ensure the user is actually a startup
        if ($startup->role !== 'startup') {
            abort(404, 'Startup not found');
        }

        $breadcrumbsItems = [
            [
                'name' => 'Startup',
                'url' => route('startups.index'),
                'active' => false
            ],
            [
                'name' => $startup->name,
                'url' => null,
                'active' => true
            ],
        ];

        // Load comprehensive startup data
        $startup->load([
            'startupProfile.esgResponses.esgQuestion',
            'paymentMethods',
            'subscriptions.subscriptionProduct',
            'invoices',
            'media',
            'accountStatus'
        ]);

        // Get payment methods with default status indicators
        $paymentMethods = $startup->paymentMethods()
            ->orderBy('is_default', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get subscription history with status changes
        $subscriptionHistory = $startup->subscriptions()
            ->with('subscriptionProduct')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get invoices with download capabilities
        $invoices = $startup->invoices()
            ->orderBy('created_at', 'desc')
            ->get();

        // Get investors who have expressed interest in this startup
        $interestedInvestors = $startup->receivedInterestRequests()
            ->with(['requester.investorProfile', 'requester.media'])
            ->where('status', 'approved')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get associated categories
        $categories = $startup->startupProfile ? $startup->startupProfile->categories : collect();

        // Get ESG data
        $esgData = $startup->startupProfile ? $startup->startupProfile->esgResponses : collect();

        return view('startup.show', [
            'pageTitle' => 'Startup Profile - ' . $startup->name,
            'breadcrumbItems' => $breadcrumbsItems,
            'startup' => $startup,
            'paymentMethods' => $paymentMethods,
            'subscriptionHistory' => $subscriptionHistory,
            'invoices' => $invoices,
            'interestedInvestors' => $interestedInvestors,
            'categories' => $categories,
            'esgData' => $esgData
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $startup)
    {
        // Ensure the user is actually a startup
        if ($startup->role !== 'startup') {
            abort(404, 'Startup not found');
        }

        // TODO: Implement edit functionality
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $startup)
    {
        // Ensure the user is actually a startup
        if ($startup->role !== 'startup') {
            abort(404, 'Startup not found');
        }

        // TODO: Implement update functionality
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $startup)
    {
        // Ensure the user is actually a startup
        if ($startup->role !== 'startup') {
            abort(404, 'Startup not found');
        }

        // TODO: Implement delete functionality
    }
}
