<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Pagination\Paginator;

class StartupController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $breadcrumbsItems = [
            [
                'name' => 'Startup',
                'url' => 'startups',
                'active' => true
            ],
        ];

        // Fetch startups with their profiles and media
        $startups = \App\Models\User::where('role', 'startup')
            ->with(['startupProfile', 'media'])
            ->latest()
            ->get();

        return view('startup.index', [
            'pageTitle' => 'Startup Company',
            'breadcrumbItems' => $breadcrumbsItems,
            'startups' => $startups
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(User $startup)
    {
        // Ensure the user is actually a startup
        if ($startup->role !== 'startup') {
            abort(404, 'Startup not found');
        }

        $breadcrumbsItems = [
            [
                'name' => 'Startup',
                'url' => route('startups.index'),
                'active' => false
            ],
            [
                'name' => $startup->name,
                'url' => null,
                'active' => true
            ],
        ];

        // Load comprehensive startup data
        $startup->load([
            'startupProfile.esgResponses.esgQuestion',
            'paymentMethods',
            'subscriptions.subscriptionProduct',
            'invoices',
            'media',
            'accountStatus'
        ]);

        // Get payment methods with default status indicators
        $paymentMethods = $startup->paymentMethods()
            ->orderBy('is_default', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get subscription history with status changes
        $subscriptionHistory = $startup->subscriptions()
            ->with('subscriptionProduct')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get invoices with download capabilities
        $invoices = $startup->invoices()
            ->orderBy('created_at', 'desc')
            ->get();

        // Get investors who have expressed interest in this startup
        $interestedInvestors = $startup->receivedInterestRequests()
            ->with(['requester.investorProfile', 'requester.media'])
            ->where('status', 'approved')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get associated categories
        $categories = $startup->startupProfile ? $startup->startupProfile->categories : collect();

        // Get ESG data
        $esgData = $startup->startupProfile ? $startup->startupProfile->esgResponses : collect();

        return view('startup.show', [
            'pageTitle' => 'Startup Profile - ' . $startup->name,
            'breadcrumbItems' => $breadcrumbsItems,
            'startup' => $startup,
            'paymentMethods' => $paymentMethods,
            'subscriptionHistory' => $subscriptionHistory,
            'invoices' => $invoices,
            'interestedInvestors' => $interestedInvestors,
            'categories' => $categories,
            'esgData' => $esgData
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $startup)
    {
        // Ensure the user is actually a startup
        if ($startup->role !== 'startup') {
            abort(404, 'Startup not found');
        }

        // TODO: Implement edit functionality
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $startup)
    {
        // Ensure the user is actually a startup
        if ($startup->role !== 'startup') {
            abort(404, 'Startup not found');
        }

        // TODO: Implement update functionality
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $startup)
    {
        // Ensure the user is actually a startup
        if ($startup->role !== 'startup') {
            abort(404, 'Startup not found');
        }

        // TODO: Implement delete functionality
    }
}
