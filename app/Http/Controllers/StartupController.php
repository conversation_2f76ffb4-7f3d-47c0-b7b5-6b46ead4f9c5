<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Pagination\Paginator;

class StartupController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $breadcrumbsItems = [
            [
                'name' => 'Startup',
                'url' => 'startups',
                'active' => true
            ],
        ];

        return view('startup.index', [
            'pageTitle' => 'Startup Company',
            'breadcrumbItems' => $breadcrumbsItems
        ]);
    }

    /**
     * Handle DataTable server-side processing for startups
     */
    public function datatable(Request $request)
    {
        $draw = $request->get('draw');
        $start = $request->get('start');
        $length = $request->get('length');
        $search = $request->get('search')['value'] ?? '';
        $orderColumn = $request->get('order')[0]['column'] ?? 0;
        $orderDir = $request->get('order')[0]['dir'] ?? 'desc';

        // Column mapping for sorting
        $columns = [
            0 => 'id', // SL
            1 => 'name', // Company Name
            2 => 'esg_score', // ESG Avg Score (placeholder)
            3 => 'xcorn_status', // X-Corn Status (placeholder)
            4 => 'location', // Location (placeholder)
            5 => 'funding_amount', // Total Equity Funding
            6 => 'created_at', // Founded
            7 => 'funding_stage', // Company Stage
            8 => 'id' // Action (not sortable)
        ];

        $query = User::where('role', 'startup')
            ->with(['startupProfile', 'media']);

        // Apply search
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('email', 'LIKE', "%{$search}%")
                  ->orWhereHas('startupProfile', function ($profile) use ($search) {
                      $profile->where('company_name', 'LIKE', "%{$search}%")
                              ->orWhere('funding_stage', 'LIKE', "%{$search}%")
                              ->orWhere('location', 'LIKE', "%{$search}%");
                  });
            });
        }

        // Get total count before filtering
        $totalRecords = User::where('role', 'startup')->count();

        // Get filtered count
        $filteredRecords = $query->count();

        // Apply sorting
        $sortColumn = $columns[$orderColumn] ?? 'created_at';
        if (in_array($sortColumn, ['funding_amount', 'funding_stage'])) {
            $query->join('startup_profiles', 'users.id', '=', 'startup_profiles.user_id')
                  ->orderBy('startup_profiles.' . $sortColumn, $orderDir)
                  ->select('users.*');
        } else {
            $query->orderBy($sortColumn, $orderDir);
        }

        // Apply pagination
        $startups = $query->skip($start)->take($length)->get();

        // Format data for DataTable
        $data = [];
        foreach ($startups as $index => $startup) {
            $data[] = [
                $start + $index + 1, // SL
                $this->getCompanyNameCell($startup), // Company Name with avatar
                $startup->startupProfile->esg_score ?? 'N/A', // ESG Avg Score
                'N/A', // X-Corn Status
                '$' . number_format($startup->startupProfile->funding_amount ?? 0, 2), // Location (showing funding amount)
                '$' . number_format($startup->startupProfile->valuation ?? 0, 2), // Total Equity Funding (showing valuation)
                $startup->created_at->format('m-d-Y'), // Founded
                $startup->startupProfile->funding_stage ?? 'N/A', // Company Stage
                $this->getStartupActionButtons($startup) // Action
            ];
        }

        return response()->json([
            'draw' => intval($draw),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ]);
    }

    /**
     * Generate company name cell with avatar
     */
    private function getCompanyNameCell($startup)
    {
        $avatarUrl = $startup->getFirstMediaUrl('profile-image') ?: asset('images/all-img/customer_1.png');
        $companyName = $startup->startupProfile->company_name ?? $startup->name;

        return '<span class="flex">
                    <span class="w-7 h-7 rounded-full ltr:mr-3 rtl:ml-3 flex-none">
                        <img src="' . $avatarUrl . '" alt="' . $startup->name . '" class="object-cover w-full h-full rounded-full">
                    </span>
                    <span class="text-sm text-slate-600 dark:text-slate-300 capitalize">' . $companyName . '</span>
                </span>';
    }

    /**
     * Generate action buttons for DataTable
     */
    private function getStartupActionButtons($startup)
    {
        return '<div class="relative">
                    <div class="dropdown relative">
                        <button class="text-xl text-center block w-full" type="button"
                                id="tableDropdownMenuButton' . $startup->id . '" data-bs-toggle="dropdown"
                                aria-expanded="false">
                            <iconify-icon icon="heroicons-outline:dots-vertical"></iconify-icon>
                        </button>
                        <ul class="dropdown-menu min-w-[120px] absolute text-sm text-slate-700 dark:text-white hidden bg-white dark:bg-slate-700
                          shadow z-[2] float-left overflow-hidden list-none text-left rounded-lg mt-1 m-0 bg-clip-padding border-none">
                            <li>
                                <a href="' . route('startups.show', $startup) . '"
                                   class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600 dark:hover:text-white">
                                    View
                                </a>
                            </li>
                            <li>
                                <a href="' . route('startups.edit', $startup) . '"
                                   class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600 dark:hover:text-white">
                                    Edit
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>';
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(User $startup)
    {
        // Ensure the user is actually a startup
        if ($startup->role !== 'startup') {
            abort(404, 'Startup not found');
        }

        $breadcrumbsItems = [
            [
                'name' => 'Startup',
                'url' => route('startups.index'),
                'active' => false
            ],
            [
                'name' => $startup->name,
                'url' => null,
                'active' => true
            ],
        ];

        // Load comprehensive startup data
        $startup->load([
            'startupProfile.esgResponses.esgQuestion',
            'paymentMethods',
            'subscriptions.subscriptionProduct',
            'invoices',
            'media',
            'accountStatus'
        ]);

        // Get payment methods with default status indicators
        $paymentMethods = $startup->paymentMethods()
            ->orderBy('is_default', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get subscription history with status changes
        $subscriptionHistory = $startup->subscriptions()
            ->with('subscriptionProduct')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get invoices with download capabilities
        $invoices = $startup->invoices()
            ->orderBy('created_at', 'desc')
            ->get();

        // Get investors who have expressed interest in this startup
        $interestedInvestors = $startup->receivedInterestRequests()
            ->with(['requester.investorProfile', 'requester.media'])
            ->where('status', 'approved')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get associated categories
        $categories = $startup->startupProfile ? $startup->startupProfile->categories : collect();

        // Get ESG data
        $esgData = $startup->startupProfile ? $startup->startupProfile->esgResponses : collect();

        return view('startup.show', [
            'pageTitle' => 'Startup Profile - ' . $startup->name,
            'breadcrumbItems' => $breadcrumbsItems,
            'startup' => $startup,
            'paymentMethods' => $paymentMethods,
            'subscriptionHistory' => $subscriptionHistory,
            'invoices' => $invoices,
            'interestedInvestors' => $interestedInvestors,
            'categories' => $categories,
            'esgData' => $esgData
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $startup)
    {
        // Ensure the user is actually a startup
        if ($startup->role !== 'startup') {
            abort(404, 'Startup not found');
        }

        // TODO: Implement edit functionality
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $startup)
    {
        // Ensure the user is actually a startup
        if ($startup->role !== 'startup') {
            abort(404, 'Startup not found');
        }

        // TODO: Implement update functionality
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $startup)
    {
        // Ensure the user is actually a startup
        if ($startup->role !== 'startup') {
            abort(404, 'Startup not found');
        }

        // TODO: Implement delete functionality
    }
}
