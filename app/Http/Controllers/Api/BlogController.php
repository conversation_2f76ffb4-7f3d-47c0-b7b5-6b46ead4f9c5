<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Blog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;

class BlogController extends Controller
{
    /**
     * Display a listing of published blogs
     */
    public function index(Request $request): JsonResponse
    {
        $query = Blog::with(['author', 'taxonomies'])
            ->where('status', 'published')
            ->orderBy('published_at', 'desc');

        // Filter by category
        if ($request->has('category') && $request->category) {
            $query->whereHas('taxonomies', function ($q) use ($request) {
                $q->where('taxonomies.id', $request->category)
                  ->where('type', 'category');
            });
        }

        // Search functionality
        if ($request->has('search') && $request->search) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('excerpt', 'like', "%{$searchTerm}%")
                  ->orWhere('content', 'like', "%{$searchTerm}%");
            });
        }

        // Pagination
        $perPage = $request->get('per_page', 12);
        $blogs = $query->paginate($perPage);

        // Transform the data to include featured image and categories/tags
        $blogs->getCollection()->transform(function ($blog) {
            return $this->transformBlog($blog);
        });

        return response()->json($blogs);
    }

    /**
     * Display the specified blog
     */
    public function show($id): JsonResponse
    {
        $blog = Blog::with(['author', 'taxonomies'])
            ->where('status', 'published')
            ->findOrFail($id);

        // Increment view count
        $blog->increment('views_count');

        return response()->json($this->transformBlog($blog));
    }

    /**
     * Get blogs by category
     */
    public function byCategory($categoryId, Request $request): JsonResponse
    {
        $query = Blog::with(['author', 'taxonomies'])
            ->where('status', 'published')
            ->whereHas('taxonomies', function ($q) use ($categoryId) {
                $q->where('taxonomies.id', $categoryId)
                  ->where('type', 'category');
            })
            ->orderBy('published_at', 'desc');

        $perPage = $request->get('per_page', 12);
        $blogs = $query->paginate($perPage);

        $blogs->getCollection()->transform(function ($blog) {
            return $this->transformBlog($blog);
        });

        return response()->json($blogs);
    }

    /**
     * Search blogs
     */
    public function search(Request $request): JsonResponse
    {
        $query = $request->get('q', '');
        
        if (empty($query)) {
            return response()->json([
                'data' => [],
                'message' => 'Search query is required'
            ], 400);
        }

        $blogs = Blog::with(['author', 'taxonomies'])
            ->where('status', 'published')
            ->where(function ($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                  ->orWhere('excerpt', 'like', "%{$query}%")
                  ->orWhere('content', 'like', "%{$query}%");
            })
            ->orderBy('published_at', 'desc')
            ->paginate($request->get('per_page', 12));

        $blogs->getCollection()->transform(function ($blog) {
            return $this->transformBlog($blog);
        });

        return response()->json($blogs);
    }

    /**
     * Get related blogs
     */
    public function related($id, Request $request): JsonResponse
    {
        $blog = Blog::findOrFail($id);
        $limit = $request->get('limit', 3);

        // Get blogs with similar categories/tags
        $relatedBlogs = Blog::with(['author', 'taxonomies'])
            ->where('status', 'published')
            ->where('id', '!=', $id)
            ->whereHas('taxonomies', function ($q) use ($blog) {
                $taxonomyIds = $blog->taxonomies->pluck('id')->toArray();
                if (!empty($taxonomyIds)) {
                    $q->whereIn('taxonomies.id', $taxonomyIds);
                }
            })
            ->orderBy('published_at', 'desc')
            ->limit($limit)
            ->get();

        // If we don't have enough related blogs, fill with recent ones
        if ($relatedBlogs->count() < $limit) {
            $additionalBlogs = Blog::with(['author', 'taxonomies'])
                ->where('status', 'published')
                ->where('id', '!=', $id)
                ->whereNotIn('id', $relatedBlogs->pluck('id'))
                ->orderBy('published_at', 'desc')
                ->limit($limit - $relatedBlogs->count())
                ->get();

            $relatedBlogs = $relatedBlogs->merge($additionalBlogs);
        }

        $transformedBlogs = $relatedBlogs->map(function ($blog) {
            return $this->transformBlog($blog);
        });

        return response()->json($transformedBlogs);
    }

    /**
     * Get blog categories
     */
    public function categories(): JsonResponse
    {
        $categories = Taxonomy::where('type', 'category')
            ->whereHas('taxonomables', function ($q) {
                $q->where('taxonomable_type', Blog::class)
                  ->whereHas('taxonomable', function ($subQ) {
                      $subQ->where('status', 'published');
                  });
            })
            ->withCount(['taxonomables' => function ($q) {
                $q->where('taxonomable_type', Blog::class)
                  ->whereHas('taxonomable', function ($subQ) {
                      $subQ->where('status', 'published');
                  });
            }])
            ->orderBy('name')
            ->get();

        return response()->json($categories);
    }

    /**
     * Transform blog data for API response
     */
    private function transformBlog($blog): array
    {
        $featuredImage = null;
        if ($blog->getFirstMedia('featured_images')) {
            $featuredImage = $blog->getFirstMedia('featured_images')->getUrl();
        } elseif ($blog->featured_image) {
            $featuredImage = asset('storage/' . $blog->featured_image);
        }

        // Separate categories and tags
        $categories = $blog->taxonomies->where('type', 'category')->values();
        $tags = $blog->taxonomies->where('type', 'tag')->values();

        return [
            'id' => $blog->id,
            'title' => $blog->title,
            'excerpt' => $blog->excerpt,
            'content' => $blog->content,
            'featured_image' => $featuredImage,
            'status' => $blog->status,
            'published_at' => $blog->published_at?->toISOString(),
            'created_at' => $blog->created_at->toISOString(),
            'updated_at' => $blog->updated_at->toISOString(),
            'slug' => $blog->slug,
            'views_count' => $blog->views_count,
            'featured' => $blog->featured,
            'meta_data' => $blog->meta_data,
            'author' => [
                'id' => $blog->author->id,
                'name' => $blog->author->name,
                'email' => $blog->author->email,
            ],
            'categories' => $categories->map(function ($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'slug' => $category->slug,
                ];
            }),
            'tags' => $tags->map(function ($tag) {
                return [
                    'id' => $tag->id,
                    'name' => $tag->name,
                    'slug' => $tag->slug,
                ];
            }),
        ];
    }
}
