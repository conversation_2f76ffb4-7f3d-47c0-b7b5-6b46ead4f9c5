<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\InvestorProfile;
use App\Models\StartupProfile;
use App\Models\InterestRequest;
use App\Models\EsgResponse;
use Illuminate\Http\JsonResponse;

class DashboardController extends Controller
{
    /**
     * Get investor dashboard statistics
     */
    public function getInvestorStats(): JsonResponse
    {
        $user = auth()->user();
        $investorProfile = $user->investorProfile;

        if (!$investorProfile) {
            return response()->json([
                'total_investments' => 0,
                'active_interests' => 0,
                'startups_discovered' => 0,
                'pending_reviews' => 0,
            ]);
        }

        // Get actual stats from interest requests
        $activeInterests = InterestRequest::where('requester_id', $user->id)
            ->where('status', 'pending')
            ->count();

        $totalInterests = InterestRequest::where('requester_id', $user->id)->count();

        $startupsDiscovered = StartupProfile::whereHas('user', function($query) {
            $query->where('role', 'startup');
        })->count();

        return response()->json([
            'total_investments' => $investorProfile->investment_budget_max ?? 0,
            'active_interests' => $activeInterests,
            'startups_discovered' => $startupsDiscovered,
            'pending_reviews' => InterestRequest::where('target_id', $user->id)
                ->where('status', 'pending')
                ->count(),
        ]);
    }

    /**
     * Get startup dashboard statistics
     */
    public function getStartupStats(): JsonResponse
    {
        $user = auth()->user();
        $startupProfile = $user->startupProfile;

        if (!$startupProfile) {
            return response()->json([
                'profile_completion' => 0,
                'esg_score' => 0,
                'investor_interests' => 0,
                'profile_views' => 0,
            ]);
        }

        // Calculate profile completion percentage
        $profileCompletion = $this->calculateProfileCompletion($startupProfile);

        // Get investor interests
        $investorInterests = InterestRequest::where('target_id', $user->id)
            ->where('type', 'investment_interest')
            ->count();

        return response()->json([
            'profile_completion' => $profileCompletion,
            'esg_score' => $startupProfile->esg_score ?? 0,
            'investor_interests' => $investorInterests,
            'profile_views' => rand(50, 500), // Mock data for now
        ]);
    }

    /**
     * Get analyst dashboard statistics
     */
    public function getAnalystStats(): JsonResponse
    {
        $pendingApprovals = InterestRequest::where('status', 'pending')->count();
        $totalUsers = User::count();
        $activeInterests = InterestRequest::where('status', 'approved')->count();
        $esgAssessments = EsgResponse::count();

        return response()->json([
            'pending_approvals' => $pendingApprovals,
            'total_users' => $totalUsers,
            'active_interests' => $activeInterests,
            'esg_assessments' => $esgAssessments,
        ]);
    }

    /**
     * Get recent activity for dashboard
     */
    public function getRecentActivity(): JsonResponse
    {
        $recentInterests = InterestRequest::with(['requester', 'target'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($request) {
                return [
                    'id' => $request->id,
                    'type' => 'interest_request',
                    'message' => $request->requester->name . ' expressed interest in ' . $request->target->name,
                    'status' => $request->status,
                    'created_at' => $request->created_at->diffForHumans(),
                ];
            });

        return response()->json([
            'activities' => $recentInterests,
        ]);
    }

    /**
     * Calculate startup profile completion percentage
     */
    private function calculateProfileCompletion(StartupProfile $profile): int
    {
        $fields = [
            'company_name',
            'company_description',
            'founding_date',
            'employee_count',
            'funding_stage',
            'funding_amount_sought',
            'business_model',
        ];

        $completedFields = 0;
        foreach ($fields as $field) {
            if (!empty($profile->$field)) {
                $completedFields++;
            }
        }

        return round(($completedFields / count($fields)) * 100);
    }
}
