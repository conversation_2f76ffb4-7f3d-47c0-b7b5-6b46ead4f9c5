<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\InvestorProfile;
use App\Models\StartupProfile;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class DiscoveryController extends Controller
{
    public function __construct()
    {
        // Authentication is handled by route middleware
        // Role-based authorization is handled by route middleware
    }

    /**
     * Discover startups for investors
     */
    public function discoverStartups(Request $request): JsonResponse
    {
        $user = auth()->user();

        if (!$user->isInvestor()) {
            return response()->json([
                'success' => false,
                'message' => 'Only investors can discover startups',
            ], 403);
        }

        $investorProfile = $user->investorProfile;
        if (!$investorProfile || !$investorProfile->profile_completed) {
            return response()->json([
                'success' => false,
                'message' => 'Please complete your investor profile first',
            ], 422);
        }

        $query = StartupProfile::with(['user', 'user.categories'])
            ->completed()
            ->esgCompleted();

        // Apply investor preferences
        $this->applyInvestorFilters($query, $investorProfile, $request);

        // Apply category matching
        $userCategories = $user->categories->pluck('id')->toArray();
        if (!empty($userCategories)) {
            $query->whereHas('user.categories', function ($q) use ($userCategories) {
                $q->whereIn('categories.id', $userCategories);
            });
        }

        // Exclude startups already contacted
        $contactedStartupIds = $user->sentInterestRequests()
            ->where('type', 'investment_interest')
            ->pluck('target_id')
            ->toArray();

        if (!empty($contactedStartupIds)) {
            $query->whereNotIn('user_id', $contactedStartupIds);
        }

        $startups = $query->paginate($request->per_page ?? 10);

        // Calculate match scores
        $startups->getCollection()->transform(function ($startup) use ($investorProfile, $userCategories) {
            $startup->match_score = $this->calculateStartupMatchScore($startup, $investorProfile, $userCategories);
            return $startup;
        });

        // Sort by match score
        $sortedStartups = $startups->getCollection()->sortByDesc('match_score')->values();
        $startups->setCollection($sortedStartups);

        return response()->json([
            'success' => true,
            'data' => $startups,
        ]);
    }

    /**
     * Discover investors for startups
     */
    public function discoverInvestors(Request $request): JsonResponse
    {
        $user = auth()->user();

        if (!$user->isStartup()) {
            return response()->json([
                'success' => false,
                'message' => 'Only startups can discover investors',
            ], 403);
        }

        $startupProfile = $user->startupProfile;
        if (!$startupProfile || !$startupProfile->profile_completed) {
            return response()->json([
                'success' => false,
                'message' => 'Please complete your startup profile first',
            ], 422);
        }

        $query = InvestorProfile::with(['user', 'user.categories'])
            ->completed();

        // Apply startup preferences
        $this->applyStartupFilters($query, $startupProfile, $request);

        // Apply category matching
        $userCategories = $user->categories->pluck('id')->toArray();
        if (!empty($userCategories)) {
            $query->whereHas('user.categories', function ($q) use ($userCategories) {
                $q->whereIn('categories.id', $userCategories);
            });
        }

        // Exclude investors already contacted
        $contactedInvestorIds = $user->sentInterestRequests()
            ->where('type', 'funding_request')
            ->pluck('target_id')
            ->toArray();

        if (!empty($contactedInvestorIds)) {
            $query->whereNotIn('user_id', $contactedInvestorIds);
        }

        $investors = $query->paginate($request->per_page ?? 10);

        // Calculate match scores
        $investors->getCollection()->transform(function ($investor) use ($startupProfile, $userCategories) {
            $investor->match_score = $this->calculateInvestorMatchScore($investor, $startupProfile, $userCategories);
            return $investor;
        });

        // Sort by match score
        $sortedInvestors = $investors->getCollection()->sortByDesc('match_score')->values();
        $investors->setCollection($sortedInvestors);

        return response()->json([
            'success' => true,
            'data' => $investors,
        ]);
    }

    /**
     * Apply investor-specific filters to startup query
     */
    private function applyInvestorFilters($query, $investorProfile, $request)
    {
        // Filter by funding amount
        if ($investorProfile->investment_budget_min && $investorProfile->investment_budget_max) {
            $query->seekingFunding(
                $investorProfile->investment_budget_min,
                $investorProfile->investment_budget_max
            );
        }

        // Filter by ESG score based on investor preferences
        if ($request->has('min_esg_score')) {
            $query->byEsgScore($request->min_esg_score);
        }

        // Filter by funding stage
        if ($request->has('funding_stages')) {
            $stages = is_array($request->funding_stages)
                ? $request->funding_stages
                : explode(',', $request->funding_stages);
            $query->whereIn('funding_stage', $stages);
        }
    }

    /**
     * Apply startup-specific filters to investor query
     */
    private function applyStartupFilters($query, $startupProfile, $request)
    {
        // Filter by investment budget that covers startup's funding needs
        if ($startupProfile->funding_amount_sought) {
            $query->withinBudget(0, $startupProfile->funding_amount_sought);
        }

        // Filter by risk tolerance
        if ($request->has('risk_tolerance')) {
            $query->byRiskTolerance($request->risk_tolerance);
        }

        // Filter by experience level
        if ($request->has('experience_levels')) {
            $levels = is_array($request->experience_levels)
                ? $request->experience_levels
                : explode(',', $request->experience_levels);
            $query->whereIn('investment_experience', $levels);
        }
    }

    /**
     * Calculate match score for startup
     */
    private function calculateStartupMatchScore($startup, $investorProfile, $userCategories): float
    {
        $score = 0;

        // Category match (40% weight)
        $startupCategories = $startup->user->categories->pluck('id')->toArray();
        $categoryMatch = count(array_intersect($userCategories, $startupCategories));
        $totalCategories = count(array_unique(array_merge($userCategories, $startupCategories)));
        $categoryScore = $totalCategories > 0 ? ($categoryMatch / $totalCategories) * 40 : 0;
        $score += $categoryScore;

        // Funding amount match (30% weight)
        if ($investorProfile->investment_budget_min && $investorProfile->investment_budget_max && $startup->funding_amount_sought) {
            if ($startup->funding_amount_sought >= $investorProfile->investment_budget_min &&
                $startup->funding_amount_sought <= $investorProfile->investment_budget_max) {
                $score += 30;
            } else {
                // Partial score for close matches
                $midpoint = ($investorProfile->investment_budget_min + $investorProfile->investment_budget_max) / 2;
                $distance = abs($startup->funding_amount_sought - $midpoint);
                $maxDistance = $investorProfile->investment_budget_max - $investorProfile->investment_budget_min;
                $proximityScore = max(0, 1 - ($distance / $maxDistance)) * 15;
                $score += $proximityScore;
            }
        }

        // ESG score (20% weight)
        if ($startup->esg_score) {
            $esgScore = ($startup->esg_score / 100) * 20;
            $score += $esgScore;
        }

        // Profile completeness (10% weight)
        if ($startup->profile_completed && $startup->esg_completed) {
            $score += 10;
        }

        return round($score, 2);
    }

    /**
     * Calculate match score for investor
     */
    private function calculateInvestorMatchScore($investor, $startupProfile, $userCategories): float
    {
        $score = 0;

        // Category match (40% weight)
        $investorCategories = $investor->user->categories->pluck('id')->toArray();
        $categoryMatch = count(array_intersect($userCategories, $investorCategories));
        $totalCategories = count(array_unique(array_merge($userCategories, $investorCategories)));
        $categoryScore = $totalCategories > 0 ? ($categoryMatch / $totalCategories) * 40 : 0;
        $score += $categoryScore;

        // Budget compatibility (40% weight)
        if ($investor->investment_budget_min && $investor->investment_budget_max && $startupProfile->funding_amount_sought) {
            if ($startupProfile->funding_amount_sought >= $investor->investment_budget_min &&
                $startupProfile->funding_amount_sought <= $investor->investment_budget_max) {
                $score += 40;
            } else {
                // Partial score for close matches
                $midpoint = ($investor->investment_budget_min + $investor->investment_budget_max) / 2;
                $distance = abs($startupProfile->funding_amount_sought - $midpoint);
                $maxDistance = $investor->investment_budget_max - $investor->investment_budget_min;
                $proximityScore = max(0, 1 - ($distance / $maxDistance)) * 20;
                $score += $proximityScore;
            }
        }

        // Experience level (20% weight)
        $experienceScores = ['beginner' => 5, 'intermediate' => 10, 'experienced' => 15, 'expert' => 20];
        $score += $experienceScores[$investor->investment_experience] ?? 0;

        return round($score, 2);
    }

    /**
     * Get category-based startup matches for investors
     */
    public function getCategoryMatchedStartups(Request $request): JsonResponse
    {
        $user = auth()->user();

        if (!$user->isInvestor()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Check if user has active subscription
        $hasActiveSubscription = $user->subscriptions()
            ->whereIn('status', ['active', 'trialing'])
            ->exists();

        if (!$hasActiveSubscription) {
            return response()->json([
                'status' => 'error',
                'message' => 'Active subscription required for discovery features',
                'data' => []
            ], 403);
        }

        // Get investor's categories
        $investorCategories = $user->categories->pluck('id');

        if ($investorCategories->isEmpty()) {
            return response()->json([
                'status' => 'success',
                'message' => 'No categories selected. Please update your profile to get better matches.',
                'data' => []
            ]);
        }

        // Find startups with matching categories
        $startups = User::where('role', 'startup')
            ->where('id', '!=', $user->id)
            ->whereHas('categories', function ($query) use ($investorCategories) {
                $query->whereIn('categories.id', $investorCategories);
            })
            ->with(['startupProfile', 'categories', 'media'])
            ->get()
            ->map(function ($startup) use ($investorCategories) {
                $startupCategories = $startup->categories->pluck('id');

                // Calculate match score based on category overlap
                $matchingCategories = $investorCategories->intersect($startupCategories);
                $matchScore = $startupCategories->isNotEmpty() ?
                    round(($matchingCategories->count() / $startupCategories->count()) * 100) : 0;

                return [
                    'id' => $startup->id,
                    'name' => $startup->name,
                    'email' => $startup->email,
                    'company_name' => $startup->startupProfile->company_name ?? $startup->name,
                    'industry' => $startup->startupProfile->industry ?? 'Technology',
                    'description' => $startup->startupProfile->description ?? 'No description available',
                    'funding_stage' => $startup->startupProfile->funding_stage ?? 'Not specified',
                    'match_score' => $matchScore,
                    'categories' => $startup->categories->pluck('name'),
                    'profile_image' => $startup->getFirstMediaUrl('profile-image'),
                    'created_at' => $startup->created_at->toISOString(),
                ];
            })
            ->sortByDesc('match_score')
            ->values();

        return response()->json([
            'status' => 'success',
            'data' => $startups
        ]);
    }

    /**
     * Get category-based investor matches for startups
     */
    public function getCategoryMatchedInvestors(Request $request): JsonResponse
    {
        $user = auth()->user();

        if (!$user->isStartup()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Check if user has active subscription
        $hasActiveSubscription = $user->subscriptions()
            ->whereIn('status', ['active', 'trialing'])
            ->exists();

        if (!$hasActiveSubscription) {
            return response()->json([
                'status' => 'error',
                'message' => 'Active subscription required for discovery features',
                'data' => []
            ], 403);
        }

        // Get startup's categories
        $startupCategories = $user->categories->pluck('id');

        if ($startupCategories->isEmpty()) {
            return response()->json([
                'status' => 'success',
                'message' => 'No categories selected. Please update your profile to get better matches.',
                'data' => []
            ]);
        }

        // Find investors with matching categories
        $investors = User::where('role', 'investor')
            ->where('id', '!=', $user->id)
            ->whereHas('categories', function ($query) use ($startupCategories) {
                $query->whereIn('categories.id', $startupCategories);
            })
            ->with(['investorProfile', 'categories', 'media'])
            ->get()
            ->map(function ($investor) use ($startupCategories) {
                $investorCategories = $investor->categories->pluck('id');

                // Calculate match score based on category overlap
                $matchingCategories = $startupCategories->intersect($investorCategories);
                $matchScore = $investorCategories->isNotEmpty() ?
                    round(($matchingCategories->count() / $investorCategories->count()) * 100) : 0;

                return [
                    'id' => $investor->id,
                    'name' => $investor->name,
                    'email' => $investor->email,
                    'full_name' => $investor->name,
                    'investment_focus' => $investor->investorProfile->preferred_sectors ?? ['Various Industries'],
                    'description' => $investor->investorProfile->bio ?? 'No bio available',
                    'min_investment' => $investor->investorProfile->investment_range_min ?? 0,
                    'max_investment' => $investor->investorProfile->investment_range_max ?? 0,
                    'match_score' => $matchScore,
                    'categories' => $investor->categories->pluck('name'),
                    'profile_image' => $investor->getFirstMediaUrl('profile-image'),
                    'created_at' => $investor->created_at->toISOString(),
                ];
            })
            ->sortByDesc('match_score')
            ->values();

        return response()->json([
            'status' => 'success',
            'data' => $investors
        ]);
    }
}
