<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\AccountStatusService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class AccountStatusController extends Controller
{
    protected AccountStatusService $accountStatusService;

    public function __construct(AccountStatusService $accountStatusService)
    {
        $this->accountStatusService = $accountStatusService;
    }

    /**
     * Get current user's account status
     */
    public function show(): JsonResponse
    {
        try {
            $user = Auth::user();

            return response()->json([
                'status' => 'success',
                'data' => [
                    'account_status' => $user->account_status,
                    'locked_at' => $user->account_locked_at,
                    'account_locked' => $user->account_locked,
                    'is_locked' => $user->account_status === 'locked',
                    'is_suspended' => $user->account_status === 'suspended',
                    'is_active' => $user->account_status === 'active',
                ],
                'message' => 'Account status retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving account status', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve account status'
            ], 500);
        }
    }

    /**
     * Lock a user account (Admin only)
     */
    public function lock(Request $request, User $user): JsonResponse
    {
        $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        try {
            $success = $this->accountStatusService->lockAccount(
                $user, 
                $request->reason, 
                Auth::user()
            );

            if ($success) {
                return response()->json([
                    'status' => 'success',
                    'message' => 'Account locked successfully',
                    'data' => [
                        'user_id' => $user->id,
                        'account_status' => 'locked',
                        'reason' => $request->reason,
                        'locked_at' => now()->toISOString(),
                    ]
                ]);
            }

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to lock account'
            ], 500);

        } catch (\Exception $e) {
            Log::error('Error locking account', [
                'user_id' => $user->id,
                'admin_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to lock account'
            ], 500);
        }
    }

    /**
     * Unlock a user account (Admin only)
     */
    public function unlock(Request $request, User $user): JsonResponse
    {
        $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        try {
            $success = $this->accountStatusService->unlockAccount(
                $user, 
                $request->reason, 
                Auth::user()
            );

            if ($success) {
                return response()->json([
                    'status' => 'success',
                    'message' => 'Account unlocked successfully',
                    'data' => [
                        'user_id' => $user->id,
                        'account_status' => 'active',
                        'reason' => $request->reason,
                        'unlocked_at' => now()->toISOString(),
                    ]
                ]);
            }

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to unlock account'
            ], 500);

        } catch (\Exception $e) {
            Log::error('Error unlocking account', [
                'user_id' => $user->id,
                'admin_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to unlock account'
            ], 500);
        }
    }

    /**
     * Suspend a user account (Admin only)
     */
    public function suspend(Request $request, User $user): JsonResponse
    {
        $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        try {
            $success = $this->accountStatusService->suspendAccount(
                $user, 
                $request->reason, 
                Auth::user()
            );

            if ($success) {
                return response()->json([
                    'status' => 'success',
                    'message' => 'Account suspended successfully',
                    'data' => [
                        'user_id' => $user->id,
                        'account_status' => 'suspended',
                        'reason' => $request->reason,
                        'suspended_at' => now()->toISOString(),
                    ]
                ]);
            }

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to suspend account'
            ], 500);

        } catch (\Exception $e) {
            Log::error('Error suspending account', [
                'user_id' => $user->id,
                'admin_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to suspend account'
            ], 500);
        }
    }

    /**
     * Activate a user account (Admin only)
     */
    public function activate(Request $request, User $user): JsonResponse
    {
        $request->validate([
            'reason' => 'string|max:500',
        ]);

        try {
            $success = $this->accountStatusService->activateAccount(
                $user, 
                $request->reason ?? 'Account activated by admin', 
                Auth::user()
            );

            if ($success) {
                return response()->json([
                    'status' => 'success',
                    'message' => 'Account activated successfully',
                    'data' => [
                        'user_id' => $user->id,
                        'account_status' => 'active',
                        'reason' => $request->reason ?? 'Account activated by admin',
                        'activated_at' => now()->toISOString(),
                    ]
                ]);
            }

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to activate account'
            ], 500);

        } catch (\Exception $e) {
            Log::error('Error activating account', [
                'user_id' => $user->id,
                'admin_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to activate account'
            ], 500);
        }
    }

    /**
     * Bulk update account statuses (Admin only)
     */
    public function bulkUpdate(Request $request): JsonResponse
    {
        $request->validate([
            'user_ids' => 'required|array|min:1',
            'user_ids.*' => 'exists:users,id',
            'action' => ['required', Rule::in(['lock', 'unlock', 'suspend', 'activate'])],
            'reason' => 'required|string|max:500',
        ]);

        try {
            $userIds = $request->user_ids;
            $action = $request->action;
            $reason = $request->reason;
            $admin = Auth::user();

            $results = ['success' => 0, 'failed' => 0, 'errors' => []];

            foreach ($userIds as $userId) {
                $user = User::find($userId);
                
                if (!$user) {
                    $results['failed']++;
                    $results['errors'][] = "User with ID {$userId} not found";
                    continue;
                }

                $success = false;
                switch ($action) {
                    case 'lock':
                        $success = $this->accountStatusService->lockAccount($user, $reason, $admin);
                        break;
                    case 'unlock':
                        $success = $this->accountStatusService->unlockAccount($user, $reason, $admin);
                        break;
                    case 'suspend':
                        $success = $this->accountStatusService->suspendAccount($user, $reason, $admin);
                        break;
                    case 'activate':
                        $success = $this->accountStatusService->activateAccount($user, $reason, $admin);
                        break;
                }

                if ($success) {
                    $results['success']++;
                } else {
                    $results['failed']++;
                    $results['errors'][] = "Failed to {$action} account for user {$user->email}";
                }
            }

            return response()->json([
                'status' => 'success',
                'message' => "Bulk {$action} operation completed",
                'data' => $results
            ]);

        } catch (\Exception $e) {
            Log::error('Error in bulk account status update', [
                'admin_id' => Auth::id(),
                'action' => $request->action ?? 'unknown',
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to perform bulk operation'
            ], 500);
        }
    }
}
