<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SocialMediaLink;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class SocialMediaLinkController extends Controller
{
    /**
     * Get social media links for authenticated user
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();

        $links = $user->socialMediaLinks()
            ->orderBy('is_primary', 'desc')
            ->orderBy('platform')
            ->get()
            ->map(function ($link) {
                return [
                    'id' => $link->id,
                    'platform' => $link->platform,
                    'platform_name' => $link->platform_name,
                    'platform_icon' => $link->platform_icon,
                    'url' => $link->url,
                    'formatted_url' => $link->formatted_url,
                    'username' => $link->username,
                    'is_primary' => $link->is_primary,
                    'is_public' => $link->is_public,
                    'created_at' => $link->created_at,
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $links,
        ]);
    }

    /**
     * Store a new social media link
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'platform' => ['required', Rule::in(['linkedin', 'twitter', 'facebook', 'instagram', 'website', 'github', 'youtube', 'tiktok'])],
            'url' => ['required', 'url', 'max:255'],
            'username' => ['nullable', 'string', 'max:100'],
            'is_primary' => ['boolean'],
            'is_public' => ['boolean'],
        ]);

        $user = $request->user();

        // Check if user already has a link for this platform
        $existingLink = $user->socialMediaLinks()
            ->where('platform', $validated['platform'])
            ->first();

        if ($existingLink) {
            return response()->json([
                'success' => false,
                'message' => "You already have a {$validated['platform']} link. Please update the existing one.",
            ], 422);
        }

        // If this is set as primary, remove primary status from other links of the same platform
        if ($validated['is_primary'] ?? false) {
            $user->socialMediaLinks()
                ->where('platform', $validated['platform'])
                ->update(['is_primary' => false]);
        }

        $link = $user->socialMediaLinks()->create([
            'platform' => $validated['platform'],
            'url' => $validated['url'],
            'username' => $validated['username'] ?? null,
            'is_primary' => $validated['is_primary'] ?? true,
            'is_public' => $validated['is_public'] ?? true,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Social media link added successfully.',
            'data' => [
                'id' => $link->id,
                'platform' => $link->platform,
                'platform_name' => $link->platform_name,
                'platform_icon' => $link->platform_icon,
                'url' => $link->url,
                'formatted_url' => $link->formatted_url,
                'username' => $link->username,
                'is_primary' => $link->is_primary,
                'is_public' => $link->is_public,
                'created_at' => $link->created_at,
            ],
        ], 201);
    }

    /**
     * Update an existing social media link
     */
    public function update(Request $request, SocialMediaLink $socialMediaLink): JsonResponse
    {
        // Ensure the link belongs to the authenticated user
        if ($socialMediaLink->linkable_id !== $request->user()->id ||
            $socialMediaLink->linkable_type !== get_class($request->user())) {
            return response()->json([
                'success' => false,
                'message' => 'Social media link not found.',
            ], 404);
        }

        $validated = $request->validate([
            'url' => ['required', 'url', 'max:255'],
            'username' => ['nullable', 'string', 'max:100'],
            'is_primary' => ['boolean'],
            'is_public' => ['boolean'],
        ]);

        // If this is set as primary, remove primary status from other links of the same platform
        if ($validated['is_primary'] ?? false) {
            $request->user()->socialMediaLinks()
                ->where('platform', $socialMediaLink->platform)
                ->where('id', '!=', $socialMediaLink->id)
                ->update(['is_primary' => false]);
        }

        $socialMediaLink->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Social media link updated successfully.',
            'data' => [
                'id' => $socialMediaLink->id,
                'platform' => $socialMediaLink->platform,
                'platform_name' => $socialMediaLink->platform_name,
                'platform_icon' => $socialMediaLink->platform_icon,
                'url' => $socialMediaLink->url,
                'formatted_url' => $socialMediaLink->formatted_url,
                'username' => $socialMediaLink->username,
                'is_primary' => $socialMediaLink->is_primary,
                'is_public' => $socialMediaLink->is_public,
                'updated_at' => $socialMediaLink->updated_at,
            ],
        ]);
    }

    /**
     * Delete a social media link
     */
    public function destroy(Request $request, SocialMediaLink $socialMediaLink): JsonResponse
    {
        // Ensure the link belongs to the authenticated user
        if ($socialMediaLink->linkable_id !== $request->user()->id ||
            $socialMediaLink->linkable_type !== get_class($request->user())) {
            return response()->json([
                'success' => false,
                'message' => 'Social media link not found.',
            ], 404);
        }

        $platform = $socialMediaLink->platform_name;
        $socialMediaLink->delete();

        return response()->json([
            'success' => true,
            'message' => "{$platform} link deleted successfully.",
        ]);
    }

    /**
     * Get available social media platforms
     */
    public function platforms(): JsonResponse
    {
        $platforms = [
            'linkedin' => [
                'name' => 'LinkedIn',
                'icon' => 'fab fa-linkedin',
                'url_pattern' => 'https://linkedin.com/in/{username}',
                'validation_regex' => '/^https?:\/\/(www\.)?linkedin\.com\/.*$/',
            ],
            'twitter' => [
                'name' => 'Twitter',
                'icon' => 'fab fa-twitter',
                'url_pattern' => 'https://twitter.com/{username}',
                'validation_regex' => '/^https?:\/\/(www\.)?twitter\.com\/.*$/',
            ],
            'facebook' => [
                'name' => 'Facebook',
                'icon' => 'fab fa-facebook',
                'url_pattern' => 'https://facebook.com/{username}',
                'validation_regex' => '/^https?:\/\/(www\.)?facebook\.com\/.*$/',
            ],
            'instagram' => [
                'name' => 'Instagram',
                'icon' => 'fab fa-instagram',
                'url_pattern' => 'https://instagram.com/{username}',
                'validation_regex' => '/^https?:\/\/(www\.)?instagram\.com\/.*$/',
            ],
            'github' => [
                'name' => 'GitHub',
                'icon' => 'fab fa-github',
                'url_pattern' => 'https://github.com/{username}',
                'validation_regex' => '/^https?:\/\/(www\.)?github\.com\/.*$/',
            ],
            'youtube' => [
                'name' => 'YouTube',
                'icon' => 'fab fa-youtube',
                'url_pattern' => 'https://youtube.com/c/{username}',
                'validation_regex' => '/^https?:\/\/(www\.)?youtube\.com\/.*$/',
            ],
            'tiktok' => [
                'name' => 'TikTok',
                'icon' => 'fab fa-tiktok',
                'url_pattern' => 'https://tiktok.com/@{username}',
                'validation_regex' => '/^https?:\/\/(www\.)?tiktok\.com\/.*$/',
            ],
            'website' => [
                'name' => 'Website',
                'icon' => 'fas fa-globe',
                'url_pattern' => 'https://{domain}',
                'validation_regex' => '/^https?:\/\/.*$/',
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $platforms,
        ]);
    }
}
