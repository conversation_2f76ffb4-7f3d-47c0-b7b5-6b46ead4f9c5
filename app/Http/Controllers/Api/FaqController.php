<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Faq;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;

class FaqController extends Controller
{
    /**
     * Display a listing of FAQs
     */
    public function index(Request $request): JsonResponse
    {
        $query = Faq::with(['taxonomies'])
            ->where('status', 'published')
            ->orderBy('order', 'asc')
            ->orderBy('created_at', 'desc');

        // Filter by role
        if ($request->has('role') && $request->role) {
            $query->where(function ($q) use ($request) {
                $q->where('target_role', $request->role)
                  ->orWhere('target_role', 'all');
            });
        }

        // Filter by category
        if ($request->has('category') && $request->category) {
            $query->whereHas('taxonomies', function ($q) use ($request) {
                $q->where('taxonomies.id', $request->category)
                  ->where('type', 'faq_category');
            });
        }

        // Search functionality
        if ($request->has('search') && $request->search) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('question', 'like', "%{$searchTerm}%")
                  ->orWhere('answer', 'like', "%{$searchTerm}%");
            });
        }

        // Pagination
        $perPage = $request->get('per_page', 50);
        $faqs = $query->paginate($perPage);

        // Transform the data
        $faqs->getCollection()->transform(function ($faq) {
            return $this->transformFaq($faq);
        });

        return response()->json($faqs);
    }

    /**
     * Get FAQs by role
     */
    public function byRole($role, Request $request): JsonResponse
    {
        $query = Faq::with(['taxonomies'])
            ->where('status', 'published')
            ->where(function ($q) use ($role) {
                $q->where('target_role', $role)
                  ->orWhere('target_role', 'all');
            })
            ->orderBy('order', 'asc')
            ->orderBy('created_at', 'desc');

        // Filter by category
        if ($request->has('category') && $request->category) {
            $query->whereHas('taxonomies', function ($q) use ($request) {
                $q->where('taxonomies.id', $request->category)
                  ->where('type', 'faq_category');
            });
        }

        $perPage = $request->get('per_page', 50);
        $faqs = $query->paginate($perPage);

        $faqs->getCollection()->transform(function ($faq) {
            return $this->transformFaq($faq);
        });

        return response()->json($faqs);
    }

    /**
     * Get FAQs by category
     */
    public function byCategory($categoryId, Request $request): JsonResponse
    {
        $query = Faq::with(['taxonomies'])
            ->where('status', 'published')
            ->whereHas('taxonomies', function ($q) use ($categoryId) {
                $q->where('taxonomies.id', $categoryId)
                  ->where('type', 'faq_category');
            })
            ->orderBy('order', 'asc')
            ->orderBy('created_at', 'desc');

        // Filter by role
        if ($request->has('role') && $request->role) {
            $query->where(function ($q) use ($request) {
                $q->where('target_role', $request->role)
                  ->orWhere('target_role', 'all');
            });
        }

        $perPage = $request->get('per_page', 50);
        $faqs = $query->paginate($perPage);

        $faqs->getCollection()->transform(function ($faq) {
            return $this->transformFaq($faq);
        });

        return response()->json($faqs);
    }

    /**
     * Search FAQs
     */
    public function search(Request $request): JsonResponse
    {
        $query = $request->get('q', '');
        
        if (empty($query)) {
            return response()->json([
                'data' => [],
                'message' => 'Search query is required'
            ], 400);
        }

        $faqQuery = Faq::with(['taxonomies'])
            ->where('status', 'published')
            ->where(function ($q) use ($query) {
                $q->where('question', 'like', "%{$query}%")
                  ->orWhere('answer', 'like', "%{$query}%");
            });

        // Filter by role if provided
        if ($request->has('role') && $request->role) {
            $faqQuery->where(function ($q) use ($request) {
                $q->where('target_role', $request->role)
                  ->orWhere('target_role', 'all');
            });
        }

        $faqs = $faqQuery->orderBy('order', 'asc')
            ->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 50));

        $faqs->getCollection()->transform(function ($faq) {
            return $this->transformFaq($faq);
        });

        return response()->json($faqs);
    }

    /**
     * Get FAQ categories
     */
    public function categories(Request $request): JsonResponse
    {
        $query = Taxonomy::where('type', 'faq_category')
            ->whereHas('taxonomables', function ($q) use ($request) {
                $q->where('taxonomable_type', Faq::class)
                  ->whereHas('taxonomable', function ($subQ) use ($request) {
                      $subQ->where('status', 'published');
                      
                      // Filter by role if provided
                      if ($request->has('role') && $request->role) {
                          $subQ->where(function ($roleQ) use ($request) {
                              $roleQ->where('target_role', $request->role)
                                    ->orWhere('target_role', 'all');
                          });
                      }
                  });
            })
            ->withCount(['taxonomables' => function ($q) use ($request) {
                $q->where('taxonomable_type', Faq::class)
                  ->whereHas('taxonomable', function ($subQ) use ($request) {
                      $subQ->where('status', 'published');
                      
                      // Filter by role if provided
                      if ($request->has('role') && $request->role) {
                          $subQ->where(function ($roleQ) use ($request) {
                              $roleQ->where('target_role', $request->role)
                                    ->orWhere('target_role', 'all');
                          });
                      }
                  });
            }])
            ->orderBy('name')
            ->get();

        return response()->json($query);
    }

    /**
     * Transform FAQ data for API response
     */
    private function transformFaq($faq): array
    {
        // Get category
        $category = $faq->taxonomies->where('type', 'faq_category')->first();

        return [
            'id' => $faq->id,
            'question' => $faq->question,
            'answer' => $faq->answer,
            'status' => $faq->status,
            'target_role' => $faq->target_role,
            'order' => $faq->order,
            'created_at' => $faq->created_at->toISOString(),
            'updated_at' => $faq->updated_at->toISOString(),
            'category' => $category ? [
                'id' => $category->id,
                'name' => $category->name,
                'slug' => $category->slug,
            ] : null,
        ];
    }
}
