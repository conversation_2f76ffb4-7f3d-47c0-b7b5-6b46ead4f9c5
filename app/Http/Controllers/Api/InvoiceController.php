<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Invoice;
use App\Models\UserSubscription;
use App\Services\StripeService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class InvoiceController extends Controller
{
    protected StripeService $stripeService;

    public function __construct(StripeService $stripeService)
    {
        $this->stripeService = $stripeService;
        $this->authorizeResource(Invoice::class, 'invoice');
    }

    /**
     * Display a listing of user's invoices
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            
            $query = Invoice::where('user_id', $user->id)
                ->with(['userSubscription.product']);

            // Apply filters
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            if ($request->filled('from_date')) {
                $query->whereDate('created_at', '>=', $request->from_date);
            }

            if ($request->filled('to_date')) {
                $query->whereDate('created_at', '<=', $request->to_date);
            }

            // Apply sorting
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);

            // Paginate results
            $perPage = min($request->get('per_page', 15), 50);
            $invoices = $query->paginate($perPage);

            return response()->json([
                'status' => 'success',
                'data' => $invoices,
                'message' => 'Invoices retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving invoices', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve invoices'
            ], 500);
        }
    }

    /**
     * Display the specified invoice
     */
    public function show(Invoice $invoice): JsonResponse
    {
        try {
            $invoice->load(['userSubscription.subscriptionProduct', 'payment']);

            return response()->json([
                'status' => 'success',
                'data' => $invoice,
                'message' => 'Invoice retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving invoice', [
                'invoice_id' => $invoice->id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve invoice'
            ], 500);
        }
    }

    /**
     * Download invoice PDF
     */
    public function download(Invoice $invoice): JsonResponse
    {
        try {
            // Check if invoice has a Stripe invoice ID
            if (!$invoice->stripe_invoice_id) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invoice PDF not available'
                ], 404);
            }

            // Get the invoice PDF URL from Stripe
            $stripeInvoice = $this->stripeService->getInvoice($invoice->stripe_invoice_id);
            
            if (!$stripeInvoice->invoice_pdf) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invoice PDF not available'
                ], 404);
            }

            return response()->json([
                'status' => 'success',
                'data' => [
                    'download_url' => $stripeInvoice->invoice_pdf,
                    'hosted_url' => $stripeInvoice->hosted_invoice_url
                ],
                'message' => 'Invoice download links retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error getting invoice download', [
                'invoice_id' => $invoice->id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to get invoice download'
            ], 500);
        }
    }

    /**
     * Generate invoice for subscription
     */
    public function generate(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'subscription_id' => 'required|exists:user_subscriptions,id',
                'description' => 'nullable|string|max:500',
            ]);

            $subscription = UserSubscription::where('id', $request->subscription_id)
                ->where('user_id', Auth::id())
                ->firstOrFail();

            // Create invoice in Stripe
            $stripeInvoice = $this->stripeService->createInvoice(
                $subscription->stripe_customer_id,
                [
                    'subscription' => $subscription->stripe_subscription_id,
                    'description' => $request->description,
                    'auto_advance' => false, // Don't automatically finalize
                ]
            );

            // Generate custom invoice number
            $customInvoiceNumber = Invoice::generateInvoiceNumber();

            // Create local invoice record
            $invoice = Invoice::create([
                'user_id' => Auth::id(),
                'user_subscription_id' => $subscription->id,
                'stripe_invoice_id' => $stripeInvoice->id,
                'invoice_number' => $customInvoiceNumber,
                'subtotal' => ($stripeInvoice->subtotal ?? 0) / 100, // Convert from cents
                'tax_amount' => ($stripeInvoice->tax ?? 0) / 100,
                'total' => ($stripeInvoice->amount_due ?? 0) / 100,
                'currency' => $stripeInvoice->currency,
                'status' => $stripeInvoice->status,
                'due_date' => $stripeInvoice->due_date ? \Carbon\Carbon::createFromTimestamp($stripeInvoice->due_date) : null,
                'paid_at' => $stripeInvoice->status === 'paid' && $stripeInvoice->status_transitions?->paid_at
                    ? \Carbon\Carbon::createFromTimestamp($stripeInvoice->status_transitions->paid_at)
                    : null,
                'line_items' => $stripeInvoice->lines->data ?? [],
                'metadata' => [
                    'description' => $request->description,
                    'generated_manually' => true,
                    'stripe_invoice_url' => $stripeInvoice->hosted_invoice_url ?? null,
                    'stripe_pdf_url' => $stripeInvoice->invoice_pdf ?? null,
                ],
                'pdf_url' => $stripeInvoice->invoice_pdf ?? null,
            ]);

            // Update Stripe invoice with custom invoice number in metadata
            try {
                $this->stripeService->updateInvoice($stripeInvoice->id, [
                    'metadata' => [
                        'custom_invoice_number' => $customInvoiceNumber,
                        'application_invoice_id' => $invoice->id,
                        'generated_manually' => 'true',
                    ]
                ]);
            } catch (\Exception $e) {
                \Log::warning('Failed to update Stripe invoice with custom number', [
                    'invoice_id' => $invoice->id,
                    'stripe_invoice_id' => $stripeInvoice->id,
                    'custom_invoice_number' => $customInvoiceNumber,
                    'error' => $e->getMessage()
                ]);
            }

            $invoice->load(['userSubscription.product']);

            return response()->json([
                'status' => 'success',
                'data' => $invoice,
                'message' => 'Invoice generated successfully'
            ], 201);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            Log::error('Error generating invoice', [
                'user_id' => Auth::id(),
                'subscription_id' => $request->subscription_id ?? null,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to generate invoice'
            ], 500);
        }
    }

    /**
     * Pay an invoice
     */
    public function pay(Invoice $invoice): JsonResponse
    {
        try {
            // Check if invoice is payable
            if ($invoice->status === 'paid') {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invoice is already paid'
                ], 400);
            }

            if (!in_array($invoice->status, ['open', 'draft'])) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invoice cannot be paid in current status'
                ], 400);
            }

            // Pay the invoice in Stripe
            $stripeInvoice = $this->stripeService->payInvoice($invoice->stripe_invoice_id);

            // Update local invoice
            $invoice->update([
                'status' => $stripeInvoice->status,
                'paid_at' => $stripeInvoice->status === 'paid' ? now() : null,
            ]);

            $invoice->load(['userSubscription.subscriptionProduct']);

            return response()->json([
                'status' => 'success',
                'data' => $invoice,
                'message' => 'Invoice payment processed successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error paying invoice', [
                'invoice_id' => $invoice->id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to process invoice payment'
            ], 500);
        }
    }
}
