<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\StartupProfile;
use App\Models\SocialMediaLink;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;

class StartupProfileController extends Controller
{
    public function __construct()
    {
        // Authentication is handled by route middleware
        // Role-based authorization is handled by route middleware
    }

    /**
     * Display a listing of startup profiles (for analysts and investors)
     */
    public function index(Request $request): JsonResponse
    {
        $query = StartupProfile::with(['user', 'taxonomies'])
            ->completed();

        // Apply filters
        if ($request->has('funding_stage')) {
            $query->byFundingStage($request->funding_stage);
        }

        if ($request->has('min_esg_score')) {
            $query->byEsgScore($request->min_esg_score, $request->max_esg_score);
        }

        if ($request->has('min_funding') || $request->has('max_funding')) {
            $query->seekingFunding(
                $request->min_funding ?? 0,
                $request->max_funding
            );
        }

        // Updated to use StartupProfile taxonomy system instead of user categories
        if ($request->has('categories')) {
            $categoryIds = is_array($request->categories)
                ? $request->categories
                : explode(',', $request->categories);

            $query->whereHas('taxonomies', function ($q) use ($categoryIds) {
                $q->whereIn('taxonomies.id', $categoryIds)
                  ->where('taxonomies.type', 'category');
            });
        }

        // New filters for enhanced taxonomy system
        if ($request->has('keywords')) {
            $keywordIds = is_array($request->keywords)
                ? $request->keywords
                : explode(',', $request->keywords);

            $query->whereHas('taxonomies', function ($q) use ($keywordIds) {
                $q->whereIn('taxonomies.id', $keywordIds)
                  ->where('taxonomies.type', 'keyword');
            });
        }

        if ($request->has('industries')) {
            $industryIds = is_array($request->industries)
                ? $request->industries
                : explode(',', $request->industries);

            $query->whereHas('taxonomies', function ($q) use ($industryIds) {
                $q->whereIn('taxonomies.id', $industryIds)
                  ->where('taxonomies.type', 'industry');
            });
        }

        if ($request->has('technologies')) {
            $technologyIds = is_array($request->technologies)
                ? $request->technologies
                : explode(',', $request->technologies);

            $query->whereHas('taxonomies', function ($q) use ($technologyIds) {
                $q->whereIn('taxonomies.id', $technologyIds)
                  ->where('taxonomies.type', 'technology');
            });
        }

        $profiles = $query->paginate($request->per_page ?? 15);

        return response()->json([
            'success' => true,
            'data' => $profiles,
        ]);
    }

    /**
     * Store or update startup profile
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $user = auth()->user();

            if (!$user->isStartup()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Only startups can create startup profiles',
                ], 403);
            }

            $validated = $request->validate([
                // Company Information
                'company_name' => 'required|string|max:255',
                'company_description' => 'required|string|max:2000',
                'founding_date' => 'required|date|before_or_equal:today',
                'employee_count' => 'required|integer|min:1|max:100000',
                'website' => 'nullable|url|max:255',

                // Funding Information
                'funding_stage' => ['required', Rule::in([
                    'pre-seed', 'seed', 'series-a', 'series-b', 'series-c',
                    'series-d', 'growth', 'ipo', 'acquired'
                ])],
                'funding_amount_sought' => 'required|numeric|min:0|max:*********.99',
                'current_valuation' => 'nullable|numeric|min:0|max:*********.99',
                'business_model' => 'required|array|min:1',
                'business_model.*' => ['string', Rule::in([
                    'b2b', 'b2c', 'b2b2c', 'marketplace', 'saas', 'subscription',
                    'freemium', 'advertising', 'commission', 'licensing', 'other'
                ])],

                // Social Media
                'linkedin_url' => 'nullable|url|max:255',
                'twitter_handle' => 'nullable|string|max:50',
                'facebook_url' => 'nullable|url|max:255',
                'instagram_url' => 'nullable|url|max:255',

                // Taxonomies
                'categories' => 'nullable|array|max:5',
                'categories.*' => 'integer|exists:taxonomies,id',
                'keywords' => 'nullable|array|max:10',
                'keywords.*' => 'string|max:50',
                'industries' => 'nullable|array|max:5',
                'industries.*' => 'integer|exists:taxonomies,id',
                'technologies' => 'nullable|array|max:10',
                'technologies.*' => 'integer|exists:taxonomies,id',
            ]);

            DB::beginTransaction();

            // Prepare profile data (exclude social media and taxonomy fields)
            $profileData = collect($validated)->except([
                'linkedin_url', 'twitter_handle', 'facebook_url', 'instagram_url',
                'categories', 'keywords', 'industries', 'technologies'
            ])->toArray();

            $profile = StartupProfile::updateOrCreate(
                ['user_id' => $user->id],
                array_merge($profileData, [
                    'profile_completed' => $this->isProfileComplete($profileData),
                ])
            );

            // Handle social media links
            $this->updateSocialMediaLinks($profile, $validated);

            // Handle taxonomies
            $this->updateTaxonomies($profile, $validated);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Startup profile saved successfully',
                'data' => $profile->fresh(['socialMediaLinks', 'taxonomies']),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            \Log::error('Failed to save startup profile', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to save profile.',
                'error' => app()->isLocal() ? $e->getMessage() : 'Internal server error',
            ], 500);
        }
    }

    /**
     * Display the current user's startup profile
     */
    public function show(): JsonResponse
    {
        try {
            $user = auth()->user();

            if (!$user->isStartup()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied. Only startups can access this resource.',
                ], 403);
            }

            $profile = StartupProfile::with([
                'socialMediaLinks',
                'taxonomies',
                'media'
            ])->where('user_id', $user->id)->first();

            if (!$profile) {
                return response()->json([
                    'success' => true,
                    'data' => null,
                    'message' => 'No profile found. Please create your startup profile.',
                ]);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'profile' => $profile,
                    'categories' => $profile->categories()->get(),
                    'keywords' => $profile->keywords()->get(),
                    'industries' => $profile->industryTags()->get(),
                    'technologies' => $profile->technologyTags()->get(),
                    'social_media_links' => $profile->socialMediaLinks,
                    'media' => [
                        'company_logo' => $profile->getFirstMediaUrl('company_logo'),
                        'pitch_deck' => $profile->getFirstMediaUrl('pitch_deck'),
                        'business_plan' => $profile->getFirstMediaUrl('business_plan'),
                        'financial_projections' => $profile->getFirstMediaUrl('financial_projections'),
                    ],
                ],
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to fetch startup profile', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch profile data.',
                'error' => app()->isLocal() ? $e->getMessage() : 'Internal server error',
            ], 500);
        }
    }

    /**
     * Update startup profile
     */
    public function update(Request $request): JsonResponse
    {
        return $this->store($request);
    }

    /**
     * Remove startup profile
     */
    public function destroy(): JsonResponse
    {
        $user = auth()->user();
        $profile = $user->startupProfile;

        if (!$profile) {
            return response()->json([
                'success' => false,
                'message' => 'Startup profile not found',
            ], 404);
        }

        // Delete ESG responses
        $profile->esgResponses()->delete();
        $profile->delete();
        $user->categories()->detach();

        return response()->json([
            'success' => true,
            'message' => 'Startup profile deleted successfully',
        ]);
    }

    /**
     * Check if profile is complete
     */
    private function isProfileComplete(array $data): bool
    {
        $requiredFields = [
            'company_name',
            'company_description',
            'founding_date',
            'employee_count',
            'funding_stage',
            'funding_amount_sought',
        ];

        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get available taxonomy options for profile creation
     */
    public function getTaxonomyOptions(): JsonResponse
    {
        try {
            $categories = Taxonomy::where('type', 'category')->get(['id', 'name', 'description']);
            $industries = Taxonomy::where('type', 'industry')->get(['id', 'name', 'description']);
            $technologies = Taxonomy::where('type', 'technology')->get(['id', 'name', 'description']);

            return response()->json([
                'success' => true,
                'data' => [
                    'categories' => $categories,
                    'industries' => $industries,
                    'technologies' => $technologies,
                    'funding_stages' => [
                        ['value' => 'pre-seed', 'label' => 'Pre-Seed'],
                        ['value' => 'seed', 'label' => 'Seed'],
                        ['value' => 'series-a', 'label' => 'Series A'],
                        ['value' => 'series-b', 'label' => 'Series B'],
                        ['value' => 'series-c', 'label' => 'Series C'],
                        ['value' => 'series-d', 'label' => 'Series D'],
                        ['value' => 'growth', 'label' => 'Growth'],
                        ['value' => 'ipo', 'label' => 'IPO'],
                        ['value' => 'acquired', 'label' => 'Acquired'],
                    ],
                    'business_models' => [
                        ['value' => 'b2b', 'label' => 'B2B (Business to Business)'],
                        ['value' => 'b2c', 'label' => 'B2C (Business to Consumer)'],
                        ['value' => 'b2b2c', 'label' => 'B2B2C (Business to Business to Consumer)'],
                        ['value' => 'marketplace', 'label' => 'Marketplace'],
                        ['value' => 'saas', 'label' => 'SaaS (Software as a Service)'],
                        ['value' => 'subscription', 'label' => 'Subscription'],
                        ['value' => 'freemium', 'label' => 'Freemium'],
                        ['value' => 'advertising', 'label' => 'Advertising'],
                        ['value' => 'commission', 'label' => 'Commission'],
                        ['value' => 'licensing', 'label' => 'Licensing'],
                        ['value' => 'other', 'label' => 'Other'],
                    ],
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch taxonomy options.',
                'error' => app()->isLocal() ? $e->getMessage() : 'Internal server error',
            ], 500);
        }
    }

    /**
     * Update social media links for startup profile
     */
    private function updateSocialMediaLinks(StartupProfile $profile, array $data): void
    {
        // Delete existing social media links
        $profile->socialMediaLinks()->delete();

        $socialMediaMappings = [
            'linkedin_url' => 'linkedin',
            'facebook_url' => 'facebook',
            'instagram_url' => 'instagram',
        ];

        foreach ($socialMediaMappings as $field => $platform) {
            if (!empty($data[$field])) {
                SocialMediaLink::create([
                    'linkable_type' => StartupProfile::class,
                    'linkable_id' => $profile->id,
                    'platform' => $platform,
                    'url' => $data[$field],
                    'is_primary' => true,
                    'is_public' => true,
                ]);
            }
        }

        // Handle Twitter separately to extract username
        if (!empty($data['twitter_handle'])) {
            $username = ltrim($data['twitter_handle'], '@');
            SocialMediaLink::create([
                'linkable_type' => StartupProfile::class,
                'linkable_id' => $profile->id,
                'platform' => 'twitter',
                'url' => "https://twitter.com/{$username}",
                'username' => $username,
                'is_primary' => true,
                'is_public' => true,
            ]);
        }
    }

    /**
     * Update taxonomies for startup profile
     */
    private function updateTaxonomies(StartupProfile $profile, array $data): void
    {
        // Detach all existing taxonomies
        $profile->detachTaxonomies();

        // Attach categories
        if (!empty($data['categories'])) {
            $profile->attachTaxonomies($data['categories']);
        }

        // Attach industries
        if (!empty($data['industries'])) {
            $profile->attachTaxonomies($data['industries']);
        }

        // Attach technologies
        if (!empty($data['technologies'])) {
            $profile->attachTaxonomies($data['technologies']);
        }

        // Create keyword taxonomies if they don't exist
        if (!empty($data['keywords'])) {
            $keywordIds = [];
            foreach ($data['keywords'] as $keyword) {
                $taxonomy = Taxonomy::firstOrCreate([
                    'name' => $keyword,
                    'type' => 'keyword',
                ]);
                $keywordIds[] = $taxonomy->id;
            }
            $profile->attachTaxonomies($keywordIds);
        }
    }
}
