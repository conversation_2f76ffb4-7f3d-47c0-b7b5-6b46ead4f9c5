<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\InvestorProfile;
use App\Models\StartupProfile;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;

class InvestorDiscoveryController extends Controller
{
    /**
     * Get investors for startup discovery with advanced filtering
     */
    public function searchInvestors(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            
            if ($user->role !== 'startup') {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied. Only startups can search for investors.',
                ], 403);
            }

            // Validate search parameters
            $validated = $request->validate([
                'investment_min' => 'nullable|numeric|min:0',
                'investment_max' => 'nullable|numeric|min:0',
                'risk_tolerance' => 'nullable|string|in:low,medium,high',
                'investment_experience' => 'nullable|string|in:beginner,intermediate,experienced,expert',
                'categories' => 'nullable|array',
                'categories.*' => 'integer|exists:taxonomies,id',
                'industries' => 'nullable|array',
                'industries.*' => 'integer|exists:taxonomies,id',
                'location' => 'nullable|string|max:255',
                'esg_focused' => 'nullable|boolean',
                'page' => 'nullable|integer|min:1',
                'per_page' => 'nullable|integer|min:1|max:50',
                'sort_by' => 'nullable|string|in:relevance,budget_desc,budget_asc,experience,recent',
            ]);

            $page = $validated['page'] ?? 1;
            $perPage = $validated['per_page'] ?? 20;

            // Build the query
            $query = InvestorProfile::with([
                'user',
                'user.taxonomies' => function ($query) {
                    $query->whereIn('type', ['category', 'industry']);
                }
            ])
            ->whereHas('user', function ($q) {
                $q->where('role', 'investor')
                  ->whereNotNull('email_verified_at');
            })
            ->where('profile_completed', true);

            // Apply filters
            $this->applySearchFilters($query, $validated);

            // Apply sorting
            $this->applySorting($query, $validated['sort_by'] ?? 'relevance', $user);

            // Paginate results
            $investors = $query->paginate($perPage, ['*'], 'page', $page);

            // Calculate match scores for each investor
            $investorsWithScores = $investors->getCollection()->map(function ($investor) use ($user) {
                $matchScore = $this->calculateMatchScore($investor, $user);
                $investor->match_score = $matchScore;
                $investor->match_reasons = $this->getMatchReasons($investor, $user);
                return $investor;
            });

            $investors->setCollection($investorsWithScores);

            return response()->json([
                'success' => true,
                'data' => [
                    'investors' => $investors->items(),
                    'pagination' => [
                        'current_page' => $investors->currentPage(),
                        'last_page' => $investors->lastPage(),
                        'per_page' => $investors->perPage(),
                        'total' => $investors->total(),
                        'from' => $investors->firstItem(),
                        'to' => $investors->lastItem(),
                    ],
                    'filters_applied' => array_filter($validated),
                ],
            ]);

        } catch (\Exception $e) {
            \Log::error('Investor discovery search failed', [
                'user_id' => $request->user()->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to search investors.',
                'error' => app()->isLocal() ? $e->getMessage() : 'Internal server error',
            ], 500);
        }
    }

    /**
     * Get recommended investors based on startup profile
     */
    public function getRecommendations(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            
            if ($user->role !== 'startup') {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied. Only startups can get investor recommendations.',
                ], 403);
            }

            $startupProfile = StartupProfile::where('user_id', $user->id)->first();
            
            if (!$startupProfile || !$startupProfile->profile_completed) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please complete your startup profile to get investor recommendations.',
                ], 400);
            }

            $limit = $request->input('limit', 10);

            // Get investors with matching criteria
            $recommendedInvestors = $this->getMatchingInvestors($startupProfile, $limit);

            return response()->json([
                'success' => true,
                'data' => [
                    'recommendations' => $recommendedInvestors,
                    'startup_profile' => [
                        'funding_stage' => $startupProfile->funding_stage,
                        'funding_amount_sought' => $startupProfile->funding_amount_sought,
                        'categories' => $startupProfile->categories()->pluck('name'),
                        'industries' => $startupProfile->industryTags()->pluck('name'),
                    ],
                ],
            ]);

        } catch (\Exception $e) {
            \Log::error('Investor recommendations failed', [
                'user_id' => $request->user()->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get investor recommendations.',
                'error' => app()->isLocal() ? $e->getMessage() : 'Internal server error',
            ], 500);
        }
    }

    /**
     * Get available filter options for investor search
     */
    public function getFilterOptions(): JsonResponse
    {
        try {
            $categories = Taxonomy::where('type', 'category')->get(['id', 'name', 'description']);
            $industries = Taxonomy::where('type', 'industry')->get(['id', 'name', 'description']);

            // Get investment budget ranges from existing investor profiles
            $budgetRanges = InvestorProfile::select(
                DB::raw('MIN(investment_budget_min) as min_budget'),
                DB::raw('MAX(investment_budget_max) as max_budget')
            )->first();

            return response()->json([
                'success' => true,
                'data' => [
                    'categories' => $categories,
                    'industries' => $industries,
                    'risk_tolerance_options' => [
                        ['value' => 'low', 'label' => 'Low Risk'],
                        ['value' => 'medium', 'label' => 'Medium Risk'],
                        ['value' => 'high', 'label' => 'High Risk'],
                    ],
                    'experience_options' => [
                        ['value' => 'beginner', 'label' => 'Beginner'],
                        ['value' => 'intermediate', 'label' => 'Intermediate'],
                        ['value' => 'experienced', 'label' => 'Experienced'],
                        ['value' => 'expert', 'label' => 'Expert'],
                    ],
                    'budget_ranges' => [
                        'min' => $budgetRanges->min_budget ?? 0,
                        'max' => $budgetRanges->max_budget ?? 10000000,
                        'suggested_ranges' => [
                            ['min' => 0, 'max' => 50000, 'label' => 'Under $50K'],
                            ['min' => 50000, 'max' => 250000, 'label' => '$50K - $250K'],
                            ['min' => 250000, 'max' => 1000000, 'label' => '$250K - $1M'],
                            ['min' => 1000000, 'max' => 5000000, 'label' => '$1M - $5M'],
                            ['min' => 5000000, 'max' => null, 'label' => '$5M+'],
                        ],
                    ],
                    'sort_options' => [
                        ['value' => 'relevance', 'label' => 'Most Relevant'],
                        ['value' => 'budget_desc', 'label' => 'Highest Budget'],
                        ['value' => 'budget_asc', 'label' => 'Lowest Budget'],
                        ['value' => 'experience', 'label' => 'Most Experienced'],
                        ['value' => 'recent', 'label' => 'Recently Active'],
                    ],
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get filter options.',
                'error' => app()->isLocal() ? $e->getMessage() : 'Internal server error',
            ], 500);
        }
    }

    /**
     * Apply search filters to the query
     */
    private function applySearchFilters($query, array $filters): void
    {
        // Investment budget filter
        if (!empty($filters['investment_min']) || !empty($filters['investment_max'])) {
            $minAmount = $filters['investment_min'] ?? 0;
            $maxAmount = $filters['investment_max'] ?? PHP_INT_MAX;
            $query->withinBudget($minAmount, $maxAmount);
        }

        // Risk tolerance filter
        if (!empty($filters['risk_tolerance'])) {
            $query->byRiskTolerance($filters['risk_tolerance']);
        }

        // Investment experience filter
        if (!empty($filters['investment_experience'])) {
            $query->byExperience($filters['investment_experience']);
        }

        // Location filter
        if (!empty($filters['location'])) {
            $query->whereHas('user', function ($q) use ($filters) {
                $q->where('city', 'LIKE', '%' . $filters['location'] . '%')
                  ->orWhere('country', 'LIKE', '%' . $filters['location'] . '%');
            });
        }

        // Category filter
        if (!empty($filters['categories'])) {
            $query->whereHas('user.taxonomies', function ($q) use ($filters) {
                $q->whereIn('taxonomies.id', $filters['categories'])
                  ->where('taxonomies.type', 'category');
            });
        }

        // Industry filter
        if (!empty($filters['industries'])) {
            $query->whereHas('user.taxonomies', function ($q) use ($filters) {
                $q->whereIn('taxonomies.id', $filters['industries'])
                  ->where('taxonomies.type', 'industry');
            });
        }

        // ESG focused filter
        if (isset($filters['esg_focused']) && $filters['esg_focused']) {
            $query->whereJsonContains('investment_preferences', 'esg_focused');
        }
    }

    /**
     * Apply sorting to the query
     */
    private function applySorting($query, string $sortBy, User $user): void
    {
        switch ($sortBy) {
            case 'budget_desc':
                $query->orderBy('investment_budget_max', 'desc');
                break;
            case 'budget_asc':
                $query->orderBy('investment_budget_min', 'asc');
                break;
            case 'experience':
                $query->orderByRaw("FIELD(investment_experience, 'expert', 'experienced', 'intermediate', 'beginner')");
                break;
            case 'recent':
                $query->orderBy('updated_at', 'desc');
                break;
            case 'relevance':
            default:
                // For relevance, we'll sort by match score after calculating it
                $query->orderBy('created_at', 'desc');
                break;
        }
    }

    /**
     * Calculate match score between investor and startup
     */
    private function calculateMatchScore(InvestorProfile $investor, User $startup): int
    {
        $score = 0;
        $startupProfile = $startup->startupProfile;

        if (!$startupProfile) {
            return 0;
        }

        // Budget compatibility (40% of score)
        if ($this->isBudgetCompatible($investor, $startupProfile)) {
            $score += 40;
        }

        // Category overlap (30% of score)
        $categoryOverlap = $this->getCategoryOverlap($investor, $startup);
        $score += min(30, $categoryOverlap * 6); // Max 30 points for 5+ matching categories

        // Industry overlap (20% of score)
        $industryOverlap = $this->getIndustryOverlap($investor, $startup);
        $score += min(20, $industryOverlap * 4); // Max 20 points for 5+ matching industries

        // Experience bonus (10% of score)
        if ($investor->investment_experience === 'expert') {
            $score += 10;
        } elseif ($investor->investment_experience === 'experienced') {
            $score += 7;
        }

        return min(100, $score);
    }

    /**
     * Get reasons for the match
     */
    private function getMatchReasons(InvestorProfile $investor, User $startup): array
    {
        $reasons = [];
        $startupProfile = $startup->startupProfile;

        if (!$startupProfile) {
            return $reasons;
        }

        // Budget compatibility
        if ($this->isBudgetCompatible($investor, $startupProfile)) {
            $reasons[] = 'Investment budget matches your funding needs';
        }

        // Category overlap
        $categoryOverlap = $this->getCategoryOverlap($investor, $startup);
        if ($categoryOverlap > 0) {
            $reasons[] = "Shares {$categoryOverlap} category interest(s) with your startup";
        }

        // Industry overlap
        $industryOverlap = $this->getIndustryOverlap($investor, $startup);
        if ($industryOverlap > 0) {
            $reasons[] = "Active in {$industryOverlap} of your industry sectors";
        }

        // Experience
        if ($investor->investment_experience === 'expert') {
            $reasons[] = 'Expert-level investment experience';
        } elseif ($investor->investment_experience === 'experienced') {
            $reasons[] = 'Experienced investor with proven track record';
        }

        return $reasons;
    }

    /**
     * Check if investor budget is compatible with startup funding needs
     */
    private function isBudgetCompatible(InvestorProfile $investor, StartupProfile $startup): bool
    {
        $fundingNeeded = $startup->funding_amount_sought;
        
        return $fundingNeeded >= $investor->investment_budget_min && 
               $fundingNeeded <= $investor->investment_budget_max;
    }

    /**
     * Get category overlap count
     */
    private function getCategoryOverlap(InvestorProfile $investor, User $startup): int
    {
        $investorCategories = $investor->user->taxonomies()
            ->where('type', 'category')
            ->pluck('taxonomies.id')
            ->toArray();

        $startupCategories = $startup->startupProfile->categories()
            ->pluck('taxonomies.id')
            ->toArray();

        return count(array_intersect($investorCategories, $startupCategories));
    }

    /**
     * Get industry overlap count
     */
    private function getIndustryOverlap(InvestorProfile $investor, User $startup): int
    {
        $investorIndustries = $investor->user->taxonomies()
            ->where('type', 'industry')
            ->pluck('taxonomies.id')
            ->toArray();

        $startupIndustries = $startup->startupProfile->industryTags()
            ->pluck('taxonomies.id')
            ->toArray();

        return count(array_intersect($investorIndustries, $startupIndustries));
    }

    /**
     * Get matching investors for a startup profile
     */
    private function getMatchingInvestors(StartupProfile $startupProfile, int $limit): array
    {
        $investors = InvestorProfile::with([
            'user',
            'user.taxonomies'
        ])
        ->whereHas('user', function ($q) {
            $q->where('role', 'investor')
              ->whereNotNull('email_verified_at');
        })
        ->where('profile_completed', true)
        ->withinBudget($startupProfile->funding_amount_sought)
        ->get();

        // Calculate match scores and sort
        $investorsWithScores = $investors->map(function ($investor) use ($startupProfile) {
            $matchScore = $this->calculateMatchScore($investor, $startupProfile->user);
            $investor->match_score = $matchScore;
            $investor->match_reasons = $this->getMatchReasons($investor, $startupProfile->user);
            return $investor;
        })
        ->sortByDesc('match_score')
        ->take($limit)
        ->values();

        return $investorsWithScores->toArray();
    }
}
