<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\Refund;
use App\Models\Payment;
use App\Models\UserSubscription;
use App\Models\User;
use App\Services\RefundService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class AdminRefundController extends Controller
{
    protected RefundService $refundService;

    public function __construct(RefundService $refundService)
    {
        $this->refundService = $refundService;
    }

    /**
     * Get all refunds with admin filters
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Refund::with(['user', 'payment', 'userSubscription', 'invoice', 'processedBy']);

            // Apply filters
            if ($request->has('user_id')) {
                $query->where('user_id', $request->user_id);
            }

            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            if ($request->has('type')) {
                $query->where('type', $request->type);
            }

            if ($request->has('reason')) {
                $query->where('reason', $request->reason);
            }

            if ($request->has('processed_by')) {
                $query->where('processed_by', $request->processed_by);
            }

            // Date range filters
            if ($request->has('from_date')) {
                $query->whereDate('created_at', '>=', $request->from_date);
            }

            if ($request->has('to_date')) {
                $query->whereDate('created_at', '<=', $request->to_date);
            }

            // Amount range filters
            if ($request->has('min_amount')) {
                $query->where('amount', '>=', $request->min_amount * 100); // Convert to cents
            }

            if ($request->has('max_amount')) {
                $query->where('amount', '<=', $request->max_amount * 100); // Convert to cents
            }

            $refunds = $query->orderBy('created_at', 'desc')
                           ->paginate($request->get('per_page', 20));

            return response()->json([
                'status' => 'success',
                'data' => $refunds,
                'message' => 'Refunds retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving admin refunds', [
                'admin_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve refunds'
            ], 500);
        }
    }

    /**
     * Get refund details with audit trail
     */
    public function show(Refund $refund): JsonResponse
    {
        try {
            $refund->load(['user', 'payment', 'userSubscription', 'invoice', 'processedBy']);

            return response()->json([
                'status' => 'success',
                'data' => [
                    'refund' => $refund,
                    'audit_trail' => $this->refundService->getRefundAuditTrail($refund),
                ],
                'message' => 'Refund details retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving admin refund details', [
                'refund_id' => $refund->id,
                'admin_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve refund details'
            ], 500);
        }
    }

    /**
     * Process a refund for a payment (admin action)
     */
    public function processRefund(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'payment_id' => 'required|exists:payments,id',
                'type' => 'required|in:full,partial',
                'amount' => 'required_if:type,partial|numeric|min:0.01',
                'reason' => 'required|in:requested_by_customer,duplicate,fraudulent,subscription_cancellation,prorated_downgrade,prorated_cancellation,other',
                'description' => 'nullable|string|max:500',
                'internal_notes' => 'nullable|string|max:1000',
            ]);

            $admin = Auth::user();
            $payment = Payment::with(['user', 'userSubscription'])->findOrFail($validated['payment_id']);

            if ($payment->status !== 'succeeded') {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Payment is not eligible for refund'
                ], 400);
            }

            // Check existing refunds
            $existingRefunds = $payment->refunds()->successful()->sum('amount');
            $availableAmount = $payment->amount - $existingRefunds;

            if ($availableAmount <= 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Payment has already been fully refunded'
                ], 400);
            }

            if ($validated['type'] === 'partial') {
                $amountInCents = round($validated['amount'] * 100);
                
                if ($amountInCents > $availableAmount) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Refund amount exceeds available amount'
                    ], 400);
                }

                $refund = $this->refundService->processPartialRefund(
                    $payment,
                    $amountInCents,
                    $validated['reason'],
                    $validated['description'] ?? null,
                    $admin
                );
            } else {
                $refund = $this->refundService->processFullRefund(
                    $payment,
                    $validated['reason'],
                    $validated['description'] ?? null,
                    $admin
                );
            }

            // Add internal notes if provided
            if (!empty($validated['internal_notes'])) {
                $refund->update(['internal_notes' => $validated['internal_notes']]);
            }

            $refund->load(['user', 'payment', 'userSubscription', 'processedBy']);

            return response()->json([
                'status' => 'success',
                'data' => $refund,
                'message' => 'Refund processed successfully'
            ], 201);

        } catch (\Exception $e) {
            Log::error('Error processing admin refund', [
                'admin_id' => Auth::id(),
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to process refund: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process prorated refund for subscription cancellation
     */
    public function processProratedRefund(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'subscription_id' => 'required|exists:user_subscriptions,id',
                'cancellation_date' => 'nullable|date|after_or_equal:today',
                'reason' => 'required|in:subscription_cancellation,prorated_downgrade,prorated_cancellation',
                'description' => 'nullable|string|max:500',
                'internal_notes' => 'nullable|string|max:1000',
            ]);

            $admin = Auth::user();
            $subscription = UserSubscription::with(['user', 'payments'])
                                          ->findOrFail($validated['subscription_id']);

            if ($subscription->status !== 'active') {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Subscription is not active'
                ], 400);
            }

            $cancellationDate = $validated['cancellation_date'] 
                ? \Carbon\Carbon::parse($validated['cancellation_date'])
                : now();

            $refund = $this->refundService->processProratedRefund(
                $subscription,
                $validated['reason'],
                $cancellationDate,
                $admin
            );

            // Add internal notes if provided
            if (!empty($validated['internal_notes'])) {
                $refund->update(['internal_notes' => $validated['internal_notes']]);
            }

            $refund->load(['user', 'payment', 'userSubscription', 'processedBy']);

            return response()->json([
                'status' => 'success',
                'data' => $refund,
                'message' => 'Prorated refund processed successfully'
            ], 201);

        } catch (\Exception $e) {
            Log::error('Error processing admin prorated refund', [
                'admin_id' => Auth::id(),
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to process prorated refund: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get refund statistics for admin dashboard
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $query = Refund::query();

            // Apply date range if provided
            if ($request->has('from_date')) {
                $query->whereDate('created_at', '>=', $request->from_date);
            }

            if ($request->has('to_date')) {
                $query->whereDate('created_at', '<=', $request->to_date);
            }

            $statistics = [
                'total_refunds' => $query->count(),
                'total_amount' => $query->successful()->sum('amount'),
                'successful_refunds' => $query->successful()->count(),
                'pending_refunds' => $query->pending()->count(),
                'failed_refunds' => $query->where('status', 'failed')->count(),
                'by_type' => [
                    'full' => $query->where('type', 'full')->count(),
                    'partial' => $query->where('type', 'partial')->count(),
                    'prorated' => $query->where('type', 'prorated')->count(),
                ],
                'by_reason' => $query->groupBy('reason')
                                   ->selectRaw('reason, count(*) as count')
                                   ->pluck('count', 'reason'),
                'recent_refunds' => Refund::with(['user', 'processedBy'])
                                        ->orderBy('created_at', 'desc')
                                        ->limit(10)
                                        ->get(),
            ];

            // Add formatted amounts
            $statistics['formatted_total_amount'] = '$' . number_format($statistics['total_amount'] / 100, 2);

            return response()->json([
                'status' => 'success',
                'data' => $statistics,
                'message' => 'Refund statistics retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving admin refund statistics', [
                'admin_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve refund statistics'
            ], 500);
        }
    }
}
