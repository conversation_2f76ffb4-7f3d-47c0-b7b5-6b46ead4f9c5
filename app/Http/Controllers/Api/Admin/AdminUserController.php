<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserSubscription;
use App\Models\Payment;
use App\Models\Invoice;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class AdminUserController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth:sanctum', 'permission:manage users']);
    }

    /**
     * Display a listing of users with subscription and payment info.
     */
    public function index(Request $request): JsonResponse
    {
        $query = User::with([
            'activeSubscription.subscriptionProduct',
            'subscriptions' => function($q) {
                $q->latest()->limit(3);
            },
            'payments' => function($q) {
                $q->latest()->limit(5);
            },
            'media'
        ]);

        // Filter by account status
        if ($request->has('account_locked')) {
            $query->whereHas('accountStatus', function($q) use ($request) {
                $q->where('is_locked', $request->boolean('account_locked'));
            });
        }

        // Filter by subscription status
        if ($request->has('subscription_status')) {
            $status = $request->get('subscription_status');
            if ($status === 'active') {
                $query->whereHas('subscriptions', function($q) {
                    $q->where('status', 'active');
                });
            } elseif ($status === 'inactive') {
                $query->whereDoesntHave('subscriptions', function($q) {
                    $q->where('status', 'active');
                });
            }
        }

        // Search by name or email
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Pagination
        $perPage = $request->get('per_page', 15);
        $users = $query->paginate($perPage);

        // Add computed fields
        $users->getCollection()->transform(function ($user) {
            $user->total_payments = $user->payments()->where('status', 'succeeded')->sum('amount');
            $user->failed_payments_count = $user->payments()->where('status', 'failed')->count();
            $user->last_payment_date = $user->payments()->where('status', 'succeeded')->latest()->value('paid_at');
            return $user;
        });

        return $this->responseWithSuccess(
            'Users retrieved successfully',
            $users
        );
    }

    /**
     * Display detailed user information.
     */
    public function show(User $user): JsonResponse
    {
        $user->load([
            'subscriptions.subscriptionProduct',
            'payments.userSubscription.subscriptionProduct',
            'invoices',
            'paymentMethods',
            'refunds',
            'media'
        ]);

        // Add analytics
        $analytics = [
            'total_spent' => $user->payments()->where('status', 'succeeded')->sum('amount'),
            'total_refunded' => $user->refunds()->where('status', 'succeeded')->sum('amount'),
            'failed_payments_count' => $user->payments()->where('status', 'failed')->count(),
            'subscription_history_count' => $user->subscriptions()->count(),
            'account_age_days' => $user->created_at->diffInDays(now()),
            'last_login' => $user->last_login_at ?? null,
        ];

        return $this->responseWithSuccess(
            'User details retrieved successfully',
            [
                'user' => $user,
                'analytics' => $analytics
            ]
        );
    }

    /**
     * Lock or unlock user account.
     */
    public function updateAccountStatus(Request $request, User $user): JsonResponse
    {
        $validated = $request->validate([
            'action' => 'required|in:lock,unlock',
            'reason' => 'nullable|string|max:500',
        ]);

        try {
            if ($validated['action'] === 'lock') {
                $user->lockAccount();
                $message = 'User account locked successfully';
            } else {
                $user->unlockAccount();
                $message = 'User account unlocked successfully';
            }

            // Log the action (you might want to create an audit log)
            \Log::info("Admin action: {$validated['action']} user account", [
                'admin_id' => auth()->id(),
                'user_id' => $user->id,
                'reason' => $validated['reason'] ?? null,
            ]);

            return $this->responseWithSuccess(
                $message,
                $user->fresh()
            );

        } catch (\Exception $e) {
            return $this->responseWithError(
                'Failed to update account status: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get user subscription analytics.
     */
    public function subscriptionAnalytics(User $user): JsonResponse
    {
        $subscriptions = $user->subscriptions()->with('subscriptionProduct')->get();

        $analytics = [
            'total_subscriptions' => $subscriptions->count(),
            'active_subscriptions' => $subscriptions->where('status', 'active')->count(),
            'canceled_subscriptions' => $subscriptions->where('status', 'canceled')->count(),
            'subscription_revenue' => $subscriptions->sum('amount'),
            'current_mrr' => $subscriptions->where('status', 'active')->sum('amount'),
            'subscription_timeline' => $subscriptions->map(function($sub) {
                return [
                    'id' => $sub->id,
                    'product_name' => $sub->subscriptionProduct->name,
                    'status' => $sub->status,
                    'amount' => $sub->amount,
                    'started_at' => $sub->created_at,
                    'ended_at' => $sub->canceled_at,
                ];
            }),
        ];

        return $this->responseWithSuccess(
            'User subscription analytics retrieved successfully',
            $analytics
        );
    }

    /**
     * Get user payment history with details.
     */
    public function paymentHistory(Request $request, User $user): JsonResponse
    {
        $query = $user->payments()->with([
            'userSubscription.subscriptionProduct',
            'invoice',
            'refunds'
        ]);

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }

        // Filter by date range
        if ($request->has('from_date')) {
            $query->whereDate('created_at', '>=', $request->get('from_date'));
        }
        if ($request->has('to_date')) {
            $query->whereDate('created_at', '<=', $request->get('to_date'));
        }

        $payments = $query->orderBy('created_at', 'desc')->paginate(20);

        return $this->responseWithSuccess(
            'User payment history retrieved successfully',
            $payments
        );
    }
}
