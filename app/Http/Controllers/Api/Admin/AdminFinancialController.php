<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\Invoice;
use App\Models\Refund;
use App\Models\UserSubscription;
use App\Services\StripeService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Symfony\Component\HttpFoundation\Response;

class AdminFinancialController extends Controller
{
    protected StripeService $stripeService;

    public function __construct(StripeService $stripeService)
    {
        $this->stripeService = $stripeService;
        $this->middleware(['auth:sanctum', 'permission:view financial data']);
    }

    /**
     * Get financial overview and metrics.
     */
    public function overview(Request $request): JsonResponse
    {
        $period = $request->get('period', '30'); // days
        $fromDate = Carbon::now()->subDays($period);

        $overview = [
            'revenue' => [
                'total_revenue' => Payment::where('status', 'succeeded')->sum('amount'),
                'period_revenue' => Payment::where('status', 'succeeded')
                                         ->where('created_at', '>=', $fromDate)
                                         ->sum('amount'),
                'monthly_recurring_revenue' => $this->calculateMRR(),
                'annual_recurring_revenue' => $this->calculateARR(),
            ],

            'payments' => [
                'total_payments' => Payment::count(),
                'successful_payments' => Payment::where('status', 'succeeded')->count(),
                'failed_payments' => Payment::where('status', 'failed')->count(),
                'pending_payments' => Payment::where('status', 'pending')->count(),
                'period_payments' => Payment::where('created_at', '>=', $fromDate)->count(),
                'success_rate' => $this->calculatePaymentSuccessRate(),
            ],

            'refunds' => [
                'total_refunds' => Refund::where('status', 'succeeded')->sum('amount'),
                'period_refunds' => Refund::where('status', 'succeeded')
                                         ->where('created_at', '>=', $fromDate)
                                         ->sum('amount'),
                'refund_count' => Refund::where('status', 'succeeded')->count(),
                'refund_rate' => $this->calculateRefundRate(),
            ],

            'invoices' => [
                'total_invoices' => Invoice::count(),
                'paid_invoices' => Invoice::where('status', 'paid')->count(),
                'open_invoices' => Invoice::where('status', 'open')->count(),
                'overdue_invoices' => Invoice::where('status', 'open')
                                            ->where('due_date', '<', now())
                                            ->count(),
            ],

            'trends' => [
                'revenue_trend' => $this->getRevenueTrend($period),
                'payment_trend' => $this->getPaymentTrend($period),
                'refund_trend' => $this->getRefundTrend($period),
            ]
        ];

        return $this->responseWithSuccess(
            'Financial overview retrieved successfully',
            $overview
        );
    }

    /**
     * Get all payments with filtering.
     */
    public function payments(Request $request): JsonResponse
    {
        $query = Payment::with([
            'user:id,name,email',
            'userSubscription.subscriptionProduct:id,name',
            'invoice:id,invoice_number,total',
            'refunds'
        ]);

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }

        // Filter by payment type
        if ($request->has('type')) {
            $query->where('type', $request->get('type'));
        }

        // Filter by date range
        if ($request->has('from_date')) {
            $query->whereDate('created_at', '>=', $request->get('from_date'));
        }
        if ($request->has('to_date')) {
            $query->whereDate('created_at', '<=', $request->get('to_date'));
        }

        // Filter by amount range
        if ($request->has('min_amount')) {
            $query->where('amount', '>=', $request->get('min_amount'));
        }
        if ($request->has('max_amount')) {
            $query->where('amount', '<=', $request->get('max_amount'));
        }

        // Search by user
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->whereHas('user', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $payments = $query->orderBy('created_at', 'desc')
                         ->paginate($request->get('per_page', 20));

        return $this->responseWithSuccess(
            'Payments retrieved successfully',
            $payments
        );
    }

    /**
     * Get all invoices with filtering.
     */
    public function invoices(Request $request): JsonResponse
    {
        $query = Invoice::with([
            'user:id,name,email',
            'userSubscription.subscriptionProduct:id,name',
            'payment:id,status,amount'
        ]);

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }

        // Filter by date range
        if ($request->has('from_date')) {
            $query->whereDate('created_at', '>=', $request->get('from_date'));
        }
        if ($request->has('to_date')) {
            $query->whereDate('created_at', '<=', $request->get('to_date'));
        }

        // Search by user or invoice number
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        $invoices = $query->orderBy('created_at', 'desc')
                         ->paginate($request->get('per_page', 20));

        return $this->responseWithSuccess(
            'Invoices retrieved successfully',
            $invoices
        );
    }

    /**
     * Get all refunds with filtering.
     */
    public function refunds(Request $request): JsonResponse
    {
        $query = Refund::with([
            'user:id,name,email',
            'payment:id,amount,stripe_payment_intent_id',
            'userSubscription.subscriptionProduct:id,name'
        ]);

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }

        // Filter by reason
        if ($request->has('reason')) {
            $query->where('reason', $request->get('reason'));
        }

        // Filter by date range
        if ($request->has('from_date')) {
            $query->whereDate('created_at', '>=', $request->get('from_date'));
        }
        if ($request->has('to_date')) {
            $query->whereDate('created_at', '<=', $request->get('to_date'));
        }

        $refunds = $query->orderBy('created_at', 'desc')
                        ->paginate($request->get('per_page', 20));

        return $this->responseWithSuccess(
            'Refunds retrieved successfully',
            $refunds
        );
    }

    /**
     * Process a refund.
     */
    public function processRefund(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'payment_id' => 'required|exists:payments,id',
            'amount' => 'nullable|numeric|min:0',
            'reason' => 'required|in:duplicate,fraudulent,requested_by_customer,expired_uncaptured_charge',
            'description' => 'nullable|string|max:500',
        ]);

        try {
            $payment = Payment::findOrFail($validated['payment_id']);

            // Check if payment can be refunded
            if ($payment->status !== 'succeeded') {
                return $this->responseWithError(
                    'Only successful payments can be refunded',
                    Response::HTTP_BAD_REQUEST
                );
            }

            $refundAmount = $validated['amount'] ?? $payment->amount;

            // Check if refund amount is valid
            $totalRefunded = $payment->refunds()->where('status', 'succeeded')->sum('amount');
            if (($totalRefunded + $refundAmount) > $payment->amount) {
                return $this->responseWithError(
                    'Refund amount exceeds available amount',
                    Response::HTTP_BAD_REQUEST
                );
            }

            // Process refund in Stripe
            $stripeRefund = $this->stripeService->createRefund(
                $payment->stripe_payment_intent_id,
                $refundAmount * 100, // Convert to cents
                [
                    'reason' => $validated['reason'],
                    'admin_id' => auth()->id(),
                ]
            );

            // Create local refund record
            $refund = Refund::create([
                'user_id' => $payment->user_id,
                'payment_id' => $payment->id,
                'user_subscription_id' => $payment->user_subscription_id,
                'stripe_refund_id' => $stripeRefund->id,
                'amount' => $refundAmount,
                'currency' => $payment->currency,
                'status' => $stripeRefund->status,
                'reason' => $validated['reason'],
                'description' => $validated['description'],
                'processed_at' => now(),
            ]);

            \Log::info('Admin processed refund', [
                'admin_id' => auth()->id(),
                'refund_id' => $refund->id,
                'payment_id' => $payment->id,
                'amount' => $refundAmount,
                'reason' => $validated['reason'],
            ]);

            return $this->responseWithSuccess(
                'Refund processed successfully',
                $refund->load(['user', 'payment']),
                Response::HTTP_CREATED
            );

        } catch (\Exception $e) {
            return $this->responseWithError(
                'Failed to process refund: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Calculate Monthly Recurring Revenue.
     */
    private function calculateMRR(): float
    {
        return UserSubscription::where('status', 'active')
                              ->whereHas('subscriptionProduct', function($q) {
                                  $q->where('billing_cycle', 'monthly');
                              })
                              ->sum('amount');
    }

    /**
     * Calculate Annual Recurring Revenue.
     */
    private function calculateARR(): float
    {
        $monthlyMRR = $this->calculateMRR();
        $yearlyRevenue = UserSubscription::where('status', 'active')
                                        ->whereHas('subscriptionProduct', function($q) {
                                            $q->where('billing_cycle', 'yearly');
                                        })
                                        ->sum('amount');

        return ($monthlyMRR * 12) + $yearlyRevenue;
    }

    /**
     * Calculate payment success rate.
     */
    private function calculatePaymentSuccessRate(): float
    {
        $totalPayments = Payment::count();
        $successfulPayments = Payment::where('status', 'succeeded')->count();

        return $totalPayments > 0 ? ($successfulPayments / $totalPayments) * 100 : 0;
    }

    /**
     * Calculate refund rate.
     */
    private function calculateRefundRate(): float
    {
        $totalRevenue = Payment::where('status', 'succeeded')->sum('amount');
        $totalRefunds = Refund::where('status', 'succeeded')->sum('amount');

        return $totalRevenue > 0 ? ($totalRefunds / $totalRevenue) * 100 : 0;
    }

    /**
     * Get revenue trend for the given period.
     */
    private function getRevenueTrend(int $days): array
    {
        $startDate = Carbon::now()->subDays($days);

        return Payment::selectRaw('DATE(created_at) as date, SUM(amount) as revenue')
                     ->where('created_at', '>=', $startDate)
                     ->where('status', 'succeeded')
                     ->groupBy('date')
                     ->orderBy('date')
                     ->get()
                     ->toArray();
    }

    /**
     * Get payment trend for the given period.
     */
    private function getPaymentTrend(int $days): array
    {
        $startDate = Carbon::now()->subDays($days);

        return Payment::selectRaw('DATE(created_at) as date, COUNT(*) as count, status')
                     ->where('created_at', '>=', $startDate)
                     ->groupBy('date', 'status')
                     ->orderBy('date')
                     ->get()
                     ->groupBy('date')
                     ->toArray();
    }

    /**
     * Get refund trend for the given period.
     */
    private function getRefundTrend(int $days): array
    {
        $startDate = Carbon::now()->subDays($days);

        return Refund::selectRaw('DATE(created_at) as date, SUM(amount) as amount, COUNT(*) as count')
                    ->where('created_at', '>=', $startDate)
                    ->where('status', 'succeeded')
                    ->groupBy('date')
                    ->orderBy('date')
                    ->get()
                    ->toArray();
    }
}
