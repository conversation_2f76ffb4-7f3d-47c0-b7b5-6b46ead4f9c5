<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\UserSubscription;
use App\Models\SubscriptionProduct;
use App\Services\StripeService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Symfony\Component\HttpFoundation\Response;

class AdminSubscriptionController extends Controller
{
    protected StripeService $stripeService;

    public function __construct(StripeService $stripeService)
    {
        $this->stripeService = $stripeService;
        $this->middleware(['auth:sanctum', 'permission:manage subscriptions']);
    }

    /**
     * Display all subscriptions with filtering and analytics.
     */
    public function index(Request $request): JsonResponse
    {
        $query = UserSubscription::with([
            'user:id,name,email,account_locked',
            'subscriptionProduct:id,name,price,billing_cycle',
            'payments' => function($q) {
                $q->latest()->limit(3);
            }
        ]);

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }

        // Filter by product
        if ($request->has('product_id')) {
            $query->where('subscription_product_id', $request->get('product_id'));
        }

        // Filter by date range
        if ($request->has('from_date')) {
            $query->whereDate('created_at', '>=', $request->get('from_date'));
        }
        if ($request->has('to_date')) {
            $query->whereDate('created_at', '<=', $request->get('to_date'));
        }

        // Search by user
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->whereHas('user', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $subscriptions = $query->orderBy('created_at', 'desc')
                              ->paginate($request->get('per_page', 20));

        return $this->responseWithSuccess(
            'Subscriptions retrieved successfully',
            $subscriptions
        );
    }

    /**
     * Get subscription analytics and metrics.
     */
    public function analytics(Request $request): JsonResponse
    {
        $period = $request->get('period', '30'); // days
        $fromDate = Carbon::now()->subDays($period);

        $analytics = [
            'total_subscriptions' => UserSubscription::count(),
            'active_subscriptions' => UserSubscription::where('status', 'active')->count(),
            'canceled_subscriptions' => UserSubscription::where('status', 'canceled')->count(),
            'past_due_subscriptions' => UserSubscription::where('status', 'past_due')->count(),

            'new_subscriptions_period' => UserSubscription::where('created_at', '>=', $fromDate)->count(),
            'canceled_subscriptions_period' => UserSubscription::where('canceled_at', '>=', $fromDate)->count(),

            'monthly_recurring_revenue' => UserSubscription::where('status', 'active')
                ->whereHas('subscriptionProduct', function($q) {
                    $q->where('billing_cycle', 'monthly');
                })->sum('amount'),

            'yearly_recurring_revenue' => UserSubscription::where('status', 'active')
                ->whereHas('subscriptionProduct', function($q) {
                    $q->where('billing_cycle', 'yearly');
                })->sum('amount'),

            'churn_rate' => $this->calculateChurnRate($period),

            'subscription_by_product' => SubscriptionProduct::withCount([
                'userSubscriptions as active_count' => function($q) {
                    $q->where('status', 'active');
                },
                'userSubscriptions as total_count'
            ])->get(),

            'revenue_trend' => $this->getRevenueTrend($period),
        ];

        return $this->responseWithSuccess(
            'Subscription analytics retrieved successfully',
            $analytics
        );
    }

    /**
     * Display detailed subscription information.
     */
    public function show(UserSubscription $subscription): JsonResponse
    {
        $subscription->load([
            'user',
            'subscriptionProduct',
            'payments.refunds',
            'invoices'
        ]);

        // Get Stripe subscription details
        try {
            $stripeSubscription = $this->stripeService->getSubscription($subscription->stripe_subscription_id);
            $subscription->stripe_details = $stripeSubscription;
        } catch (\Exception $e) {
            $subscription->stripe_details = null;
        }

        return $this->responseWithSuccess(
            'Subscription details retrieved successfully',
            $subscription
        );
    }

    /**
     * Manually cancel a subscription.
     */
    public function cancel(Request $request, UserSubscription $subscription): JsonResponse
    {
        $validated = $request->validate([
            'reason' => 'nullable|string|max:500',
            'immediate' => 'boolean',
        ]);

        try {
            $immediate = $validated['immediate'] ?? false;

            if ($immediate) {
                // Cancel immediately
                $stripeSubscription = $this->stripeService->cancelSubscriptionImmediately(
                    $subscription->stripe_subscription_id
                );

                $subscription->update([
                    'status' => 'canceled',
                    'canceled_at' => now(),
                    'ends_at' => now(),
                ]);
            } else {
                // Cancel at period end
                $stripeSubscription = $this->stripeService->cancelSubscription(
                    $subscription->stripe_subscription_id
                );

                $subscription->update([
                    'status' => 'canceled',
                    'canceled_at' => now(),
                    'ends_at' => Carbon::createFromTimestamp($stripeSubscription->current_period_end),
                ]);
            }

            // Log the admin action
            \Log::info('Admin canceled subscription', [
                'admin_id' => auth()->id(),
                'subscription_id' => $subscription->id,
                'user_id' => $subscription->user_id,
                'reason' => $validated['reason'] ?? null,
                'immediate' => $immediate,
            ]);

            return $this->responseWithSuccess(
                'Subscription canceled successfully',
                $subscription->fresh()
            );

        } catch (\Exception $e) {
            return $this->responseWithError(
                'Failed to cancel subscription: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Pause a subscription.
     */
    public function pause(Request $request, UserSubscription $subscription): JsonResponse
    {
        $validated = $request->validate([
            'reason' => 'nullable|string|max:500',
            'resume_date' => 'nullable|date|after:today',
        ]);

        try {
            // Pause subscription in Stripe
            $stripeSubscription = $this->stripeService->pauseSubscription(
                $subscription->stripe_subscription_id,
                $validated['resume_date'] ?? null
            );

            $subscription->update([
                'status' => 'paused',
                'metadata' => array_merge($subscription->metadata ?? [], [
                    'paused_by_admin' => true,
                    'pause_reason' => $validated['reason'] ?? null,
                    'resume_date' => $validated['resume_date'] ?? null,
                ])
            ]);

            \Log::info('Admin paused subscription', [
                'admin_id' => auth()->id(),
                'subscription_id' => $subscription->id,
                'user_id' => $subscription->user_id,
                'reason' => $validated['reason'] ?? null,
            ]);

            return $this->responseWithSuccess(
                'Subscription paused successfully',
                $subscription->fresh()
            );

        } catch (\Exception $e) {
            return $this->responseWithError(
                'Failed to pause subscription: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Calculate churn rate for given period.
     */
    private function calculateChurnRate(int $days): float
    {
        $startDate = Carbon::now()->subDays($days);

        $activeAtStart = UserSubscription::where('created_at', '<', $startDate)
                                        ->where(function($q) use ($startDate) {
                                            $q->whereNull('canceled_at')
                                              ->orWhere('canceled_at', '>=', $startDate);
                                        })->count();

        $canceledInPeriod = UserSubscription::whereBetween('canceled_at', [$startDate, now()])->count();

        return $activeAtStart > 0 ? ($canceledInPeriod / $activeAtStart) * 100 : 0;
    }

    /**
     * Get revenue trend for the given period.
     */
    private function getRevenueTrend(int $days): array
    {
        $startDate = Carbon::now()->subDays($days);

        return UserSubscription::selectRaw('DATE(created_at) as date, SUM(amount) as revenue')
                              ->where('created_at', '>=', $startDate)
                              ->where('status', 'active')
                              ->groupBy('date')
                              ->orderBy('date')
                              ->get()
                              ->toArray();
    }
}
