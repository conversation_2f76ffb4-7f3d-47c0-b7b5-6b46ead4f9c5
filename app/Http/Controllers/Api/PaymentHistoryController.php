<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\Refund;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class PaymentHistoryController extends Controller
{
    /**
     * Get comprehensive payment history for the authenticated user
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            
            $query = Payment::where('user_id', $user->id)
                ->with(['userSubscription.subscriptionProduct', 'invoice', 'refunds']);

            // Apply filters
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            if ($request->filled('type')) {
                $query->where('type', $request->type);
            }

            if ($request->filled('from_date')) {
                $query->whereDate('created_at', '>=', $request->from_date);
            }

            if ($request->filled('to_date')) {
                $query->whereDate('created_at', '<=', $request->to_date);
            }

            if ($request->filled('amount_min')) {
                $query->where('amount', '>=', $request->amount_min);
            }

            if ($request->filled('amount_max')) {
                $query->where('amount', '<=', $request->amount_max);
            }

            // Apply sorting
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);

            // Paginate results
            $perPage = min($request->get('per_page', 15), 50);
            $payments = $query->paginate($perPage);

            return response()->json([
                'status' => 'success',
                'data' => $payments,
                'message' => 'Payment history retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving payment history', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve payment history'
            ], 500);
        }
    }

    /**
     * Get payment statistics for the authenticated user
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            
            // Get date range (default to last 12 months)
            $fromDate = $request->filled('from_date') 
                ? Carbon::parse($request->from_date) 
                : Carbon::now()->subMonths(12);
            
            $toDate = $request->filled('to_date') 
                ? Carbon::parse($request->to_date) 
                : Carbon::now();

            $baseQuery = Payment::where('user_id', $user->id)
                ->whereBetween('created_at', [$fromDate, $toDate]);

            // Total payments
            $totalPayments = (clone $baseQuery)->count();
            
            // Successful payments
            $successfulPayments = (clone $baseQuery)->where('status', 'succeeded')->count();
            
            // Failed payments
            $failedPayments = (clone $baseQuery)->where('status', 'failed')->count();
            
            // Total amount paid
            $totalAmountPaid = (clone $baseQuery)
                ->where('status', 'succeeded')
                ->sum('amount');
            
            // Total amount refunded
            $totalAmountRefunded = Refund::whereHas('payment', function ($query) use ($user, $fromDate, $toDate) {
                $query->where('user_id', $user->id)
                      ->whereBetween('created_at', [$fromDate, $toDate]);
            })->sum('amount');

            // Monthly breakdown
            $monthlyBreakdown = (clone $baseQuery)
                ->where('status', 'succeeded')
                ->selectRaw('
                    DATE_FORMAT(created_at, "%Y-%m") as month,
                    COUNT(*) as payment_count,
                    SUM(amount) as total_amount
                ')
                ->groupBy('month')
                ->orderBy('month')
                ->get();

            // Payment method breakdown
            $paymentMethodBreakdown = (clone $baseQuery)
                ->where('status', 'succeeded')
                ->selectRaw('
                    payment_method_type,
                    COUNT(*) as payment_count,
                    SUM(amount) as total_amount
                ')
                ->groupBy('payment_method_type')
                ->get();

            // Payment type breakdown
            $paymentTypeBreakdown = (clone $baseQuery)
                ->where('status', 'succeeded')
                ->selectRaw('
                    type,
                    COUNT(*) as payment_count,
                    SUM(amount) as total_amount
                ')
                ->groupBy('type')
                ->get();

            return response()->json([
                'status' => 'success',
                'data' => [
                    'summary' => [
                        'total_payments' => $totalPayments,
                        'successful_payments' => $successfulPayments,
                        'failed_payments' => $failedPayments,
                        'success_rate' => $totalPayments > 0 ? round(($successfulPayments / $totalPayments) * 100, 2) : 0,
                        'total_amount_paid' => round($totalAmountPaid, 2),
                        'total_amount_refunded' => round($totalAmountRefunded, 2),
                        'net_amount' => round($totalAmountPaid - $totalAmountRefunded, 2),
                    ],
                    'monthly_breakdown' => $monthlyBreakdown,
                    'payment_method_breakdown' => $paymentMethodBreakdown,
                    'payment_type_breakdown' => $paymentTypeBreakdown,
                    'date_range' => [
                        'from' => $fromDate->format('Y-m-d'),
                        'to' => $toDate->format('Y-m-d'),
                    ],
                ],
                'message' => 'Payment statistics retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving payment statistics', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve payment statistics'
            ], 500);
        }
    }

    /**
     * Get recent payment activity
     */
    public function recent(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $limit = min($request->get('limit', 10), 50);

            $recentPayments = Payment::where('user_id', $user->id)
                ->with(['userSubscription.subscriptionProduct', 'invoice'])
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->get();

            return response()->json([
                'status' => 'success',
                'data' => $recentPayments,
                'message' => 'Recent payment activity retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving recent payment activity', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve recent payment activity'
            ], 500);
        }
    }

    /**
     * Export payment history as CSV
     */
    public function export(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            
            $query = Payment::where('user_id', $user->id)
                ->with(['userSubscription.subscriptionProduct', 'invoice']);

            // Apply date filters
            if ($request->filled('from_date')) {
                $query->whereDate('created_at', '>=', $request->from_date);
            }

            if ($request->filled('to_date')) {
                $query->whereDate('created_at', '<=', $request->to_date);
            }

            $payments = $query->orderBy('created_at', 'desc')->get();

            // Generate CSV data
            $csvData = [];
            $csvData[] = [
                'Date',
                'Amount',
                'Currency',
                'Status',
                'Type',
                'Payment Method',
                'Subscription',
                'Description',
                'Stripe Payment Intent ID'
            ];

            foreach ($payments as $payment) {
                $csvData[] = [
                    $payment->created_at->format('Y-m-d H:i:s'),
                    $payment->amount,
                    strtoupper($payment->currency),
                    ucfirst($payment->status),
                    ucfirst($payment->type),
                    $payment->payment_method_type ?? 'N/A',
                    $payment->userSubscription?->subscriptionProduct?->name ?? 'N/A',
                    $payment->description ?? 'N/A',
                    $payment->stripe_payment_intent_id ?? 'N/A',
                ];
            }

            return response()->json([
                'status' => 'success',
                'data' => [
                    'csv_data' => $csvData,
                    'filename' => 'payment_history_' . now()->format('Y_m_d_H_i_s') . '.csv',
                    'total_records' => count($csvData) - 1, // Exclude header
                ],
                'message' => 'Payment history export data generated successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error exporting payment history', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to export payment history'
            ], 500);
        }
    }
}
