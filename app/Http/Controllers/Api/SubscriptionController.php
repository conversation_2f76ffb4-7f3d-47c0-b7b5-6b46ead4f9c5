<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Subscription\SubscribeRequest;
use App\Http\Requests\Api\Subscription\UpdateSubscriptionRequest;
use App\Models\SubscriptionProduct;
use App\Models\UserSubscription;
use App\Services\StripeService;
use App\Services\InvoiceService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;

class SubscriptionController extends Controller
{
    protected StripeService $stripeService;
    protected InvoiceService $invoiceService;

    public function __construct(StripeService $stripeService, InvoiceService $invoiceService)
    {
        $this->stripeService = $stripeService;
        $this->invoiceService = $invoiceService;
        $this->middleware(['auth:sanctum', 'check.account.locked']);
        $this->authorizeResource(UserSubscription::class, 'subscription');
    }

    /**
     * Display user's subscriptions.
     */
    public function index(): JsonResponse
    {
        $user = Auth::user();
        $subscriptions = $user->subscriptions()
            ->with('subscriptionProduct')
            ->orderBy('created_at', 'desc')
            ->get();

        return $this->responseWithSuccess(
            'User subscriptions retrieved successfully',
            $subscriptions
        );
    }

    /**
     * Create subscription - optimized to use existing payment methods when available.
     */
    public function store(SubscribeRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $user = Auth::user();
        $product = SubscriptionProduct::findOrFail($validated['subscription_product_id']);

        try {
            // Check if user already has an active subscription
            if ($user->hasActiveSubscription()) {
                return $this->responseWithError(
                    'User already has an active subscription',
                    Response::HTTP_CONFLICT
                );
            }

            // Create or get Stripe customer
            $customer = $this->stripeService->createOrGetCustomer($user);

            // Check if user has valid payment methods
            $paymentMethod = $this->getValidPaymentMethod($user, $validated);

            if ($paymentMethod) {
                // User has valid payment method - create subscription directly
                return $this->createSubscriptionWithExistingPaymentMethod(
                    $user,
                    $product,
                    $customer,
                    $paymentMethod
                );
            } else {
                // No valid payment method - redirect to Stripe Checkout
                return $this->createSubscriptionCheckoutSession(
                    $user,
                    $product,
                    $customer
                );
            }

        } catch (\Exception $e) {
            \Log::error('Failed to create subscription', [
                'user_id' => $user->id,
                'product_id' => $product->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->responseWithError(
                'Failed to create subscription: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Handle successful subscription checkout.
     */
    public function handleCheckoutSuccess(Request $request): JsonResponse
    {
        try {
            $sessionId = $request->get('session_id');

            if (!$sessionId) {
                return $this->responseWithError(
                    'No session ID provided',
                    Response::HTTP_BAD_REQUEST
                );
            }

            // Retrieve the checkout session
            $session = $this->stripeService->retrieveCheckoutSession($sessionId);

            if (!$session->subscription) {
                return $this->responseWithError(
                    'No subscription found in session',
                    Response::HTTP_BAD_REQUEST
                );
            }

            $user = Auth::user();
            $stripeSubscription = $this->stripeService->getSubscription($session->subscription);

            // Find the subscription product
            $productId = $session->metadata['subscription_product_id'] ?? null;
            if (!$productId) {
                return $this->responseWithError(
                    'Product ID not found in session metadata',
                    Response::HTTP_BAD_REQUEST
                );
            }

            $product = SubscriptionProduct::findOrFail($productId);

            // Create local subscription record with safe timestamp handling
            $subscriptionData = UserSubscription::createSubscriptionData(
                $stripeSubscription,
                $user->id,
                $product->id,
                $product->price
            );

            $subscription = UserSubscription::create($subscriptionData);

            // Generate invoice automatically
            $invoice = $this->invoiceService->generateInvoiceForSubscription($subscription);

            $responseData = $subscription->load('subscriptionProduct');
            if ($invoice) {
                $responseData->invoice = $invoice;
            }

            return $this->responseWithSuccess(
                'Subscription created successfully',
                $responseData
            );

        } catch (\Exception $e) {
            \Log::error('Failed to process subscription checkout success', [
                'user_id' => Auth::id(),
                'session_id' => $request->get('session_id'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->responseWithError(
                'Failed to process subscription: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Display the specified subscription.
     */
    public function show(UserSubscription $subscription): JsonResponse
    {
        // Authorization is handled by the policy
        $subscription->load(['subscriptionProduct', 'payments', 'invoices']);

        return $this->responseWithSuccess(
            'Subscription retrieved successfully',
            $subscription
        );
    }

    /**
     * Update subscription (upgrade/downgrade).
     */
    public function update(UpdateSubscriptionRequest $request, UserSubscription $subscription): JsonResponse
    {
        // Authorization is handled by the policy
        $validated = $request->validated();

        $newProduct = SubscriptionProduct::findOrFail($validated['subscription_product_id']);

        try {
            // Update subscription in Stripe
            $stripeSubscription = $this->stripeService->updateSubscription(
                $subscription->stripe_subscription_id,
                $newProduct->stripe_price_id
            );

            // Update local subscription
            $subscription->update([
                'subscription_product_id' => $newProduct->id,
                'status' => $stripeSubscription->status,
                'amount' => $newProduct->price,
            ]);

            return $this->responseWithSuccess(
                'Subscription updated successfully',
                $subscription->fresh()->load('subscriptionProduct')
            );

        } catch (\Exception $e) {
            return $this->responseWithError(
                'Failed to update subscription: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Cancel subscription (POST route).
     */
    public function cancel(UserSubscription $subscription): JsonResponse
    {
        // Authorization is handled by the policy
        $this->authorize('delete', $subscription);

        try {
            // Cancel subscription in Stripe
            $stripeSubscription = $this->stripeService->cancelSubscription(
                $subscription->stripe_subscription_id
            );

            // Update local subscription
            $subscription->update([
                'status' => 'canceled',
                'canceled_at' => now(),
                'ends_at' => UserSubscription::safeTimestamp($stripeSubscription->current_period_end),
            ]);

            \Log::info('User canceled subscription', [
                'user_id' => auth()->id(),
                'subscription_id' => $subscription->id,
                'stripe_subscription_id' => $subscription->stripe_subscription_id,
            ]);

            return $this->responseWithSuccess(
                'Subscription canceled successfully',
                $subscription->fresh()
            );

        } catch (\Exception $e) {
            \Log::error('Failed to cancel subscription', [
                'user_id' => auth()->id(),
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->responseWithError(
                'Failed to cancel subscription: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Cancel subscription with prorated refund.
     */
    public function cancelWithRefund(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'subscription_id' => 'required|exists:user_subscriptions,id',
            'cancellation_date' => 'required|date|after_or_equal:today',
            'reason' => 'required|string|in:requested_by_customer,subscription_cancellation,other',
            'description' => 'nullable|string|max:500',
            'process_refund' => 'boolean'
        ]);

        $subscription = UserSubscription::where('id', $validated['subscription_id'])
            ->where('user_id', auth()->id())
            ->firstOrFail();

        // Authorization is handled by the policy
        $this->authorize('delete', $subscription);

        try {
            DB::beginTransaction();

            // Cancel subscription in Stripe
            $stripeSubscription = $this->stripeService->cancelSubscription(
                $subscription->stripe_subscription_id,
                true // Cancel immediately
            );

            // Update local subscription
            $subscription->update([
                'status' => 'canceled',
                'canceled_at' => now(),
                'ends_at' => now(),
            ]);

            $refund = null;

            // Process prorated refund if requested
            if ($validated['process_refund'] ?? false) {
                $refundService = app(\App\Services\RefundService::class);
                $refund = $refundService->processProratedRefund(
                    $subscription,
                    $validated['reason'],
                    \Carbon\Carbon::parse($validated['cancellation_date']),
                    auth()->user()
                );
            }

            DB::commit();

            \Log::info('User canceled subscription with refund', [
                'user_id' => auth()->id(),
                'subscription_id' => $subscription->id,
                'stripe_subscription_id' => $subscription->stripe_subscription_id,
                'refund_id' => $refund?->id,
                'refund_amount' => $refund?->amount,
            ]);

            return $this->responseWithSuccess(
                'Subscription canceled successfully' . ($refund ? ' with prorated refund' : ''),
                [
                    'subscription' => $subscription->fresh(),
                    'refund' => $refund
                ]
            );

        } catch (\Exception $e) {
            DB::rollBack();

            \Log::error('Failed to cancel subscription with refund', [
                'user_id' => auth()->id(),
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->responseWithError(
                'Failed to cancel subscription: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Resume a canceled subscription.
     */
    public function resume(UserSubscription $subscription): JsonResponse
    {
        // Authorization is handled by the policy
        $this->authorize('update', $subscription);

        try {
            // Check if subscription can be resumed
            if ($subscription->status !== 'canceled') {
                return $this->responseWithError(
                    'Only canceled subscriptions can be resumed',
                    Response::HTTP_BAD_REQUEST
                );
            }

            // Resume subscription in Stripe
            $stripeSubscription = $this->stripeService->resumeSubscription(
                $subscription->stripe_subscription_id
            );

            // Update local subscription
            $subscription->update([
                'status' => $stripeSubscription->status,
                'canceled_at' => null,
                'ends_at' => null,
            ]);

            \Log::info('User resumed subscription', [
                'user_id' => auth()->id(),
                'subscription_id' => $subscription->id,
                'stripe_subscription_id' => $subscription->stripe_subscription_id,
            ]);

            return $this->responseWithSuccess(
                'Subscription resumed successfully',
                $subscription->fresh()
            );

        } catch (\Exception $e) {
            \Log::error('Failed to resume subscription', [
                'user_id' => auth()->id(),
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->responseWithError(
                'Failed to resume subscription: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Cancel subscription (DELETE route).
     */
    public function destroy(UserSubscription $subscription): JsonResponse
    {
        // Use the same logic as cancel method
        return $this->cancel($subscription);
    }

    /**
     * Get valid payment method for the user
     */
    private function getValidPaymentMethod($user, array $validated)
    {
        // If specific payment method ID is provided, use it
        if (isset($validated['payment_method_id'])) {
            $paymentMethod = $user->paymentMethods()
                ->where('id', $validated['payment_method_id'])
                ->where('is_active', true)
                ->first();

            if ($paymentMethod && !$paymentMethod->isExpired()) {
                return $paymentMethod;
            }
        }

        // Otherwise, try to get the default payment method
        $defaultPaymentMethod = $user->paymentMethods()
            ->where('is_default', true)
            ->where('is_active', true)
            ->first();

        if ($defaultPaymentMethod && !$defaultPaymentMethod->isExpired()) {
            return $defaultPaymentMethod;
        }

        // If no default, get any valid payment method
        $anyValidPaymentMethod = $user->paymentMethods()
            ->where('is_active', true)
            ->where('type', 'card')
            ->get()
            ->filter(function ($pm) {
                return !$pm->isExpired();
            })
            ->first();

        return $anyValidPaymentMethod;
    }

    /**
     * Create subscription using existing payment method
     */
    private function createSubscriptionWithExistingPaymentMethod($user, $product, $customer, $paymentMethod)
    {
        try {
            // Create subscription directly with existing payment method
            $subscription = $this->stripeService->createSubscription(
                $customer->id,
                $product->stripe_price_id,
                [
                    'default_payment_method' => $paymentMethod->stripe_payment_method_id,
                    'metadata' => [
                        'user_id' => $user->id,
                        'subscription_product_id' => $product->id,
                        'payment_method_id' => $paymentMethod->id,
                        'created_with_existing_payment_method' => 'true',
                    ],
                ]
            );

            // Create local subscription record
            $subscriptionData = UserSubscription::createSubscriptionData(
                $subscription,
                $user->id,
                $product->id,
                $product->price
            );

            $userSubscription = UserSubscription::create($subscriptionData);

            // Generate invoice automatically
            $invoice = $this->invoiceService->generateInvoiceForSubscription($userSubscription);

            $responseData = $userSubscription->load('subscriptionProduct');
            if ($invoice) {
                $responseData->invoice = $invoice;
            }

            \Log::info('Subscription created with existing payment method', [
                'user_id' => $user->id,
                'subscription_id' => $userSubscription->id,
                'payment_method_id' => $paymentMethod->id,
                'stripe_subscription_id' => $subscription->id
            ]);

            return $this->responseWithSuccess(
                'Subscription created successfully using existing payment method',
                $responseData,
                Response::HTTP_CREATED
            );

        } catch (\Exception $e) {
            \Log::error('Failed to create subscription with existing payment method', [
                'user_id' => $user->id,
                'product_id' => $product->id,
                'payment_method_id' => $paymentMethod->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // If direct subscription creation fails, fall back to checkout session
            \Log::info('Falling back to checkout session due to payment method failure', [
                'user_id' => $user->id,
                'payment_method_id' => $paymentMethod->id
            ]);

            return $this->createSubscriptionCheckoutSession($user, $product, $customer);
        }
    }

    /**
     * Create subscription checkout session (fallback method)
     */
    private function createSubscriptionCheckoutSession($user, $product, $customer)
    {
        // Create success and cancel URLs
        $successUrl = config('app.frontend_url', 'http://localhost:5176') . '/app/subscription/success?session_id={CHECKOUT_SESSION_ID}';
        $cancelUrl = config('app.frontend_url', 'http://localhost:5176') . '/app/subscription/plans?canceled=true';

        // Create subscription checkout session
        $session = $this->stripeService->createSubscriptionCheckoutSession(
            $customer->id,
            $product->stripe_price_id,
            $successUrl,
            $cancelUrl,
            [
                'user_id' => $user->id,
                'subscription_product_id' => $product->id,
                'fallback_to_checkout' => 'true',
            ]
        );

        \Log::info('Created checkout session for subscription', [
            'user_id' => $user->id,
            'product_id' => $product->id,
            'session_id' => $session->id
        ]);

        return $this->responseWithSuccess(
            'Checkout session created successfully',
            [
                'checkout_url' => $session->url,
                'session_id' => $session->id,
                'requires_redirect' => true,
            ],
            Response::HTTP_CREATED
        );
    }
}
