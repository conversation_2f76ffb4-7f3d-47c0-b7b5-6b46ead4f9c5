<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\InterestRequest;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class InterestRequestController extends Controller
{
    public function __construct()
    {
        // Authentication is handled by route middleware
        // Role-based authorization is handled by route middleware
    }

    /**
     * Display a listing of interest requests
     */
    public function index(Request $request): JsonResponse
    {
        $user = auth()->user();
        $query = InterestRequest::with(['requester', 'target', 'approver']);

        // Filter based on user role
        if ($user->isAnalyst()) {
            // Analysts can see all requests
            if ($request->has('status')) {
                $query->where('status', $request->status);
            }
        } else {
            // Users can only see their own requests
            $query->where(function ($q) use ($user) {
                $q->where('requester_id', $user->id)
                  ->orWhere('target_id', $user->id);
            });
        }

        // Filter by type
        if ($request->has('type')) {
            $query->byType($request->type);
        }

        $requests = $query->orderBy('created_at', 'desc')
            ->paginate($request->per_page ?? 15);

        return response()->json([
            'success' => true,
            'data' => $requests,
        ]);
    }

    /**
     * Store a new interest request
     */
    public function store(Request $request): JsonResponse
    {
        $user = auth()->user();

        $validated = $request->validate([
            'target_id' => 'required|exists:users,id|different:' . $user->id,
            'type' => ['required', Rule::in(['investment_interest', 'funding_request'])],
            'message' => 'nullable|string|max:1000',
            'proposed_amount' => 'nullable|numeric|min:0',
            'terms' => 'nullable|array',
        ]);

        // Validate request type based on user role
        if ($user->isInvestor() && $validated['type'] !== 'investment_interest') {
            return response()->json([
                'success' => false,
                'message' => 'Investors can only submit investment interest requests',
            ], 422);
        }

        if ($user->isStartup() && $validated['type'] !== 'funding_request') {
            return response()->json([
                'success' => false,
                'message' => 'Startups can only submit funding requests',
            ], 422);
        }

        // Check if request already exists
        $existingRequest = InterestRequest::where([
            'requester_id' => $user->id,
            'target_id' => $validated['target_id'],
            'type' => $validated['type'],
        ])->first();

        if ($existingRequest) {
            return response()->json([
                'success' => false,
                'message' => 'You have already submitted this type of request to this user',
            ], 422);
        }

        // Validate target user role
        $targetUser = User::find($validated['target_id']);
        if ($validated['type'] === 'investment_interest' && !$targetUser->isStartup()) {
            return response()->json([
                'success' => false,
                'message' => 'Investment interest can only be sent to startups',
            ], 422);
        }

        if ($validated['type'] === 'funding_request' && !$targetUser->isInvestor()) {
            return response()->json([
                'success' => false,
                'message' => 'Funding requests can only be sent to investors',
            ], 422);
        }

        $interestRequest = InterestRequest::create([
            'requester_id' => $user->id,
            'target_id' => $validated['target_id'],
            'type' => $validated['type'],
            'message' => $validated['message'] ?? null,
            'proposed_amount' => $validated['proposed_amount'] ?? null,
            'terms' => $validated['terms'] ?? null,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Interest request submitted successfully',
            'data' => $interestRequest->load(['requester', 'target']),
        ], 201);
    }

    /**
     * Display the specified interest request
     */
    public function show(InterestRequest $interestRequest): JsonResponse
    {
        $user = auth()->user();

        // Check if user can view this request
        if (!$user->isAnalyst() &&
            $interestRequest->requester_id !== $user->id &&
            $interestRequest->target_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to view this request',
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $interestRequest->load(['requester', 'target', 'approver']),
        ]);
    }

    /**
     * Approve an interest request (analysts only)
     */
    public function approve(InterestRequest $interestRequest): JsonResponse
    {
        $user = auth()->user();

        if (!$user->isAnalyst()) {
            return response()->json([
                'success' => false,
                'message' => 'Only analysts can approve requests',
            ], 403);
        }

        if (!$interestRequest->canBeApproved()) {
            return response()->json([
                'success' => false,
                'message' => 'Request cannot be approved',
            ], 422);
        }

        $interestRequest->approve($user);

        return response()->json([
            'success' => true,
            'message' => 'Interest request approved successfully',
            'data' => $interestRequest->load(['requester', 'target', 'approver']),
        ]);
    }

    /**
     * Reject an interest request (analysts only)
     */
    public function reject(Request $request, InterestRequest $interestRequest): JsonResponse
    {
        $user = auth()->user();

        if (!$user->isAnalyst()) {
            return response()->json([
                'success' => false,
                'message' => 'Only analysts can reject requests',
            ], 403);
        }

        if (!$interestRequest->canBeRejected()) {
            return response()->json([
                'success' => false,
                'message' => 'Request cannot be rejected',
            ], 422);
        }

        $validated = $request->validate([
            'reason' => 'nullable|string|max:500',
        ]);

        $interestRequest->reject($validated['reason']);

        return response()->json([
            'success' => true,
            'message' => 'Interest request rejected successfully',
            'data' => $interestRequest->load(['requester', 'target']),
        ]);
    }

    /**
     * Get request statistics (for analysts)
     */
    public function statistics(): JsonResponse
    {
        $user = auth()->user();

        if (!$user->isAnalyst()) {
            return response()->json([
                'success' => false,
                'message' => 'Only analysts can view statistics',
            ], 403);
        }

        $stats = [
            'total_requests' => InterestRequest::count(),
            'pending_requests' => InterestRequest::pending()->count(),
            'approved_requests' => InterestRequest::approved()->count(),
            'rejected_requests' => InterestRequest::rejected()->count(),
            'investment_interests' => InterestRequest::investmentInterest()->count(),
            'funding_requests' => InterestRequest::fundingRequest()->count(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }
}
