<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\EsgQuestion;
use App\Models\EsgResponse;
use App\Models\StartupProfile;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class EsgController extends Controller
{
    public function __construct()
    {
        // Authentication is handled by route middleware
        // Role-based authorization is handled by route middleware
    }

    /**
     * Get ESG questions for the questionnaire
     */
    public function questions(): JsonResponse
    {
        $questions = EsgQuestion::active()
            ->ordered()
            ->get()
            ->groupBy('category');

        return response()->json([
            'success' => true,
            'data' => $questions,
        ]);
    }

    /**
     * Submit ESG questionnaire responses
     */
    public function submitResponses(Request $request): JsonResponse
    {
        $user = auth()->user();

        if (!$user->isStartup()) {
            return response()->json([
                'success' => false,
                'message' => 'Only startups can submit ESG responses',
            ], 403);
        }

        $startupProfile = $user->startupProfile;
        if (!$startupProfile) {
            return response()->json([
                'success' => false,
                'message' => 'Startup profile not found. Please complete your profile first.',
            ], 404);
        }

        $validated = $request->validate([
            'responses' => 'required|array',
            'responses.*.question_id' => 'required|exists:esg_questions,id',
            'responses.*.response_value' => 'required|string',
        ]);

        try {
            DB::beginTransaction();

            // Delete existing responses
            $startupProfile->esgResponses()->delete();

            // Create new responses
            foreach ($validated['responses'] as $responseData) {
                EsgResponse::create([
                    'startup_profile_id' => $startupProfile->id,
                    'esg_question_id' => $responseData['question_id'],
                    'response_value' => $responseData['response_value'],
                ]);
            }

            // Calculate ESG score
            $startupProfile->calculateEsgScore();

            // Mark ESG as completed
            $startupProfile->update(['esg_completed' => true]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'ESG questionnaire submitted successfully',
                'data' => [
                    'esg_score' => $startupProfile->fresh()->esg_score,
                    'esg_breakdown' => $startupProfile->fresh()->esg_breakdown,
                ],
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to submit ESG questionnaire',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get current user's ESG responses
     */
    public function responses(): JsonResponse
    {
        $user = auth()->user();
        $startupProfile = $user->startupProfile;

        if (!$startupProfile) {
            return response()->json([
                'success' => false,
                'message' => 'Startup profile not found',
            ], 404);
        }

        $responses = $startupProfile->esgResponses()
            ->with('esgQuestion')
            ->get()
            ->groupBy('esgQuestion.category');

        return response()->json([
            'success' => true,
            'data' => [
                'responses' => $responses,
                'esg_score' => $startupProfile->esg_score,
                'esg_breakdown' => $startupProfile->esg_breakdown,
                'esg_completed' => $startupProfile->esg_completed,
            ],
        ]);
    }

    /**
     * Get ESG analytics (for analysts)
     */
    public function analytics(): JsonResponse
    {
        $user = auth()->user();

        if (!$user->isAnalyst()) {
            return response()->json([
                'success' => false,
                'message' => 'Only analysts can view ESG analytics',
            ], 403);
        }

        $analytics = [
            'total_completed' => StartupProfile::esgCompleted()->count(),
            'average_score' => StartupProfile::esgCompleted()->avg('esg_score'),
            'score_distribution' => [
                'excellent' => StartupProfile::byEsgScore(80, 100)->count(),
                'good' => StartupProfile::byEsgScore(60, 79.99)->count(),
                'fair' => StartupProfile::byEsgScore(40, 59.99)->count(),
                'poor' => StartupProfile::byEsgScore(0, 39.99)->count(),
            ],
            'category_averages' => $this->getCategoryAverages(),
        ];

        return response()->json([
            'success' => true,
            'data' => $analytics,
        ]);
    }

    /**
     * Calculate category averages
     */
    private function getCategoryAverages(): array
    {
        $profiles = StartupProfile::esgCompleted()
            ->whereNotNull('esg_breakdown')
            ->get();

        $categoryTotals = [
            'environmental' => 0,
            'social' => 0,
            'governance' => 0,
        ];

        $count = $profiles->count();

        foreach ($profiles as $profile) {
            $breakdown = $profile->esg_breakdown;
            foreach ($categoryTotals as $category => $total) {
                if (isset($breakdown[$category])) {
                    $categoryTotals[$category] += $breakdown[$category];
                }
            }
        }

        return $count > 0 ? array_map(function ($total) use ($count) {
            return round($total / $count, 2);
        }, $categoryTotals) : $categoryTotals;
    }
}
