<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class NotificationController extends Controller
{
    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Get user's notifications
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $perPage = min($request->get('per_page', 15), 50);
            $unreadOnly = $request->boolean('unread_only', false);

            $query = $unreadOnly 
                ? $user->unreadNotifications() 
                : $user->notifications();

            // Apply type filter
            if ($request->filled('type')) {
                $query->where('type', $request->type);
            }

            // Apply date filters
            if ($request->filled('from_date')) {
                $query->whereDate('created_at', '>=', $request->from_date);
            }

            if ($request->filled('to_date')) {
                $query->whereDate('created_at', '<=', $request->to_date);
            }

            $notifications = $query->orderBy('created_at', 'desc')
                ->paginate($perPage);

            return response()->json([
                'status' => 'success',
                'data' => $notifications,
                'message' => 'Notifications retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving notifications', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve notifications'
            ], 500);
        }
    }

    /**
     * Get unread notifications count
     */
    public function unreadCount(): JsonResponse
    {
        try {
            $user = Auth::user();
            $count = $user->unreadNotifications()->count();

            return response()->json([
                'status' => 'success',
                'data' => [
                    'unread_count' => $count
                ],
                'message' => 'Unread count retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving unread notifications count', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve unread count'
            ], 500);
        }
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(Request $request, string $notificationId): JsonResponse
    {
        try {
            $user = Auth::user();
            $success = $this->notificationService->markNotificationAsRead($user, $notificationId);

            if ($success) {
                return response()->json([
                    'status' => 'success',
                    'message' => 'Notification marked as read'
                ]);
            }

            return response()->json([
                'status' => 'error',
                'message' => 'Notification not found'
            ], 404);

        } catch (\Exception $e) {
            Log::error('Error marking notification as read', [
                'user_id' => Auth::id(),
                'notification_id' => $notificationId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to mark notification as read'
            ], 500);
        }
    }

    /**
     * Mark all notifications as read
     */
    public function markAllAsRead(): JsonResponse
    {
        try {
            $user = Auth::user();
            $success = $this->notificationService->markAllNotificationsAsRead($user);

            if ($success) {
                return response()->json([
                    'status' => 'success',
                    'message' => 'All notifications marked as read'
                ]);
            }

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to mark notifications as read'
            ], 500);

        } catch (\Exception $e) {
            Log::error('Error marking all notifications as read', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to mark all notifications as read'
            ], 500);
        }
    }

    /**
     * Delete a notification
     */
    public function destroy(string $notificationId): JsonResponse
    {
        try {
            $user = Auth::user();
            $notification = $user->notifications()->find($notificationId);

            if (!$notification) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Notification not found'
                ], 404);
            }

            $notification->delete();

            return response()->json([
                'status' => 'success',
                'message' => 'Notification deleted successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error deleting notification', [
                'user_id' => Auth::id(),
                'notification_id' => $notificationId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to delete notification'
            ], 500);
        }
    }

    /**
     * Get notification statistics
     */
    public function statistics(): JsonResponse
    {
        try {
            $user = Auth::user();

            $totalNotifications = $user->notifications()->count();
            $unreadNotifications = $user->unreadNotifications()->count();
            $readNotifications = $totalNotifications - $unreadNotifications;

            // Get notifications by type
            $notificationsByType = $user->notifications()
                ->selectRaw('
                    SUBSTRING_INDEX(type, "\\\\", -1) as notification_type,
                    COUNT(*) as count
                ')
                ->groupBy('type')
                ->get()
                ->mapWithKeys(function ($item) {
                    return [str_replace('Notification', '', $item->notification_type) => $item->count];
                });

            // Get recent activity (last 7 days)
            $recentActivity = $user->notifications()
                ->where('created_at', '>=', now()->subDays(7))
                ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->groupBy('date')
                ->orderBy('date')
                ->get();

            return response()->json([
                'status' => 'success',
                'data' => [
                    'summary' => [
                        'total_notifications' => $totalNotifications,
                        'unread_notifications' => $unreadNotifications,
                        'read_notifications' => $readNotifications,
                        'read_percentage' => $totalNotifications > 0 ? round(($readNotifications / $totalNotifications) * 100, 2) : 0,
                    ],
                    'notifications_by_type' => $notificationsByType,
                    'recent_activity' => $recentActivity,
                ],
                'message' => 'Notification statistics retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving notification statistics', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve notification statistics'
            ], 500);
        }
    }
}
