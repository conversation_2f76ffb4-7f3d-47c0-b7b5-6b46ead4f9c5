<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\InvestorProfile;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class InvestorProfileController extends Controller
{
    public function __construct()
    {
        // Authentication is handled by route middleware
        // Role-based authorization is handled by route middleware
    }

    /**
     * Display a listing of investor profiles (for analysts)
     */
    public function index(Request $request): JsonResponse
    {
        $query = InvestorProfile::with(['user', 'user.categories'])
            ->completed();

        // Apply filters
        if ($request->has('risk_tolerance')) {
            $query->byRiskTolerance($request->risk_tolerance);
        }

        if ($request->has('experience')) {
            $query->byExperience($request->experience);
        }

        if ($request->has('min_budget') || $request->has('max_budget')) {
            $query->withinBudget(
                $request->min_budget ?? 0,
                $request->max_budget
            );
        }

        $profiles = $query->paginate($request->per_page ?? 15);

        return response()->json([
            'success' => true,
            'data' => $profiles,
        ]);
    }

    /**
     * Store or update investor profile
     */
    public function store(Request $request): JsonResponse
    {
        $user = auth()->user();

        if (!$user->isInvestor()) {
            return response()->json([
                'success' => false,
                'message' => 'Only investors can create investor profiles',
            ], 403);
        }

        $validated = $request->validate([
            'investment_budget_min' => 'nullable|numeric|min:0',
            'investment_budget_max' => 'nullable|numeric|min:0|gte:investment_budget_min',
            'risk_tolerance' => ['nullable', Rule::in(['low', 'medium', 'high'])],
            'investment_experience' => ['nullable', Rule::in(['beginner', 'intermediate', 'experienced', 'expert'])],
            'bio' => 'nullable|string|max:1000',
            'website' => 'nullable|url|max:255',
            'linkedin' => 'nullable|url|max:255',
            'investment_preferences' => 'nullable|array',
            'categories' => 'nullable|array',
            'categories.*' => 'exists:categories,id',
        ]);

        // Remove categories from profile data as it's handled separately
        $profileData = $validated;
        unset($profileData['categories']);

        $profile = InvestorProfile::updateOrCreate(
            ['user_id' => $user->id],
            array_merge($profileData, [
                'profile_completed' => $this->isProfileComplete($profileData),
            ])
        );

        // Sync categories if provided
        if (isset($validated['categories'])) {
            $user->categories()->sync($validated['categories']);
        }

        return response()->json([
            'success' => true,
            'message' => 'Investor profile saved successfully',
            'data' => $profile->load(['user', 'user.categories']),
        ]);
    }

    /**
     * Display the current user's investor profile
     */
    public function show(): JsonResponse
    {
        $user = auth()->user();
        $profile = $user->investorProfile()->with(['user', 'user.categories'])->first();

        if (!$profile) {
            return response()->json([
                'success' => false,
                'message' => 'Investor profile not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $profile,
        ]);
    }

    /**
     * Update investor profile
     */
    public function update(Request $request): JsonResponse
    {
        return $this->store($request);
    }

    /**
     * Remove investor profile
     */
    public function destroy(): JsonResponse
    {
        $user = auth()->user();
        $profile = $user->investorProfile;

        if (!$profile) {
            return response()->json([
                'success' => false,
                'message' => 'Investor profile not found',
            ], 404);
        }

        $profile->delete();
        $user->categories()->detach();

        return response()->json([
            'success' => true,
            'message' => 'Investor profile deleted successfully',
        ]);
    }

    /**
     * Check if profile is complete
     */
    private function isProfileComplete(array $data): bool
    {
        $requiredFields = [
            'investment_budget_min',
            'investment_budget_max',
            'risk_tolerance',
            'investment_experience',
            'bio',
        ];

        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                return false;
            }
        }

        return true;
    }
}
