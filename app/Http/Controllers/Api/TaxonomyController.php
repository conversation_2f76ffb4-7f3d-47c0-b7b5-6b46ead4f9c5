<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class TaxonomyController extends Controller
{
    /**
     * Get taxonomies by type with optional search
     */
    public function index(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'type' => ['required', Rule::in(['keyword', 'tag', 'category', 'brand', 'type', 'model', 'industry', 'technology', 'market', 'stage'])],
            'search' => ['nullable', 'string', 'max:255'],
            'limit' => ['nullable', 'integer', 'min:1', 'max:100'],
        ]);

        $query = Taxonomy::where('type', $validated['type']);

        if (!empty($validated['search'])) {
            $query->where(function ($q) use ($validated) {
                $q->where('name', 'like', '%' . $validated['search'] . '%')
                  ->orWhere('description', 'like', '%' . $validated['search'] . '%');
            });
        }

        $taxonomies = $query->orderBy('sort_order')
            ->orderBy('name')
            ->limit($validated['limit'] ?? 50)
            ->get()
            ->map(function ($taxonomy) {
                return [
                    'id' => $taxonomy->id,
                    'name' => $taxonomy->name,
                    'slug' => $taxonomy->slug,
                    'description' => $taxonomy->description,
                    'type' => $taxonomy->type,
                    'sort_order' => $taxonomy->sort_order,
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $taxonomies,
            'meta' => [
                'type' => $validated['type'],
                'search' => $validated['search'] ?? null,
                'count' => $taxonomies->count(),
            ],
        ]);
    }

    /**
     * Get startup profile's selected taxonomies by type
     */
    public function getUserTaxonomies(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'type' => ['required', Rule::in(['keyword', 'tag', 'category', 'brand', 'type', 'model', 'industry', 'technology', 'market', 'stage'])],
        ]);

        $user = $request->user();

        // Only startup users have taxonomy relationships through their profile
        if ($user->role !== 'startup') {
            return response()->json([
                'success' => false,
                'message' => 'Access denied. Startup role required.',
            ], 403);
        }

        $startupProfile = $user->startupProfile;
        if (!$startupProfile) {
            return response()->json([
                'success' => false,
                'message' => 'Startup profile not found. Please create your profile first.',
            ], 404);
        }

        $taxonomies = $startupProfile->taxonomies()
            ->where('type', $validated['type'])
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get()
            ->map(function ($taxonomy) {
                return [
                    'id' => $taxonomy->id,
                    'name' => $taxonomy->name,
                    'slug' => $taxonomy->slug,
                    'description' => $taxonomy->description,
                    'type' => $taxonomy->type,
                    'sort_order' => $taxonomy->sort_order,
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $taxonomies,
            'meta' => [
                'type' => $validated['type'],
                'count' => $taxonomies->count(),
            ],
        ]);
    }

    /**
     * Attach taxonomies to startup profile
     */
    public function attachToUser(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'taxonomy_ids' => ['required', 'array', 'min:1', 'max:10'],
            'taxonomy_ids.*' => ['required', 'integer', 'exists:taxonomies,id'],
            'type' => ['required', Rule::in(['keyword', 'tag', 'category', 'brand', 'type', 'model', 'industry', 'technology', 'market', 'stage'])],
        ]);

        $user = $request->user();

        // Only startup users can attach taxonomies
        if ($user->role !== 'startup') {
            return response()->json([
                'success' => false,
                'message' => 'Access denied. Startup role required.',
            ], 403);
        }

        $startupProfile = $user->startupProfile;
        if (!$startupProfile) {
            return response()->json([
                'success' => false,
                'message' => 'Startup profile not found. Please create your profile first.',
            ], 404);
        }

        // Verify all taxonomies are of the specified type
        $taxonomies = Taxonomy::whereIn('id', $validated['taxonomy_ids'])
            ->where('type', $validated['type'])
            ->get();

        if ($taxonomies->count() !== count($validated['taxonomy_ids'])) {
            return response()->json([
                'success' => false,
                'message' => 'Some taxonomies are invalid or not of the specified type.',
            ], 422);
        }

        // Apply business rules based on type
        $limits = [
            'category' => 5,
            'keyword' => 10,
            'industry' => 3,
            'technology' => 8,
            'market' => 5,
            'stage' => 1,
        ];

        $limit = $limits[$validated['type']] ?? 10;

        if (count($validated['taxonomy_ids']) > $limit) {
            return response()->json([
                'success' => false,
                'message' => "You can select a maximum of {$limit} {$validated['type']} items.",
            ], 422);
        }

        // Remove existing taxonomies of this type and attach new ones
        $startupProfile->taxonomies()->where('type', $validated['type'])->detach();
        $startupProfile->taxonomies()->attach($validated['taxonomy_ids']);

        $attachedTaxonomies = $startupProfile->taxonomies()
            ->where('type', $validated['type'])
            ->get()
            ->map(function ($taxonomy) {
                return [
                    'id' => $taxonomy->id,
                    'name' => $taxonomy->name,
                    'slug' => $taxonomy->slug,
                    'description' => $taxonomy->description,
                    'type' => $taxonomy->type,
                ];
            });

        return response()->json([
            'success' => true,
            'message' => ucfirst($validated['type']) . ' selection updated successfully.',
            'data' => $attachedTaxonomies,
            'meta' => [
                'type' => $validated['type'],
                'count' => $attachedTaxonomies->count(),
                'limit' => $limit,
            ],
        ]);
    }

    /**
     * Detach taxonomies from startup profile
     */
    public function detachFromUser(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'taxonomy_ids' => ['required', 'array', 'min:1'],
            'taxonomy_ids.*' => ['required', 'integer', 'exists:taxonomies,id'],
        ]);

        $user = $request->user();

        // Only startup users can detach taxonomies
        if ($user->role !== 'startup') {
            return response()->json([
                'success' => false,
                'message' => 'Access denied. Startup role required.',
            ], 403);
        }

        $startupProfile = $user->startupProfile;
        if (!$startupProfile) {
            return response()->json([
                'success' => false,
                'message' => 'Startup profile not found.',
            ], 404);
        }

        $startupProfile->taxonomies()->detach($validated['taxonomy_ids']);

        return response()->json([
            'success' => true,
            'message' => 'Taxonomies removed successfully.',
        ]);
    }

    /**
     * Get taxonomy suggestions based on startup profile's existing selections
     */
    public function getSuggestions(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'type' => ['required', Rule::in(['keyword', 'tag', 'category', 'brand', 'type', 'model', 'industry', 'technology', 'market', 'stage'])],
            'limit' => ['nullable', 'integer', 'min:1', 'max:20'],
        ]);

        $user = $request->user();
        $limit = $validated['limit'] ?? 10;

        // Only startup users can get suggestions
        if ($user->role !== 'startup') {
            return response()->json([
                'success' => false,
                'message' => 'Access denied. Startup role required.',
            ], 403);
        }

        $startupProfile = $user->startupProfile;
        if (!$startupProfile) {
            return response()->json([
                'success' => false,
                'message' => 'Startup profile not found.',
            ], 404);
        }

        // Get startup profile's existing taxonomies to find related suggestions
        $profileTaxonomyIds = $startupProfile->taxonomies()->pluck('taxonomies.id')->toArray();

        // Get suggestions based on what other startup profiles with similar taxonomies have selected
        $suggestions = Taxonomy::where('type', $validated['type'])
            ->whereNotIn('id', $profileTaxonomyIds)
            ->whereHas('taxonomables', function ($query) use ($profileTaxonomyIds) {
                $query->where('taxonomable_type', 'App\\Models\\StartupProfile')
                      ->whereHas('taxonomable', function ($q) use ($profileTaxonomyIds) {
                          $q->whereHas('taxonomies', function ($tq) use ($profileTaxonomyIds) {
                              $tq->whereIn('taxonomies.id', $profileTaxonomyIds);
                          });
                      });
            })
            ->withCount(['taxonomables' => function ($query) use ($profileTaxonomyIds) {
                $query->where('taxonomable_type', 'App\\Models\\StartupProfile')
                      ->whereHas('taxonomable', function ($q) use ($profileTaxonomyIds) {
                          $q->whereHas('taxonomies', function ($tq) use ($profileTaxonomyIds) {
                              $tq->whereIn('taxonomies.id', $profileTaxonomyIds);
                          });
                      });
            }])
            ->orderBy('taxonomables_count', 'desc')
            ->orderBy('name')
            ->limit($limit)
            ->get()
            ->map(function ($taxonomy) {
                return [
                    'id' => $taxonomy->id,
                    'name' => $taxonomy->name,
                    'slug' => $taxonomy->slug,
                    'description' => $taxonomy->description,
                    'type' => $taxonomy->type,
                    'relevance_score' => $taxonomy->taxonomables_count,
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $suggestions,
            'meta' => [
                'type' => $validated['type'],
                'count' => $suggestions->count(),
                'limit' => $limit,
            ],
        ]);
    }
}
