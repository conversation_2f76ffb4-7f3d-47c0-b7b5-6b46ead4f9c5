<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Payment\CreatePaymentIntentRequest;
use App\Http\Requests\Api\Payment\ConfirmPaymentRequest;
use App\Models\Payment;
use App\Models\UserSubscription;
use App\Services\StripeService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class PaymentController extends Controller
{
    protected StripeService $stripeService;

    public function __construct(StripeService $stripeService)
    {
        $this->stripeService = $stripeService;
        $this->middleware(['auth:sanctum', 'check.account.locked']);
        $this->authorizeResource(Payment::class, 'payment');
    }

    /**
     * Display a listing of user's payments.
     */
    public function index(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        $query = $user->payments()->with([
            'userSubscription.subscriptionProduct',
            'invoice',
            'refunds'
        ]);

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }

        // Filter by type
        if ($request->has('type')) {
            $query->where('type', $request->get('type'));
        }

        // Filter by date range
        if ($request->has('from_date')) {
            $query->whereDate('created_at', '>=', $request->get('from_date'));
        }
        if ($request->has('to_date')) {
            $query->whereDate('created_at', '<=', $request->get('to_date'));
        }

        $payments = $query->orderBy('created_at', 'desc')->paginate(20);

        return $this->responseWithSuccess(
            'Payments retrieved successfully',
            $payments
        );
    }

    /**
     * Display the specified payment.
     */
    public function show(Payment $payment): JsonResponse
    {
        // Authorization is handled by the policy
        $payment->load(['userSubscription.subscriptionProduct', 'invoice', 'refunds']);

        return $this->responseWithSuccess(
            'Payment retrieved successfully',
            $payment
        );
    }

    /**
     * Create a payment intent for one-time payments.
     */
    public function createPaymentIntent(CreatePaymentIntentRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $user = Auth::user();

        try {
            // Create or get Stripe customer
            $customer = $this->stripeService->createOrGetCustomer($user);

            // Create payment intent
            $paymentIntent = $this->stripeService->createPaymentIntent(
                $validated['amount'] * 100, // Convert to cents
                $validated['currency'] ?? 'usd',
                [
                    'user_id' => $user->id,
                    'type' => 'one_time',
                    'description' => $validated['description'] ?? 'One-time payment',
                ]
            );

            // Create local payment record
            $payment = Payment::create([
                'user_id' => $user->id,
                'stripe_payment_intent_id' => $paymentIntent->id,
                'amount' => $validated['amount'],
                'currency' => $validated['currency'] ?? 'usd',
                'status' => $paymentIntent->status,
                'type' => 'one_time',
                'metadata' => [
                    'description' => $validated['description'] ?? 'One-time payment',
                ],
            ]);

            return $this->responseWithSuccess(
                'Payment intent created successfully',
                [
                    'payment' => $payment,
                    'client_secret' => $paymentIntent->client_secret,
                    'requires_action' => $paymentIntent->status === 'requires_action',
                    'next_action' => $paymentIntent->next_action,
                ]
            );

        } catch (\Exception $e) {
            Log::error('Failed to create payment intent', [
                'user_id' => $user->id,
                'amount' => $validated['amount'],
                'error' => $e->getMessage()
            ]);

            return $this->responseWithError(
                'Failed to create payment intent: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Confirm a payment intent (handle 3D Secure).
     */
    public function confirmPayment(ConfirmPaymentRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $user = Auth::user();

        try {
            // Retrieve the payment intent from Stripe
            $paymentIntent = $this->stripeService->retrievePaymentIntent($validated['payment_intent_id']);

            // Find the local payment record
            $payment = Payment::where('stripe_payment_intent_id', $paymentIntent->id)
                             ->where('user_id', $user->id)
                             ->firstOrFail();

            // Update payment status
            $payment->update([
                'status' => $paymentIntent->status,
                'payment_method_type' => $paymentIntent->charges->data[0]->payment_method_details->type ?? null,
                'paid_at' => $paymentIntent->status === 'succeeded' ? now() : null,
            ]);

            // Handle 3D Secure requirements
            if ($paymentIntent->status === 'requires_action') {
                $payment->update([
                    'requires_3d_secure' => true,
                    '3d_secure_url' => $paymentIntent->next_action->redirect_to_url->url ?? null,
                ]);

                return $this->responseWithSuccess(
                    'Payment requires additional authentication',
                    [
                        'payment' => $payment,
                        'requires_action' => true,
                        'next_action' => $paymentIntent->next_action,
                    ]
                );
            }

            if ($paymentIntent->status === 'succeeded') {
                return $this->responseWithSuccess(
                    'Payment completed successfully',
                    [
                        'payment' => $payment,
                        'requires_action' => false,
                    ]
                );
            }

            if ($paymentIntent->status === 'failed') {
                $payment->update([
                    'failure_code' => $paymentIntent->last_payment_error->code ?? null,
                    'failure_message' => $paymentIntent->last_payment_error->message ?? 'Payment failed',
                ]);

                return $this->responseWithError(
                    'Payment failed: ' . ($paymentIntent->last_payment_error->message ?? 'Unknown error'),
                    Response::HTTP_PAYMENT_REQUIRED
                );
            }

            return $this->responseWithSuccess(
                'Payment status updated',
                [
                    'payment' => $payment,
                    'status' => $paymentIntent->status,
                ]
            );

        } catch (\Exception $e) {
            Log::error('Failed to confirm payment', [
                'user_id' => $user->id,
                'payment_intent_id' => $validated['payment_intent_id'],
                'error' => $e->getMessage()
            ]);

            return $this->responseWithError(
                'Failed to confirm payment: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Retry a failed payment.
     */
    public function retryPayment(Payment $payment): JsonResponse
    {
        // Authorization is handled by the policy
        
        if (!$payment->isFailed()) {
            return $this->responseWithError(
                'Only failed payments can be retried',
                Response::HTTP_BAD_REQUEST
            );
        }

        try {
            // Create a new payment intent for retry
            $paymentIntent = $this->stripeService->createPaymentIntent(
                $payment->amount * 100, // Convert to cents
                $payment->currency,
                [
                    'user_id' => $payment->user_id,
                    'type' => $payment->type,
                    'retry_of' => $payment->id,
                ]
            );

            // Create new payment record for retry
            $newPayment = Payment::create([
                'user_id' => $payment->user_id,
                'user_subscription_id' => $payment->user_subscription_id,
                'stripe_payment_intent_id' => $paymentIntent->id,
                'amount' => $payment->amount,
                'currency' => $payment->currency,
                'status' => $paymentIntent->status,
                'type' => $payment->type,
                'metadata' => array_merge($payment->metadata ?? [], [
                    'retry_of' => $payment->id,
                ]),
            ]);

            return $this->responseWithSuccess(
                'Payment retry initiated successfully',
                [
                    'payment' => $newPayment,
                    'client_secret' => $paymentIntent->client_secret,
                ]
            );

        } catch (\Exception $e) {
            Log::error('Failed to retry payment', [
                'payment_id' => $payment->id,
                'user_id' => $payment->user_id,
                'error' => $e->getMessage()
            ]);

            return $this->responseWithError(
                'Failed to retry payment: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }
}
