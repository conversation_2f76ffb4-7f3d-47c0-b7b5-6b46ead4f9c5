<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Refund;
use App\Models\Payment;
use App\Models\UserSubscription;
use App\Services\RefundService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class RefundController extends Controller
{
    protected RefundService $refundService;

    public function __construct(RefundService $refundService)
    {
        $this->refundService = $refundService;
    }

    /**
     * Get user's refunds
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            
            $query = $user->refunds()->with(['payment', 'userSubscription', 'processedBy']);

            // Apply filters
            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            if ($request->has('type')) {
                $query->where('type', $request->type);
            }

            if ($request->has('reason')) {
                $query->where('reason', $request->reason);
            }

            // Date range filter
            if ($request->has('from_date')) {
                $query->whereDate('created_at', '>=', $request->from_date);
            }

            if ($request->has('to_date')) {
                $query->whereDate('created_at', '<=', $request->to_date);
            }

            $refunds = $query->orderBy('created_at', 'desc')
                           ->paginate($request->get('per_page', 15));

            return response()->json([
                'status' => 'success',
                'data' => $refunds,
                'message' => 'Refunds retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving refunds', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve refunds'
            ], 500);
        }
    }

    /**
     * Get specific refund details
     */
    public function show(Refund $refund): JsonResponse
    {
        try {
            // Check if user owns this refund
            if ($refund->user_id !== Auth::id()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Unauthorized access to refund'
                ], 403);
            }

            $refund->load(['payment', 'userSubscription', 'invoice', 'processedBy']);

            return response()->json([
                'status' => 'success',
                'data' => [
                    'refund' => $refund,
                    'audit_trail' => $this->refundService->getRefundAuditTrail($refund),
                ],
                'message' => 'Refund details retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving refund details', [
                'refund_id' => $refund->id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve refund details'
            ], 500);
        }
    }

    /**
     * Request a refund for a payment
     */
    public function requestRefund(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'payment_id' => 'required|exists:payments,id',
                'type' => 'required|in:full,partial',
                'amount' => 'required_if:type,partial|integer|min:1',
                'reason' => 'required|in:requested_by_customer,duplicate,fraudulent,other',
                'description' => 'nullable|string|max:500',
            ]);

            $user = Auth::user();
            $payment = Payment::where('id', $validated['payment_id'])
                             ->where('user_id', $user->id)
                             ->where('status', 'succeeded')
                             ->first();

            if (!$payment) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Payment not found or not eligible for refund'
                ], 404);
            }

            // Check if payment already has refunds
            $existingRefunds = $payment->refunds()->successful()->sum('amount');
            $availableAmount = $payment->amount - $existingRefunds;

            if ($availableAmount <= 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Payment has already been fully refunded'
                ], 400);
            }

            if ($validated['type'] === 'partial') {
                if ($validated['amount'] > $availableAmount) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Refund amount exceeds available amount'
                    ], 400);
                }

                $refund = $this->refundService->processPartialRefund(
                    $payment,
                    $validated['amount'],
                    $validated['reason'],
                    $validated['description'] ?? null,
                    $user
                );
            } else {
                $refund = $this->refundService->processFullRefund(
                    $payment,
                    $validated['reason'],
                    $validated['description'] ?? null,
                    $user
                );
            }

            return response()->json([
                'status' => 'success',
                'data' => $refund,
                'message' => 'Refund processed successfully'
            ], 201);

        } catch (\Exception $e) {
            Log::error('Error processing refund request', [
                'user_id' => Auth::id(),
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to process refund: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate prorated refund for subscription cancellation
     */
    public function calculateProratedRefund(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'subscription_id' => 'required|exists:user_subscriptions,id',
                'cancellation_date' => 'nullable|date|after_or_equal:today',
            ]);

            $user = Auth::user();
            $subscription = UserSubscription::where('id', $validated['subscription_id'])
                                          ->where('user_id', $user->id)
                                          ->where('status', 'active')
                                          ->first();

            if (!$subscription) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Active subscription not found'
                ], 404);
            }

            $cancellationDate = $validated['cancellation_date'] 
                ? \Carbon\Carbon::parse($validated['cancellation_date'])
                : now();

            $calculation = $this->refundService->calculateProratedRefund($subscription, $cancellationDate);

            return response()->json([
                'status' => 'success',
                'data' => [
                    'calculation' => $calculation,
                    'formatted_amounts' => [
                        'original_amount' => '$' . number_format($calculation['original_amount'] / 100, 2),
                        'prorated_amount' => '$' . number_format($calculation['prorated_amount'] / 100, 2),
                    ],
                ],
                'message' => 'Prorated refund calculated successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error calculating prorated refund', [
                'user_id' => Auth::id(),
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to calculate prorated refund: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get refund statistics for the user
     */
    public function statistics(): JsonResponse
    {
        try {
            $user = Auth::user();
            $statistics = $this->refundService->getRefundStatistics($user);

            // Add formatted amounts
            $statistics['formatted_total_amount'] = '$' . number_format($statistics['total_amount'] / 100, 2);

            return response()->json([
                'status' => 'success',
                'data' => $statistics,
                'message' => 'Refund statistics retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving refund statistics', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve refund statistics'
            ], 500);
        }
    }
}
