<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class StartupRequestController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $breadcrumbsItems = [
            [
                'name' => 'Startup Request',
                'url' => 'startup-request',
                'active' => true
            ],
        ];

        // Fetch interest requests where target is a startup
        $startupRequests = \App\Models\InterestRequest::with([
                'requester.media',
                'target.media',
                'approver.media'
            ])
            ->whereHas('target', function ($query) {
                $query->where('role', 'startup');
            })
            ->latest()
            ->paginate(10);

        return view('startup-request.index', [
            'pageTitle' => 'Startup Request',
            'breadcrumbItems' => $breadcrumbsItems,
            'startupRequests' => $startupRequests
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(\App\Models\InterestRequest $startupRequest)
    {
        // Ensure this is actually a startup request (target is a startup)
        if ($startupRequest->target->role !== 'startup') {
            abort(404, 'Startup request not found');
        }

        $breadcrumbsItems = [
            [
                'name' => 'Startup Request',
                'url' => route('startup-request.index'),
                'active' => false
            ],
            [
                'name' => 'Request Details',
                'url' => null,
                'active' => true
            ],
        ];

        // Load comprehensive request data
        $startupRequest->load([
            'requester.media',
            'requester.investorProfile.categories',
            'target.media',
            'target.startupProfile.categories',
            'target.startupProfile.esgResponses.esgQuestion',
            'approver.media'
        ]);

        return view('startup-request.show', [
            'pageTitle' => 'Startup Request Details',
            'breadcrumbItems' => $breadcrumbsItems,
            'startupRequest' => $startupRequest
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
