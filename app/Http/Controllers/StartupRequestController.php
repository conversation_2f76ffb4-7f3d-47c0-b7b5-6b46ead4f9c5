<?php

namespace App\Http\Controllers;

use App\Models\InterestRequest;
use App\Traits\DataTableTrait;
use Illuminate\Http\Request;

class StartupRequestController extends Controller
{
    use DataTableTrait;
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $breadcrumbsItems = [
            [
                'name' => 'Startup Request',
                'url' => 'startup-request',
                'active' => true
            ],
        ];

        return view('startup-request.index', [
            'pageTitle' => 'Startup Request',
            'breadcrumbItems' => $breadcrumbsItems
        ]);
    }

    /**
     * Handle DataTable server-side processing for startup requests
     */
    public function datatable(Request $request)
    {
        $columns = [
            0 => null, // SL (not sortable)
            1 => null, // Startup Company Name (related field, not directly sortable)
            2 => null, // Investor Name (related field, not directly sortable)
            3 => 'created_at', // Request Date & Time
            4 => 'status', // Status
            5 => null // Action (not sortable)
        ];

        $query = InterestRequest::with([
                'requester.media',
                'target.media',
                'target.startupProfile',
                'approver.media'
            ])
            ->whereHas('target', function ($q) {
                $q->where('role', 'startup');
            });

        return $this->processDataTable($request, $query, $columns, function ($request, $index) {
            return [
                $index, // SL
                $request->target->startupProfile->company_name ?? $request->target->name, // Startup Company Name
                $request->requester->name, // Investor Name
                $request->created_at->format('M d, Y H:i'), // Request Date & Time
                $this->getStatusBadge($request->status), // Status
                $this->getActionButtons('startup-request', $request) // Action
            ];
        });
    }

    /**
     * Apply search filtering for startup requests
     */
    protected function applySearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('status', 'LIKE', "%{$search}%")
              ->orWhere('message', 'LIKE', "%{$search}%")
              ->orWhereHas('requester', function ($requester) use ($search) {
                  $requester->where('name', 'LIKE', "%{$search}%")
                           ->orWhere('email', 'LIKE', "%{$search}%");
              })
              ->orWhereHas('target', function ($target) use ($search) {
                  $target->where('name', 'LIKE', "%{$search}%")
                        ->orWhere('email', 'LIKE', "%{$search}%");
              });
        });
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(\App\Models\InterestRequest $startupRequest)
    {
        // Ensure this is actually a startup request (target is a startup)
        if ($startupRequest->target->role !== 'startup') {
            abort(404, 'Startup request not found');
        }

        $breadcrumbsItems = [
            [
                'name' => 'Startup Request',
                'url' => route('startup-request.index'),
                'active' => false
            ],
            [
                'name' => 'Request Details',
                'url' => null,
                'active' => true
            ],
        ];

        // Load comprehensive request data
        $startupRequest->load([
            'requester.media',
            'requester.investorProfile',
            'target.media',
            'target.startupProfile.esgResponses.esgQuestion',
            'approver.media'
        ]);

        return view('startup-request.show', [
            'pageTitle' => 'Startup Request Details',
            'breadcrumbItems' => $breadcrumbsItems,
            'startupRequest' => $startupRequest
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
