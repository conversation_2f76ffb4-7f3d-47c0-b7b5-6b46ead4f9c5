<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class StartupRequestController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $breadcrumbsItems = [
            [
                'name' => 'Startup Request',
                'url' => 'startup-request',
                'active' => true
            ],
        ];

        return view('startup-request.index', [
            'pageTitle' => 'Startup Request',
            'breadcrumbItems' => $breadcrumbsItems
        ]);
    }

    /**
     * Handle DataTable server-side processing for startup requests
     */
    public function datatable(Request $request)
    {
        $draw = $request->get('draw');
        $start = $request->get('start');
        $length = $request->get('length');
        $search = $request->get('search')['value'] ?? '';
        $orderColumn = $request->get('order')[0]['column'] ?? 0;
        $orderDir = $request->get('order')[0]['dir'] ?? 'desc';

        // Column mapping for sorting
        $columns = [
            0 => 'id', // SL
            1 => 'target_name', // Startup Company Name
            2 => 'requester_name', // Investor Name
            3 => 'created_at', // Request Date & Time
            4 => 'status', // Status
            5 => 'id' // Action (not sortable)
        ];

        $query = \App\Models\InterestRequest::with([
                'requester.media',
                'target.media',
                'approver.media'
            ])
            ->whereHas('target', function ($q) {
                $q->where('role', 'startup');
            });

        // Apply search
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('status', 'LIKE', "%{$search}%")
                  ->orWhere('message', 'LIKE', "%{$search}%")
                  ->orWhereHas('requester', function ($requester) use ($search) {
                      $requester->where('name', 'LIKE', "%{$search}%")
                               ->orWhere('email', 'LIKE', "%{$search}%");
                  })
                  ->orWhereHas('target', function ($target) use ($search) {
                      $target->where('name', 'LIKE', "%{$search}%")
                            ->orWhere('email', 'LIKE', "%{$search}%");
                  });
            });
        }

        // Get total count before filtering
        $totalRecords = \App\Models\InterestRequest::whereHas('target', function ($q) {
            $q->where('role', 'startup');
        })->count();

        // Get filtered count
        $filteredRecords = $query->count();

        // Apply sorting
        $sortColumn = $columns[$orderColumn] ?? 'created_at';
        if (in_array($sortColumn, ['target_name', 'requester_name'])) {
            // For related model sorting, we'll use the default created_at
            $query->orderBy('created_at', $orderDir);
        } else {
            $query->orderBy($sortColumn, $orderDir);
        }

        // Apply pagination
        $startupRequests = $query->skip($start)->take($length)->get();

        // Format data for DataTable
        $data = [];
        foreach ($startupRequests as $index => $request) {
            $data[] = [
                $start + $index + 1, // SL
                $request->target->startupProfile->company_name ?? $request->target->name, // Startup Company Name
                $request->requester->name, // Investor Name
                $request->created_at->format('M d, Y H:i'), // Request Date & Time
                $this->getStatusBadge($request->status), // Status
                $this->getRequestActionButtons($request) // Action
            ];
        }

        return response()->json([
            'draw' => intval($draw),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ]);
    }

    /**
     * Generate status badge
     */
    private function getStatusBadge($status)
    {
        $badgeClass = match($status) {
            'pending' => 'bg-warning-500',
            'approved' => 'bg-success-500',
            'rejected' => 'bg-danger-500',
            default => 'bg-slate-500'
        };

        return '<span class="badge ' . $badgeClass . ' text-white capitalize">' . $status . '</span>';
    }

    /**
     * Generate action buttons for DataTable
     */
    private function getRequestActionButtons($request)
    {
        return '<div class="relative">
                    <div class="dropdown relative">
                        <button class="text-xl text-center block w-full" type="button"
                                id="tableDropdownMenuButton' . $request->id . '" data-bs-toggle="dropdown"
                                aria-expanded="false">
                            <iconify-icon icon="heroicons-outline:dots-vertical"></iconify-icon>
                        </button>
                        <ul class="dropdown-menu min-w-[120px] absolute text-sm text-slate-700 dark:text-white hidden bg-white dark:bg-slate-700
                          shadow z-[2] float-left overflow-hidden list-none text-left rounded-lg mt-1 m-0 bg-clip-padding border-none">
                            <li>
                                <a href="' . route('startup-request.show', $request) . '"
                                   class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600 dark:hover:text-white">
                                    View
                                </a>
                            </li>
                            <li>
                                <a href="' . route('startup-request.edit', $request) . '"
                                   class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600 dark:hover:text-white">
                                    Edit
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>';
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(\App\Models\InterestRequest $startupRequest)
    {
        // Ensure this is actually a startup request (target is a startup)
        if ($startupRequest->target->role !== 'startup') {
            abort(404, 'Startup request not found');
        }

        $breadcrumbsItems = [
            [
                'name' => 'Startup Request',
                'url' => route('startup-request.index'),
                'active' => false
            ],
            [
                'name' => 'Request Details',
                'url' => null,
                'active' => true
            ],
        ];

        // Load comprehensive request data
        $startupRequest->load([
            'requester.media',
            'requester.investorProfile',
            'target.media',
            'target.startupProfile.esgResponses.esgQuestion',
            'approver.media'
        ]);

        return view('startup-request.show', [
            'pageTitle' => 'Startup Request Details',
            'breadcrumbItems' => $breadcrumbsItems,
            'startupRequest' => $startupRequest
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
