<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Traits\DataTableTrait;
use Illuminate\Http\Request;

class InvestorController extends Controller
{
    use DataTableTrait;
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $breadcrumbsItems = [
            [
                'name' => 'Investor',
                'url' => 'investors',
                'active' => true
            ],
        ];

        return view('investor.index', [
            'pageTitle' => 'Investor',
            'breadcrumbItems' => $breadcrumbsItems
        ]);
    }

    /**
     * Handle DataTable server-side processing for investors
     */
    public function datatable(Request $request)
    {
        $columns = [
            0 => null, // SL (not sortable)
            1 => 'name', // Investor Name
            2 => 'investor_profile.investor_type', // Investor Type
            3 => 'email', // E-Mail
            4 => 'investor_profile.website', // Website URL
            5 => 'created_at', // Founded
            6 => 'country', // Country
            7 => null // Action (not sortable)
        ];

        $query = User::where('role', 'investor')
            ->with(['investorProfile', 'media']);

        return $this->processDataTable($request, $query, $columns, function ($investor, $index) {
            return [
                $index, // SL
                $investor->name, // Investor Name
                $investor->investorProfile->investor_type ?? 'N/A', // Investor Type
                $investor->email, // E-Mail
                $investor->investorProfile->website ?? 'N/A', // Website URL
                $investor->created_at->format('m/d/Y'), // Founded
                $investor->country ?? 'United States', // Country
                $this->getActionButtons('investors', $investor) // Action
            ];
        });
    }

    /**
     * Apply search filtering for investors
     */
    protected function applySearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'LIKE', "%{$search}%")
              ->orWhere('email', 'LIKE', "%{$search}%")
              ->orWhereHas('investorProfile', function ($profile) use ($search) {
                  $profile->where('bio', 'LIKE', "%{$search}%")
                          ->orWhere('investor_type', 'LIKE', "%{$search}%")
                          ->orWhere('website', 'LIKE', "%{$search}%");
              });
        });
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(User $investor)
    {
        // Ensure the user is actually an investor
        if ($investor->role !== 'investor') {
            abort(404, 'Investor not found');
        }

        $breadcrumbsItems = [
            [
                'name' => 'Investor',
                'url' => route('investors.index'),
                'active' => false
            ],
            [
                'name' => $investor->name,
                'url' => null,
                'active' => true
            ],
        ];

        // Load comprehensive investor data
        $investor->load([
            'investorProfile',
            'paymentMethods',
            'subscriptions.subscriptionProduct',
            'invoices',
            'sentInterestRequests.target.startupProfile',
            'receivedInterestRequests.requester.startupProfile',
            'media',
            'accountStatus'
        ]);

        // Get subscription history with status changes
        $subscriptionHistory = $investor->subscriptions()
            ->with('subscriptionProduct')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get invoices with download capabilities
        $invoices = $investor->invoices()
            ->orderBy('created_at', 'desc')
            ->get();

        // Get startups this investor has shown interest in
        $interestedStartups = $investor->sentInterestRequests()
            ->with(['target.startupProfile', 'target.media'])
            ->where('status', 'approved')
            ->orderBy('created_at', 'desc')
            ->get();

        return view('investor.show', [
            'pageTitle' => 'Investor Profile - ' . $investor->name,
            'breadcrumbItems' => $breadcrumbsItems,
            'investor' => $investor,
            'subscriptionHistory' => $subscriptionHistory,
            'invoices' => $invoices,
            'interestedStartups' => $interestedStartups
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $investor)
    {
        // Ensure the user is actually an investor
        if ($investor->role !== 'investor') {
            abort(404, 'Investor not found');
        }

        return view('investor.edit', compact('investor'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $investor)
    {
        // Ensure the user is actually an investor
        if ($investor->role !== 'investor') {
            abort(404, 'Investor not found');
        }

        // Implementation for updating investor
        return redirect()->route('investors.show', $investor);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $investor)
    {
        // Ensure the user is actually an investor
        if ($investor->role !== 'investor') {
            abort(404, 'Investor not found');
        }

        // Implementation for deleting investor
        return redirect()->route('investors.index');
    }
}
