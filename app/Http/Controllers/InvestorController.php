<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;

class InvestorController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $breadcrumbsItems = [
            [
                'name' => 'Investor',
                'url' => 'investors',
                'active' => true
            ],
        ];

        return view('investor.index', [
            'pageTitle' => 'Investor',
            'breadcrumbItems' => $breadcrumbsItems
        ]);
    }

    /**
     * Handle DataTable server-side processing for investors
     */
    public function datatable(Request $request)
    {
        $draw = $request->get('draw');
        $start = $request->get('start');
        $length = $request->get('length');
        $search = $request->get('search')['value'] ?? '';
        $orderColumn = $request->get('order')[0]['column'] ?? 0;
        $orderDir = $request->get('order')[0]['dir'] ?? 'desc';

        // Column mapping for sorting
        $columns = [
            0 => 'id', // SL
            1 => 'name', // Investor Name
            2 => 'investor_type', // Investor Type (placeholder)
            3 => 'email', // E-Mail
            4 => 'website', // Website URL (placeholder)
            5 => 'created_at', // Founded
            6 => 'country', // Country (placeholder)
            7 => 'id' // Action (not sortable)
        ];

        $query = User::where('role', 'investor')
            ->with(['investorProfile', 'media']);

        // Apply search
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('email', 'LIKE', "%{$search}%")
                  ->orWhereHas('investorProfile', function ($profile) use ($search) {
                      $profile->where('bio', 'LIKE', "%{$search}%");
                  });
            });
        }

        // Get total count before filtering
        $totalRecords = User::where('role', 'investor')->count();

        // Get filtered count
        $filteredRecords = $query->count();

        // Apply sorting
        $sortColumn = $columns[$orderColumn] ?? 'created_at';
        if ($sortColumn === 'created_at') {
            $query->orderBy($sortColumn, $orderDir);
        } else {
            $query->orderBy($sortColumn, $orderDir);
        }

        // Apply pagination
        $investors = $query->skip($start)->take($length)->get();

        // Format data for DataTable
        $data = [];
        foreach ($investors as $index => $investor) {
            $data[] = [
                $start + $index + 1, // SL
                $investor->name, // Investor Name
                'N/A', // Investor Type
                $investor->email, // E-Mail
                'N/A', // Website URL
                $investor->created_at->format('m/d/Y'), // Founded
                'United States', // Country (placeholder)
                $this->getActionButtons($investor) // Action
            ];
        }

        return response()->json([
            'draw' => intval($draw),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ]);
    }

    /**
     * Generate action buttons for DataTable
     */
    private function getActionButtons($investor)
    {
        return '<div class="relative">
                    <div class="dropdown relative">
                        <button class="text-xl text-center block w-full" type="button"
                                id="tableDropdownMenuButton' . $investor->id . '" data-bs-toggle="dropdown"
                                aria-expanded="false">
                            <iconify-icon icon="heroicons-outline:dots-vertical"></iconify-icon>
                        </button>
                        <ul class="dropdown-menu min-w-[120px] absolute text-sm text-slate-700 dark:text-white hidden bg-white dark:bg-slate-700
                          shadow z-[2] float-left overflow-hidden list-none text-left rounded-lg mt-1 m-0 bg-clip-padding border-none">
                            <li>
                                <a href="' . route('investors.show', $investor) . '"
                                   class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600 dark:hover:text-white">
                                    View
                                </a>
                            </li>
                            <li>
                                <a href="' . route('investors.edit', $investor) . '"
                                   class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600 dark:hover:text-white">
                                    Edit
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>';
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(User $investor)
    {
        // Ensure the user is actually an investor
        if ($investor->role !== 'investor') {
            abort(404, 'Investor not found');
        }

        $breadcrumbsItems = [
            [
                'name' => 'Investor',
                'url' => route('investors.index'),
                'active' => false
            ],
            [
                'name' => $investor->name,
                'url' => null,
                'active' => true
            ],
        ];

        // Load comprehensive investor data
        $investor->load([
            'investorProfile',
            'paymentMethods',
            'subscriptions.subscriptionProduct',
            'invoices',
            'sentInterestRequests.target.startupProfile',
            'receivedInterestRequests.requester.startupProfile',
            'media',
            'accountStatus'
        ]);

        // Get subscription history with status changes
        $subscriptionHistory = $investor->subscriptions()
            ->with('subscriptionProduct')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get invoices with download capabilities
        $invoices = $investor->invoices()
            ->orderBy('created_at', 'desc')
            ->get();

        // Get startups this investor has shown interest in
        $interestedStartups = $investor->sentInterestRequests()
            ->with(['target.startupProfile', 'target.media'])
            ->where('status', 'approved')
            ->orderBy('created_at', 'desc')
            ->get();

        return view('investor.show', [
            'pageTitle' => 'Investor Profile - ' . $investor->name,
            'breadcrumbItems' => $breadcrumbsItems,
            'investor' => $investor,
            'subscriptionHistory' => $subscriptionHistory,
            'invoices' => $invoices,
            'interestedStartups' => $interestedStartups
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $investor)
    {
        // Ensure the user is actually an investor
        if ($investor->role !== 'investor') {
            abort(404, 'Investor not found');
        }

        return view('investor.edit', compact('investor'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $investor)
    {
        // Ensure the user is actually an investor
        if ($investor->role !== 'investor') {
            abort(404, 'Investor not found');
        }

        // Implementation for updating investor
        return redirect()->route('investors.show', $investor);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $investor)
    {
        // Ensure the user is actually an investor
        if ($investor->role !== 'investor') {
            abort(404, 'Investor not found');
        }

        // Implementation for deleting investor
        return redirect()->route('investors.index');
    }
}
