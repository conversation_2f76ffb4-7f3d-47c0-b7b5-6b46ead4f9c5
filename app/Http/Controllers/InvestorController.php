<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;

class InvestorController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $breadcrumbsItems = [
            [
                'name' => 'Investor',
                'url' => 'investors',
                'active' => true
            ],
        ];

        // Fetch investors with their profiles and media
        $investors = User::where('role', 'investor')
            ->with(['investorProfile', 'media'])
            ->latest()
            ->paginate(10);

        return view('investor.index', [
            'pageTitle' => 'Investor',
            'breadcrumbItems' => $breadcrumbsItems,
            'investors' => $investors
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(User $investor)
    {
        // Ensure the user is actually an investor
        if ($investor->role !== 'investor') {
            abort(404, 'Investor not found');
        }

        $breadcrumbsItems = [
            [
                'name' => 'Investor',
                'url' => route('investors.index'),
                'active' => false
            ],
            [
                'name' => $investor->name,
                'url' => null,
                'active' => true
            ],
        ];

        // Load comprehensive investor data
        $investor->load([
            'investorProfile',
            'paymentMethods',
            'subscriptions.subscriptionProduct',
            'invoices',
            'sentInterestRequests.target.startupProfile',
            'receivedInterestRequests.requester.startupProfile',
            'media',
            'accountStatus'
        ]);

        // Get subscription history with status changes
        $subscriptionHistory = $investor->subscriptions()
            ->with('subscriptionProduct')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get invoices with download capabilities
        $invoices = $investor->invoices()
            ->orderBy('created_at', 'desc')
            ->get();

        // Get startups this investor has shown interest in
        $interestedStartups = $investor->sentInterestRequests()
            ->with(['target.startupProfile', 'target.media'])
            ->where('status', 'approved')
            ->orderBy('created_at', 'desc')
            ->get();

        return view('investor.show', [
            'pageTitle' => 'Investor Profile - ' . $investor->name,
            'breadcrumbItems' => $breadcrumbsItems,
            'investor' => $investor,
            'subscriptionHistory' => $subscriptionHistory,
            'invoices' => $invoices,
            'interestedStartups' => $interestedStartups
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $investor)
    {
        // Ensure the user is actually an investor
        if ($investor->role !== 'investor') {
            abort(404, 'Investor not found');
        }

        return view('investor.edit', compact('investor'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $investor)
    {
        // Ensure the user is actually an investor
        if ($investor->role !== 'investor') {
            abort(404, 'Investor not found');
        }

        // Implementation for updating investor
        return redirect()->route('investors.show', $investor);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $investor)
    {
        // Ensure the user is actually an investor
        if ($investor->role !== 'investor') {
            abort(404, 'Investor not found');
        }

        // Implementation for deleting investor
        return redirect()->route('investors.index');
    }
}
