<?php

namespace App\Http\Controllers;

use App\Models\EsgQuestion;
use App\Models\StartupProfile;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\DB;

class StartupProfileController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:startup']);
    }

    /**
     * Display startup profile dashboard
     */
    public function index(): View
    {
        $user = auth()->user();
        $startupProfile = $user->startupProfile;

        $breadcrumbsItems = [
            [
                'name' => 'Dashboard',
                'url' => route('dashboard.index'),
                'active' => false,
            ],
            [
                'name' => 'My Profile',
                'url' => null,
                'active' => true,
            ],
        ];

        $stats = [
            'profile_completed' => $startupProfile ? $startupProfile->profile_completed : false,
            'esg_completed' => $startupProfile ? $startupProfile->esg_completed : false,
            'esg_score' => $startupProfile ? $startupProfile->esg_score : 0,
            'funding_amount_sought' => $startupProfile ? $startupProfile->funding_amount_sought : 0,
        ];

        return view('startup-profile.index', [
            'pageTitle' => 'My Startup Profile',
            'breadcrumbItems' => $breadcrumbsItems,
            'user' => $user,
            'startupProfile' => $startupProfile,
            'stats' => $stats,
        ]);
    }

    /**
     * Display ESG questionnaire
     */
    public function esgQuestionnaire(): View
    {
        $user = auth()->user();
        $startupProfile = $user->startupProfile;

        if (!$startupProfile) {
            return redirect()->route('startup-profile.index')
                ->with('error', 'Please complete your startup profile first.');
        }

        $breadcrumbsItems = [
            [
                'name' => 'Dashboard',
                'url' => route('dashboard.index'),
                'active' => false,
            ],
            [
                'name' => 'My Profile',
                'url' => route('startup-profile.index'),
                'active' => false,
            ],
            [
                'name' => 'ESG Questionnaire',
                'url' => null,
                'active' => true,
            ],
        ];

        // Get ESG questions grouped by category
        $questions = EsgQuestion::active()
            ->ordered()
            ->get()
            ->groupBy('category');

        // Get existing responses
        $existingResponses = $startupProfile->esgResponses()
            ->with('esgQuestion')
            ->get()
            ->keyBy('esg_question_id');

        return view('startup-profile.esg-questionnaire', [
            'pageTitle' => 'ESG Questionnaire',
            'breadcrumbItems' => $breadcrumbsItems,
            'questions' => $questions,
            'existingResponses' => $existingResponses,
            'startupProfile' => $startupProfile,
        ]);
    }

    /**
     * Submit ESG questionnaire responses
     */
    public function submitEsgResponses(Request $request): RedirectResponse
    {
        $user = auth()->user();
        $startupProfile = $user->startupProfile;

        if (!$startupProfile) {
            return redirect()->route('startup-profile.index')
                ->with('error', 'Please complete your startup profile first.');
        }

        $validated = $request->validate([
            'responses' => 'required|array',
            'responses.*.question_id' => 'required|exists:esg_questions,id',
            'responses.*.response_value' => 'required|string',
        ]);

        try {
            DB::beginTransaction();

            // Delete existing responses
            $startupProfile->esgResponses()->delete();

            // Create new responses
            foreach ($validated['responses'] as $responseData) {
                $startupProfile->esgResponses()->create([
                    'esg_question_id' => $responseData['question_id'],
                    'response_value' => $responseData['response_value'],
                ]);
            }

            // Calculate ESG score
            $startupProfile->calculateEsgScore();

            // Mark ESG as completed
            $startupProfile->update(['esg_completed' => true]);

            DB::commit();

            return redirect()->route('startup-profile.index')
                ->with('success', 'ESG questionnaire submitted successfully! Your ESG score has been calculated.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            return redirect()->back()
                ->with('error', 'An error occurred while submitting your responses. Please try again.')
                ->withInput();
        }
    }

    /**
     * Display ESG results
     */
    public function esgResults(): View
    {
        $user = auth()->user();
        $startupProfile = $user->startupProfile;

        if (!$startupProfile || !$startupProfile->esg_completed) {
            return redirect()->route('startup-profile.esg-questionnaire')
                ->with('error', 'Please complete the ESG questionnaire first.');
        }

        $breadcrumbsItems = [
            [
                'name' => 'Dashboard',
                'url' => route('dashboard.index'),
                'active' => false,
            ],
            [
                'name' => 'My Profile',
                'url' => route('startup-profile.index'),
                'active' => false,
            ],
            [
                'name' => 'ESG Results',
                'url' => null,
                'active' => true,
            ],
        ];

        // Get responses grouped by category
        $responses = $startupProfile->esgResponses()
            ->with('esgQuestion')
            ->get()
            ->groupBy('esgQuestion.category');

        return view('startup-profile.esg-results', [
            'pageTitle' => 'ESG Results',
            'breadcrumbItems' => $breadcrumbsItems,
            'startupProfile' => $startupProfile,
            'responses' => $responses,
        ]);
    }
}
