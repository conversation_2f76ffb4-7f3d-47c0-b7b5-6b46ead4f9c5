<?php

namespace App\Http\Controllers;

use App\Traits\DataTableTrait;
use Illuminate\Http\Request;

class InvestorRequestController extends Controller
{
    use DataTableTrait;
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $breadcrumbsItems = [
            [
                'name' => 'Investor Request',
                'url' => 'investor-request',
                'active' => true
            ],
        ];

        return view('investor-request.index', [
            'pageTitle' => 'Investor Request',
            'breadcrumbItems' => $breadcrumbsItems
        ]);
    }

    /**
     * Handle DataTable server-side processing for investor requests
     */
    public function datatable(Request $request)
    {
        $columns = [
            0 => null, // SL (not sortable)
            1 => null, // Investor Name (related field, not directly sortable)
            2 => null, // Startup Company Name (related field, not directly sortable)
            3 => 'created_at', // Request Date & Time
            4 => 'status', // Status
            5 => null // Action (not sortable)
        ];

        $query = \App\Models\InterestRequest::with([
                'requester.media',
                'target.media',
                'approver.media'
            ])
            ->whereHas('target', function ($q) {
                $q->where('role', 'investor');
            });

        return $this->processDataTable($request, $query, $columns, function ($request, $index) {
            return [
                $index, // SL
                $request->target->name, // Investor Name
                $request->requester->startupProfile->company_name ?? $request->requester->name, // Startup Company Name
                $request->created_at->format('M d, Y H:i'), // Request Date & Time
                $this->getStatusBadge($request->status), // Status
                $this->getActionButtons('investor-request', $request) // Action
            ];
        });
    }

    /**
     * Apply search filtering for investor requests
     */
    protected function applySearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('status', 'LIKE', "%{$search}%")
              ->orWhere('message', 'LIKE', "%{$search}%")
              ->orWhereHas('requester', function ($requester) use ($search) {
                  $requester->where('name', 'LIKE', "%{$search}%")
                           ->orWhere('email', 'LIKE', "%{$search}%");
              })
              ->orWhereHas('target', function ($target) use ($search) {
                  $target->where('name', 'LIKE', "%{$search}%")
                        ->orWhere('email', 'LIKE', "%{$search}%");
              });
        });
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(\App\Models\InterestRequest $investorRequest)
    {
        // Ensure this is actually an investor request (target is an investor)
        if ($investorRequest->target->role !== 'investor') {
            abort(404, 'Investor request not found');
        }

        $breadcrumbsItems = [
            [
                'name' => 'Investor Request',
                'url' => route('investor-request.index'),
                'active' => false
            ],
            [
                'name' => 'Request Details',
                'url' => null,
                'active' => true
            ],
        ];

        // Load comprehensive request data
        $investorRequest->load([
            'requester.media',
            'requester.startupProfile.esgResponses.esgQuestion',
            'target.media',
            'target.investorProfile',
            'approver.media'
        ]);

        return view('investor-request.show', [
            'pageTitle' => 'Investor Request Details',
            'breadcrumbItems' => $breadcrumbsItems,
            'investorRequest' => $investorRequest
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
