<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class InvestorRequestController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $breadcrumbsItems = [
            [
                'name' => 'Investor Request',
                'url' => 'investor-request',
                'active' => true
            ],
        ];

        // Fetch interest requests where target is an investor
        $investorRequests = \App\Models\InterestRequest::with([
                'requester.media',
                'target.media',
                'approver.media'
            ])
            ->whereHas('target', function ($query) {
                $query->where('role', 'investor');
            })
            ->latest()
            ->get();

        return view('investor-request.index', [
            'pageTitle' => 'Investor Request',
            'breadcrumbItems' => $breadcrumbsItems,
            'investorRequests' => $investorRequests
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(\App\Models\InterestRequest $investorRequest)
    {
        // Ensure this is actually an investor request (target is an investor)
        if ($investorRequest->target->role !== 'investor') {
            abort(404, 'Investor request not found');
        }

        $breadcrumbsItems = [
            [
                'name' => 'Investor Request',
                'url' => route('investor-request.index'),
                'active' => false
            ],
            [
                'name' => 'Request Details',
                'url' => null,
                'active' => true
            ],
        ];

        // Load comprehensive request data
        $investorRequest->load([
            'requester.media',
            'requester.startupProfile.esgResponses.esgQuestion',
            'target.media',
            'target.investorProfile',
            'approver.media'
        ]);

        return view('investor-request.show', [
            'pageTitle' => 'Investor Request Details',
            'breadcrumbItems' => $breadcrumbsItems,
            'investorRequest' => $investorRequest
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
