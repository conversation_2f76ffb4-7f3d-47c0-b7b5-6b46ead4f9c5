<?php

namespace App\Services;

use App\Models\Refund;
use App\Models\Payment;
use App\Models\UserSubscription;
use App\Models\Invoice;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Stripe\Stripe;
use Stripe\Refund as StripeRefund;

class RefundService
{
    protected StripeService $stripeService;

    public function __construct(StripeService $stripeService)
    {
        $this->stripeService = $stripeService;
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    /**
     * Calculate prorated refund amount for subscription cancellation
     */
    public function calculateProratedRefund(UserSubscription $subscription, Carbon $cancellationDate = null): array
    {
        $cancellationDate = $cancellationDate ?? now();
        
        // Get the current billing period
        $billingStart = $subscription->current_period_start;
        $billingEnd = $subscription->current_period_end;
        
        if (!$billingStart || !$billingEnd) {
            throw new \Exception('Subscription billing period not found');
        }

        // Calculate days
        $totalDays = $billingStart->diffInDays($billingEnd);
        $daysUsed = $billingStart->diffInDays($cancellationDate);
        $unusedDays = max(0, $totalDays - $daysUsed);

        // Get the last payment for this subscription
        $lastPayment = $subscription->payments()
            ->where('status', 'succeeded')
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$lastPayment) {
            throw new \Exception('No successful payment found for subscription');
        }

        $originalAmount = $lastPayment->amount; // in cents
        $proratedAmount = 0;

        if ($unusedDays > 0) {
            $proratedAmount = round(($unusedDays / $totalDays) * $originalAmount);
        }

        return [
            'original_amount' => $originalAmount,
            'prorated_amount' => $proratedAmount,
            'total_days' => $totalDays,
            'days_used' => $daysUsed,
            'unused_days' => $unusedDays,
            'proration_percentage' => $unusedDays > 0 ? ($unusedDays / $totalDays) * 100 : 0,
            'service_start_date' => $billingStart->toDateString(),
            'service_end_date' => $billingEnd->toDateString(),
            'cancellation_date' => $cancellationDate->toDateString(),
            'last_payment' => $lastPayment,
        ];
    }

    /**
     * Process a prorated refund for subscription cancellation
     */
    public function processProratedRefund(
        UserSubscription $subscription, 
        string $reason = 'subscription_cancellation',
        Carbon $cancellationDate = null,
        ?User $processedBy = null
    ): Refund {
        return DB::transaction(function () use ($subscription, $reason, $cancellationDate, $processedBy) {
            $calculation = $this->calculateProratedRefund($subscription, $cancellationDate);
            
            if ($calculation['prorated_amount'] <= 0) {
                throw new \Exception('No refund amount calculated - service period may be fully used');
            }

            $lastPayment = $calculation['last_payment'];

            // Create Stripe refund
            $stripeRefund = StripeRefund::create([
                'payment_intent' => $lastPayment->stripe_payment_intent_id,
                'amount' => $calculation['prorated_amount'],
                'reason' => $this->mapReasonToStripe($reason),
                'metadata' => [
                    'subscription_id' => $subscription->id,
                    'user_id' => $subscription->user_id,
                    'type' => 'prorated',
                    'days_used' => $calculation['days_used'],
                    'total_days' => $calculation['total_days'],
                ],
            ]);

            // Create refund record
            $refund = Refund::create([
                'user_id' => $subscription->user_id,
                'payment_id' => $lastPayment->id,
                'user_subscription_id' => $subscription->id,
                'stripe_refund_id' => $stripeRefund->id,
                'stripe_payment_intent_id' => $lastPayment->stripe_payment_intent_id,
                'stripe_charge_id' => $stripeRefund->charge,
                'amount' => $calculation['prorated_amount'],
                'original_amount' => $calculation['original_amount'],
                'prorated_amount' => $calculation['prorated_amount'],
                'currency' => $lastPayment->currency,
                'type' => 'prorated',
                'reason' => $reason,
                'status' => $stripeRefund->status,
                'proration_details' => $calculation,
                'service_start_date' => $calculation['service_start_date'],
                'service_end_date' => $calculation['service_end_date'],
                'cancellation_date' => $calculation['cancellation_date'],
                'days_used' => $calculation['days_used'],
                'total_days' => $calculation['total_days'],
                'description' => "Prorated refund for subscription cancellation",
                'processed_by' => $processedBy?->id,
                'processed_at' => now(),
                'stripe_created_at' => Carbon::createFromTimestamp($stripeRefund->created),
                'metadata' => [
                    'stripe_refund' => $stripeRefund->toArray(),
                ],
            ]);

            Log::info('Prorated refund processed', [
                'refund_id' => $refund->id,
                'stripe_refund_id' => $stripeRefund->id,
                'user_id' => $subscription->user_id,
                'subscription_id' => $subscription->id,
                'amount' => $calculation['prorated_amount'],
                'proration_percentage' => $calculation['proration_percentage'],
            ]);

            return $refund;
        });
    }

    /**
     * Process a full refund
     */
    public function processFullRefund(
        Payment $payment,
        string $reason = 'requested_by_customer',
        ?string $description = null,
        ?User $processedBy = null
    ): Refund {
        return DB::transaction(function () use ($payment, $reason, $description, $processedBy) {
            // Create Stripe refund
            $stripeRefund = StripeRefund::create([
                'payment_intent' => $payment->stripe_payment_intent_id,
                'reason' => $this->mapReasonToStripe($reason),
                'metadata' => [
                    'payment_id' => $payment->id,
                    'user_id' => $payment->user_id,
                    'type' => 'full',
                ],
            ]);

            // Create refund record
            $refund = Refund::create([
                'user_id' => $payment->user_id,
                'payment_id' => $payment->id,
                'user_subscription_id' => $payment->user_subscription_id,
                'stripe_refund_id' => $stripeRefund->id,
                'stripe_payment_intent_id' => $payment->stripe_payment_intent_id,
                'stripe_charge_id' => $stripeRefund->charge,
                'amount' => $payment->amount,
                'original_amount' => $payment->amount,
                'currency' => $payment->currency,
                'type' => 'full',
                'reason' => $reason,
                'status' => $stripeRefund->status,
                'description' => $description ?? "Full refund for payment",
                'processed_by' => $processedBy?->id,
                'processed_at' => now(),
                'stripe_created_at' => Carbon::createFromTimestamp($stripeRefund->created),
                'metadata' => [
                    'stripe_refund' => $stripeRefund->toArray(),
                ],
            ]);

            Log::info('Full refund processed', [
                'refund_id' => $refund->id,
                'stripe_refund_id' => $stripeRefund->id,
                'payment_id' => $payment->id,
                'amount' => $payment->amount,
            ]);

            return $refund;
        });
    }

    /**
     * Process a partial refund
     */
    public function processPartialRefund(
        Payment $payment,
        int $amount, // in cents
        string $reason = 'requested_by_customer',
        ?string $description = null,
        ?User $processedBy = null
    ): Refund {
        if ($amount <= 0 || $amount > $payment->amount) {
            throw new \Exception('Invalid refund amount');
        }

        return DB::transaction(function () use ($payment, $amount, $reason, $description, $processedBy) {
            // Create Stripe refund
            $stripeRefund = StripeRefund::create([
                'payment_intent' => $payment->stripe_payment_intent_id,
                'amount' => $amount,
                'reason' => $this->mapReasonToStripe($reason),
                'metadata' => [
                    'payment_id' => $payment->id,
                    'user_id' => $payment->user_id,
                    'type' => 'partial',
                ],
            ]);

            // Create refund record
            $refund = Refund::create([
                'user_id' => $payment->user_id,
                'payment_id' => $payment->id,
                'user_subscription_id' => $payment->user_subscription_id,
                'stripe_refund_id' => $stripeRefund->id,
                'stripe_payment_intent_id' => $payment->stripe_payment_intent_id,
                'stripe_charge_id' => $stripeRefund->charge,
                'amount' => $amount,
                'original_amount' => $payment->amount,
                'currency' => $payment->currency,
                'type' => 'partial',
                'reason' => $reason,
                'status' => $stripeRefund->status,
                'description' => $description ?? "Partial refund for payment",
                'processed_by' => $processedBy?->id,
                'processed_at' => now(),
                'stripe_created_at' => Carbon::createFromTimestamp($stripeRefund->created),
                'metadata' => [
                    'stripe_refund' => $stripeRefund->toArray(),
                ],
            ]);

            Log::info('Partial refund processed', [
                'refund_id' => $refund->id,
                'stripe_refund_id' => $stripeRefund->id,
                'payment_id' => $payment->id,
                'amount' => $amount,
                'original_amount' => $payment->amount,
            ]);

            return $refund;
        });
    }

    /**
     * Map internal reason to Stripe reason
     */
    protected function mapReasonToStripe(string $reason): string
    {
        return match($reason) {
            'duplicate' => 'duplicate',
            'fraudulent' => 'fraudulent',
            'subscription_cancellation',
            'prorated_downgrade',
            'prorated_cancellation',
            'requested_by_customer' => 'requested_by_customer',
            default => 'requested_by_customer',
        };
    }

    /**
     * Get refund statistics for a user
     */
    public function getRefundStatistics(User $user): array
    {
        $refunds = $user->refunds();

        return [
            'total_refunds' => $refunds->count(),
            'total_amount' => $refunds->successful()->sum('amount'),
            'successful_refunds' => $refunds->successful()->count(),
            'pending_refunds' => $refunds->pending()->count(),
            'prorated_refunds' => $refunds->where('type', 'prorated')->count(),
            'full_refunds' => $refunds->where('type', 'full')->count(),
            'partial_refunds' => $refunds->where('type', 'partial')->count(),
        ];
    }

    /**
     * Get refund audit trail
     */
    public function getRefundAuditTrail(Refund $refund): array
    {
        return [
            'refund_id' => $refund->id,
            'created_at' => $refund->created_at,
            'processed_by' => $refund->processedBy?->name ?? 'System',
            'stripe_refund_id' => $refund->stripe_refund_id,
            'amount' => $refund->amount,
            'type' => $refund->type,
            'reason' => $refund->reason,
            'status' => $refund->status,
            'proration_details' => $refund->proration_details,
            'metadata' => $refund->metadata,
        ];
    }
}
