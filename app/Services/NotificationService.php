<?php

namespace App\Services;

use App\Models\User;
use App\Models\Payment;
use App\Models\Invoice;
use App\Models\UserSubscription;
use App\Notifications\PaymentFailedNotification;
use App\Notifications\PaymentRequiresActionNotification;
use App\Notifications\PaymentSucceededNotification;
use App\Notifications\InvoiceGeneratedNotification;
use App\Notifications\AccountStatusChangedNotification;
use App\Notifications\SubscriptionCancelledNotification;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    /**
     * Send payment failed notification
     */
    public function sendPaymentFailedNotification(User $user, Payment $payment, string $failureReason = ''): void
    {
        try {
            $user->notify(new PaymentFailedNotification($payment, $failureReason));
            
            Log::info('Payment failed notification sent', [
                'user_id' => $user->id,
                'payment_id' => $payment->id,
                'failure_reason' => $failureReason
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send payment failed notification', [
                'user_id' => $user->id,
                'payment_id' => $payment->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send payment requires action notification (3D Secure)
     */
    public function sendPaymentRequiresActionNotification(User $user, Payment $payment, string $actionUrl): void
    {
        try {
            $user->notify(new PaymentRequiresActionNotification($payment, $actionUrl));
            
            Log::info('Payment requires action notification sent', [
                'user_id' => $user->id,
                'payment_id' => $payment->id,
                'action_url' => $actionUrl
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send payment requires action notification', [
                'user_id' => $user->id,
                'payment_id' => $payment->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send payment succeeded notification
     */
    public function sendPaymentSucceededNotification(User $user, Payment $payment): void
    {
        try {
            $user->notify(new PaymentSucceededNotification($payment));
            
            Log::info('Payment succeeded notification sent', [
                'user_id' => $user->id,
                'payment_id' => $payment->id
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send payment succeeded notification', [
                'user_id' => $user->id,
                'payment_id' => $payment->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send invoice generated notification
     */
    public function sendInvoiceGeneratedNotification(User $user, Invoice $invoice): void
    {
        try {
            $user->notify(new InvoiceGeneratedNotification($invoice));
            
            Log::info('Invoice generated notification sent', [
                'user_id' => $user->id,
                'invoice_id' => $invoice->id
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send invoice generated notification', [
                'user_id' => $user->id,
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send account status changed notification
     */
    public function sendAccountStatusChangedNotification(User $user, string $newStatus, string $reason = '', ?string $actionUrl = null): void
    {
        try {
            $user->notify(new AccountStatusChangedNotification($newStatus, $reason, $actionUrl));
            
            Log::info('Account status changed notification sent', [
                'user_id' => $user->id,
                'new_status' => $newStatus,
                'reason' => $reason
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send account status changed notification', [
                'user_id' => $user->id,
                'new_status' => $newStatus,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send subscription cancelled notification
     */
    public function sendSubscriptionCancelledNotification(User $user, UserSubscription $subscription, string $reason = ''): void
    {
        try {
            $user->notify(new SubscriptionCancelledNotification($subscription, $reason));
            
            Log::info('Subscription cancelled notification sent', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'reason' => $reason
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send subscription cancelled notification', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send bulk notifications to multiple users
     */
    public function sendBulkNotification(array $users, $notification): void
    {
        try {
            foreach ($users as $user) {
                if ($user instanceof User) {
                    $user->notify($notification);
                }
            }
            
            Log::info('Bulk notification sent', [
                'user_count' => count($users),
                'notification_class' => get_class($notification)
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send bulk notification', [
                'user_count' => count($users),
                'notification_class' => get_class($notification),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get user's unread notifications
     */
    public function getUserUnreadNotifications(User $user, int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return $user->unreadNotifications()
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Mark notification as read
     */
    public function markNotificationAsRead(User $user, string $notificationId): bool
    {
        try {
            $notification = $user->unreadNotifications()->find($notificationId);
            
            if ($notification) {
                $notification->markAsRead();
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            Log::error('Failed to mark notification as read', [
                'user_id' => $user->id,
                'notification_id' => $notificationId,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Mark all notifications as read for a user
     */
    public function markAllNotificationsAsRead(User $user): bool
    {
        try {
            $user->unreadNotifications->markAsRead();
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to mark all notifications as read', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }
}
