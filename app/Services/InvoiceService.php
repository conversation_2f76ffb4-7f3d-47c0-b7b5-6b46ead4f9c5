<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\UserSubscription;
use App\Models\User;
use App\Notifications\InvoiceGeneratedNotification;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class InvoiceService
{
    protected StripeService $stripeService;

    public function __construct(StripeService $stripeService)
    {
        $this->stripeService = $stripeService;
    }

    /**
     * Generate invoice for a subscription automatically
     */
    public function generateInvoiceForSubscription(UserSubscription $subscription): ?Invoice
    {
        try {
            // Get the latest Stripe invoice for this subscription
            $stripeInvoices = $this->stripeService->getSubscriptionInvoices($subscription->stripe_subscription_id, 1);
            
            if (empty($stripeInvoices)) {
                Log::warning('No Stripe invoices found for subscription', [
                    'subscription_id' => $subscription->id,
                    'stripe_subscription_id' => $subscription->stripe_subscription_id
                ]);
                return null;
            }

            $stripeInvoice = $stripeInvoices[0];

            // Check if we already have this invoice
            $existingInvoice = Invoice::where('stripe_invoice_id', $stripeInvoice->id)->first();
            if ($existingInvoice) {
                Log::info('Invoice already exists', [
                    'invoice_id' => $existingInvoice->id,
                    'stripe_invoice_id' => $stripeInvoice->id
                ]);
                return $existingInvoice;
            }

            // Generate custom invoice number
            $customInvoiceNumber = Invoice::generateInvoiceNumber();

            // Create local invoice record
            $invoice = Invoice::create([
                'user_id' => $subscription->user_id,
                'user_subscription_id' => $subscription->id,
                'stripe_invoice_id' => $stripeInvoice->id,
                'invoice_number' => $customInvoiceNumber,
                'subtotal' => ($stripeInvoice->subtotal ?? 0) / 100, // Convert from cents
                'tax_amount' => ($stripeInvoice->tax ?? 0) / 100,
                'total' => ($stripeInvoice->total ?? 0) / 100,
                'currency' => $stripeInvoice->currency ?? 'usd',
                'status' => $stripeInvoice->status ?? 'draft',
                'due_date' => $stripeInvoice->due_date ? Carbon::createFromTimestamp($stripeInvoice->due_date) : null,
                'paid_at' => $stripeInvoice->status === 'paid' && $stripeInvoice->status_transitions?->paid_at
                    ? Carbon::createFromTimestamp($stripeInvoice->status_transitions->paid_at)
                    : null,
                'line_items' => $this->formatLineItems($stripeInvoice->lines->data ?? []),
                'metadata' => [
                    'stripe_invoice_url' => $stripeInvoice->hosted_invoice_url ?? null,
                    'stripe_pdf_url' => $stripeInvoice->invoice_pdf ?? null,
                    'auto_generated' => true,
                    'subscription_period_start' => $stripeInvoice->period_start ? Carbon::createFromTimestamp($stripeInvoice->period_start)->toISOString() : null,
                    'subscription_period_end' => $stripeInvoice->period_end ? Carbon::createFromTimestamp($stripeInvoice->period_end)->toISOString() : null,
                ],
                'pdf_url' => $stripeInvoice->invoice_pdf ?? null,
            ]);

            // Update Stripe invoice with custom invoice number in metadata
            try {
                $this->stripeService->updateInvoice($stripeInvoice->id, [
                    'metadata' => [
                        'custom_invoice_number' => $customInvoiceNumber,
                        'application_invoice_id' => $invoice->id,
                    ]
                ]);
            } catch (\Exception $e) {
                Log::warning('Failed to update Stripe invoice with custom number', [
                    'invoice_id' => $invoice->id,
                    'stripe_invoice_id' => $stripeInvoice->id,
                    'custom_invoice_number' => $customInvoiceNumber,
                    'error' => $e->getMessage()
                ]);
            }

            $invoice->load(['userSubscription.subscriptionProduct']);

            // Send notification to user
            $this->sendInvoiceNotification($invoice);

            Log::info('Invoice generated successfully', [
                'invoice_id' => $invoice->id,
                'user_id' => $subscription->user_id,
                'subscription_id' => $subscription->id,
                'stripe_invoice_id' => $stripeInvoice->id
            ]);

            return $invoice;

        } catch (\Exception $e) {
            Log::error('Failed to generate invoice for subscription', [
                'subscription_id' => $subscription->id,
                'user_id' => $subscription->user_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return null;
        }
    }

    /**
     * Send invoice notification to user
     */
    protected function sendInvoiceNotification(Invoice $invoice): void
    {
        try {
            $user = $invoice->user;
            if ($user) {
                $user->notify(new InvoiceGeneratedNotification($invoice));
                
                Log::info('Invoice notification sent', [
                    'invoice_id' => $invoice->id,
                    'user_id' => $user->id,
                    'user_email' => $user->email
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to send invoice notification', [
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Format Stripe line items for storage
     */
    protected function formatLineItems(array $stripeLineItems): array
    {
        $lineItems = [];

        foreach ($stripeLineItems as $item) {
            $lineItems[] = [
                'description' => $item->description ?? 'Subscription',
                'quantity' => $item->quantity ?? 1,
                'unit_amount' => ($item->price->unit_amount ?? 0) / 100,
                'total_amount' => ($item->amount ?? 0) / 100,
                'currency' => $item->currency ?? 'usd',
                'period_start' => $item->period?->start ? Carbon::createFromTimestamp($item->period->start)->toISOString() : null,
                'period_end' => $item->period?->end ? Carbon::createFromTimestamp($item->period->end)->toISOString() : null,
                'price_id' => $item->price?->id ?? null,
                'product_id' => $item->price?->product ?? null,
            ];
        }

        return $lineItems;
    }

    /**
     * Update invoice from Stripe webhook data
     */
    public function updateInvoiceFromWebhook(object $stripeInvoice): ?Invoice
    {
        try {
            $invoice = Invoice::where('stripe_invoice_id', $stripeInvoice->id)->first();

            if (!$invoice) {
                // Try to create invoice if it doesn't exist and has a subscription
                if ($stripeInvoice->subscription) {
                    $subscription = \App\Models\UserSubscription::where('stripe_subscription_id', $stripeInvoice->subscription)->first();
                    if ($subscription) {
                        Log::info('Creating missing invoice from webhook', [
                            'stripe_invoice_id' => $stripeInvoice->id,
                            'subscription_id' => $subscription->id
                        ]);
                        return $this->createInvoiceFromStripeData($stripeInvoice, $subscription);
                    }
                }

                Log::warning('Invoice not found for webhook update and cannot create', [
                    'stripe_invoice_id' => $stripeInvoice->id
                ]);
                return null;
            }

            // Ensure invoice has a custom number
            if (empty($invoice->invoice_number)) {
                $customInvoiceNumber = Invoice::generateInvoiceNumber();
                $invoice->invoice_number = $customInvoiceNumber;

                // Update Stripe metadata
                try {
                    $this->stripeService->updateInvoice($stripeInvoice->id, [
                        'metadata' => array_merge($stripeInvoice->metadata ?? [], [
                            'custom_invoice_number' => $customInvoiceNumber,
                            'application_invoice_id' => $invoice->id,
                        ])
                    ]);
                } catch (\Exception $e) {
                    Log::warning('Failed to update Stripe invoice metadata in webhook', [
                        'invoice_id' => $invoice->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            $invoice->update([
                'status' => $stripeInvoice->status,
                'paid_at' => $stripeInvoice->status === 'paid' && $stripeInvoice->status_transitions?->paid_at
                    ? Carbon::createFromTimestamp($stripeInvoice->status_transitions->paid_at)
                    : null,
                'pdf_url' => $stripeInvoice->invoice_pdf ?? $invoice->pdf_url,
                'metadata' => array_merge($invoice->metadata ?? [], [
                    'stripe_invoice_url' => $stripeInvoice->hosted_invoice_url ?? null,
                    'stripe_pdf_url' => $stripeInvoice->invoice_pdf ?? null,
                    'last_webhook_update' => now()->toISOString(),
                ]),
            ]);

            Log::info('Invoice updated from webhook', [
                'invoice_id' => $invoice->id,
                'stripe_invoice_id' => $stripeInvoice->id,
                'new_status' => $stripeInvoice->status,
                'invoice_number' => $invoice->invoice_number
            ]);

            return $invoice;

        } catch (\Exception $e) {
            Log::error('Failed to update invoice from webhook', [
                'stripe_invoice_id' => $stripeInvoice->id,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Create invoice from Stripe webhook data
     */
    private function createInvoiceFromStripeData(object $stripeInvoice, \App\Models\UserSubscription $subscription): Invoice
    {
        $customInvoiceNumber = Invoice::generateInvoiceNumber();

        $invoice = Invoice::create([
            'user_id' => $subscription->user_id,
            'user_subscription_id' => $subscription->id,
            'stripe_invoice_id' => $stripeInvoice->id,
            'invoice_number' => $customInvoiceNumber,
            'subtotal' => ($stripeInvoice->subtotal ?? 0) / 100,
            'tax_amount' => ($stripeInvoice->tax ?? 0) / 100,
            'total' => ($stripeInvoice->total ?? 0) / 100,
            'currency' => $stripeInvoice->currency ?? 'usd',
            'status' => $stripeInvoice->status ?? 'draft',
            'due_date' => $stripeInvoice->due_date ? Carbon::createFromTimestamp($stripeInvoice->due_date) : null,
            'paid_at' => $stripeInvoice->status === 'paid' && $stripeInvoice->status_transitions?->paid_at
                ? Carbon::createFromTimestamp($stripeInvoice->status_transitions->paid_at)
                : null,
            'line_items' => $this->formatLineItems($stripeInvoice->lines->data ?? []),
            'metadata' => [
                'stripe_invoice_url' => $stripeInvoice->hosted_invoice_url ?? null,
                'stripe_pdf_url' => $stripeInvoice->invoice_pdf ?? null,
                'created_from_webhook' => true,
                'webhook_created_at' => now()->toISOString(),
            ],
            'pdf_url' => $stripeInvoice->invoice_pdf ?? null,
        ]);

        // Update Stripe invoice with custom invoice number
        try {
            $this->stripeService->updateInvoice($stripeInvoice->id, [
                'metadata' => array_merge($stripeInvoice->metadata ?? [], [
                    'custom_invoice_number' => $customInvoiceNumber,
                    'application_invoice_id' => $invoice->id,
                ])
            ]);
        } catch (\Exception $e) {
            Log::warning('Failed to update Stripe invoice with custom number from webhook', [
                'invoice_id' => $invoice->id,
                'stripe_invoice_id' => $stripeInvoice->id,
                'error' => $e->getMessage()
            ]);
        }

        return $invoice;
    }
}
