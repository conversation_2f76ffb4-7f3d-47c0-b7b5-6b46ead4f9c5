<?php

namespace App\Services;

use App\Models\User;
use App\Services\NotificationService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class AccountStatusService
{
    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Lock a user account
     */
    public function lockAccount(User $user, string $reason = '', ?User $lockedBy = null): bool
    {
        try {
            DB::beginTransaction();

            $oldStatus = $user->account_status_string;

            // Use the User model's lockAccount method which handles the relationship
            $user->lockAccount($reason, $lockedBy?->id);

            // Log the account lock
            Log::warning('Account locked', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'reason' => $reason,
                'locked_by' => $lockedBy?->id,
                'previous_status' => $oldStatus,
            ]);

            // Send notification
            $this->notificationService->sendAccountStatusChangedNotification(
                $user, 
                'locked', 
                $reason,
                route('contact.support')
            );

            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to lock account', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'reason' => $reason,
            ]);

            return false;
        }
    }

    /**
     * Unlock a user account
     */
    public function unlockAccount(User $user, string $reason = '', ?User $unlockedBy = null): bool
    {
        try {
            DB::beginTransaction();

            $oldStatus = $user->account_status;
            
            $user->update([
                'account_status' => 'active',
                'account_locked_at' => null,
                'account_locked_reason' => null,
                'account_locked_by' => null,
                'account_unlocked_at' => now(),
                'account_unlocked_by' => $unlockedBy?->id,
            ]);

            // Log the account unlock
            Log::info('Account unlocked', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'reason' => $reason,
                'unlocked_by' => $unlockedBy?->id,
                'previous_status' => $oldStatus,
            ]);

            // Send notification
            $this->notificationService->sendAccountStatusChangedNotification(
                $user, 
                'unlocked', 
                $reason,
                route('dashboard')
            );

            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to unlock account', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'reason' => $reason,
            ]);

            return false;
        }
    }

    /**
     * Suspend a user account
     */
    public function suspendAccount(User $user, string $reason = '', ?User $suspendedBy = null): bool
    {
        try {
            DB::beginTransaction();

            $oldStatus = $user->account_status_string;

            // Create or update account status record
            if (!$user->accountStatus) {
                $user->accountStatus()->create(['user_id' => $user->id]);
                $user->load('accountStatus');
            }

            $user->accountStatus->suspend($reason, $suspendedBy?->id);

            // Log the account suspension
            Log::warning('Account suspended', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'reason' => $reason,
                'suspended_by' => $suspendedBy?->id,
                'previous_status' => $oldStatus,
            ]);

            // Send notification
            $this->notificationService->sendAccountStatusChangedNotification(
                $user, 
                'suspended', 
                $reason,
                route('contact.support')
            );

            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to suspend account', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'reason' => $reason,
            ]);

            return false;
        }
    }

    /**
     * Activate a user account
     */
    public function activateAccount(User $user, string $reason = '', ?User $activatedBy = null): bool
    {
        try {
            DB::beginTransaction();

            $oldStatus = $user->account_status;
            
            $user->update([
                'account_status' => 'active',
                'account_suspended_at' => null,
                'account_suspended_reason' => null,
                'account_suspended_by' => null,
                'account_activated_at' => now(),
                'account_activated_by' => $activatedBy?->id,
            ]);

            // Log the account activation
            Log::info('Account activated', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'reason' => $reason,
                'activated_by' => $activatedBy?->id,
                'previous_status' => $oldStatus,
            ]);

            // Send notification
            $this->notificationService->sendAccountStatusChangedNotification(
                $user, 
                'active', 
                $reason,
                route('dashboard')
            );

            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to activate account', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'reason' => $reason,
            ]);

            return false;
        }
    }

    /**
     * Check if account should be locked due to failed payments
     */
    public function checkAndLockForFailedPayments(User $user): bool
    {
        $failedPaymentsCount = $user->payments()
            ->where('status', 'failed')
            ->where('created_at', '>=', now()->subDays(30))
            ->count();

        $maxFailedPayments = config('subscription.max_failed_payments_before_lock', 3);

        if ($failedPaymentsCount >= $maxFailedPayments) {
            return $this->lockAccount(
                $user, 
                "Account locked due to {$failedPaymentsCount} failed payments in the last 30 days."
            );
        }

        return false;
    }

    /**
     * Check if account should be locked due to overdue invoices
     */
    public function checkAndLockForOverdueInvoices(User $user): bool
    {
        $overdueInvoicesCount = $user->invoices()
            ->where('status', 'open')
            ->where('due_date', '<', now())
            ->count();

        $maxOverdueInvoices = config('subscription.max_overdue_invoices_before_lock', 2);

        if ($overdueInvoicesCount >= $maxOverdueInvoices) {
            return $this->lockAccount(
                $user, 
                "Account locked due to {$overdueInvoicesCount} overdue invoices."
            );
        }

        return false;
    }

    /**
     * Get account status history for a user
     */
    public function getAccountStatusHistory(User $user): array
    {
        $history = [];

        // Add current status
        $history[] = [
            'status' => $user->account_status,
            'timestamp' => $user->updated_at,
            'reason' => $this->getCurrentStatusReason($user),
            'changed_by' => $this->getCurrentStatusChangedBy($user),
        ];

        return $history;
    }

    /**
     * Get current status reason
     */
    protected function getCurrentStatusReason(User $user): ?string
    {
        switch ($user->account_status) {
            case 'locked':
                return $user->account_locked_reason;
            case 'suspended':
                return $user->account_suspended_reason;
            default:
                return null;
        }
    }

    /**
     * Get who changed the current status
     */
    protected function getCurrentStatusChangedBy(User $user): ?User
    {
        switch ($user->account_status) {
            case 'locked':
                return $user->account_locked_by ? User::find($user->account_locked_by) : null;
            case 'suspended':
                return $user->account_suspended_by ? User::find($user->account_suspended_by) : null;
            default:
                return null;
        }
    }

    /**
     * Bulk lock accounts
     */
    public function bulkLockAccounts(array $userIds, string $reason = '', ?User $lockedBy = null): array
    {
        $results = ['success' => 0, 'failed' => 0, 'errors' => []];

        foreach ($userIds as $userId) {
            $user = User::find($userId);
            
            if (!$user) {
                $results['failed']++;
                $results['errors'][] = "User with ID {$userId} not found";
                continue;
            }

            if ($this->lockAccount($user, $reason, $lockedBy)) {
                $results['success']++;
            } else {
                $results['failed']++;
                $results['errors'][] = "Failed to lock account for user {$user->email}";
            }
        }

        return $results;
    }
}
