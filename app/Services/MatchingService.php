<?php

namespace App\Services;

use App\Models\InvestorProfile;
use App\Models\StartupProfile;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;

class MatchingService
{
    /**
     * Find matching startups for an investor
     */
    public function findMatchingStartups(User $investor, array $filters = []): Collection
    {
        if (!$investor->isInvestor() || !$investor->investorProfile) {
            return collect();
        }

        $investorProfile = $investor->investorProfile;
        $userCategories = $investor->categories->pluck('id')->toArray();

        $query = StartupProfile::with(['user', 'user.categories'])
            ->completed()
            ->esgCompleted();

        // Apply investor preferences
        $this->applyInvestorFilters($query, $investorProfile, $filters);

        // Apply category matching
        if (!empty($userCategories)) {
            $query->whereHas('user.categories', function ($q) use ($userCategories) {
                $q->whereIn('categories.id', $userCategories);
            });
        }

        // Exclude already contacted startups
        $contactedStartupIds = $investor->sentInterestRequests()
            ->where('type', 'investment_interest')
            ->pluck('target_id')
            ->toArray();

        if (!empty($contactedStartupIds)) {
            $query->whereNotIn('user_id', $contactedStartupIds);
        }

        $startups = $query->get();

        // Calculate match scores and sort
        return $startups->map(function ($startup) use ($investorProfile, $userCategories) {
            $startup->match_score = $this->calculateStartupMatchScore($startup, $investorProfile, $userCategories);
            return $startup;
        })->sortByDesc('match_score');
    }

    /**
     * Find matching investors for a startup
     */
    public function findMatchingInvestors(User $startup, array $filters = []): Collection
    {
        if (!$startup->isStartup() || !$startup->startupProfile) {
            return collect();
        }

        $startupProfile = $startup->startupProfile;
        $userCategories = $startup->categories->pluck('id')->toArray();

        $query = InvestorProfile::with(['user', 'user.categories'])
            ->completed();

        // Apply startup preferences
        $this->applyStartupFilters($query, $startupProfile, $filters);

        // Apply category matching
        if (!empty($userCategories)) {
            $query->whereHas('user.categories', function ($q) use ($userCategories) {
                $q->whereIn('categories.id', $userCategories);
            });
        }

        // Exclude already contacted investors
        $contactedInvestorIds = $startup->sentInterestRequests()
            ->where('type', 'funding_request')
            ->pluck('target_id')
            ->toArray();

        if (!empty($contactedInvestorIds)) {
            $query->whereNotIn('user_id', $contactedInvestorIds);
        }

        $investors = $query->get();

        // Calculate match scores and sort
        return $investors->map(function ($investor) use ($startupProfile, $userCategories) {
            $investor->match_score = $this->calculateInvestorMatchScore($investor, $startupProfile, $userCategories);
            return $investor;
        })->sortByDesc('match_score');
    }

    /**
     * Apply investor-specific filters
     */
    private function applyInvestorFilters($query, InvestorProfile $investorProfile, array $filters)
    {
        // Filter by funding amount
        if ($investorProfile->investment_budget_min && $investorProfile->investment_budget_max) {
            $query->seekingFunding(
                $investorProfile->investment_budget_min,
                $investorProfile->investment_budget_max
            );
        }

        // Filter by ESG score
        if (isset($filters['min_esg_score'])) {
            $query->byEsgScore($filters['min_esg_score']);
        }

        // Filter by funding stage
        if (isset($filters['funding_stages'])) {
            $stages = is_array($filters['funding_stages']) 
                ? $filters['funding_stages'] 
                : explode(',', $filters['funding_stages']);
            $query->whereIn('funding_stage', $stages);
        }

        // Filter by employee count
        if (isset($filters['min_employees'])) {
            $query->where('employee_count', '>=', $filters['min_employees']);
        }

        if (isset($filters['max_employees'])) {
            $query->where('employee_count', '<=', $filters['max_employees']);
        }
    }

    /**
     * Apply startup-specific filters
     */
    private function applyStartupFilters($query, StartupProfile $startupProfile, array $filters)
    {
        // Filter by investment budget that covers startup's funding needs
        if ($startupProfile->funding_amount_sought) {
            $query->withinBudget(0, $startupProfile->funding_amount_sought);
        }

        // Filter by risk tolerance
        if (isset($filters['risk_tolerance'])) {
            $query->byRiskTolerance($filters['risk_tolerance']);
        }

        // Filter by experience level
        if (isset($filters['experience_levels'])) {
            $levels = is_array($filters['experience_levels']) 
                ? $filters['experience_levels'] 
                : explode(',', $filters['experience_levels']);
            $query->whereIn('investment_experience', $levels);
        }

        // Filter by minimum budget
        if (isset($filters['min_budget'])) {
            $query->where('investment_budget_min', '>=', $filters['min_budget']);
        }
    }

    /**
     * Calculate match score for startup
     */
    public function calculateStartupMatchScore(StartupProfile $startup, InvestorProfile $investorProfile, array $userCategories): float
    {
        $score = 0;
        $maxScore = 100;

        // Category match (40% weight)
        $categoryScore = $this->calculateCategoryMatchScore($startup->user->categories->pluck('id')->toArray(), $userCategories);
        $score += $categoryScore * 0.4;

        // Funding amount compatibility (30% weight)
        $fundingScore = $this->calculateFundingCompatibilityScore(
            $startup->funding_amount_sought,
            $investorProfile->investment_budget_min,
            $investorProfile->investment_budget_max
        );
        $score += $fundingScore * 0.3;

        // ESG score (20% weight)
        $esgScore = $startup->esg_score ? ($startup->esg_score / 100) * 100 : 0;
        $score += $esgScore * 0.2;

        // Profile completeness and quality (10% weight)
        $qualityScore = $this->calculateProfileQualityScore($startup);
        $score += $qualityScore * 0.1;

        return round(min($score, $maxScore), 2);
    }

    /**
     * Calculate match score for investor
     */
    public function calculateInvestorMatchScore(InvestorProfile $investor, StartupProfile $startupProfile, array $userCategories): float
    {
        $score = 0;
        $maxScore = 100;

        // Category match (40% weight)
        $categoryScore = $this->calculateCategoryMatchScore($investor->user->categories->pluck('id')->toArray(), $userCategories);
        $score += $categoryScore * 0.4;

        // Budget compatibility (40% weight)
        $budgetScore = $this->calculateFundingCompatibilityScore(
            $startupProfile->funding_amount_sought,
            $investor->investment_budget_min,
            $investor->investment_budget_max
        );
        $score += $budgetScore * 0.4;

        // Experience level (20% weight)
        $experienceScore = $this->calculateExperienceScore($investor->investment_experience);
        $score += $experienceScore * 0.2;

        return round(min($score, $maxScore), 2);
    }

    /**
     * Calculate category match score
     */
    private function calculateCategoryMatchScore(array $categories1, array $categories2): float
    {
        if (empty($categories1) || empty($categories2)) {
            return 0;
        }

        $intersection = count(array_intersect($categories1, $categories2));
        $union = count(array_unique(array_merge($categories1, $categories2)));

        return $union > 0 ? ($intersection / $union) * 100 : 0;
    }

    /**
     * Calculate funding compatibility score
     */
    private function calculateFundingCompatibilityScore(?float $sought, ?float $minBudget, ?float $maxBudget): float
    {
        if (!$sought || !$minBudget || !$maxBudget) {
            return 50; // Neutral score if data is missing
        }

        if ($sought >= $minBudget && $sought <= $maxBudget) {
            return 100; // Perfect match
        }

        // Calculate proximity score
        $midpoint = ($minBudget + $maxBudget) / 2;
        $distance = abs($sought - $midpoint);
        $range = $maxBudget - $minBudget;

        if ($range == 0) {
            return $sought == $midpoint ? 100 : 0;
        }

        $proximityScore = max(0, 1 - ($distance / $range)) * 100;
        return round($proximityScore, 2);
    }

    /**
     * Calculate profile quality score
     */
    private function calculateProfileQualityScore(StartupProfile $startup): float
    {
        $score = 0;

        // Profile completion
        if ($startup->profile_completed) {
            $score += 40;
        }

        // ESG completion
        if ($startup->esg_completed) {
            $score += 30;
        }

        // Additional information
        if ($startup->website) {
            $score += 10;
        }

        if ($startup->linkedin) {
            $score += 10;
        }

        if ($startup->business_model && !empty($startup->business_model)) {
            $score += 10;
        }

        return $score;
    }

    /**
     * Calculate experience score
     */
    private function calculateExperienceScore(?string $experience): float
    {
        $experienceScores = [
            'beginner' => 25,
            'intermediate' => 50,
            'experienced' => 75,
            'expert' => 100,
        ];

        return $experienceScores[$experience] ?? 50;
    }

    /**
     * Get match explanation
     */
    public function getMatchExplanation(float $score): array
    {
        if ($score >= 80) {
            return [
                'level' => 'Excellent Match',
                'description' => 'Highly compatible based on categories, funding requirements, and profile quality.',
                'color' => 'green',
            ];
        } elseif ($score >= 60) {
            return [
                'level' => 'Good Match',
                'description' => 'Strong compatibility with good alignment in key areas.',
                'color' => 'blue',
            ];
        } elseif ($score >= 40) {
            return [
                'level' => 'Fair Match',
                'description' => 'Moderate compatibility with some alignment in key areas.',
                'color' => 'yellow',
            ];
        } else {
            return [
                'level' => 'Poor Match',
                'description' => 'Limited compatibility with minimal alignment.',
                'color' => 'red',
            ];
        }
    }
}
