<?php

namespace App\Services;

use App\Models\EsgQuestion;
use App\Models\EsgResponse;
use App\Models\StartupProfile;

class EsgScoringService
{
    /**
     * Calculate ESG score for a startup profile
     */
    public function calculateScore(StartupProfile $startupProfile): array
    {
        $responses = $startupProfile->esgResponses()->with('esgQuestion')->get();
        
        if ($responses->isEmpty()) {
            return [
                'overall_score' => 0,
                'category_scores' => [
                    'environmental' => 0,
                    'social' => 0,
                    'governance' => 0,
                ],
                'total_questions' => 0,
                'answered_questions' => 0,
            ];
        }

        $totalScore = 0;
        $totalWeight = 0;
        $categoryScores = [
            'environmental' => ['score' => 0, 'weight' => 0, 'count' => 0],
            'social' => ['score' => 0, 'weight' => 0, 'count' => 0],
            'governance' => ['score' => 0, 'weight' => 0, 'count' => 0],
        ];

        foreach ($responses as $response) {
            $question = $response->esgQuestion;
            $score = $this->calculateResponseScore($response, $question);
            
            $totalScore += $score * $question->weight;
            $totalWeight += $question->weight;
            
            $categoryScores[$question->category]['score'] += $score * $question->weight;
            $categoryScores[$question->category]['weight'] += $question->weight;
            $categoryScores[$question->category]['count']++;
        }

        // Calculate overall score (0-100)
        $overallScore = $totalWeight > 0 ? ($totalScore / $totalWeight) * 20 : 0; // Scale to 100

        // Calculate category scores
        $categoryBreakdown = [];
        foreach ($categoryScores as $category => $data) {
            $categoryBreakdown[$category] = $data['weight'] > 0 ? 
                ($data['score'] / $data['weight']) * 20 : 0; // Scale to 100
        }

        $totalQuestions = EsgQuestion::active()->count();
        $answeredQuestions = $responses->count();

        return [
            'overall_score' => round($overallScore, 2),
            'category_scores' => array_map(function($score) {
                return round($score, 2);
            }, $categoryBreakdown),
            'total_questions' => $totalQuestions,
            'answered_questions' => $answeredQuestions,
            'completion_percentage' => $totalQuestions > 0 ? round(($answeredQuestions / $totalQuestions) * 100, 2) : 0,
        ];
    }

    /**
     * Calculate score for individual response
     */
    public function calculateResponseScore(EsgResponse $response, EsgQuestion $question): float
    {
        switch ($question->type) {
            case 'yes_no':
                return $this->calculateYesNoScore($response->response_value);
            
            case 'scale':
                return $this->calculateScaleScore($response->response_value);
            
            case 'multiple_choice':
                return $this->calculateMultipleChoiceScore($response->response_value, $question->options);
            
            case 'text':
                return $this->calculateTextScore($response->response_value);
            
            default:
                return 3; // Default middle score
        }
    }

    /**
     * Calculate score for yes/no questions
     */
    private function calculateYesNoScore(string $response): float
    {
        $response = strtolower(trim($response));
        
        // Positive responses get higher scores
        if (in_array($response, ['yes', 'true', '1', 'y'])) {
            return 5.0;
        }
        
        if (in_array($response, ['no', 'false', '0', 'n'])) {
            return 1.0;
        }
        
        return 3.0; // Default for unclear responses
    }

    /**
     * Calculate score for scale questions (1-5 or 1-10)
     */
    private function calculateScaleScore(string $response): float
    {
        // Extract numeric value from response
        preg_match('/(\d+)/', $response, $matches);
        
        if (!isset($matches[1])) {
            return 3.0; // Default middle score
        }
        
        $value = (float) $matches[1];
        
        // Determine scale range from response
        if ($value <= 5) {
            // Assume 1-5 scale
            return $value;
        } elseif ($value <= 10) {
            // Assume 1-10 scale, convert to 1-5
            return ($value / 10) * 5;
        } else {
            // Assume percentage, convert to 1-5
            return ($value / 100) * 5;
        }
    }

    /**
     * Calculate score for multiple choice questions
     */
    private function calculateMultipleChoiceScore(string $response, ?array $options): float
    {
        if (!$options || empty($options)) {
            return 3.0; // Default middle score
        }
        
        $index = array_search($response, $options);
        
        if ($index === false) {
            return 3.0; // Default if response not found in options
        }
        
        // Score based on position in options array
        // Assume options are ordered from worst to best
        $optionCount = count($options);
        return (($index + 1) / $optionCount) * 5;
    }

    /**
     * Calculate score for text responses
     */
    private function calculateTextScore(string $response): float
    {
        $response = strtolower(trim($response));
        
        if (empty($response)) {
            return 1.0; // No response
        }
        
        // Simple keyword-based scoring
        $positiveKeywords = [
            'excellent', 'outstanding', 'comprehensive', 'robust', 'strong',
            'effective', 'sustainable', 'responsible', 'ethical', 'transparent',
            'innovative', 'proactive', 'committed', 'dedicated', 'extensive'
        ];
        
        $negativeKeywords = [
            'none', 'no', 'minimal', 'limited', 'poor', 'weak',
            'inadequate', 'insufficient', 'lacking', 'basic', 'simple'
        ];
        
        $positiveCount = 0;
        $negativeCount = 0;
        
        foreach ($positiveKeywords as $keyword) {
            if (strpos($response, $keyword) !== false) {
                $positiveCount++;
            }
        }
        
        foreach ($negativeKeywords as $keyword) {
            if (strpos($response, $keyword) !== false) {
                $negativeCount++;
            }
        }
        
        // Calculate score based on keyword analysis
        if ($positiveCount > $negativeCount) {
            return min(5.0, 3.0 + ($positiveCount * 0.5));
        } elseif ($negativeCount > $positiveCount) {
            return max(1.0, 3.0 - ($negativeCount * 0.5));
        }
        
        // Default middle score for neutral responses
        return 3.0;
    }

    /**
     * Get ESG score interpretation
     */
    public function getScoreInterpretation(float $score): array
    {
        if ($score >= 80) {
            return [
                'level' => 'Excellent',
                'description' => 'Outstanding ESG performance with comprehensive sustainability practices.',
                'color' => 'green',
            ];
        } elseif ($score >= 60) {
            return [
                'level' => 'Good',
                'description' => 'Strong ESG performance with well-developed sustainability practices.',
                'color' => 'blue',
            ];
        } elseif ($score >= 40) {
            return [
                'level' => 'Fair',
                'description' => 'Moderate ESG performance with room for improvement in sustainability practices.',
                'color' => 'yellow',
            ];
        } else {
            return [
                'level' => 'Poor',
                'description' => 'Limited ESG performance requiring significant improvement in sustainability practices.',
                'color' => 'red',
            ];
        }
    }

    /**
     * Get category-specific recommendations
     */
    public function getCategoryRecommendations(array $categoryScores): array
    {
        $recommendations = [];
        
        foreach ($categoryScores as $category => $score) {
            if ($score < 60) {
                $recommendations[$category] = $this->getCategorySpecificRecommendations($category, $score);
            }
        }
        
        return $recommendations;
    }

    /**
     * Get specific recommendations for a category
     */
    private function getCategorySpecificRecommendations(string $category, float $score): array
    {
        $recommendations = [
            'environmental' => [
                'Implement energy efficiency measures',
                'Develop waste reduction programs',
                'Set carbon emission reduction targets',
                'Adopt renewable energy sources',
                'Implement water conservation practices',
            ],
            'social' => [
                'Enhance employee diversity and inclusion programs',
                'Improve workplace safety measures',
                'Develop community engagement initiatives',
                'Implement fair labor practices',
                'Enhance employee training and development',
            ],
            'governance' => [
                'Strengthen board independence and diversity',
                'Improve transparency in reporting',
                'Enhance risk management frameworks',
                'Implement ethical business practices',
                'Develop stakeholder engagement processes',
            ],
        ];
        
        return $recommendations[$category] ?? [];
    }
}
