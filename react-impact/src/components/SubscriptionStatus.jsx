import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { apiService } from '../services/apiService';
import { useAuth } from '../contexts/AuthContext';

const SubscriptionStatus = ({ showUpgradePrompt = true, compact = false }) => {
    const { user } = useAuth();
    const [subscription, setSubscription] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        fetchSubscription();
    }, []);

    const fetchSubscription = async () => {
        try {
            setLoading(true);
            const response = await apiService.get('/subscriptions');
            
            if (response.data.data && response.data.data.length > 0) {
                // Get the active subscription
                const activeSubscription = response.data.data.find(sub => 
                    sub.status === 'active' || sub.status === 'trialing'
                );
                setSubscription(activeSubscription || response.data.data[0]);
            }
        } catch (err) {
            setError('Failed to load subscription');
            console.error('Error fetching subscription:', err);
        } finally {
            setLoading(false);
        }
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'active':
                return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900';
            case 'trialing':
                return 'text-blue-600 bg-blue-100 dark:text-blue-400 dark:bg-blue-900';
            case 'past_due':
                return 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900';
            case 'canceled':
            case 'unpaid':
                return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900';
            default:
                return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900';
        }
    };

    const getStatusText = (status) => {
        switch (status) {
            case 'active':
                return 'Active';
            case 'trialing':
                return 'Trial';
            case 'past_due':
                return 'Past Due';
            case 'canceled':
                return 'Canceled';
            case 'unpaid':
                return 'Unpaid';
            default:
                return 'Unknown';
        }
    };

    if (loading) {
        return (
            <div className={`${compact ? 'p-4' : 'p-6'} bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700`}>
                <div className="animate-pulse">
                    <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-1/3 mb-2"></div>
                    <div className="h-3 bg-slate-200 dark:bg-slate-700 rounded w-1/2"></div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className={`${compact ? 'p-4' : 'p-6'} bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700`}>
                <div className="text-red-600 dark:text-red-400 text-sm">{error}</div>
            </div>
        );
    }

    // No subscription case
    if (!subscription) {
        return (
            <div className={`${compact ? 'p-4' : 'p-6'} bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700`}>
                <div className="flex items-center justify-between">
                    <div>
                        <h3 className={`${compact ? 'text-sm' : 'text-lg'} font-medium text-slate-900 dark:text-white`}>
                            No Active Subscription
                        </h3>
                        <p className={`${compact ? 'text-xs' : 'text-sm'} text-slate-500 dark:text-slate-400 mt-1`}>
                            Subscribe to access premium features
                        </p>
                    </div>
                    {showUpgradePrompt && (
                        <Link
                            to="/app/subscription/plans"
                            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                            Subscribe
                        </Link>
                    )}
                </div>
            </div>
        );
    }

    // Has subscription case
    return (
        <div className={`${compact ? 'p-4' : 'p-6'} bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700`}>
            <div className="flex items-center justify-between">
                <div className="flex-1">
                    <div className="flex items-center space-x-2">
                        <h3 className={`${compact ? 'text-sm' : 'text-lg'} font-medium text-slate-900 dark:text-white`}>
                            {subscription.subscription_product?.name || 'Subscription'}
                        </h3>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(subscription.status)}`}>
                            {getStatusText(subscription.status)}
                        </span>
                    </div>
                    
                    <div className={`${compact ? 'text-xs' : 'text-sm'} text-slate-500 dark:text-slate-400 mt-1 space-y-1`}>
                        <div>
                            ${subscription.subscription_product?.price}/{subscription.subscription_product?.billing_cycle || 'month'}
                        </div>
                        {subscription.current_period_end && (
                            <div>
                                {subscription.status === 'canceled' ? 'Ends' : 'Renews'}: {' '}
                                {new Date(subscription.current_period_end).toLocaleDateString()}
                            </div>
                        )}
                    </div>
                </div>
                
                <div className="flex space-x-2">
                    <Link
                        to="/app/subscription/manage"
                        className={`inline-flex items-center px-3 py-2 border border-slate-300 dark:border-slate-600 ${compact ? 'text-xs' : 'text-sm'} leading-4 font-medium rounded-md text-slate-700 dark:text-slate-300 bg-white dark:bg-slate-800 hover:bg-slate-50 dark:hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`}
                    >
                        Manage
                    </Link>
                    
                    {showUpgradePrompt && user?.role !== 'startup' && subscription.subscription_product?.name !== 'Enterprise' && (
                        <Link
                            to="/app/subscription/plans"
                            className={`inline-flex items-center px-3 py-2 border border-transparent ${compact ? 'text-xs' : 'text-sm'} leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`}
                        >
                            Upgrade
                        </Link>
                    )}
                </div>
            </div>
            
            {!compact && subscription.subscription_product?.features && (
                <div className="mt-4 pt-4 border-t border-slate-200 dark:border-slate-700">
                    <h4 className="text-sm font-medium text-slate-900 dark:text-white mb-2">
                        Current Plan Features:
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                        {subscription.subscription_product.features.slice(0, 4).map((feature, index) => (
                            <div key={index} className="flex items-center text-xs text-slate-600 dark:text-slate-400">
                                <svg className="w-3 h-3 text-green-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                                {feature}
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
};

export default SubscriptionStatus;
