import React, { useState } from 'react';

const InvestorCard = ({ investor, onSendInterestRequest, showMatchScore = false }) => {
    const [sendingRequest, setSendingRequest] = useState(false);

    const handleSendRequest = async () => {
        setSendingRequest(true);
        try {
            await onSendInterestRequest(investor.user.id);
        } finally {
            setSendingRequest(false);
        }
    };

    const formatCurrency = (amount) => {
        if (!amount) return 'Not specified';
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(amount);
    };

    const getRiskToleranceColor = (risk) => {
        switch (risk) {
            case 'low':
                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
            case 'medium':
                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
            case 'high':
                return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
        }
    };

    const getExperienceColor = (experience) => {
        switch (experience) {
            case 'expert':
                return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
            case 'experienced':
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
            case 'intermediate':
                return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200';
            case 'beginner':
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
        }
    };

    const getMatchScoreColor = (score) => {
        if (score >= 80) return 'text-green-600 dark:text-green-400';
        if (score >= 60) return 'text-blue-600 dark:text-blue-400';
        if (score >= 40) return 'text-yellow-600 dark:text-yellow-400';
        return 'text-gray-600 dark:text-gray-400';
    };

    return (
        <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700 p-6" data-testid="investor-card">
            <div className="flex items-start justify-between">
                <div className="flex-1">
                    {/* Header */}
                    <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-lg">
                                    {investor.user.name.charAt(0).toUpperCase()}
                                </div>
                            </div>
                            <div>
                                <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                                    {investor.user.name}
                                </h3>
                                <div className="flex items-center space-x-2 text-sm text-slate-600 dark:text-slate-400">
                                    {investor.user.city && (
                                        <span>{investor.user.city}</span>
                                    )}
                                    {investor.user.city && investor.user.country && (
                                        <span>•</span>
                                    )}
                                    {investor.user.country && (
                                        <span>{investor.user.country}</span>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* Match Score */}
                        {showMatchScore && investor.match_score !== undefined && (
                            <div className="text-right">
                                <div className={`text-2xl font-bold ${getMatchScoreColor(investor.match_score)}`}>
                                    {investor.match_score}%
                                </div>
                                <div className="text-xs text-slate-500 dark:text-slate-400">
                                    Match Score
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Bio */}
                    {investor.bio && (
                        <div className="mb-4">
                            <p className="text-sm text-slate-600 dark:text-slate-400 line-clamp-3">
                                {investor.bio}
                            </p>
                        </div>
                    )}

                    {/* Investment Details */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <h4 className="text-sm font-medium text-slate-900 dark:text-white mb-2">
                                Investment Range
                            </h4>
                            <p className="text-sm text-slate-600 dark:text-slate-400">
                                {formatCurrency(investor.investment_budget_min)} - {formatCurrency(investor.investment_budget_max)}
                            </p>
                        </div>
                        <div>
                            <h4 className="text-sm font-medium text-slate-900 dark:text-white mb-2">
                                Experience & Risk
                            </h4>
                            <div className="flex space-x-2">
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getExperienceColor(investor.investment_experience)}`}>
                                    {investor.investment_experience?.charAt(0).toUpperCase() + investor.investment_experience?.slice(1)}
                                </span>
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRiskToleranceColor(investor.risk_tolerance)}`}>
                                    {investor.risk_tolerance?.charAt(0).toUpperCase() + investor.risk_tolerance?.slice(1)} Risk
                                </span>
                            </div>
                        </div>
                    </div>

                    {/* Categories & Industries */}
                    {investor.user.taxonomies && investor.user.taxonomies.length > 0 && (
                        <div className="mb-4">
                            <h4 className="text-sm font-medium text-slate-900 dark:text-white mb-2">
                                Investment Focus
                            </h4>
                            <div className="flex flex-wrap gap-2">
                                {investor.user.taxonomies.slice(0, 6).map((taxonomy) => (
                                    <span
                                        key={taxonomy.id}
                                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-800 dark:bg-slate-700 dark:text-slate-200"
                                    >
                                        {taxonomy.name}
                                    </span>
                                ))}
                                {investor.user.taxonomies.length > 6 && (
                                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-800 dark:bg-slate-700 dark:text-slate-200">
                                        +{investor.user.taxonomies.length - 6} more
                                    </span>
                                )}
                            </div>
                        </div>
                    )}

                    {/* Match Reasons */}
                    {showMatchScore && investor.match_reasons && investor.match_reasons.length > 0 && (
                        <div className="mb-4">
                            <h4 className="text-sm font-medium text-slate-900 dark:text-white mb-2">
                                Why this is a good match
                            </h4>
                            <ul className="space-y-1">
                                {investor.match_reasons.map((reason, index) => (
                                    <li key={index} className="flex items-start space-x-2 text-sm text-slate-600 dark:text-slate-400">
                                        <svg className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                        </svg>
                                        <span>{reason}</span>
                                    </li>
                                ))}
                            </ul>
                        </div>
                    )}

                    {/* Contact Links */}
                    <div className="flex items-center space-x-4 mb-4">
                        {investor.website && (
                            <a
                                href={investor.website}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 flex items-center space-x-1"
                            >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                </svg>
                                <span>Website</span>
                            </a>
                        )}
                        {investor.linkedin && (
                            <a
                                href={investor.linkedin}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 flex items-center space-x-1"
                            >
                                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                </svg>
                                <span>LinkedIn</span>
                            </a>
                        )}
                    </div>

                    {/* Action Button */}
                    <div className="flex items-center justify-between">
                        <div className="text-xs text-slate-500 dark:text-slate-400">
                            Member since {new Date(investor.user.created_at).getFullYear()}
                        </div>
                        
                        {investor.interest_request_sent ? (
                            <span className="inline-flex items-center px-3 py-2 border border-green-300 rounded-md text-sm font-medium text-green-700 bg-green-50 dark:bg-green-900 dark:text-green-200 dark:border-green-700" data-testid="request-sent-indicator">
                                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                </svg>
                                Request Sent
                            </span>
                        ) : (
                            <button
                                onClick={handleSendRequest}
                                disabled={sendingRequest}
                                data-testid="send-interest-request"
                                className="btn btn-primary btn-sm disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {sendingRequest ? (
                                    <>
                                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Sending...
                                    </>
                                ) : (
                                    <>
                                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                        </svg>
                                        Send Interest Request
                                    </>
                                )}
                            </button>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default InvestorCard;
