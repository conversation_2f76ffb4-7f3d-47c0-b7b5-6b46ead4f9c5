import React, { useState } from 'react';
import { Link } from 'react-router-dom';

const EnhancedRegistrationForm = ({ onSubmit, loading, errors }) => {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
        linkedin_url: '',
        twitter_handle: '',
        facebook_url: '',
        personal_website: '',
        phone: '',
        city: '',
        country: '',
        role: 'startup'
    });

    const [currentStep, setCurrentStep] = useState(1);
    const [validationErrors, setValidationErrors] = useState({});

    const steps = [
        {
            id: 1,
            title: 'Basic Information',
            description: 'Your personal details',
            fields: ['name', 'email', 'password', 'password_confirmation']
        },
        {
            id: 2,
            title: 'Contact Information',
            description: 'How to reach you',
            fields: ['phone', 'city', 'country']
        },
        {
            id: 3,
            title: 'Social Media Profiles',
            description: 'Connect your professional profiles',
            fields: ['linkedin_url', 'twitter_handle', 'facebook_url', 'personal_website']
        }
    ];

    const handleChange = (e) => {
        const { name, value } = e.target;
        
        // Format Twitter handle
        if (name === 'twitter_handle' && value && !value.startsWith('@')) {
            setFormData(prev => ({ ...prev, [name]: '@' + value }));
        } else {
            setFormData(prev => ({ ...prev, [name]: value }));
        }

        // Clear validation errors when user starts typing
        if (validationErrors[name]) {
            setValidationErrors(prev => ({ ...prev, [name]: '' }));
        }
    };

    const validateStep = (stepNumber) => {
        const step = steps.find(s => s.id === stepNumber);
        const stepErrors = {};

        step.fields.forEach(field => {
            const value = formData[field];
            
            // Required field validation
            if (['name', 'email', 'password', 'password_confirmation'].includes(field) && !value) {
                stepErrors[field] = `${field.replace('_', ' ')} is required`;
            }

            // Email validation
            if (field === 'email' && value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                stepErrors[field] = 'Please enter a valid email address';
            }

            // Password validation
            if (field === 'password' && value && value.length < 8) {
                stepErrors[field] = 'Password must be at least 8 characters';
            }

            // Password confirmation validation
            if (field === 'password_confirmation' && value !== formData.password) {
                stepErrors[field] = 'Passwords do not match';
            }

            // URL validation for social media
            if (['linkedin_url', 'facebook_url', 'personal_website'].includes(field) && value) {
                try {
                    new URL(value);
                } catch {
                    stepErrors[field] = 'Please enter a valid URL';
                }
            }

            // LinkedIn specific validation
            if (field === 'linkedin_url' && value && !value.includes('linkedin.com')) {
                stepErrors[field] = 'Please enter a valid LinkedIn URL';
            }

            // Facebook specific validation
            if (field === 'facebook_url' && value && !value.includes('facebook.com')) {
                stepErrors[field] = 'Please enter a valid Facebook URL';
            }
        });

        setValidationErrors(stepErrors);
        return Object.keys(stepErrors).length === 0;
    };

    const nextStep = () => {
        if (validateStep(currentStep)) {
            setCurrentStep(prev => Math.min(prev + 1, steps.length));
        }
    };

    const prevStep = () => {
        setCurrentStep(prev => Math.max(prev - 1, 1));
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        e.stopPropagation();

        // Prevent multiple submissions
        if (loading) {
            return;
        }

        // Validate all steps before submission
        let allValid = true;
        for (let i = 1; i <= steps.length; i++) {
            if (!validateStep(i)) {
                allValid = false;
                break;
            }
        }

        if (allValid) {
            onSubmit(formData);
        }
    };

    const renderProgressBar = () => (
        <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
                {steps.map((step, index) => (
                    <div key={step.id} className="flex items-center">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                            currentStep >= step.id 
                                ? 'bg-blue-600 text-white' 
                                : 'bg-gray-200 text-gray-600'
                        }`}>
                            {step.id}
                        </div>
                        {index < steps.length - 1 && (
                            <div className={`w-16 h-1 mx-2 ${
                                currentStep > step.id ? 'bg-blue-600' : 'bg-gray-200'
                            }`} />
                        )}
                    </div>
                ))}
            </div>
            <div className="text-center">
                <h3 className="text-lg font-medium text-gray-900">
                    {steps[currentStep - 1].title}
                </h3>
                <p className="text-sm text-gray-600">
                    {steps[currentStep - 1].description}
                </p>
            </div>
        </div>
    );

    const renderFormField = (field, label, type = 'text', placeholder = '', required = false) => (
        <div key={field}>
            <label htmlFor={field} className="block text-sm font-medium text-gray-700">
                {label} {required && <span className="text-red-500">*</span>}
            </label>
            <input
                id={field}
                name={field}
                type={type}
                required={required}
                className={`mt-1 appearance-none relative block w-full px-3 py-2 border ${
                    validationErrors[field] || errors[field] ? 'border-red-300' : 'border-gray-300'
                } placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                placeholder={placeholder}
                value={formData[field]}
                onChange={handleChange}
            />
            {(validationErrors[field] || errors[field]) && (
                <p className="mt-1 text-sm text-red-600">
                    {validationErrors[field] || (Array.isArray(errors[field]) ? errors[field][0] : errors[field])}
                </p>
            )}
        </div>
    );

    const renderStep1 = () => (
        <div className="space-y-4">
            {renderFormField('name', 'Full Name', 'text', 'Enter your full name', true)}
            {renderFormField('email', 'Email Address', 'email', 'Enter your email address', true)}
            {renderFormField('password', 'Password', 'password', 'Create a strong password', true)}
            {renderFormField('password_confirmation', 'Confirm Password', 'password', 'Confirm your password', true)}
        </div>
    );

    const renderStep2 = () => (
        <div className="space-y-4">
            {renderFormField('phone', 'Phone Number', 'tel', '+****************')}
            {renderFormField('city', 'City', 'text', 'San Francisco')}
            {renderFormField('country', 'Country', 'text', 'United States')}
        </div>
    );

    const renderStep3 = () => (
        <div className="space-y-4">
            {renderFormField('linkedin_url', 'LinkedIn Profile', 'url', 'https://linkedin.com/in/yourprofile')}
            {renderFormField('twitter_handle', 'Twitter Handle', 'text', '@yourusername')}
            {renderFormField('facebook_url', 'Facebook Profile', 'url', 'https://facebook.com/yourprofile')}
            {renderFormField('personal_website', 'Personal Website', 'url', 'https://yourwebsite.com')}
        </div>
    );

    const renderCurrentStep = () => {
        switch (currentStep) {
            case 1: return renderStep1();
            case 2: return renderStep2();
            case 3: return renderStep3();
            default: return renderStep1();
        }
    };

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-md w-full space-y-8">
                <div className="text-center">
                    <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
                        Create Your Startup Account
                    </h2>
                    <p className="mt-2 text-sm text-gray-600">
                        Join our investment platform and connect with investors
                    </p>
                </div>

                {renderProgressBar()}

                <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
                    {errors.general && (
                        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                            {errors.general}
                        </div>
                    )}

                    {renderCurrentStep()}

                    <div className="flex justify-between space-x-4">
                        {currentStep > 1 && (
                            <button
                                type="button"
                                onClick={prevStep}
                                className="flex-1 py-2 px-4 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            >
                                Previous
                            </button>
                        )}
                        
                        {currentStep < steps.length ? (
                            <button
                                type="button"
                                onClick={nextStep}
                                disabled={loading}
                                className="flex-1 py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                Next
                            </button>
                        ) : (
                            <button
                                type="submit"
                                disabled={loading}
                                className="flex-1 py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {loading ? 'Creating Account...' : 'Create Account'}
                            </button>
                        )}
                    </div>
                </form>

                <div className="text-center">
                    <p className="text-sm text-gray-600">
                        Already have an account?{' '}
                        <Link
                            to="/login"
                            className="font-medium text-blue-600 hover:text-blue-500"
                        >
                            Sign in here
                        </Link>
                    </p>
                </div>
            </div>
        </div>
    );
};

export default EnhancedRegistrationForm;
