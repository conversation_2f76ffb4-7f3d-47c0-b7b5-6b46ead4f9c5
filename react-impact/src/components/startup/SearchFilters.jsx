import React, { useState, useEffect } from 'react';

const SearchFilters = ({ filters, filterOptions, onFilterChange, loading }) => {
    const [localFilters, setLocalFilters] = useState(filters);
    const [showAdvanced, setShowAdvanced] = useState(false);

    useEffect(() => {
        setLocalFilters(filters);
    }, [filters]);

    const handleFilterUpdate = (key, value) => {
        const newFilters = { ...localFilters, [key]: value };
        setLocalFilters(newFilters);
        onFilterChange(newFilters);
    };

    const handleMultiSelectChange = (key, value, checked) => {
        const currentValues = localFilters[key] || [];
        const newValues = checked
            ? [...currentValues, value]
            : currentValues.filter(v => v !== value);
        
        handleFilterUpdate(key, newValues);
    };

    const clearFilters = () => {
        const clearedFilters = {
            investment_min: '',
            investment_max: '',
            risk_tolerance: '',
            investment_experience: '',
            categories: [],
            industries: [],
            location: '',
            esg_focused: false,
            sort_by: 'relevance',
        };
        setLocalFilters(clearedFilters);
        onFilterChange(clearedFilters);
    };

    const formatCurrency = (value) => {
        if (!value) return '';
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(value);
    };

    return (
        <div className="space-y-6" data-testid="search-filters">
            {/* Quick Budget Ranges */}
            <div>
                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-3">
                    Investment Budget
                </label>
                <div className="space-y-2">
                    {filterOptions.budget_ranges.suggested_ranges.map((range, index) => (
                        <button
                            key={index}
                            onClick={() => {
                                handleFilterUpdate('investment_min', range.min);
                                handleFilterUpdate('investment_max', range.max);
                            }}
                            className={`w-full text-left px-3 py-2 text-sm rounded-md border transition-colors ${
                                localFilters.investment_min === range.min && localFilters.investment_max === range.max
                                    ? 'border-blue-500 bg-blue-50 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
                                    : 'border-slate-200 hover:border-slate-300 dark:border-slate-600 dark:hover:border-slate-500'
                            }`}
                        >
                            {range.label}
                        </button>
                    ))}
                </div>
            </div>

            {/* Custom Budget Range */}
            <div>
                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                    Custom Range
                </label>
                <div className="grid grid-cols-2 gap-2">
                    <div>
                        <input
                            type="number"
                            name="investment_min"
                            placeholder="Min"
                            value={localFilters.investment_min}
                            onChange={(e) => handleFilterUpdate('investment_min', e.target.value)}
                            className="form-control text-sm"
                        />
                    </div>
                    <div>
                        <input
                            type="number"
                            name="investment_max"
                            placeholder="Max"
                            value={localFilters.investment_max}
                            onChange={(e) => handleFilterUpdate('investment_max', e.target.value)}
                            className="form-control text-sm"
                        />
                    </div>
                </div>
            </div>

            {/* Location */}
            <div>
                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                    Location
                </label>
                <input
                    type="text"
                    name="location"
                    placeholder="City or Country"
                    value={localFilters.location}
                    onChange={(e) => handleFilterUpdate('location', e.target.value)}
                    className="form-control text-sm"
                />
            </div>

            {/* Sort By */}
            <div>
                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                    Sort By
                </label>
                <select
                    name="sort_by"
                    value={localFilters.sort_by}
                    onChange={(e) => handleFilterUpdate('sort_by', e.target.value)}
                    className="form-control text-sm"
                >
                    {filterOptions.sort_options.map((option) => (
                        <option key={option.value} value={option.value}>
                            {option.label}
                        </option>
                    ))}
                </select>
            </div>

            {/* Advanced Filters Toggle */}
            <div>
                <button
                    onClick={() => setShowAdvanced(!showAdvanced)}
                    className="flex items-center justify-between w-full text-sm font-medium text-slate-700 dark:text-slate-300 hover:text-slate-900 dark:hover:text-slate-100"
                >
                    <span>Advanced Filters</span>
                    <svg
                        className={`w-4 h-4 transition-transform ${showAdvanced ? 'rotate-180' : ''}`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                </button>
            </div>

            {/* Advanced Filters */}
            {showAdvanced && (
                <div className="space-y-6 pt-4 border-t border-slate-200 dark:border-slate-600">
                    {/* Risk Tolerance */}
                    <div>
                        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                            Risk Tolerance
                        </label>
                        <select
                            value={localFilters.risk_tolerance}
                            onChange={(e) => handleFilterUpdate('risk_tolerance', e.target.value)}
                            className="form-control text-sm"
                        >
                            <option value="">Any Risk Level</option>
                            {filterOptions.risk_tolerance_options.map((option) => (
                                <option key={option.value} value={option.value}>
                                    {option.label}
                                </option>
                            ))}
                        </select>
                    </div>

                    {/* Investment Experience */}
                    <div>
                        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                            Experience Level
                        </label>
                        <select
                            name="investment_experience"
                            value={localFilters.investment_experience}
                            onChange={(e) => handleFilterUpdate('investment_experience', e.target.value)}
                            className="form-control text-sm"
                        >
                            <option value="">Any Experience</option>
                            {filterOptions.experience_options.map((option) => (
                                <option key={option.value} value={option.value}>
                                    {option.label}
                                </option>
                            ))}
                        </select>
                    </div>

                    {/* Categories */}
                    <div>
                        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                            Categories
                        </label>
                        <div className="max-h-40 overflow-y-auto space-y-2 border border-slate-200 dark:border-slate-600 rounded-md p-3">
                            {filterOptions.categories.map((category) => (
                                <label key={category.id} className="flex items-center space-x-2">
                                    <input
                                        type="checkbox"
                                        checked={localFilters.categories.includes(category.id)}
                                        onChange={(e) => handleMultiSelectChange('categories', category.id, e.target.checked)}
                                        className="form-checkbox text-sm"
                                    />
                                    <span className="text-sm text-slate-700 dark:text-slate-300">
                                        {category.name}
                                    </span>
                                </label>
                            ))}
                        </div>
                    </div>

                    {/* Industries */}
                    <div>
                        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                            Industries
                        </label>
                        <div className="max-h-40 overflow-y-auto space-y-2 border border-slate-200 dark:border-slate-600 rounded-md p-3">
                            {filterOptions.industries.map((industry) => (
                                <label key={industry.id} className="flex items-center space-x-2">
                                    <input
                                        type="checkbox"
                                        checked={localFilters.industries.includes(industry.id)}
                                        onChange={(e) => handleMultiSelectChange('industries', industry.id, e.target.checked)}
                                        className="form-checkbox text-sm"
                                    />
                                    <span className="text-sm text-slate-700 dark:text-slate-300">
                                        {industry.name}
                                    </span>
                                </label>
                            ))}
                        </div>
                    </div>

                    {/* ESG Focused */}
                    <div>
                        <label className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                checked={localFilters.esg_focused}
                                onChange={(e) => handleFilterUpdate('esg_focused', e.target.checked)}
                                className="form-checkbox"
                            />
                            <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                ESG Focused Investors Only
                            </span>
                        </label>
                        <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                            Show only investors who prioritize Environmental, Social, and Governance factors
                        </p>
                    </div>
                </div>
            )}

            {/* Clear Filters */}
            <div className="pt-4 border-t border-slate-200 dark:border-slate-600">
                <button
                    onClick={clearFilters}
                    disabled={loading}
                    className="w-full btn btn-outline-secondary btn-sm disabled:opacity-50"
                >
                    Clear All Filters
                </button>
            </div>

            {/* Active Filters Summary */}
            {(localFilters.investment_min || localFilters.investment_max || localFilters.location || 
              localFilters.risk_tolerance || localFilters.investment_experience || 
              localFilters.categories.length > 0 || localFilters.industries.length > 0 || 
              localFilters.esg_focused) && (
                <div className="pt-4 border-t border-slate-200 dark:border-slate-600">
                    <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                        Active Filters
                    </h4>
                    <div className="space-y-1 text-xs">
                        {(localFilters.investment_min || localFilters.investment_max) && (
                            <div className="text-slate-600 dark:text-slate-400">
                                Budget: {formatCurrency(localFilters.investment_min || 0)} - {formatCurrency(localFilters.investment_max || 'No limit')}
                            </div>
                        )}
                        {localFilters.location && (
                            <div className="text-slate-600 dark:text-slate-400">
                                Location: {localFilters.location}
                            </div>
                        )}
                        {localFilters.risk_tolerance && (
                            <div className="text-slate-600 dark:text-slate-400">
                                Risk: {localFilters.risk_tolerance}
                            </div>
                        )}
                        {localFilters.investment_experience && (
                            <div className="text-slate-600 dark:text-slate-400">
                                Experience: {localFilters.investment_experience}
                            </div>
                        )}
                        {localFilters.categories.length > 0 && (
                            <div className="text-slate-600 dark:text-slate-400">
                                Categories: {localFilters.categories.length} selected
                            </div>
                        )}
                        {localFilters.industries.length > 0 && (
                            <div className="text-slate-600 dark:text-slate-400">
                                Industries: {localFilters.industries.length} selected
                            </div>
                        )}
                        {localFilters.esg_focused && (
                            <div className="text-slate-600 dark:text-slate-400">
                                ESG Focused: Yes
                            </div>
                        )}
                    </div>
                </div>
            )}
        </div>
    );
};

export default SearchFilters;
