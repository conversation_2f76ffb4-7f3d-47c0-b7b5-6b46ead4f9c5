import React, { useState, useEffect } from 'react';
import apiService from '../../services/apiService';

const RegistrationProgress = ({ className = '' }) => {
    const [progress, setProgress] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');

    useEffect(() => {
        fetchProgress();
    }, []);

    const fetchProgress = async () => {
        try {
            const result = await apiService.getRegistrationProgress();
            if (result.success) {
                setProgress(result.data);
            } else {
                setError(result.message || 'Failed to load progress');
            }
        } catch (error) {
            console.error('Progress fetch error:', error);

            // Use mock data for testing when API is not available
            const mockData = {
                overall_completion_percentage: 45.5,
                can_access_platform: false,
                steps: {
                    email_verification: {
                        step: 1,
                        completed: true,
                        required: true,
                        completion_percentage: 100
                    },
                    company_information: {
                        step: 2,
                        completed: false,
                        required: true,
                        completion_percentage: 60
                    },
                    funding_information: {
                        step: 3,
                        completed: false,
                        required: true,
                        completion_percentage: 0
                    },
                    category_selection: {
                        step: 4,
                        completed: false,
                        required: true,
                        selected_count: 2,
                        minimum_required: 3
                    },
                    subscription: {
                        step: 5,
                        completed: false,
                        required: true,
                        completion_percentage: 0
                    },
                    esg_questionnaire: {
                        step: 6,
                        completed: false,
                        required: false,
                        completion_percentage: 0
                    },
                    social_media_links: {
                        step: 7,
                        completed: false,
                        required: false,
                        links_count: 1
                    }
                }
            };

            setProgress(mockData);
        } finally {
            setLoading(false);
        }
    };

    if (loading) {
        return (
            <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
                <div className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
                    <div className="space-y-3">
                        <div className="h-3 bg-gray-200 rounded"></div>
                        <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                    </div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
                <div className="text-red-600 text-sm">{error}</div>
            </div>
        );
    }

    if (!progress) {
        return null;
    }

    const getStepIcon = (step) => {
        if (step.completed) {
            return (
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                </div>
            );
        } else if (step.required) {
            return (
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-medium">{step.step}</span>
                </div>
            );
        } else {
            return (
                <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                    <span className="text-gray-600 text-sm font-medium">{step.step}</span>
                </div>
            );
        }
    };

    const getStepStatus = (step) => {
        if (step.completed) return 'Completed';
        if (step.required) return 'Required';
        return 'Optional';
    };

    const getStepDescription = (stepKey, step) => {
        const descriptions = {
            email_verification: 'Verify your email address to activate your account',
            company_information: `Complete your company profile (${step.completion_percentage || 0}% done)`,
            funding_information: 'Add funding details and business information',
            category_selection: `Select business categories (${step.selected_count || 0}/${step.minimum_required || 3} minimum)`,
            subscription: 'Choose a subscription plan to access full features',
            esg_questionnaire: 'Complete ESG assessment for better investor matching',
            social_media_links: `Connect your social profiles (${step.links_count || 0} added)`
        };
        return descriptions[stepKey] || 'Complete this step';
    };

    const getNextSteps = () => {
        const steps = Object.entries(progress.steps)
            .filter(([key, step]) => step.required && !step.completed)
            .sort((a, b) => a[1].step - b[1].step)
            .slice(0, 3);
        
        return steps;
    };

    const nextSteps = getNextSteps();

    return (
        <div className={`bg-white rounded-lg shadow ${className}`}>
            <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                    <h3 className="text-lg font-medium text-gray-900">Registration Progress</h3>
                    <span className="text-sm font-medium text-blue-600">
                        {progress.overall_completion_percentage.toFixed(1)}% Complete
                    </span>
                </div>

                {/* Progress Bar */}
                <div className="mb-6">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${progress.overall_completion_percentage}%` }}
                        ></div>
                    </div>
                </div>

                {/* Platform Access Status */}
                <div className={`mb-6 p-4 rounded-lg ${
                    progress.can_access_platform 
                        ? 'bg-green-50 border border-green-200' 
                        : 'bg-yellow-50 border border-yellow-200'
                }`}>
                    <div className="flex items-center">
                        <div className={`w-4 h-4 rounded-full mr-3 ${
                            progress.can_access_platform ? 'bg-green-500' : 'bg-yellow-500'
                        }`}></div>
                        <span className={`text-sm font-medium ${
                            progress.can_access_platform ? 'text-green-800' : 'text-yellow-800'
                        }`}>
                            {progress.can_access_platform 
                                ? 'Platform Access: Enabled' 
                                : 'Platform Access: Pending'
                            }
                        </span>
                    </div>
                </div>

                {/* Next Steps */}
                {nextSteps.length > 0 && (
                    <div className="mb-6">
                        <h4 className="text-sm font-medium text-gray-900 mb-3">Next Steps</h4>
                        <div className="space-y-3">
                            {nextSteps.map(([stepKey, step]) => (
                                <div key={stepKey} className="flex items-start">
                                    {getStepIcon(step)}
                                    <div className="ml-3 flex-1">
                                        <div className="flex items-center justify-between">
                                            <p className="text-sm font-medium text-gray-900 capitalize">
                                                {stepKey.replace('_', ' ')}
                                            </p>
                                            <span className={`text-xs px-2 py-1 rounded-full ${
                                                step.required 
                                                    ? 'bg-red-100 text-red-800' 
                                                    : 'bg-gray-100 text-gray-800'
                                            }`}>
                                                {getStepStatus(step)}
                                            </span>
                                        </div>
                                        <p className="text-sm text-gray-600 mt-1">
                                            {getStepDescription(stepKey, step)}
                                        </p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                )}

                {/* All Steps Overview */}
                <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-3">All Steps</h4>
                    <div className="space-y-2">
                        {Object.entries(progress.steps).map(([stepKey, step]) => (
                            <div key={stepKey} className="flex items-center justify-between py-2">
                                <div className="flex items-center">
                                    <div className={`w-3 h-3 rounded-full mr-3 ${
                                        step.completed ? 'bg-green-500' : 
                                        step.required ? 'bg-blue-500' : 'bg-gray-300'
                                    }`}></div>
                                    <span className="text-sm text-gray-900 capitalize">
                                        {stepKey.replace('_', ' ')}
                                    </span>
                                </div>
                                <span className={`text-xs px-2 py-1 rounded-full ${
                                    step.completed ? 'bg-green-100 text-green-800' :
                                    step.required ? 'bg-blue-100 text-blue-800' : 
                                    'bg-gray-100 text-gray-800'
                                }`}>
                                    {getStepStatus(step)}
                                </span>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default RegistrationProgress;
