import React, { useState, useEffect } from 'react';

const ProfileWizard = ({ initialData, taxonomyOptions, onSave, loading, errors }) => {
    const [currentStep, setCurrentStep] = useState(1);
    const [formData, setFormData] = useState({
        // Company Information
        company_name: '',
        company_description: '',
        founding_date: '',
        employee_count: '',
        website: '',
        
        // Funding Information
        funding_stage: '',
        funding_amount_sought: '',
        current_valuation: '',
        business_model: [],
        
        // Social Media
        linkedin_url: '',
        twitter_handle: '',
        facebook_url: '',
        instagram_url: '',
        
        // Taxonomies
        categories: [],
        keywords: [],
        industries: [],
        technologies: [],
    });

    const steps = [
        {
            id: 1,
            title: 'Company Information',
            description: 'Basic details about your startup',
            fields: ['company_name', 'company_description', 'founding_date', 'employee_count', 'website']
        },
        {
            id: 2,
            title: 'Funding Details',
            description: 'Investment and business model information',
            fields: ['funding_stage', 'funding_amount_sought', 'current_valuation', 'business_model']
        },
        {
            id: 3,
            title: 'Social Media',
            description: 'Connect your professional profiles',
            fields: ['linkedin_url', 'twitter_handle', 'facebook_url', 'instagram_url']
        },
        {
            id: 4,
            title: 'Categories & Tags',
            description: 'Help investors find your startup',
            fields: ['categories', 'keywords', 'industries', 'technologies']
        }
    ];

    // Initialize form data with existing profile data
    useEffect(() => {
        if (initialData?.profile) {
            const profile = initialData.profile;
            const socialMediaLinks = initialData.social_media_links || [];
            
            // Extract social media URLs
            const socialMedia = {};
            socialMediaLinks.forEach(link => {
                if (link.platform === 'linkedin') socialMedia.linkedin_url = link.url;
                if (link.platform === 'twitter') socialMedia.twitter_handle = `@${link.username || ''}`;
                if (link.platform === 'facebook') socialMedia.facebook_url = link.url;
                if (link.platform === 'instagram') socialMedia.instagram_url = link.url;
            });

            setFormData({
                // Company Information
                company_name: profile.company_name || '',
                company_description: profile.company_description || '',
                founding_date: profile.founding_date || '',
                employee_count: profile.employee_count || '',
                website: profile.website || '',
                
                // Funding Information
                funding_stage: profile.funding_stage || '',
                funding_amount_sought: profile.funding_amount_sought || '',
                current_valuation: profile.current_valuation || '',
                business_model: profile.business_model || [],
                
                // Social Media
                linkedin_url: socialMedia.linkedin_url || '',
                twitter_handle: socialMedia.twitter_handle || '',
                facebook_url: socialMedia.facebook_url || '',
                instagram_url: socialMedia.instagram_url || '',
                
                // Taxonomies
                categories: initialData.categories?.map(cat => cat.id) || [],
                keywords: initialData.keywords?.map(kw => kw.name) || [],
                industries: initialData.industries?.map(ind => ind.id) || [],
                technologies: initialData.technologies?.map(tech => tech.id) || [],
            });
        }
    }, [initialData]);

    const handleChange = (e) => {
        const { name, value, type, checked } = e.target;
        
        if (type === 'checkbox') {
            if (name === 'business_model') {
                setFormData(prev => ({
                    ...prev,
                    business_model: checked 
                        ? [...prev.business_model, value]
                        : prev.business_model.filter(item => item !== value)
                }));
            }
        } else if (name === 'twitter_handle' && value && !value.startsWith('@')) {
            setFormData(prev => ({ ...prev, [name]: '@' + value }));
        } else {
            setFormData(prev => ({ ...prev, [name]: value }));
        }
    };

    const handleMultiSelect = (name, selectedValues) => {
        setFormData(prev => ({ ...prev, [name]: selectedValues }));
    };

    const handleKeywordAdd = (keyword) => {
        if (keyword && !formData.keywords.includes(keyword) && formData.keywords.length < 10) {
            setFormData(prev => ({
                ...prev,
                keywords: [...prev.keywords, keyword]
            }));
        }
    };

    const handleKeywordRemove = (keyword) => {
        setFormData(prev => ({
            ...prev,
            keywords: prev.keywords.filter(k => k !== keyword)
        }));
    };

    const validateStep = (stepId) => {
        const step = steps.find(s => s.id === stepId);
        const stepErrors = {};

        step.fields.forEach(field => {
            const value = formData[field];
            
            // Required field validation
            if (['company_name', 'company_description', 'founding_date', 'employee_count', 
                 'funding_stage', 'funding_amount_sought', 'business_model'].includes(field)) {
                if (!value || (Array.isArray(value) && value.length === 0)) {
                    stepErrors[field] = 'This field is required';
                }
            }

            // URL validation
            if (['website', 'linkedin_url', 'facebook_url', 'instagram_url'].includes(field) && value) {
                try {
                    new URL(value);
                } catch {
                    stepErrors[field] = 'Please enter a valid URL';
                }
            }

            // Date validation
            if (field === 'founding_date' && value) {
                const foundingDate = new Date(value);
                const today = new Date();
                if (foundingDate > today) {
                    stepErrors[field] = 'Founding date cannot be in the future';
                }
            }

            // Number validation
            if (['employee_count', 'funding_amount_sought', 'current_valuation'].includes(field) && value) {
                if (isNaN(value) || Number(value) < 0) {
                    stepErrors[field] = 'Please enter a valid positive number';
                }
            }
        });

        return stepErrors;
    };

    const nextStep = () => {
        const stepErrors = validateStep(currentStep);
        if (Object.keys(stepErrors).length === 0) {
            setCurrentStep(prev => Math.min(prev + 1, steps.length));
        }
    };

    const prevStep = () => {
        setCurrentStep(prev => Math.max(prev - 1, 1));
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        
        // Validate all steps
        let allValid = true;
        for (let i = 1; i <= steps.length; i++) {
            const stepErrors = validateStep(i);
            if (Object.keys(stepErrors).length > 0) {
                allValid = false;
                break;
            }
        }

        if (allValid) {
            onSave(formData);
        }
    };

    const renderProgressBar = () => (
        <div className="wizard-steps flex z-[5] items-center relative justify-center md:mx-8 mb-8">
            {steps.map((step, index) => (
                <div
                    key={step.id}
                    className={`relative z-[1] items-center item flex flex-start flex-1 last:flex-none group wizard-step ${
                        currentStep >= step.id ? 'active pass' : ''
                    }`}
                >
                    <div className="number-box">
                        <span className="number">{step.id}</span>
                        <span className="no-icon text-3xl">
                            <i className="bx bx-check-double"></i>
                        </span>
                    </div>
                    {index < steps.length - 1 && <div className="bar-line"></div>}
                    <div className="circle-box">
                        <span className="w-max">{step.title}</span>
                    </div>
                </div>
            ))}
        </div>
    );

    const renderFormField = (field, label, type = 'text', placeholder = '', required = false) => (
        <div key={field} className="input-area">
            <label htmlFor={field} className="form-label">
                {label} {required && <span className="text-red-500">*</span>}
            </label>
            {type === 'textarea' ? (
                <textarea
                    id={field}
                    name={field}
                    className={`form-control ${errors[field] ? 'border-red-300' : ''}`}
                    placeholder={placeholder}
                    value={formData[field]}
                    onChange={handleChange}
                    rows={4}
                />
            ) : (
                <input
                    id={field}
                    name={field}
                    type={type}
                    className={`form-control ${errors[field] ? 'border-red-300' : ''}`}
                    placeholder={placeholder}
                    value={formData[field]}
                    onChange={handleChange}
                />
            )}
            {errors[field] && (
                <p className="mt-1 text-sm text-red-600">{errors[field]}</p>
            )}
        </div>
    );

    const renderCurrentStep = () => {
        switch (currentStep) {
            case 1:
                return (
                    <div className="space-y-4">
                        {renderFormField('company_name', 'Company Name', 'text', 'Enter your company name', true)}
                        {renderFormField('company_description', 'Company Description', 'textarea', 'Describe what your company does...', true)}
                        {renderFormField('founding_date', 'Founding Date', 'date', '', true)}
                        {renderFormField('employee_count', 'Employee Count', 'number', 'Number of employees', true)}
                        {renderFormField('website', 'Company Website', 'url', 'https://yourcompany.com')}
                    </div>
                );
            case 2:
                return (
                    <div className="space-y-4">
                        <div className="input-area">
                            <label className="form-label">
                                Funding Stage <span className="text-red-500">*</span>
                            </label>
                            <select
                                name="funding_stage"
                                className={`form-control ${errors.funding_stage ? 'border-red-300' : ''}`}
                                value={formData.funding_stage}
                                onChange={handleChange}
                            >
                                <option value="">Select funding stage</option>
                                {taxonomyOptions?.funding_stages?.map(stage => (
                                    <option key={stage.value} value={stage.value}>
                                        {stage.label}
                                    </option>
                                ))}
                            </select>
                            {errors.funding_stage && (
                                <p className="mt-1 text-sm text-red-600">{errors.funding_stage}</p>
                            )}
                        </div>
                        {renderFormField('funding_amount_sought', 'Funding Amount Sought ($)', 'number', 'Amount you are seeking to raise', true)}
                        {renderFormField('current_valuation', 'Current Valuation ($)', 'number', 'Current company valuation')}
                        
                        <div className="input-area">
                            <label className="form-label">
                                Business Model <span className="text-red-500">*</span>
                            </label>
                            <div className="grid grid-cols-2 gap-3 mt-2">
                                {taxonomyOptions?.business_models?.map(model => (
                                    <label key={model.value} className="flex items-center space-x-2">
                                        <input
                                            type="checkbox"
                                            name="business_model"
                                            value={model.value}
                                            checked={formData.business_model.includes(model.value)}
                                            onChange={handleChange}
                                            className="form-checkbox"
                                        />
                                        <span className="text-sm">{model.label}</span>
                                    </label>
                                ))}
                            </div>
                            {errors.business_model && (
                                <p className="mt-1 text-sm text-red-600">{errors.business_model}</p>
                            )}
                        </div>
                    </div>
                );
            case 3:
                return (
                    <div className="space-y-4">
                        {renderFormField('linkedin_url', 'LinkedIn Profile', 'url', 'https://linkedin.com/company/yourcompany')}
                        {renderFormField('twitter_handle', 'Twitter Handle', 'text', '@yourcompany')}
                        {renderFormField('facebook_url', 'Facebook Page', 'url', 'https://facebook.com/yourcompany')}
                        {renderFormField('instagram_url', 'Instagram Profile', 'url', 'https://instagram.com/yourcompany')}
                    </div>
                );
            case 4:
                return (
                    <div className="space-y-6">
                        {/* Categories */}
                        <div className="input-area">
                            <label className="form-label">Categories (Max 5)</label>
                            <div className="grid grid-cols-2 gap-3 mt-2 max-h-48 overflow-y-auto">
                                {taxonomyOptions?.categories?.map(category => (
                                    <label key={category.id} className="flex items-center space-x-2">
                                        <input
                                            type="checkbox"
                                            checked={formData.categories.includes(category.id)}
                                            onChange={(e) => {
                                                const checked = e.target.checked;
                                                const categoryId = category.id;
                                                
                                                if (checked && formData.categories.length < 5) {
                                                    handleMultiSelect('categories', [...formData.categories, categoryId]);
                                                } else if (!checked) {
                                                    handleMultiSelect('categories', formData.categories.filter(id => id !== categoryId));
                                                }
                                            }}
                                            disabled={!formData.categories.includes(category.id) && formData.categories.length >= 5}
                                            className="form-checkbox"
                                        />
                                        <span className="text-sm">{category.name}</span>
                                    </label>
                                ))}
                            </div>
                        </div>

                        {/* Keywords */}
                        <div className="input-area">
                            <label className="form-label">Keywords (Max 10)</label>
                            <div className="space-y-2">
                                <input
                                    type="text"
                                    placeholder="Add a keyword and press Enter"
                                    className="form-control"
                                    onKeyPress={(e) => {
                                        if (e.key === 'Enter') {
                                            e.preventDefault();
                                            handleKeywordAdd(e.target.value.trim());
                                            e.target.value = '';
                                        }
                                    }}
                                    disabled={formData.keywords.length >= 10}
                                />
                                <div className="flex flex-wrap gap-2">
                                    {formData.keywords.map(keyword => (
                                        <span
                                            key={keyword}
                                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                                        >
                                            {keyword}
                                            <button
                                                type="button"
                                                onClick={() => handleKeywordRemove(keyword)}
                                                className="ml-1 text-blue-600 hover:text-blue-800"
                                            >
                                                ×
                                            </button>
                                        </span>
                                    ))}
                                </div>
                            </div>
                        </div>

                        {/* Industries */}
                        <div className="input-area">
                            <label className="form-label">Industries (Max 5)</label>
                            <div className="grid grid-cols-2 gap-3 mt-2 max-h-48 overflow-y-auto">
                                {taxonomyOptions?.industries?.map(industry => (
                                    <label key={industry.id} className="flex items-center space-x-2">
                                        <input
                                            type="checkbox"
                                            checked={formData.industries.includes(industry.id)}
                                            onChange={(e) => {
                                                const checked = e.target.checked;
                                                const industryId = industry.id;
                                                
                                                if (checked && formData.industries.length < 5) {
                                                    handleMultiSelect('industries', [...formData.industries, industryId]);
                                                } else if (!checked) {
                                                    handleMultiSelect('industries', formData.industries.filter(id => id !== industryId));
                                                }
                                            }}
                                            disabled={!formData.industries.includes(industry.id) && formData.industries.length >= 5}
                                            className="form-checkbox"
                                        />
                                        <span className="text-sm">{industry.name}</span>
                                    </label>
                                ))}
                            </div>
                        </div>

                        {/* Technologies */}
                        <div className="input-area">
                            <label className="form-label">Technologies (Max 10)</label>
                            <div className="grid grid-cols-2 gap-3 mt-2 max-h-48 overflow-y-auto">
                                {taxonomyOptions?.technologies?.map(technology => (
                                    <label key={technology.id} className="flex items-center space-x-2">
                                        <input
                                            type="checkbox"
                                            checked={formData.technologies.includes(technology.id)}
                                            onChange={(e) => {
                                                const checked = e.target.checked;
                                                const technologyId = technology.id;
                                                
                                                if (checked && formData.technologies.length < 10) {
                                                    handleMultiSelect('technologies', [...formData.technologies, technologyId]);
                                                } else if (!checked) {
                                                    handleMultiSelect('technologies', formData.technologies.filter(id => id !== technologyId));
                                                }
                                            }}
                                            disabled={!formData.technologies.includes(technology.id) && formData.technologies.length >= 10}
                                            className="form-checkbox"
                                        />
                                        <span className="text-sm">{technology.name}</span>
                                    </label>
                                ))}
                            </div>
                        </div>
                    </div>
                );
            default:
                return null;
        }
    };

    return (
        <div className="wizard card">
            <div className="card-body p-6">
                {renderProgressBar()}
                
                <form className="wizard-form" onSubmit={handleSubmit}>
                    <div className="wizard-form-step active">
                        {/* Step Content */}
                        <div className="mb-8">
                            <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">
                                {steps[currentStep - 1]?.title}
                            </h3>
                            <p className="text-sm text-slate-600 dark:text-slate-400">
                                {steps[currentStep - 1]?.description}
                            </p>
                        </div>

                        {renderCurrentStep()}

                        {/* Navigation Buttons */}
                        <div className="flex justify-between mt-8 pt-6 border-t border-slate-200 dark:border-slate-700">
                            <button
                                type="button"
                                onClick={prevStep}
                                disabled={currentStep === 1}
                                className="btn btn-outline-secondary"
                            >
                                Previous
                            </button>
                            
                            {currentStep < steps.length ? (
                                <button
                                    type="button"
                                    onClick={nextStep}
                                    className="btn btn-primary"
                                >
                                    Next
                                </button>
                            ) : (
                                <button
                                    type="submit"
                                    disabled={loading}
                                    className="btn btn-primary"
                                >
                                    {loading ? 'Saving...' : 'Save Profile'}
                                </button>
                            )}
                        </div>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default ProfileWizard;
