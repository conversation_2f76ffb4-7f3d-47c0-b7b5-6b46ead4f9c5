import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import LoadingSpinner from './LoadingSpinner';

const PublicRoute = ({ children }) => {
    const { isAuthenticated, user, loading } = useAuth();

    if (loading) {
        return <LoadingSpinner />;
    }

    if (isAuthenticated) {
        // Redirect to appropriate dashboard based on user role
        if (user?.roles?.includes('investor')) {
            return <Navigate to="/investor/dashboard" replace />;
        } else if (user?.roles?.includes('startup')) {
            return <Navigate to="/startup/dashboard" replace />;
        } else if (user?.roles?.includes('analyst')) {
            return <Navigate to="/analyst/dashboard" replace />;
        }
    }

    return children;
};

export default PublicRoute;
