import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import LoadingSpinner from './LoadingSpinner';

const ProtectedRoute = ({ children, allowedRoles = [] }) => {
    const { isAuthenticated, user, loading, hasAnyRole } = useAuth();

    if (loading) {
        return <LoadingSpinner />;
    }

    if (!isAuthenticated) {
        return <Navigate to="/login" replace />;
    }

    if (allowedRoles.length > 0 && !hasAnyRole(allowedRoles)) {
        // Redirect to appropriate dashboard based on user role
        if (user?.roles?.includes('investor')) {
            return <Navigate to="/investor/dashboard" replace />;
        } else if (user?.roles?.includes('startup')) {
            return <Navigate to="/startup/dashboard" replace />;
        } else if (user?.roles?.includes('analyst')) {
            return <Navigate to="/analyst/dashboard" replace />;
        } else {
            return <Navigate to="/login" replace />;
        }
    }

    return children;
};

export default ProtectedRoute;
