import React, { useState } from 'react';
import { apiService } from '../services/apiService';

const SubscriptionCancellation = ({ subscription, onCancel, onClose }) => {
    const [loading, setLoading] = useState(false);
    const [proratedRefund, setProratedRefund] = useState(null);
    const [calculatingRefund, setCalculatingRefund] = useState(false);
    const [cancellationDate, setCancellationDate] = useState(
        new Date().toISOString().split('T')[0]
    );
    const [reason, setReason] = useState('requested_by_customer');
    const [description, setDescription] = useState('');

    const calculateProratedRefund = async () => {
        setCalculatingRefund(true);
        try {
            const response = await apiService.calculateProratedRefund(
                subscription.id,
                cancellationDate
            );
            setProratedRefund(response.data.data);
        } catch (error) {
            console.error('Failed to calculate prorated refund:', error);
            alert('Failed to calculate refund amount. Please try again.');
        } finally {
            setCalculatingRefund(false);
        }
    };

    const handleCancelSubscription = async () => {
        setLoading(true);
        try {
            // Cancel subscription with prorated refund
            await apiService.post('/subscriptions/cancel-with-refund', {
                subscription_id: subscription.id,
                cancellation_date: cancellationDate,
                reason: reason,
                description: description,
                process_refund: proratedRefund && proratedRefund.prorated_amount > 0
            });

            alert('Subscription canceled successfully! Refund will be processed within 5-10 business days.');
            onCancel();
        } catch (error) {
            console.error('Failed to cancel subscription:', error);
            alert('Failed to cancel subscription. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const formatAmount = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString();
    };

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white dark:bg-gray-800">
                <div className="mt-3">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        Cancel Subscription
                    </h3>
                    
                    {/* Subscription Details */}
                    <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                            Current Subscription
                        </h4>
                        <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                            <p>Plan: {subscription.subscription_product?.name}</p>
                            <p>Amount: {formatAmount(subscription.amount)}</p>
                            <p>Current Period: {formatDate(subscription.current_period_start)} - {formatDate(subscription.current_period_end)}</p>
                            <p>Status: <span className="capitalize">{subscription.status}</span></p>
                        </div>
                    </div>

                    {/* Cancellation Date */}
                    <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Cancellation Date
                        </label>
                        <input
                            type="date"
                            value={cancellationDate}
                            onChange={(e) => setCancellationDate(e.target.value)}
                            min={new Date().toISOString().split('T')[0]}
                            max={subscription.current_period_end?.split('T')[0]}
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                        />
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Choose a date between today and your current period end
                        </p>
                    </div>

                    {/* Calculate Refund Button */}
                    <div className="mb-4">
                        <button
                            onClick={calculateProratedRefund}
                            disabled={calculatingRefund}
                            className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
                        >
                            {calculatingRefund ? 'Calculating...' : 'Calculate Prorated Refund'}
                        </button>
                    </div>

                    {/* Prorated Refund Details */}
                    {proratedRefund && (
                        <div className="mb-6 p-4 bg-green-50 dark:bg-green-900 rounded-lg">
                            <h4 className="font-medium text-green-900 dark:text-green-100 mb-2">
                                Refund Calculation
                            </h4>
                            <div className="text-sm text-green-800 dark:text-green-200 space-y-1">
                                <p>Service Period: {proratedRefund.total_days} days</p>
                                <p>Days Used: {proratedRefund.days_used} days</p>
                                <p>Days Remaining: {proratedRefund.total_days - proratedRefund.days_used} days</p>
                                <p>Original Amount: {formatAmount(proratedRefund.original_amount)}</p>
                                <p className="font-semibold">
                                    Refund Amount: {formatAmount(proratedRefund.prorated_amount)}
                                </p>
                            </div>
                            {proratedRefund.prorated_amount <= 0 && (
                                <p className="text-yellow-800 dark:text-yellow-200 text-sm mt-2">
                                    No refund available - service period has been fully used.
                                </p>
                            )}
                        </div>
                    )}

                    {/* Cancellation Reason */}
                    <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Reason for Cancellation
                        </label>
                        <select
                            value={reason}
                            onChange={(e) => setReason(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                        >
                            <option value="requested_by_customer">No longer needed</option>
                            <option value="subscription_cancellation">Service issues</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    {/* Description */}
                    <div className="mb-6">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Additional Comments (Optional)
                        </label>
                        <textarea
                            value={description}
                            onChange={(e) => setDescription(e.target.value)}
                            rows="3"
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                            placeholder="Please let us know why you're canceling..."
                        />
                    </div>

                    {/* Warning */}
                    <div className="mb-6 p-4 bg-red-50 dark:bg-red-900 rounded-lg">
                        <div className="flex">
                            <div className="flex-shrink-0">
                                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <div className="ml-3">
                                <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                                    Important Notice
                                </h3>
                                <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                                    <p>
                                        Canceling your subscription will immediately revoke access to all premium features. 
                                        {proratedRefund && proratedRefund.prorated_amount > 0 && (
                                            <span> Your prorated refund will be processed within 5-10 business days.</span>
                                        )}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex justify-end space-x-3">
                        <button
                            type="button"
                            onClick={onClose}
                            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500"
                        >
                            Keep Subscription
                        </button>
                        <button
                            onClick={handleCancelSubscription}
                            disabled={loading || !proratedRefund}
                            className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 disabled:opacity-50"
                        >
                            {loading ? 'Canceling...' : 'Cancel Subscription'}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SubscriptionCancellation;
