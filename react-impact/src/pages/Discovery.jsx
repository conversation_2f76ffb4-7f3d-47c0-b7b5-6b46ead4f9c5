import React, { useState, useEffect } from 'react';
import Layout from '../components/Layout';
import SubscriptionGate from '../components/SubscriptionGate';
import { apiService } from '../services/apiService';
import { useAuth } from '../contexts/AuthContext';
import LoadingSpinner from '../components/LoadingSpinner';

const Discovery = () => {
    const { user } = useAuth();
    const [searchQuery, setSearchQuery] = useState('');
    const [filters, setFilters] = useState({
        industry: '',
        funding_stage: '',
        esg_score: ''
    });
    const [results, setResults] = useState([]);
    const [loading, setLoading] = useState(false);
    const [categories, setCategories] = useState([]);

    useEffect(() => {
        fetchCategories();
        // Auto-load category-based matches for subscribed users
        if (user) {
            loadCategoryBasedMatches();
        }
    }, [user]);

    const fetchCategories = async () => {
        try {
            const response = await apiService.getCategories();
            setCategories(response.data.data || []);
        } catch (error) {
            console.error('Failed to fetch categories:', error);
        }
    };

    const loadCategoryBasedMatches = async () => {
        try {
            setLoading(true);

            // Check if user has an active subscription
            const subscriptionResponse = await apiService.get('/subscriptions');
            const hasActiveSubscription = subscriptionResponse.data.data &&
                subscriptionResponse.data.data.some(sub => sub.status === 'active' || sub.status === 'trialing');

            if (!hasActiveSubscription) {
                setResults([]);
                return;
            }

            // Load category-based matches
            let response;
            if (user?.role === 'investor') {
                response = await apiService.get('/discovery/category-matches/startups');
            } else if (user?.role === 'startup') {
                response = await apiService.get('/discovery/category-matches/investors');
            }

            if (response?.data?.data) {
                setResults(response.data.data);
            }
        } catch (error) {
            console.error('Failed to load category-based matches:', error);
            setResults([]);
        } finally {
            setLoading(false);
        }
    };

    const handleSearch = async () => {
        setLoading(true);
        try {
            const params = {
                query: searchQuery,
                ...filters
            };

            let response;
            if (user?.roles?.includes('investor')) {
                response = await apiService.discoverStartups(params);
            } else if (user?.roles?.includes('startup')) {
                response = await apiService.discoverInvestors(params);
            }

            setResults(response?.data?.data || []);
        } catch (error) {
            console.error('Search failed:', error);
            setResults([]);
        } finally {
            setLoading(false);
        }
    };

    const handleFilterChange = (e) => {
        const { name, value } = e.target;
        setFilters(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const renderResultCard = (item) => {
        const isStartup = user?.roles?.includes('investor');

        return (
            <div key={item.id} className="bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg p-6 hover:shadow-md transition-shadow">
                <div className="flex justify-between items-start mb-4">
                    <div>
                        <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                            {isStartup ? item.company_name : item.full_name}
                        </h3>
                        <p className="text-slate-600 dark:text-slate-400">
                            {isStartup ? item.industry : item.investment_focus}
                        </p>
                    </div>
                    {item.match_score && (
                        <div className="text-right">
                            <div className="text-sm text-slate-500 dark:text-slate-400">Match Score</div>
                            <div className="text-lg font-bold text-green-600 dark:text-green-400">
                                {item.match_score}%
                            </div>
                        </div>
                    )}
                </div>

                <p className="text-slate-600 dark:text-slate-400 mb-4 line-clamp-3">
                    {item.description}
                </p>

                <div className="flex justify-between items-center">
                    <div className="flex space-x-4 text-sm text-slate-500 dark:text-slate-400">
                        {isStartup && (
                            <>
                                <span>Stage: {item.funding_stage || 'N/A'}</span>
                                <span>ESG: {item.esg_score || 'N/A'}</span>
                            </>
                        )}
                        {!isStartup && (
                            <span>Range: ${item.min_investment || 'N/A'} - ${item.max_investment || 'N/A'}</span>
                        )}
                    </div>
                    <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors text-sm">
                        Connect
                    </button>
                </div>
            </div>
        );
    };

    return (
        <Layout>
            <div className="space-y-6">
                {/* Page Header */}
                <div className="mb-8">
                    <h1 className="text-2xl font-bold text-slate-900 dark:text-white">
                        Discovery
                    </h1>
                    <p className="text-slate-600 dark:text-slate-400 mt-1">
                        Find and connect with investment opportunities
                    </p>
                </div>

                {/* Category-Based Discovery - For subscribed users */}
                <SubscriptionGate requiredPlan="basic" showUpgradePrompt={true}>
                    <div className="bg-white dark:bg-slate-800 shadow-sm rounded-lg border border-slate-200 dark:border-slate-700 p-6">
                        <div className="flex justify-between items-center mb-4">
                            <h2 className="text-lg font-medium text-slate-900 dark:text-white">
                                {user?.role === 'investor' ? 'Recommended Startups' : 'Recommended Investors'}
                            </h2>
                            <button
                                onClick={loadCategoryBasedMatches}
                                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors text-sm"
                            >
                                Refresh Matches
                            </button>
                        </div>
                        <p className="text-slate-600 dark:text-slate-400 mb-4">
                            {user?.role === 'investor'
                                ? 'Discover startups that match your investment categories and preferences.'
                                : 'Find investors interested in your industry and business categories.'
                            }
                        </p>

                        {/* Category-based results preview */}
                        {results.length > 0 && (
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                                {results.slice(0, 6).map(item => (
                                    <div key={item.id} className="border border-slate-200 dark:border-slate-600 rounded-lg p-4 hover:shadow-md transition-shadow">
                                        <div className="flex items-center space-x-3 mb-3">
                                            <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                                                <span className="text-blue-600 dark:text-blue-400 font-semibold text-sm">
                                                    {user?.role === 'investor'
                                                        ? (item.company_name || item.name || 'S').charAt(0).toUpperCase()
                                                        : (item.name || item.full_name || 'I').charAt(0).toUpperCase()
                                                    }
                                                </span>
                                            </div>
                                            <div className="flex-1">
                                                <h4 className="font-medium text-slate-900 dark:text-white text-sm">
                                                    {user?.role === 'investor'
                                                        ? (item.company_name || item.name || 'Startup')
                                                        : (item.name || item.full_name || 'Investor')
                                                    }
                                                </h4>
                                                <p className="text-xs text-slate-500 dark:text-slate-400">
                                                    {user?.role === 'investor'
                                                        ? (item.industry || 'Technology')
                                                        : (item.investment_focus || 'Various Industries')
                                                    }
                                                </p>
                                            </div>
                                        </div>
                                        {item.match_score && (
                                            <div className="text-center mb-2">
                                                <span className="text-xs text-slate-500 dark:text-slate-400">Match Score</span>
                                                <div className="text-sm font-bold text-green-600 dark:text-green-400">
                                                    {item.match_score}%
                                                </div>
                                            </div>
                                        )}
                                        <button className="w-full bg-blue-600 text-white px-3 py-1.5 rounded-md hover:bg-blue-700 transition-colors text-xs">
                                            Connect
                                        </button>
                                    </div>
                                ))}
                            </div>
                        )}

                        {results.length === 0 && !loading && (
                            <div className="text-center py-8">
                                <div className="text-slate-400 mb-2">
                                    <svg className="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                    </svg>
                                </div>
                                <h3 className="text-sm font-medium text-slate-900 dark:text-white mb-1">
                                    No matches found
                                </h3>
                                <p className="text-xs text-slate-500 dark:text-slate-400">
                                    Complete your profile and select categories to get better matches.
                                </p>
                            </div>
                        )}
                    </div>
                </SubscriptionGate>

                {/* Basic Search - Available to all users */}
                <div className="bg-white dark:bg-slate-800 shadow-sm rounded-lg border border-slate-200 dark:border-slate-700 p-6">
                    <h2 className="text-lg font-medium text-slate-900 dark:text-white mb-4">
                        Basic Search
                    </h2>
                    <div className="space-y-4">
                        <div>
                            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                                Search Keywords
                            </label>
                            <input
                                type="text"
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
                                placeholder="Enter keywords..."
                            />
                        </div>
                        <button
                            onClick={handleSearch}
                            disabled={loading}
                            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
                        >
                            {loading ? 'Searching...' : 'Search'}
                        </button>
                    </div>
                </div>

                {/* Advanced Filters - Premium Feature */}
                <SubscriptionGate requiredPlan="premium" feature="Advanced Filtering">
                    <div className="bg-white dark:bg-slate-800 shadow-sm rounded-lg border border-slate-200 dark:border-slate-700 p-6">
                        <h2 className="text-lg font-medium text-slate-900 dark:text-white mb-4">
                            Advanced Filters
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                                    Industry
                                </label>
                                <select
                                    name="industry"
                                    value={filters.industry}
                                    onChange={handleFilterChange}
                                    className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
                                >
                                    <option value="">All Industries</option>
                                    {categories.map(category => (
                                        <option key={category.id} value={category.name}>
                                            {category.name}
                                        </option>
                                    ))}
                                </select>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                                    Funding Stage
                                </label>
                                <select
                                    name="funding_stage"
                                    value={filters.funding_stage}
                                    onChange={handleFilterChange}
                                    className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
                                >
                                    <option value="">All Stages</option>
                                    <option value="seed">Seed</option>
                                    <option value="series_a">Series A</option>
                                    <option value="series_b">Series B</option>
                                    <option value="series_c">Series C</option>
                                </select>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                                    ESG Score
                                </label>
                                <select
                                    name="esg_score"
                                    value={filters.esg_score}
                                    onChange={handleFilterChange}
                                    className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
                                >
                                    <option value="">Any Score</option>
                                    <option value="80">80+ (Excellent)</option>
                                    <option value="60">60-79 (Good)</option>
                                    <option value="40">40-59 (Fair)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </SubscriptionGate>

                {/* AI-Powered Matching - Enterprise Feature */}
                <SubscriptionGate requiredPlan="enterprise" feature="AI-Powered Matching">
                    <div className="bg-white dark:bg-slate-800 shadow-sm rounded-lg border border-slate-200 dark:border-slate-700 p-6">
                        <h2 className="text-lg font-medium text-slate-900 dark:text-white mb-4">
                            AI-Powered Matching
                        </h2>
                        <p className="text-slate-600 dark:text-slate-400 mb-4">
                            Get personalized recommendations based on your investment preferences and portfolio.
                        </p>
                        <button className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-2 rounded-md hover:from-purple-700 hover:to-blue-700 transition-colors">
                            Generate AI Recommendations
                        </button>
                    </div>
                </SubscriptionGate>

                {/* Results Section */}
                <div className="bg-white dark:bg-slate-800 shadow-sm rounded-lg border border-slate-200 dark:border-slate-700 p-6">
                    <h2 className="text-lg font-medium text-slate-900 dark:text-white mb-4">
                        Search Results
                    </h2>
                    {loading ? (
                        <div className="flex justify-center py-12">
                            <LoadingSpinner />
                        </div>
                    ) : results.length > 0 ? (
                        <div className="space-y-4">
                            {results.map(renderResultCard)}
                        </div>
                    ) : (
                        <div className="text-center py-12">
                            <svg className="mx-auto h-12 w-12 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                            <h3 className="mt-2 text-sm font-medium text-slate-900 dark:text-white">
                                {searchQuery || Object.values(filters).some(f => f) ? 'No results found' : 'No results yet'}
                            </h3>
                            <p className="mt-1 text-sm text-slate-500 dark:text-slate-400">
                                {searchQuery || Object.values(filters).some(f => f)
                                    ? 'Try adjusting your search criteria.'
                                    : 'Start searching to discover investment opportunities.'
                                }
                            </p>
                        </div>
                    )}
                </div>
            </div>
        </Layout>
    );
};

export default Discovery;
