import React, { useState, useEffect, useContext } from 'react';
import { useSearchParams } from 'react-router-dom';
import Layout from '../../components/Layout';
import LoadingSpinner from '../../components/LoadingSpinner';
import { AuthContext } from '../../contexts/AuthContext';
import { apiService } from '../../services/apiService';

const FAQ = () => {
    const { user } = useContext(AuthContext);
    const [faqs, setFaqs] = useState([]);
    const [categories, setCategories] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('');
    const [expandedItems, setExpandedItems] = useState(new Set());
    const [searchParams, setSearchParams] = useSearchParams();

    useEffect(() => {
        // Get initial parameters from URL
        const category = searchParams.get('category') || '';
        const search = searchParams.get('search') || '';
        
        setSelectedCategory(category);
        setSearchQuery(search);
        
        fetchFAQs(category, search);
        fetchCategories();
    }, [searchParams, user]);

    const fetchFAQs = async (category = '', search = '') => {
        try {
            setLoading(true);
            const params = {
                role: user?.role,
                per_page: 100 // Get all FAQs for better UX
            };
            
            if (category) params.category = category;
            if (search) params.search = search;

            let response;
            if (search) {
                response = await apiService.searchFAQs(search, user?.role, params);
            } else if (category) {
                response = await apiService.getFAQsByCategory(category, params);
            } else if (user?.role) {
                response = await apiService.getFAQsByRole(user.role, params);
            } else {
                response = await apiService.getFAQs(params);
            }
            
            setFaqs(response.data.data || response.data || []);
        } catch (error) {
            console.error('Error fetching FAQs:', error);
            setFaqs([]);
        } finally {
            setLoading(false);
        }
    };

    const fetchCategories = async () => {
        try {
            const response = await apiService.getFAQCategories(user?.role);
            setCategories(response.data || []);
        } catch (error) {
            console.error('Error fetching categories:', error);
        }
    };

    const handleSearch = (e) => {
        e.preventDefault();
        updateFilters(selectedCategory, searchQuery);
    };

    const handleCategoryChange = (category) => {
        setSelectedCategory(category);
        updateFilters(category, searchQuery);
    };

    const updateFilters = (category, search) => {
        const params = new URLSearchParams();
        if (category) params.set('category', category);
        if (search) params.set('search', search);
        
        setSearchParams(params);
    };

    const toggleExpanded = (faqId) => {
        const newExpanded = new Set(expandedItems);
        if (newExpanded.has(faqId)) {
            newExpanded.delete(faqId);
        } else {
            newExpanded.add(faqId);
        }
        setExpandedItems(newExpanded);
    };

    const expandAll = () => {
        setExpandedItems(new Set(faqs.map(faq => faq.id)));
    };

    const collapseAll = () => {
        setExpandedItems(new Set());
    };

    const getRoleDisplayName = (role) => {
        const roleNames = {
            investor: 'Investor',
            startup: 'Startup',
            analyst: 'Analyst'
        };
        return roleNames[role] || 'General';
    };

    if (loading && faqs.length === 0) {
        return (
            <Layout>
                <div className="flex justify-center items-center min-h-screen">
                    <LoadingSpinner />
                </div>
            </Layout>
        );
    }

    return (
        <Layout>
            <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
                {/* Header */}
                <div className="bg-white dark:bg-gray-800 shadow-sm">
                    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                        <div className="text-center">
                            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                                Frequently Asked Questions
                            </h1>
                            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                Find answers to common questions about our investment platform
                                {user?.role && ` for ${getRoleDisplayName(user.role)}s`}.
                            </p>
                        </div>
                    </div>
                </div>

                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    {/* Search and Filters */}
                    <div className="mb-8 bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                        <div className="flex flex-col lg:flex-row gap-4 mb-4">
                            {/* Search */}
                            <form onSubmit={handleSearch} className="flex-1">
                                <div className="relative">
                                    <input
                                        type="text"
                                        placeholder="Search FAQs..."
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                        className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                                    />
                                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                        </svg>
                                    </div>
                                </div>
                            </form>

                            {/* Category Filter */}
                            <div className="lg:w-64">
                                <select
                                    value={selectedCategory}
                                    onChange={(e) => handleCategoryChange(e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                                >
                                    <option value="">All Categories</option>
                                    {categories.map((category) => (
                                        <option key={category.id} value={category.id}>
                                            {category.name}
                                        </option>
                                    ))}
                                </select>
                            </div>
                        </div>

                        {/* Expand/Collapse Controls */}
                        {faqs.length > 0 && (
                            <div className="flex justify-end space-x-2">
                                <button
                                    onClick={expandAll}
                                    className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                                >
                                    Expand All
                                </button>
                                <span className="text-gray-300 dark:text-gray-600">|</span>
                                <button
                                    onClick={collapseAll}
                                    className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                                >
                                    Collapse All
                                </button>
                            </div>
                        )}
                    </div>

                    {/* FAQ List */}
                    {loading ? (
                        <div className="flex justify-center py-12">
                            <LoadingSpinner />
                        </div>
                    ) : faqs.length > 0 ? (
                        <div className="space-y-4">
                            {faqs.map((faq, index) => (
                                <div key={faq.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
                                    <button
                                        onClick={() => toggleExpanded(faq.id)}
                                        className="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset"
                                    >
                                        <div className="flex items-center justify-between">
                                            <div className="flex-1">
                                                <h3 className="text-lg font-medium text-gray-900 dark:text-white pr-4">
                                                    {faq.question}
                                                </h3>
                                                {faq.category && (
                                                    <span className="inline-block mt-2 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-medium px-2.5 py-0.5 rounded">
                                                        {faq.category.name}
                                                    </span>
                                                )}
                                            </div>
                                            <div className="flex-shrink-0">
                                                <svg
                                                    className={`w-5 h-5 text-gray-500 transform transition-transform duration-200 ${
                                                        expandedItems.has(faq.id) ? 'rotate-180' : ''
                                                    }`}
                                                    fill="none"
                                                    stroke="currentColor"
                                                    viewBox="0 0 24 24"
                                                >
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                                </svg>
                                            </div>
                                        </div>
                                    </button>
                                    
                                    {expandedItems.has(faq.id) && (
                                        <div className="px-6 pb-4">
                                            <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                                                <div 
                                                    className="prose prose-sm dark:prose-invert max-w-none text-gray-600 dark:text-gray-300"
                                                    dangerouslySetInnerHTML={{ __html: faq.answer }}
                                                />
                                            </div>
                                        </div>
                                    )}
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="text-center py-12">
                            <div className="max-w-md mx-auto">
                                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No FAQs found</h3>
                                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                    {searchQuery || selectedCategory ? 'Try adjusting your search or filter criteria.' : 'Check back later for new content.'}
                                </p>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </Layout>
    );
};

export default FAQ;
