import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { apiService } from '../../services/apiService';
import Layout from '../../components/Layout';

const PaymentMethodSuccess = () => {
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [processingSubscription, setProcessingSubscription] = useState(false);

    useEffect(() => {
        handlePaymentMethodSuccess();
    }, []);

    const handlePaymentMethodSuccess = async () => {
        try {
            setLoading(true);

            // Get session ID from URL parameters
            const sessionId = searchParams.get('session_id');

            if (!sessionId) {
                setError('No session ID found. Please try adding your payment method again.');
                setLoading(false);
                return;
            }

            // Process the payment method through our API
            const response = await apiService.post('/payment-methods/process-checkout-success', {
                session_id: sessionId
            });

            if (!response.data.success) {
                setError(response.data.message || 'Failed to process payment method');
                setLoading(false);
                return;
            }

            // Check if there's a pending subscription plan
            const pendingPlanStr = localStorage.getItem('pendingSubscriptionPlan');

            if (pendingPlanStr) {
                const pendingPlan = JSON.parse(pendingPlanStr);
                localStorage.removeItem('pendingSubscriptionPlan');

                // Automatically proceed with subscription
                setProcessingSubscription(true);
                await createSubscription(pendingPlan);
            } else {
                // Just redirect to payment methods page
                setTimeout(() => {
                    navigate('/app/payment-methods?success=payment_method_added');
                }, 2000);
            }
        } catch (err) {
            setError('Failed to process payment method addition');
            console.error('Error handling payment method success:', err);
        } finally {
            setLoading(false);
        }
    };

    const createSubscription = async (plan) => {
        try {
            // Create subscription with Stripe checkout
            const response = await apiService.post('/subscriptions', {
                subscription_product_id: plan.id
            });

            if (response.data.data && response.data.data.checkout_url) {
                // Redirect to Stripe Checkout for subscription
                window.location.href = response.data.data.checkout_url;
            } else {
                setError('Failed to create subscription checkout session');
            }
        } catch (err) {
            setError('Failed to start subscription process');
            console.error('Error creating subscription:', err);
        }
    };

    if (loading) {
        return (
            <Layout>
                <div className="flex items-center justify-center min-h-96">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                        <p className="text-gray-600">
                            {processingSubscription ? 'Processing your subscription...' : 'Processing payment method...'}
                        </p>
                    </div>
                </div>
            </Layout>
        );
    }

    if (error) {
        return (
            <Layout>
                <div className="text-center py-12">
                    <div className="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
                        <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </div>
                    <h1 className="text-2xl font-bold text-gray-900 mb-4">
                        Payment Method Error
                    </h1>
                    <p className="text-gray-600 mb-6">{error}</p>
                    <div className="space-x-4">
                        <button
                            onClick={() => navigate('/app/payment-methods')}
                            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
                        >
                            Go to Payment Methods
                        </button>
                        <button
                            onClick={() => navigate('/app/subscription/plans')}
                            className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700"
                        >
                            Back to Plans
                        </button>
                    </div>
                </div>
            </Layout>
        );
    }

    return (
        <Layout>
            <div className="text-center py-12">
                <div className="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
                    <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                </div>
                <h1 className="text-2xl font-bold text-gray-900 mb-4">
                    Payment Method Added Successfully!
                </h1>
                <p className="text-gray-600 mb-6">
                    Your payment method has been added. Redirecting you to continue...
                </p>
                <div className="animate-pulse">
                    <div className="h-2 bg-blue-200 rounded-full w-64 mx-auto"></div>
                </div>
            </div>
        </Layout>
    );
};

export default PaymentMethodSuccess;
