import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { apiService } from '../../services/apiService';
import Layout from '../../components/Layout';

const PaymentMethods = () => {
    const { user } = useAuth();
    const [searchParams] = useSearchParams();
    const [paymentMethods, setPaymentMethods] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [actionLoading, setActionLoading] = useState(null);
    const [successMessage, setSuccessMessage] = useState(null);

    useEffect(() => {
        fetchPaymentMethods();

        // Check for URL parameters
        const success = searchParams.get('success');
        const error = searchParams.get('error');
        const cancelled = searchParams.get('cancelled');

        if (success === 'payment_method_added') {
            setSuccessMessage('Payment method added successfully!');
        } else if (error === 'processing_failed') {
            setError('Failed to process payment method. Please try again.');
        } else if (error === 'checkout_failed') {
            setError('Failed to create checkout session. Please try again.');
        } else if (cancelled === 'true') {
            setError('Payment method addition was cancelled.');
        }
    }, [searchParams]);

    const fetchPaymentMethods = async () => {
        try {
            setLoading(true);
            const response = await apiService.getPaymentMethods();
            setPaymentMethods(response.data.data || []);
        } catch (err) {
            setError('Failed to load payment methods');
            console.error('Error fetching payment methods:', err);
        } finally {
            setLoading(false);
        }
    };

    const handleAddPaymentMethod = async () => {
        try {
            setActionLoading('add');
            const response = await apiService.createPaymentMethodCheckoutSession();
            
            if (response.data.data && response.data.data.checkout_url) {
                // Redirect to Stripe Checkout
                window.location.href = response.data.data.checkout_url;
            } else {
                setError('Failed to create checkout session');
            }
        } catch (err) {
            setError('Failed to start payment method addition process');
            console.error('Error creating checkout session:', err);
        } finally {
            setActionLoading(null);
        }
    };

    const handleSetDefault = async (paymentMethodId) => {
        try {
            setActionLoading(`default-${paymentMethodId}`);
            await apiService.setDefaultPaymentMethod(paymentMethodId);
            
            // Update local state
            setPaymentMethods(prev => prev.map(pm => ({
                ...pm,
                is_default: pm.id === paymentMethodId
            })));
            
        } catch (err) {
            setError('Failed to set default payment method');
            console.error('Error setting default payment method:', err);
        } finally {
            setActionLoading(null);
        }
    };

    const handleDeletePaymentMethod = async (paymentMethodId) => {
        if (!confirm('Are you sure you want to delete this payment method?')) {
            return;
        }

        try {
            setActionLoading(`delete-${paymentMethodId}`);
            await apiService.deletePaymentMethod(paymentMethodId);
            
            // Remove from local state
            setPaymentMethods(prev => prev.filter(pm => pm.id !== paymentMethodId));
            
        } catch (err) {
            setError('Failed to delete payment method');
            console.error('Error deleting payment method:', err);
        } finally {
            setActionLoading(null);
        }
    };

    const getCardBrandIcon = (brand) => {
        const brandIcons = {
            visa: '💳',
            mastercard: '💳',
            amex: '💳',
            discover: '💳',
            diners: '💳',
            jcb: '💳',
            unionpay: '💳'
        };
        return brandIcons[brand?.toLowerCase()] || '💳';
    };

    const formatCardBrand = (brand) => {
        if (!brand) return 'Card';
        return brand.charAt(0).toUpperCase() + brand.slice(1);
    };

    if (loading) {
        return (
            <Layout>
                <div className="flex items-center justify-center min-h-96">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                </div>
            </Layout>
        );
    }

    return (
        <Layout>
            <div className="space-y-6">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">
                            Payment Methods
                        </h1>
                        <p className="text-gray-600 mt-2">
                            Manage your saved payment methods for subscriptions and purchases
                        </p>
                    </div>
                    <button
                        onClick={handleAddPaymentMethod}
                        disabled={actionLoading === 'add'}
                        className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                    >
                        {actionLoading === 'add' ? (
                            <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                <span>Adding...</span>
                            </>
                        ) : (
                            <>
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                                </svg>
                                <span>Add Payment Method</span>
                            </>
                        )}
                    </button>
                </div>

                {/* Success Message */}
                {successMessage && (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div className="flex">
                            <svg className="w-5 h-5 text-green-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                            <div>
                                <h3 className="text-sm font-medium text-green-800">Success</h3>
                                <p className="text-sm text-green-700 mt-1">{successMessage}</p>
                            </div>
                        </div>
                    </div>
                )}

                {/* Error Message */}
                {error && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div className="flex">
                            <svg className="w-5 h-5 text-red-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                            </svg>
                            <div>
                                <h3 className="text-sm font-medium text-red-800">Error</h3>
                                <p className="text-sm text-red-700 mt-1">{error}</p>
                            </div>
                        </div>
                    </div>
                )}

                {/* Payment Methods List */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200">
                    {paymentMethods.length === 0 ? (
                        <div className="text-center py-12">
                            <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                                </svg>
                            </div>
                            <h3 className="text-lg font-medium text-gray-900 mb-2">No payment methods</h3>
                            <p className="text-gray-600 mb-6">Add a payment method to start subscribing to plans</p>
                            <button
                                onClick={handleAddPaymentMethod}
                                disabled={actionLoading === 'add'}
                                className="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50"
                            >
                                Add Your First Payment Method
                            </button>
                        </div>
                    ) : (
                        <div className="divide-y divide-gray-200">
                            {paymentMethods.map((paymentMethod) => (
                                <div key={paymentMethod.id} className="p-6">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-4">
                                            <div className="text-2xl">
                                                {getCardBrandIcon(paymentMethod.card_brand)}
                                            </div>
                                            <div>
                                                <div className="flex items-center space-x-2">
                                                    <h3 className="text-lg font-medium text-gray-900">
                                                        {formatCardBrand(paymentMethod.card_brand)} •••• {paymentMethod.card_last_four}
                                                    </h3>
                                                    {paymentMethod.is_default && (
                                                        <span className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                                            Default
                                                        </span>
                                                    )}
                                                </div>
                                                <p className="text-sm text-gray-600">
                                                    Expires {paymentMethod.card_exp_month?.toString().padStart(2, '0')}/{paymentMethod.card_exp_year}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="flex items-center space-x-3">
                                            {!paymentMethod.is_default && (
                                                <button
                                                    onClick={() => handleSetDefault(paymentMethod.id)}
                                                    disabled={actionLoading === `default-${paymentMethod.id}`}
                                                    className="text-blue-600 hover:text-blue-700 font-medium text-sm disabled:opacity-50"
                                                >
                                                    {actionLoading === `default-${paymentMethod.id}` ? 'Setting...' : 'Set as Default'}
                                                </button>
                                            )}
                                            <button
                                                onClick={() => handleDeletePaymentMethod(paymentMethod.id)}
                                                disabled={actionLoading === `delete-${paymentMethod.id}`}
                                                className="text-red-600 hover:text-red-700 font-medium text-sm disabled:opacity-50"
                                            >
                                                {actionLoading === `delete-${paymentMethod.id}` ? 'Deleting...' : 'Delete'}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </div>
        </Layout>
    );
};

export default PaymentMethods;
