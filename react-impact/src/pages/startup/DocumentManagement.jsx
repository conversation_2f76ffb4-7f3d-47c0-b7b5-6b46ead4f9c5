import React, { useState, useEffect } from 'react';
import Layout from '../../components/Layout';
import SubscriptionGate from '../../components/SubscriptionGate';
import { apiService } from '../../services/apiService';
import LoadingSpinner from '../../components/LoadingSpinner';

const DocumentManagement = () => {
    const [documents, setDocuments] = useState([]);
    const [loading, setLoading] = useState(true);
    const [uploading, setUploading] = useState(false);

    useEffect(() => {
        fetchDocuments();
    }, []);

    const fetchDocuments = async () => {
        try {
            const response = await apiService.getStartupDocuments();
            setDocuments(response.data.data || []);
        } catch (error) {
            console.error('Failed to fetch documents:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleFileUpload = async (event, documentType) => {
        const file = event.target.files[0];
        if (!file) return;

        setUploading(true);
        try {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('document_type', documentType);

            await apiService.uploadStartupDocument(formData);
            alert('Document uploaded successfully!');
            fetchDocuments(); // Refresh the list
        } catch (error) {
            console.error('Failed to upload document:', error);
            alert('Failed to upload document. Please try again.');
        } finally {
            setUploading(false);
        }
    };

    const handleDeleteDocument = async (documentId) => {
        if (!confirm('Are you sure you want to delete this document?')) return;

        try {
            await apiService.deleteStartupDocument(documentId);
            alert('Document deleted successfully!');
            fetchDocuments(); // Refresh the list
        } catch (error) {
            console.error('Failed to delete document:', error);
            alert('Failed to delete document. Please try again.');
        }
    };

    if (loading) {
        return (
            <Layout>
                <LoadingSpinner />
            </Layout>
        );
    }

    const documentTypes = [
        { key: 'pitch_deck', label: 'Pitch Deck', description: 'Your startup presentation for investors' },
        { key: 'business_plan', label: 'Business Plan', description: 'Detailed business strategy and projections' },
        { key: 'financial_statements', label: 'Financial Statements', description: 'Financial reports and projections' },
        { key: 'legal_documents', label: 'Legal Documents', description: 'Incorporation papers, contracts, etc.' },
        { key: 'product_demo', label: 'Product Demo', description: 'Product demonstrations and screenshots' },
        { key: 'team_info', label: 'Team Information', description: 'Team member profiles and resumes' }
    ];

    const getDocumentsByType = (type) => {
        return documents.filter(doc => doc.document_type === type);
    };

    return (
        <Layout>
            <div className="space-y-6">
                {/* Page Header */}
                <div className="mb-8">
                    <h1 className="text-2xl font-bold text-slate-900 dark:text-white">
                        Document Management
                    </h1>
                    <p className="text-slate-600 dark:text-slate-400 mt-1">
                        Upload and manage your startup documents for investors
                    </p>
                </div>

                {/* Basic Document Upload - Available to all users */}
                <div className="bg-white dark:bg-slate-800 shadow-sm rounded-lg border border-slate-200 dark:border-slate-700 p-6">
                    <h2 className="text-lg font-medium text-slate-900 dark:text-white mb-4">
                        Essential Documents
                    </h2>
                    <p className="text-slate-600 dark:text-slate-400 mb-6">
                        Upload your basic startup documents. Pitch deck and business plan are essential for investor interest.
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {documentTypes.slice(0, 2).map((docType) => {
                            const docs = getDocumentsByType(docType.key);
                            return (
                                <div key={docType.key} className="border border-slate-200 dark:border-slate-600 rounded-lg p-4">
                                    <h3 className="font-medium text-slate-900 dark:text-white mb-2">
                                        {docType.label}
                                    </h3>
                                    <p className="text-sm text-slate-600 dark:text-slate-400 mb-4">
                                        {docType.description}
                                    </p>
                                    
                                    {docs.length > 0 ? (
                                        <div className="space-y-2 mb-4">
                                            {docs.map((doc) => (
                                                <div key={doc.id} className="flex items-center justify-between bg-slate-50 dark:bg-slate-700 p-2 rounded">
                                                    <div className="flex items-center">
                                                        <svg className="w-4 h-4 text-slate-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                        </svg>
                                                        <span className="text-sm text-slate-700 dark:text-slate-300">
                                                            {doc.original_name}
                                                        </span>
                                                    </div>
                                                    <button
                                                        onClick={() => handleDeleteDocument(doc.id)}
                                                        className="text-red-600 hover:text-red-700 text-sm"
                                                    >
                                                        Delete
                                                    </button>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="text-center py-4 border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg mb-4">
                                            <svg className="mx-auto h-8 w-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                            </svg>
                                            <p className="text-sm text-slate-500 dark:text-slate-400 mt-2">
                                                No {docType.label.toLowerCase()} uploaded
                                            </p>
                                        </div>
                                    )}

                                    <input
                                        type="file"
                                        id={`upload-${docType.key}`}
                                        className="hidden"
                                        accept=".pdf,.doc,.docx,.ppt,.pptx"
                                        onChange={(e) => handleFileUpload(e, docType.key)}
                                        disabled={uploading}
                                    />
                                    <label
                                        htmlFor={`upload-${docType.key}`}
                                        className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors cursor-pointer text-center block disabled:opacity-50"
                                    >
                                        {uploading ? 'Uploading...' : `Upload ${docType.label}`}
                                    </label>
                                </div>
                            );
                        })}
                    </div>
                </div>

                {/* Advanced Document Management - Premium Feature */}
                <SubscriptionGate requiredPlan="premium" feature="Advanced Document Management">
                    <div className="bg-white dark:bg-slate-800 shadow-sm rounded-lg border border-slate-200 dark:border-slate-700 p-6">
                        <h2 className="text-lg font-medium text-slate-900 dark:text-white mb-4">
                            Complete Document Suite
                        </h2>
                        <p className="text-slate-600 dark:text-slate-400 mb-6">
                            Upload and organize all your startup documents with advanced categorization and sharing controls.
                        </p>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {documentTypes.slice(2).map((docType) => {
                                const docs = getDocumentsByType(docType.key);
                                return (
                                    <div key={docType.key} className="border border-slate-200 dark:border-slate-600 rounded-lg p-4">
                                        <h3 className="font-medium text-slate-900 dark:text-white mb-2">
                                            {docType.label}
                                        </h3>
                                        <p className="text-sm text-slate-600 dark:text-slate-400 mb-4">
                                            {docType.description}
                                        </p>
                                        
                                        {docs.length > 0 ? (
                                            <div className="space-y-2 mb-4">
                                                {docs.map((doc) => (
                                                    <div key={doc.id} className="flex items-center justify-between bg-slate-50 dark:bg-slate-700 p-2 rounded">
                                                        <div className="flex items-center">
                                                            <svg className="w-4 h-4 text-slate-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                            </svg>
                                                            <span className="text-sm text-slate-700 dark:text-slate-300">
                                                                {doc.original_name}
                                                            </span>
                                                        </div>
                                                        <button
                                                            onClick={() => handleDeleteDocument(doc.id)}
                                                            className="text-red-600 hover:text-red-700 text-sm"
                                                        >
                                                            Delete
                                                        </button>
                                                    </div>
                                                ))}
                                            </div>
                                        ) : (
                                            <div className="text-center py-4 border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg mb-4">
                                                <svg className="mx-auto h-8 w-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                </svg>
                                                <p className="text-sm text-slate-500 dark:text-slate-400 mt-2">
                                                    No documents uploaded
                                                </p>
                                            </div>
                                        )}

                                        <input
                                            type="file"
                                            id={`upload-${docType.key}`}
                                            className="hidden"
                                            accept=".pdf,.doc,.docx,.ppt,.pptx,.jpg,.png"
                                            onChange={(e) => handleFileUpload(e, docType.key)}
                                            disabled={uploading}
                                        />
                                        <label
                                            htmlFor={`upload-${docType.key}`}
                                            className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors cursor-pointer text-center block text-sm"
                                        >
                                            {uploading ? 'Uploading...' : 'Upload'}
                                        </label>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                </SubscriptionGate>

                {/* Document Analytics - Enterprise Feature */}
                <SubscriptionGate requiredPlan="enterprise" feature="Document Analytics">
                    <div className="bg-white dark:bg-slate-800 shadow-sm rounded-lg border border-slate-200 dark:border-slate-700 p-6">
                        <h2 className="text-lg font-medium text-slate-900 dark:text-white mb-4">
                            Document Analytics
                        </h2>
                        <p className="text-slate-600 dark:text-slate-400 mb-4">
                            Track document views, downloads, and investor engagement with your materials.
                        </p>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                                    {documents.length}
                                </div>
                                <div className="text-sm text-slate-600 dark:text-slate-400">Total Documents</div>
                            </div>
                            <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                                <div className="text-2xl font-bold text-green-600 dark:text-green-400">0</div>
                                <div className="text-sm text-slate-600 dark:text-slate-400">Document Views</div>
                            </div>
                            <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">0</div>
                                <div className="text-sm text-slate-600 dark:text-slate-400">Downloads</div>
                            </div>
                        </div>
                    </div>
                </SubscriptionGate>
            </div>
        </Layout>
    );
};

export default DocumentManagement;
