import React, { useState, useEffect } from 'react';
import Layout from '../../components/Layout';
import { apiService } from '../../services/apiService';
import LoadingSpinner from '../../components/LoadingSpinner';

const ESGQuestionnaire = () => {
    const [responses, setResponses] = useState({});
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [currentSection, setCurrentSection] = useState(0);
    const [score, setScore] = useState(null);

    const sections = [
        {
            title: 'Environmental',
            questions: [
                {
                    id: 'env_1',
                    question: 'Does your company have a formal environmental policy?',
                    type: 'boolean',
                    weight: 10
                },
                {
                    id: 'env_2',
                    question: 'What percentage of your energy comes from renewable sources?',
                    type: 'scale',
                    options: ['0%', '1-25%', '26-50%', '51-75%', '76-100%'],
                    weight: 15
                },
                {
                    id: 'env_3',
                    question: 'Does your company measure and report carbon emissions?',
                    type: 'boolean',
                    weight: 12
                },
                {
                    id: 'env_4',
                    question: 'How does your company manage waste reduction?',
                    type: 'multiple',
                    options: [
                        'Recycling programs',
                        'Waste reduction initiatives',
                        'Circular economy practices',
                        'No formal waste management'
                    ],
                    weight: 8
                }
            ]
        },
        {
            title: 'Social',
            questions: [
                {
                    id: 'soc_1',
                    question: 'What is the gender diversity ratio in leadership positions?',
                    type: 'scale',
                    options: ['<20%', '20-30%', '31-40%', '41-50%', '>50%'],
                    weight: 12
                },
                {
                    id: 'soc_2',
                    question: 'Does your company have formal diversity and inclusion policies?',
                    type: 'boolean',
                    weight: 10
                },
                {
                    id: 'soc_3',
                    question: 'How does your company support employee wellbeing?',
                    type: 'multiple',
                    options: [
                        'Mental health programs',
                        'Flexible working arrangements',
                        'Professional development',
                        'Health and safety training'
                    ],
                    weight: 8
                },
                {
                    id: 'soc_4',
                    question: 'Does your company engage in community development initiatives?',
                    type: 'boolean',
                    weight: 7
                }
            ]
        },
        {
            title: 'Governance',
            questions: [
                {
                    id: 'gov_1',
                    question: 'Does your board have independent directors?',
                    type: 'boolean',
                    weight: 15
                },
                {
                    id: 'gov_2',
                    question: 'How often does your board meet?',
                    type: 'scale',
                    options: ['Annually', 'Bi-annually', 'Quarterly', 'Monthly', 'As needed'],
                    weight: 8
                },
                {
                    id: 'gov_3',
                    question: 'Does your company have a formal code of ethics?',
                    type: 'boolean',
                    weight: 12
                },
                {
                    id: 'gov_4',
                    question: 'What transparency measures does your company implement?',
                    type: 'multiple',
                    options: [
                        'Regular financial reporting',
                        'Stakeholder communications',
                        'Public sustainability reports',
                        'Open governance policies'
                    ],
                    weight: 10
                }
            ]
        }
    ];

    useEffect(() => {
        fetchExistingResponses();
    }, []);

    const fetchExistingResponses = async () => {
        try {
            const response = await apiService.getESGResponses();
            if (response.data.data) {
                setResponses(response.data.data.responses || {});
                setScore(response.data.data.score);
            }
        } catch (error) {
            console.error('Failed to fetch ESG responses:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleResponseChange = (questionId, value) => {
        setResponses(prev => ({
            ...prev,
            [questionId]: value
        }));
    };

    const calculateProgress = () => {
        const totalQuestions = sections.reduce((sum, section) => sum + section.questions.length, 0);
        const answeredQuestions = Object.keys(responses).length;
        return Math.round((answeredQuestions / totalQuestions) * 100);
    };

    const handleSave = async () => {
        setSaving(true);
        try {
            // Transform responses to match backend expected format
            const transformedResponses = Object.entries(responses).map(([questionId, value]) => ({
                question_id: questionId,
                response_value: Array.isArray(value) ? value.join(',') : String(value)
            }));

            const response = await apiService.submitESGResponses({
                responses: transformedResponses
            });
            setScore(response.data.data.score);
            alert('ESG responses saved successfully!');
        } catch (error) {
            console.error('Failed to save ESG responses:', error);
            alert('Failed to save responses. Please try again.');
        } finally {
            setSaving(false);
        }
    };

    const renderQuestion = (question) => {
        const value = responses[question.id];

        switch (question.type) {
            case 'boolean':
                return (
                    <div className="space-y-2">
                        <label className="flex items-center">
                            <input
                                type="radio"
                                name={question.id}
                                value="yes"
                                checked={value === 'yes'}
                                onChange={(e) => handleResponseChange(question.id, e.target.value)}
                                className="mr-2"
                            />
                            Yes
                        </label>
                        <label className="flex items-center">
                            <input
                                type="radio"
                                name={question.id}
                                value="no"
                                checked={value === 'no'}
                                onChange={(e) => handleResponseChange(question.id, e.target.value)}
                                className="mr-2"
                            />
                            No
                        </label>
                    </div>
                );

            case 'scale':
                return (
                    <div className="space-y-2">
                        {question.options.map((option, index) => (
                            <label key={index} className="flex items-center">
                                <input
                                    type="radio"
                                    name={question.id}
                                    value={option}
                                    checked={value === option}
                                    onChange={(e) => handleResponseChange(question.id, e.target.value)}
                                    className="mr-2"
                                />
                                {option}
                            </label>
                        ))}
                    </div>
                );

            case 'multiple':
                return (
                    <div className="space-y-2">
                        {question.options.map((option, index) => (
                            <label key={index} className="flex items-center">
                                <input
                                    type="checkbox"
                                    checked={Array.isArray(value) && value.includes(option)}
                                    onChange={(e) => {
                                        const currentValues = Array.isArray(value) ? value : [];
                                        if (e.target.checked) {
                                            handleResponseChange(question.id, [...currentValues, option]);
                                        } else {
                                            handleResponseChange(question.id, currentValues.filter(v => v !== option));
                                        }
                                    }}
                                    className="mr-2"
                                />
                                {option}
                            </label>
                        ))}
                    </div>
                );

            default:
                return null;
        }
    };

    if (loading) {
        return (
            <Layout>
                <LoadingSpinner />
            </Layout>
        );
    }

    return (
        <Layout>
            <div className="space-y-6">
                {/* Header */}
                <div className="mb-8">
                    <h1 className="text-2xl font-bold text-slate-900 dark:text-white">
                        ESG Questionnaire
                    </h1>
                    <p className="text-slate-600 dark:text-slate-400 mt-1">
                        Complete your Environmental, Social, and Governance assessment
                    </p>
                </div>

                {/* Progress Bar */}
                <div className="bg-white dark:bg-slate-800 shadow-sm rounded-lg border border-slate-200 dark:border-slate-700 p-6">
                    <div className="flex justify-between items-center mb-4">
                        <h2 className="text-lg font-medium text-slate-900 dark:text-white">
                            Progress: {calculateProgress()}% Complete
                        </h2>
                        {score !== null && (
                            <div className="text-right">
                                <div className="text-sm text-slate-500 dark:text-slate-400">ESG Score</div>
                                <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                                    {score}/100
                                </div>
                            </div>
                        )}
                    </div>
                    <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                        <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${calculateProgress()}%` }}
                        ></div>
                    </div>
                </div>

                {/* Section Navigation */}
                <div className="bg-white dark:bg-slate-800 shadow-sm rounded-lg border border-slate-200 dark:border-slate-700">
                    <div className="border-b border-slate-200 dark:border-slate-700">
                        <nav className="flex space-x-8 px-6">
                            {sections.map((section, index) => (
                                <button
                                    key={index}
                                    onClick={() => setCurrentSection(index)}
                                    className={`py-4 px-1 border-b-2 font-medium text-sm ${
                                        currentSection === index
                                            ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                                            : 'border-transparent text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-300'
                                    }`}
                                >
                                    {section.title}
                                </button>
                            ))}
                        </nav>
                    </div>

                    {/* Questions */}
                    <div className="p-6">
                        <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-6">
                            {sections[currentSection].title} Questions
                        </h3>

                        <div className="space-y-8">
                            {sections[currentSection].questions.map((question, index) => (
                                <div key={question.id} className="border-b border-slate-200 dark:border-slate-700 pb-6 last:border-b-0">
                                    <h4 className="text-lg font-medium text-slate-900 dark:text-white mb-4">
                                        {index + 1}. {question.question}
                                    </h4>
                                    {renderQuestion(question)}
                                </div>
                            ))}
                        </div>
                    </div>
                </div>

                {/* Navigation and Save */}
                <div className="flex justify-between items-center">
                    <div className="flex space-x-4">
                        <button
                            onClick={() => setCurrentSection(Math.max(0, currentSection - 1))}
                            disabled={currentSection === 0}
                            className="bg-slate-600 text-white px-4 py-2 rounded-md hover:bg-slate-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            Previous
                        </button>
                        <button
                            onClick={() => setCurrentSection(Math.min(sections.length - 1, currentSection + 1))}
                            disabled={currentSection === sections.length - 1}
                            className="bg-slate-600 text-white px-4 py-2 rounded-md hover:bg-slate-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            Next
                        </button>
                    </div>

                    <button
                        onClick={handleSave}
                        disabled={saving}
                        className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        {saving ? 'Saving...' : 'Save Progress'}
                    </button>
                </div>
            </div>
        </Layout>
    );
};

export default ESGQuestionnaire;
