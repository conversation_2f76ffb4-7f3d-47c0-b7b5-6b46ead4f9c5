import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import apiService from '../../services/apiService';
import LoadingSpinner from '../../components/LoadingSpinner';
import ProfileWizard from '../../components/startup/ProfileWizard';

const ProfileManagement = () => {
    const [profile, setProfile] = useState(null);
    const [taxonomyOptions, setTaxonomyOptions] = useState(null);
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [errors, setErrors] = useState({});
    const navigate = useNavigate();

    useEffect(() => {
        loadProfileData();
    }, []);

    const loadProfileData = async () => {
        try {
            setLoading(true);
            
            // Load profile and taxonomy options in parallel
            const [profileResponse, taxonomyResponse] = await Promise.all([
                apiService.get('/startup-profiles/show'),
                apiService.get('/startup-profiles/taxonomy-options')
            ]);

            if (profileResponse.success) {
                setProfile(profileResponse.data);
            }

            if (taxonomyResponse.success) {
                setTaxonomyOptions(taxonomyResponse.data);
            }

        } catch (error) {
            console.error('Failed to load profile data:', error);
            setErrors({ 
                general: 'Failed to load profile data. Please try again.' 
            });
        } finally {
            setLoading(false);
        }
    };

    const handleSaveProfile = async (formData) => {
        try {
            setSaving(true);
            setErrors({});

            const response = await apiService.post('/startup-profiles', formData);

            if (response.success) {
                setProfile(response.data);
                
                // Show success message and redirect to dashboard
                navigate('/startup/dashboard', {
                    state: {
                        message: 'Profile saved successfully! Your startup profile is now complete.'
                    }
                });
            } else {
                setErrors(response.errors || { general: response.message });
            }

        } catch (error) {
            console.error('Failed to save profile:', error);
            
            if (error.response?.status === 422) {
                setErrors(error.response.data.errors || {});
            } else {
                setErrors({ 
                    general: 'Failed to save profile. Please try again.' 
                });
            }
        } finally {
            setSaving(false);
        }
    };

    if (loading) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <LoadingSpinner />
            </div>
        );
    }

    if (!taxonomyOptions) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="text-center">
                    <h2 className="text-xl font-semibold text-gray-900 mb-2">
                        Unable to Load Profile Options
                    </h2>
                    <p className="text-gray-600 mb-4">
                        There was an error loading the profile configuration.
                    </p>
                    <button
                        onClick={loadProfileData}
                        className="btn btn-primary"
                    >
                        Try Again
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-8">
            {/* Header */}
            <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700">
                <div className="px-6 py-4 border-b border-slate-200 dark:border-slate-700">
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-2xl font-bold text-slate-900 dark:text-white">
                                {profile?.profile ? 'Edit Startup Profile' : 'Create Startup Profile'}
                            </h1>
                            <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                                {profile?.profile 
                                    ? 'Update your company information and funding details'
                                    : 'Complete your startup profile to connect with investors'
                                }
                            </p>
                        </div>
                        {profile?.profile && (
                            <div className="flex items-center space-x-2">
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                    profile.profile.profile_completed
                                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                        : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                                }`}>
                                    {profile.profile.profile_completed ? 'Complete' : 'Incomplete'}
                                </span>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* Profile Wizard */}
            <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700">
                <ProfileWizard
                    initialData={profile}
                    taxonomyOptions={taxonomyOptions}
                    onSave={handleSaveProfile}
                    loading={saving}
                    errors={errors}
                />
            </div>

            {/* Profile Completion Tips */}
            {!profile?.profile?.profile_completed && (
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
                    <div className="flex items-start">
                        <div className="flex-shrink-0">
                            <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <div className="ml-3">
                            <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                                Complete Your Profile
                            </h3>
                            <div className="mt-2 text-sm text-blue-700 dark:text-blue-300">
                                <p>
                                    A complete profile helps investors find and connect with your startup. 
                                    Make sure to fill out all required fields and add relevant categories and keywords.
                                </p>
                            </div>
                            <div className="mt-4">
                                <div className="text-sm">
                                    <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
                                        Profile Benefits:
                                    </h4>
                                    <ul className="list-disc list-inside space-y-1 text-blue-700 dark:text-blue-300">
                                        <li>Increased visibility to investors</li>
                                        <li>Better matching with relevant funding opportunities</li>
                                        <li>Access to investor discovery features</li>
                                        <li>Ability to send interest requests</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Next Steps */}
            {profile?.profile?.profile_completed && (
                <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6">
                    <div className="flex items-start">
                        <div className="flex-shrink-0">
                            <svg className="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <div className="ml-3">
                            <h3 className="text-sm font-medium text-green-800 dark:text-green-200">
                                Profile Complete!
                            </h3>
                            <div className="mt-2 text-sm text-green-700 dark:text-green-300">
                                <p>
                                    Your startup profile is now complete. You can now discover investors 
                                    and send interest requests.
                                </p>
                            </div>
                            <div className="mt-4 flex space-x-3">
                                <button
                                    onClick={() => navigate('/discovery')}
                                    className="btn btn-sm bg-green-600 hover:bg-green-700 text-white"
                                >
                                    Discover Investors
                                </button>
                                <button
                                    onClick={() => navigate('/startup/esg-questionnaire')}
                                    className="btn btn-sm btn-outline-green"
                                >
                                    Complete ESG Assessment
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default ProfileManagement;
