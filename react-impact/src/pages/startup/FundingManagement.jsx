import React, { useState, useEffect } from 'react';
import Layout from '../../components/Layout';
import SubscriptionGate from '../../components/SubscriptionGate';
import { apiService } from '../../services/apiService';
import LoadingSpinner from '../../components/LoadingSpinner';

const FundingManagement = () => {
    const [fundingData, setFundingData] = useState({
        funding_goal: '',
        funding_raised: '',
        funding_stage: '',
        use_of_funds: '',
        valuation: '',
        equity_offered: ''
    });
    const [fundingRounds, setFundingRounds] = useState([]);
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);

    useEffect(() => {
        fetchFundingData();
    }, []);

    const fetchFundingData = async () => {
        try {
            const response = await apiService.getFundingData();
            if (response.data.data) {
                setFundingData(response.data.data);
                setFundingRounds(response.data.data.funding_rounds || []);
            }
        } catch (error) {
            console.error('Failed to fetch funding data:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFundingData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSave = async () => {
        setSaving(true);
        try {
            await apiService.updateFundingData(fundingData);
            alert('Funding information saved successfully!');
        } catch (error) {
            console.error('Failed to save funding data:', error);
            alert('Failed to save funding information. Please try again.');
        } finally {
            setSaving(false);
        }
    };

    if (loading) {
        return (
            <Layout>
                <LoadingSpinner />
            </Layout>
        );
    }

    const fundingProgress = fundingData.funding_goal > 0 ? 
        (fundingData.funding_raised / fundingData.funding_goal) * 100 : 0;

    return (
        <Layout>
            <div className="space-y-6">
                {/* Page Header */}
                <div className="mb-8">
                    <h1 className="text-2xl font-bold text-slate-900 dark:text-white">
                        Funding Management
                    </h1>
                    <p className="text-slate-600 dark:text-slate-400 mt-1">
                        Manage your fundraising goals and track progress
                    </p>
                </div>

                {/* Funding Overview - Available to all users */}
                <div className="bg-white dark:bg-slate-800 shadow-sm rounded-lg border border-slate-200 dark:border-slate-700 p-6">
                    <h2 className="text-lg font-medium text-slate-900 dark:text-white mb-4">
                        Funding Overview
                    </h2>
                    
                    {/* Funding Progress */}
                    <div className="mb-6">
                        <div className="flex justify-between items-center mb-2">
                            <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                Funding Progress
                            </span>
                            <span className="text-sm text-slate-500 dark:text-slate-400">
                                ${fundingData.funding_raised || 0} / ${fundingData.funding_goal || 0}
                            </span>
                        </div>
                        <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                            <div 
                                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${Math.min(fundingProgress, 100)}%` }}
                            ></div>
                        </div>
                        <div className="text-right mt-1">
                            <span className="text-sm text-slate-500 dark:text-slate-400">
                                {fundingProgress.toFixed(1)}% Complete
                            </span>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                                Funding Goal ($)
                            </label>
                            <input
                                type="number"
                                name="funding_goal"
                                value={fundingData.funding_goal}
                                onChange={handleInputChange}
                                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
                                placeholder="Enter funding goal"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                                Funding Raised ($)
                            </label>
                            <input
                                type="number"
                                name="funding_raised"
                                value={fundingData.funding_raised}
                                onChange={handleInputChange}
                                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
                                placeholder="Enter amount raised"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                                Funding Stage
                            </label>
                            <select
                                name="funding_stage"
                                value={fundingData.funding_stage}
                                onChange={handleInputChange}
                                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
                            >
                                <option value="">Select funding stage</option>
                                <option value="pre_seed">Pre-Seed</option>
                                <option value="seed">Seed</option>
                                <option value="series_a">Series A</option>
                                <option value="series_b">Series B</option>
                                <option value="series_c">Series C</option>
                                <option value="series_d">Series D+</option>
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                                Current Valuation ($)
                            </label>
                            <input
                                type="number"
                                name="valuation"
                                value={fundingData.valuation}
                                onChange={handleInputChange}
                                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
                                placeholder="Enter current valuation"
                            />
                        </div>
                        <div className="md:col-span-2">
                            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                                Use of Funds
                            </label>
                            <textarea
                                rows={4}
                                name="use_of_funds"
                                value={fundingData.use_of_funds}
                                onChange={handleInputChange}
                                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
                                placeholder="Describe how you plan to use the funding..."
                            />
                        </div>
                    </div>
                </div>

                {/* Advanced Funding Analytics - Premium Feature */}
                <SubscriptionGate requiredPlan="premium" feature="Advanced Funding Analytics">
                    <div className="bg-white dark:bg-slate-800 shadow-sm rounded-lg border border-slate-200 dark:border-slate-700 p-6">
                        <h2 className="text-lg font-medium text-slate-900 dark:text-white mb-4">
                            Funding Analytics
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                                    {fundingRounds.length}
                                </div>
                                <div className="text-sm text-slate-600 dark:text-slate-400">Funding Rounds</div>
                            </div>
                            <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                                <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                                    ${(fundingData.funding_raised || 0).toLocaleString()}
                                </div>
                                <div className="text-sm text-slate-600 dark:text-slate-400">Total Raised</div>
                            </div>
                            <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                                    {fundingProgress.toFixed(1)}%
                                </div>
                                <div className="text-sm text-slate-600 dark:text-slate-400">Goal Progress</div>
                            </div>
                        </div>
                    </div>
                </SubscriptionGate>

                {/* Investor Relations - Enterprise Feature */}
                <SubscriptionGate requiredPlan="enterprise" feature="Investor Relations Management">
                    <div className="bg-white dark:bg-slate-800 shadow-sm rounded-lg border border-slate-200 dark:border-slate-700 p-6">
                        <h2 className="text-lg font-medium text-slate-900 dark:text-white mb-4">
                            Investor Relations
                        </h2>
                        <p className="text-slate-600 dark:text-slate-400 mb-4">
                            Manage relationships with current and potential investors, track communications, and share updates.
                        </p>
                        <div className="space-y-4">
                            <button className="w-full md:w-auto bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                                Send Investor Update
                            </button>
                            <button className="w-full md:w-auto bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors ml-0 md:ml-2">
                                Schedule Investor Meeting
                            </button>
                        </div>
                    </div>
                </SubscriptionGate>

                {/* Save Button */}
                <div className="flex justify-end">
                    <button
                        onClick={handleSave}
                        disabled={saving}
                        className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        {saving ? 'Saving...' : 'Save Funding Information'}
                    </button>
                </div>
            </div>
        </Layout>
    );
};

export default FundingManagement;
