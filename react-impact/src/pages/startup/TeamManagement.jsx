import React, { useState, useEffect } from 'react';
import Layout from '../../components/Layout';
import SubscriptionGate from '../../components/SubscriptionGate';
import { apiService } from '../../services/apiService';
import LoadingSpinner from '../../components/LoadingSpinner';

const TeamManagement = () => {
    const [teamMembers, setTeamMembers] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showAddForm, setShowAddForm] = useState(false);
    const [newMember, setNewMember] = useState({
        name: '',
        position: '',
        bio: '',
        linkedin_url: '',
        email: '',
        equity_percentage: ''
    });
    const [saving, setSaving] = useState(false);

    useEffect(() => {
        fetchTeamMembers();
    }, []);

    const fetchTeamMembers = async () => {
        try {
            const response = await apiService.getTeamMembers();
            setTeamMembers(response.data.data || []);
        } catch (error) {
            console.error('Failed to fetch team members:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setNewMember(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleAddMember = async () => {
        setSaving(true);
        try {
            await apiService.addTeamMember(newMember);
            alert('Team member added successfully!');
            setNewMember({
                name: '',
                position: '',
                bio: '',
                linkedin_url: '',
                email: '',
                equity_percentage: ''
            });
            setShowAddForm(false);
            fetchTeamMembers();
        } catch (error) {
            console.error('Failed to add team member:', error);
            alert('Failed to add team member. Please try again.');
        } finally {
            setSaving(false);
        }
    };

    const handleDeleteMember = async (memberId) => {
        if (!confirm('Are you sure you want to remove this team member?')) return;

        try {
            await apiService.deleteTeamMember(memberId);
            alert('Team member removed successfully!');
            fetchTeamMembers();
        } catch (error) {
            console.error('Failed to remove team member:', error);
            alert('Failed to remove team member. Please try again.');
        }
    };

    if (loading) {
        return (
            <Layout>
                <LoadingSpinner />
            </Layout>
        );
    }

    return (
        <Layout>
            <div className="space-y-6">
                {/* Page Header */}
                <div className="mb-8">
                    <h1 className="text-2xl font-bold text-slate-900 dark:text-white">
                        Team Management
                    </h1>
                    <p className="text-slate-600 dark:text-slate-400 mt-1">
                        Showcase your startup team to potential investors
                    </p>
                </div>

                {/* Basic Team Information - Available to all users */}
                <div className="bg-white dark:bg-slate-800 shadow-sm rounded-lg border border-slate-200 dark:border-slate-700 p-6">
                    <div className="flex justify-between items-center mb-4">
                        <h2 className="text-lg font-medium text-slate-900 dark:text-white">
                            Core Team Members
                        </h2>
                        <button
                            onClick={() => setShowAddForm(!showAddForm)}
                            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                        >
                            {showAddForm ? 'Cancel' : 'Add Team Member'}
                        </button>
                    </div>

                    {/* Add Team Member Form */}
                    {showAddForm && (
                        <div className="mb-6 p-4 border border-slate-200 dark:border-slate-600 rounded-lg bg-slate-50 dark:bg-slate-700">
                            <h3 className="text-md font-medium text-slate-900 dark:text-white mb-4">
                                Add New Team Member
                            </h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                                        Full Name
                                    </label>
                                    <input
                                        type="text"
                                        name="name"
                                        value={newMember.name}
                                        onChange={handleInputChange}
                                        className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-600 dark:text-white"
                                        placeholder="Enter full name"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                                        Position/Title
                                    </label>
                                    <input
                                        type="text"
                                        name="position"
                                        value={newMember.position}
                                        onChange={handleInputChange}
                                        className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-600 dark:text-white"
                                        placeholder="e.g., CEO, CTO, CMO"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                                        Email
                                    </label>
                                    <input
                                        type="email"
                                        name="email"
                                        value={newMember.email}
                                        onChange={handleInputChange}
                                        className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-600 dark:text-white"
                                        placeholder="Enter email address"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                                        LinkedIn URL
                                    </label>
                                    <input
                                        type="url"
                                        name="linkedin_url"
                                        value={newMember.linkedin_url}
                                        onChange={handleInputChange}
                                        className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-600 dark:text-white"
                                        placeholder="https://linkedin.com/in/..."
                                    />
                                </div>
                                <div className="md:col-span-2">
                                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                                        Bio/Background
                                    </label>
                                    <textarea
                                        rows={3}
                                        name="bio"
                                        value={newMember.bio}
                                        onChange={handleInputChange}
                                        className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-600 dark:text-white"
                                        placeholder="Brief background and experience..."
                                    />
                                </div>
                            </div>
                            <div className="flex justify-end mt-4">
                                <button
                                    onClick={handleAddMember}
                                    disabled={saving || !newMember.name || !newMember.position}
                                    className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    {saving ? 'Adding...' : 'Add Member'}
                                </button>
                            </div>
                        </div>
                    )}

                    {/* Team Members List */}
                    {teamMembers.length > 0 ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {teamMembers.map((member) => (
                                <div key={member.id} className="border border-slate-200 dark:border-slate-600 rounded-lg p-4">
                                    <div className="flex items-center mb-3">
                                        <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                                            <span className="text-blue-600 dark:text-blue-400 font-medium">
                                                {member.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                                            </span>
                                        </div>
                                        <div className="ml-3 flex-1">
                                            <h3 className="font-medium text-slate-900 dark:text-white">
                                                {member.name}
                                            </h3>
                                            <p className="text-sm text-slate-600 dark:text-slate-400">
                                                {member.position}
                                            </p>
                                        </div>
                                        <button
                                            onClick={() => handleDeleteMember(member.id)}
                                            className="text-red-600 hover:text-red-700 text-sm"
                                        >
                                            Remove
                                        </button>
                                    </div>
                                    {member.bio && (
                                        <p className="text-sm text-slate-600 dark:text-slate-400 mb-3 line-clamp-3">
                                            {member.bio}
                                        </p>
                                    )}
                                    <div className="flex space-x-2">
                                        {member.email && (
                                            <a
                                                href={`mailto:${member.email}`}
                                                className="text-blue-600 hover:text-blue-700 text-sm"
                                            >
                                                Email
                                            </a>
                                        )}
                                        {member.linkedin_url && (
                                            <a
                                                href={member.linkedin_url}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="text-blue-600 hover:text-blue-700 text-sm"
                                            >
                                                LinkedIn
                                            </a>
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="text-center py-12">
                            <svg className="mx-auto h-12 w-12 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            <h3 className="mt-2 text-sm font-medium text-slate-900 dark:text-white">
                                No team members added
                            </h3>
                            <p className="mt-1 text-sm text-slate-500 dark:text-slate-400">
                                Start by adding your core team members to showcase your startup's talent.
                            </p>
                        </div>
                    )}
                </div>

                {/* Advanced Team Features - Premium Feature */}
                <SubscriptionGate requiredPlan="premium" feature="Advanced Team Management">
                    <div className="bg-white dark:bg-slate-800 shadow-sm rounded-lg border border-slate-200 dark:border-slate-700 p-6">
                        <h2 className="text-lg font-medium text-slate-900 dark:text-white mb-4">
                            Team Analytics & Insights
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                                    {teamMembers.length}
                                </div>
                                <div className="text-sm text-slate-600 dark:text-slate-400">Team Members</div>
                            </div>
                            <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                                <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                                    {teamMembers.filter(m => m.linkedin_url).length}
                                </div>
                                <div className="text-sm text-slate-600 dark:text-slate-400">LinkedIn Profiles</div>
                            </div>
                            <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                                    {teamMembers.filter(m => m.bio).length}
                                </div>
                                <div className="text-sm text-slate-600 dark:text-slate-400">Complete Profiles</div>
                            </div>
                        </div>
                    </div>
                </SubscriptionGate>

                {/* Equity Management - Enterprise Feature */}
                <SubscriptionGate requiredPlan="enterprise" feature="Equity Management">
                    <div className="bg-white dark:bg-slate-800 shadow-sm rounded-lg border border-slate-200 dark:border-slate-700 p-6">
                        <h2 className="text-lg font-medium text-slate-900 dark:text-white mb-4">
                            Equity & Ownership Management
                        </h2>
                        <p className="text-slate-600 dark:text-slate-400 mb-4">
                            Track equity distribution, vesting schedules, and ownership percentages for your team.
                        </p>
                        <div className="space-y-4">
                            <button className="w-full md:w-auto bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors">
                                Manage Equity Distribution
                            </button>
                            <button className="w-full md:w-auto bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors ml-0 md:ml-2">
                                Generate Cap Table
                            </button>
                        </div>
                    </div>
                </SubscriptionGate>
            </div>
        </Layout>
    );
};

export default TeamManagement;
