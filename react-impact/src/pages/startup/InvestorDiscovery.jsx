import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import apiService from '../../services/apiService';
import LoadingSpinner from '../../components/LoadingSpinner';
import InvestorCard from '../../components/startup/InvestorCard';
import SearchFilters from '../../components/startup/SearchFilters';

const InvestorDiscovery = () => {
    const [investors, setInvestors] = useState([]);
    const [recommendations, setRecommendations] = useState([]);
    const [filterOptions, setFilterOptions] = useState(null);
    const [loading, setLoading] = useState(true);
    const [searching, setSearching] = useState(false);
    const [filters, setFilters] = useState({
        investment_min: '',
        investment_max: '',
        risk_tolerance: '',
        investment_experience: '',
        categories: [],
        industries: [],
        location: '',
        esg_focused: false,
        sort_by: 'relevance',
    });
    const [pagination, setPagination] = useState({
        current_page: 1,
        last_page: 1,
        per_page: 20,
        total: 0,
    });
    const [activeTab, setActiveTab] = useState('search'); // 'search' or 'recommendations'
    const navigate = useNavigate();

    useEffect(() => {
        loadInitialData();
    }, []);

    const loadInitialData = async () => {
        try {
            setLoading(true);
            
            // Load filter options and recommendations in parallel
            const [filterResponse, recommendationsResponse] = await Promise.all([
                apiService.get('/investor-discovery/filter-options'),
                apiService.get('/investor-discovery/recommendations?limit=5')
            ]);

            if (filterResponse.success) {
                setFilterOptions(filterResponse.data);
            }

            if (recommendationsResponse.success) {
                setRecommendations(recommendationsResponse.data.recommendations || []);
            }

            // Perform initial search with default filters
            await searchInvestors(filters, 1);

        } catch (error) {
            console.error('Failed to load initial data:', error);
            
            if (error.response?.status === 400) {
                // Profile not complete
                navigate('/startup/profile-management', {
                    state: {
                        message: 'Please complete your startup profile to discover investors.'
                    }
                });
            }
        } finally {
            setLoading(false);
        }
    };

    const searchInvestors = async (searchFilters = filters, page = 1) => {
        try {
            setSearching(true);
            
            const params = new URLSearchParams({
                page: page.toString(),
                per_page: pagination.per_page.toString(),
                ...Object.fromEntries(
                    Object.entries(searchFilters).filter(([key, value]) => {
                        if (Array.isArray(value)) return value.length > 0;
                        return value !== '' && value !== null && value !== undefined;
                    })
                )
            });

            const response = await apiService.get(`/investor-discovery/search?${params}`);

            if (response.success) {
                setInvestors(response.data.investors || []);
                setPagination(response.data.pagination || {});
            }

        } catch (error) {
            console.error('Search failed:', error);
        } finally {
            setSearching(false);
        }
    };

    const handleFilterChange = (newFilters) => {
        setFilters(newFilters);
        searchInvestors(newFilters, 1);
    };

    const handlePageChange = (page) => {
        searchInvestors(filters, page);
    };

    const handleSendInterestRequest = async (investorId) => {
        try {
            const response = await apiService.post('/interest-requests', {
                target_id: investorId,
                type: 'investment_interest',
                message: 'I would like to connect and discuss potential investment opportunities.'
            });

            if (response.success) {
                // Update the investor's status in the list
                setInvestors(prev => prev.map(investor => 
                    investor.user.id === investorId 
                        ? { ...investor, interest_request_sent: true }
                        : investor
                ));
                
                // Show success message
                alert('Interest request sent successfully!');
            }
        } catch (error) {
            console.error('Failed to send interest request:', error);
            alert('Failed to send interest request. Please try again.');
        }
    };

    if (loading) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <LoadingSpinner />
            </div>
        );
    }

    return (
        <div className="space-y-6" data-testid="investor-discovery-page">
            {/* Header */}
            <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700">
                <div className="px-6 py-4 border-b border-slate-200 dark:border-slate-700">
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-2xl font-bold text-slate-900 dark:text-white">
                                Discover Investors
                            </h1>
                            <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                                Find investors that match your startup's funding needs and industry focus
                            </p>
                        </div>
                        <div className="flex items-center space-x-2">
                            <span className="text-sm text-slate-600 dark:text-slate-400">
                                {pagination.total} investors found
                            </span>
                        </div>
                    </div>
                </div>

                {/* Tabs */}
                <div className="px-6">
                    <nav className="flex space-x-8" aria-label="Tabs">
                        <button
                            onClick={() => setActiveTab('search')}
                            data-testid="search-tab"
                            className={`py-4 px-1 border-b-2 font-medium text-sm ${
                                activeTab === 'search'
                                    ? 'border-blue-500 text-blue-600'
                                    : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
                            }`}
                        >
                            Search Investors
                        </button>
                        <button
                            onClick={() => setActiveTab('recommendations')}
                            data-testid="recommendations-tab"
                            className={`py-4 px-1 border-b-2 font-medium text-sm ${
                                activeTab === 'recommendations'
                                    ? 'border-blue-500 text-blue-600'
                                    : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
                            }`}
                        >
                            Recommendations ({recommendations.length})
                        </button>
                    </nav>
                </div>
            </div>

            {activeTab === 'search' ? (
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    {/* Search Filters */}
                    <div className="lg:col-span-1">
                        <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700 p-6">
                            <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-4">
                                Search Filters
                            </h3>
                            {filterOptions && (
                                <SearchFilters
                                    filters={filters}
                                    filterOptions={filterOptions}
                                    onFilterChange={handleFilterChange}
                                    loading={searching}
                                />
                            )}
                        </div>
                    </div>

                    {/* Search Results */}
                    <div className="lg:col-span-3">
                        <div className="space-y-4">
                            {searching && (
                                <div className="flex items-center justify-center py-8">
                                    <LoadingSpinner />
                                </div>
                            )}

                            {!searching && investors.length === 0 && (
                                <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700 p-8 text-center">
                                    <div className="text-slate-400 mb-4">
                                        <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                        </svg>
                                    </div>
                                    <h3 className="text-lg font-medium text-slate-900 dark:text-white mb-2">
                                        No investors found
                                    </h3>
                                    <p className="text-slate-600 dark:text-slate-400">
                                        Try adjusting your search filters to find more investors.
                                    </p>
                                </div>
                            )}

                            {!searching && investors.map((investor) => (
                                <InvestorCard
                                    key={investor.id}
                                    investor={investor}
                                    onSendInterestRequest={handleSendInterestRequest}
                                />
                            ))}

                            {/* Pagination */}
                            {!searching && investors.length > 0 && pagination.last_page > 1 && (
                                <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700 p-4">
                                    <div className="flex items-center justify-between">
                                        <div className="text-sm text-slate-600 dark:text-slate-400">
                                            Showing {pagination.from} to {pagination.to} of {pagination.total} results
                                        </div>
                                        <div className="flex space-x-2">
                                            <button
                                                onClick={() => handlePageChange(pagination.current_page - 1)}
                                                disabled={pagination.current_page === 1}
                                                className="btn btn-sm btn-outline-secondary disabled:opacity-50"
                                            >
                                                Previous
                                            </button>
                                            <span className="flex items-center px-3 py-1 text-sm">
                                                Page {pagination.current_page} of {pagination.last_page}
                                            </span>
                                            <button
                                                onClick={() => handlePageChange(pagination.current_page + 1)}
                                                disabled={pagination.current_page === pagination.last_page}
                                                className="btn btn-sm btn-outline-secondary disabled:opacity-50"
                                            >
                                                Next
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            ) : (
                /* Recommendations Tab */
                <div className="space-y-4">
                    <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
                        <div className="flex items-start">
                            <div className="flex-shrink-0">
                                <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <div className="ml-3">
                                <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                                    Personalized Recommendations
                                </h3>
                                <div className="mt-2 text-sm text-blue-700 dark:text-blue-300">
                                    <p>
                                        These investors are recommended based on your startup profile, 
                                        funding requirements, and industry categories.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {recommendations.length === 0 ? (
                        <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700 p-8 text-center">
                            <div className="text-slate-400 mb-4">
                                <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                </svg>
                            </div>
                            <h3 className="text-lg font-medium text-slate-900 dark:text-white mb-2">
                                No recommendations available
                            </h3>
                            <p className="text-slate-600 dark:text-slate-400 mb-4">
                                Complete your startup profile to get personalized investor recommendations.
                            </p>
                            <button
                                onClick={() => navigate('/startup/profile-management')}
                                className="btn btn-primary"
                            >
                                Complete Profile
                            </button>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            {recommendations.map((investor) => (
                                <InvestorCard
                                    key={investor.id}
                                    investor={investor}
                                    onSendInterestRequest={handleSendInterestRequest}
                                    showMatchScore={true}
                                />
                            ))}
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export default InvestorDiscovery;
