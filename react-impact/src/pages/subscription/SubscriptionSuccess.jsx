import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { apiService } from '../../services/apiService';
import Layout from '../../components/Layout';

const SubscriptionSuccess = () => {
    const [searchParams] = useSearchParams();
    const navigate = useNavigate();
    const { user } = useAuth();
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [subscription, setSubscription] = useState(null);

    useEffect(() => {
        const sessionId = searchParams.get('session_id');
        
        if (!sessionId) {
            setError('No session ID found');
            setLoading(false);
            return;
        }

        handleCheckoutSuccess(sessionId);
    }, [searchParams]);

    const handleCheckoutSuccess = async (sessionId) => {
        try {
            setLoading(true);
            const response = await apiService.post('/subscriptions/checkout-success', {
                session_id: sessionId
            });
            
            setSubscription(response.data.data);
        } catch (err) {
            setError('Failed to process subscription');
            console.error('Error processing subscription:', err);
        } finally {
            setLoading(false);
        }
    };

    const handleContinue = () => {
        if (user?.roles?.includes('investor')) {
            navigate('/investor/dashboard');
        } else if (user?.roles?.includes('startup')) {
            navigate('/startup/dashboard');
        } else {
            navigate('/subscription/manage');
        }
    };

    if (loading) {
        return (
            <Layout>
                <div className="flex items-center justify-center min-h-96">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                        <p className="text-gray-600">Processing your subscription...</p>
                    </div>
                </div>
            </Layout>
        );
    }

    if (error) {
        return (
            <Layout>
                <div className="max-w-md mx-auto mt-12">
                    <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
                        <svg
                            className="w-12 h-12 text-red-500 mx-auto mb-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                            />
                        </svg>
                        <h3 className="text-lg font-medium text-red-900 mb-2">
                            Subscription Error
                        </h3>
                        <p className="text-red-700 mb-4">{error}</p>
                        <div className="space-y-2">
                            <button
                                onClick={() => navigate('/subscription/plans')}
                                className="w-full bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors"
                            >
                                Try Again
                            </button>
                            <button
                                onClick={handleContinue}
                                className="w-full bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors"
                            >
                                Go to Dashboard
                            </button>
                        </div>
                    </div>
                </div>
            </Layout>
        );
    }

    return (
        <Layout>
            <div className="max-w-md mx-auto mt-12">
                <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
                    <svg
                        className="w-12 h-12 text-green-500 mx-auto mb-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                    </svg>
                    <h3 className="text-lg font-medium text-green-900 mb-2">
                        Subscription Successful!
                    </h3>
                    <p className="text-green-700 mb-4">
                        Your subscription to <strong>{subscription?.subscription_product?.name}</strong> has been activated successfully.
                    </p>
                    
                    {subscription && (
                        <div className="bg-white rounded-md p-4 mb-4 text-left">
                            <h4 className="font-medium text-gray-900 mb-2">Subscription Details:</h4>
                            <div className="space-y-1 text-sm text-gray-600">
                                <div className="flex justify-between">
                                    <span>Plan:</span>
                                    <span className="font-medium">{subscription.subscription_product.name}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span>Status:</span>
                                    <span className="font-medium capitalize">{subscription.status}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span>Amount:</span>
                                    <span className="font-medium">
                                        ${subscription.amount}/{subscription.subscription_product.billing_cycle}
                                    </span>
                                </div>
                                {subscription.current_period_end && (
                                    <div className="flex justify-between">
                                        <span>Next billing:</span>
                                        <span className="font-medium">
                                            {new Date(subscription.current_period_end).toLocaleDateString()}
                                        </span>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}

                    <div className="space-y-2">
                        <button
                            onClick={handleContinue}
                            className="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
                        >
                            Continue to Dashboard
                        </button>
                        <button
                            onClick={() => navigate('/subscription/manage')}
                            className="w-full bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors"
                        >
                            Manage Subscription
                        </button>
                    </div>
                </div>
            </div>
        </Layout>
    );
};

export default SubscriptionSuccess;
