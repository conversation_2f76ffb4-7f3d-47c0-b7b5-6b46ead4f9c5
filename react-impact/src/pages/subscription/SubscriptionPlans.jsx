import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { apiService } from '../../services/apiService';
import { subscriptionService } from '../../services/subscriptionService';
import Layout from '../../components/Layout';

const SubscriptionPlans = () => {
    const { user } = useAuth();
    const navigate = useNavigate();
    const [plans, setPlans] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [paymentMethods, setPaymentMethods] = useState([]);
    const [showPaymentMethodPrompt, setShowPaymentMethodPrompt] = useState(false);
    const [selectedPlan, setSelectedPlan] = useState(null);
    const [subscriptionProgress, setSubscriptionProgress] = useState(null);

    useEffect(() => {
        fetchPlans();
        fetchPaymentMethods();
    }, []);

    const fetchPlans = async () => {
        try {
            setLoading(true);
            // Fetch plans filtered by user's role
            const response = await apiService.get('/subscription-products', {
                params: { active: true }
            });
            setPlans(response.data.data || []);
        } catch (err) {
            setError('Failed to load subscription plans');
            console.error('Error fetching plans:', err);
        } finally {
            setLoading(false);
        }
    };

    const fetchPaymentMethods = async () => {
        try {
            const response = await apiService.getPaymentMethods();
            setPaymentMethods(response.data.data || []);
        } catch (err) {
            console.error('Error fetching payment methods:', err);
            // Don't set error here as payment methods are optional for plan viewing
        }
    };

    // No need for client-side filtering since server already filters by user's role
    const getFilteredPlans = () => {
        return plans;
    };

    const handleSelectPlan = async (plan) => {
        // Check if user has payment methods
        if (paymentMethods.length === 0) {
            setSelectedPlan(plan);
            setShowPaymentMethodPrompt(true);
            return;
        }

        // Proceed with subscription creation
        await createSubscription(plan);
    };

    const createSubscription = async (plan) => {
        try {
            setLoading(true);
            setError(null);
            setSubscriptionProgress('Initializing subscription...');

            // Use the enhanced subscription service with retry logic
            const result = await subscriptionService.handleSubscriptionCreation(
                plan.id,
                (progress) => setSubscriptionProgress(progress)
            );

            if (result.success) {
                if (result.direct_creation) {
                    // Subscription was created directly - redirect to success page
                    setSubscriptionProgress('Subscription created successfully!');
                    setTimeout(() => {
                        navigate('/app/subscription/success', {
                            state: {
                                subscription: result.subscription,
                                direct_creation: true
                            }
                        });
                    }, 1000);
                } else if (result.requires_redirect && result.checkout_url) {
                    // Need to redirect to Stripe Checkout
                    setSubscriptionProgress('Redirecting to payment setup...');
                    window.location.href = result.checkout_url;
                } else {
                    setError('Unexpected response from subscription creation');
                }
            } else if (result.needsPaymentMethod) {
                setSelectedPlan(plan);
                setShowPaymentMethodPrompt(true);
                setError(result.error);
            } else {
                setError(result.error || 'Failed to create subscription');
            }
        } catch (err) {
            setError('Failed to start subscription process');
            console.error('Error creating subscription:', err);
        } finally {
            setLoading(false);
            setSubscriptionProgress(null);
        }
    };

    const handleAddPaymentMethodFirst = async () => {
        try {
            const response = await apiService.createPaymentMethodCheckoutSession();

            if (response.data.data && response.data.data.checkout_url) {
                // Store selected plan in localStorage to continue after payment method addition
                localStorage.setItem('pendingSubscriptionPlan', JSON.stringify(selectedPlan));
                // Redirect to Stripe Checkout for payment method addition
                window.location.href = response.data.data.checkout_url;
            } else {
                setError('Failed to create payment method checkout session');
            }
        } catch (err) {
            setError('Failed to start payment method addition process');
            console.error('Error creating payment method checkout session:', err);
        }
    };

    const formatPrice = (price) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(price);
    };

    const getBillingCycleText = (cycle) => {
        switch (cycle) {
            case 'monthly': return '/month';
            case 'yearly': return '/year';
            case 'quarterly': return '/quarter';
            default: return `/${cycle}`;
        }
    };

    const getPlanColor = (planName) => {
        if (planName.toLowerCase().includes('basic')) return 'blue';
        if (planName.toLowerCase().includes('premium')) return 'purple';
        if (planName.toLowerCase().includes('enterprise')) return 'green';
        return 'gray';
    };

    const getColorClasses = (color) => {
        const colors = {
            blue: {
                border: 'border-blue-200',
                bg: 'bg-blue-50',
                text: 'text-blue-600',
                button: 'bg-blue-600 hover:bg-blue-700 text-white',
                badge: 'bg-blue-100 text-blue-800'
            },
            purple: {
                border: 'border-purple-200',
                bg: 'bg-purple-50',
                text: 'text-purple-600',
                button: 'bg-purple-600 hover:bg-purple-700 text-white',
                badge: 'bg-purple-100 text-purple-800'
            },
            green: {
                border: 'border-green-200',
                bg: 'bg-green-50',
                text: 'text-green-600',
                button: 'bg-green-600 hover:bg-green-700 text-white',
                badge: 'bg-green-100 text-green-800'
            },
            gray: {
                border: 'border-gray-200',
                bg: 'bg-gray-50',
                text: 'text-gray-600',
                button: 'bg-gray-600 hover:bg-gray-700 text-white',
                badge: 'bg-gray-100 text-gray-800'
            }
        };
        return colors[color] || colors.gray;
    };

    if (loading) {
        return (
            <Layout>
                <div className="flex items-center justify-center min-h-96">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                </div>
            </Layout>
        );
    }

    if (error) {
        return (
            <Layout>
                <div className="text-center py-12">
                    <div className="text-red-600 mb-4">{error}</div>
                    <button 
                        onClick={fetchPlans}
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                    >
                        Try Again
                    </button>
                </div>
            </Layout>
        );
    }

    const filteredPlans = getFilteredPlans();

    return (
        <Layout>
            <div className="space-y-6">
                {/* Header */}
                <div className="text-center">
                    <h1 className="text-3xl font-bold text-gray-900 mb-4">
                        Choose Your Subscription Plan
                    </h1>
                    <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                        Select the perfect plan for your {user?.role || 'investment'} journey.
                        Upgrade or downgrade at any time.
                    </p>
                    <div className="mt-4">
                        <a
                            href="/app/payment-methods"
                            className="text-blue-600 hover:text-blue-700 font-medium text-sm inline-flex items-center"
                        >
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                            </svg>
                            Manage Payment Methods
                        </a>
                    </div>
                </div>

                {/* Subscription Progress */}
                {subscriptionProgress && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div className="flex items-center">
                            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"></div>
                            <div>
                                <h3 className="text-sm font-medium text-blue-800">Processing Subscription</h3>
                                <p className="text-sm text-blue-700 mt-1">{subscriptionProgress}</p>
                            </div>
                        </div>
                    </div>
                )}

                {/* User Role Info */}
                {user?.role && (
                    <div className="text-center">
                        <div className="inline-flex items-center px-4 py-2 bg-blue-50 border border-blue-200 rounded-lg">
                            <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            <span className="text-blue-800 font-medium">
                                Showing plans for: {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                            </span>
                        </div>
                    </div>
                )}

                {/* Plans Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
                    {filteredPlans.map((plan) => {
                        const color = getPlanColor(plan.name);
                        const colorClasses = getColorClasses(color);
                        const isPopular = plan.name.toLowerCase().includes('premium');

                        return (
                            <div
                                key={plan.id}
                                className={`relative bg-white rounded-xl shadow-lg border-2 ${colorClasses.border} hover:shadow-xl transition-shadow`}
                            >
                                {isPopular && (
                                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                        <span className={`px-4 py-1 rounded-full text-sm font-medium ${colorClasses.badge}`}>
                                            Most Popular
                                        </span>
                                    </div>
                                )}

                                <div className="p-8">
                                    {/* Plan Header */}
                                    <div className="text-center mb-6">
                                        <h3 className="text-xl font-bold text-gray-900 mb-2">
                                            {plan.name}
                                        </h3>
                                        <p className="text-gray-600 text-sm mb-4">
                                            {plan.description}
                                        </p>
                                        <div className="flex items-baseline justify-center">
                                            <span className="text-4xl font-bold text-gray-900">
                                                {formatPrice(plan.price)}
                                            </span>
                                            <span className="text-gray-600 ml-1">
                                                {getBillingCycleText(plan.billing_cycle)}
                                            </span>
                                        </div>
                                    </div>

                                    {/* Features List */}
                                    <div className="mb-8">
                                        <ul className="space-y-3">
                                            {plan.features?.map((feature, index) => (
                                                <li key={index} className="flex items-start">
                                                    <svg
                                                        className={`w-5 h-5 ${colorClasses.text} mr-3 mt-0.5 flex-shrink-0`}
                                                        fill="currentColor"
                                                        viewBox="0 0 20 20"
                                                    >
                                                        <path
                                                            fillRule="evenodd"
                                                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                            clipRule="evenodd"
                                                        />
                                                    </svg>
                                                    <span className="text-gray-700 text-sm">
                                                        {feature}
                                                    </span>
                                                </li>
                                            ))}
                                        </ul>
                                    </div>

                                    {/* CTA Button */}
                                    <button
                                        onClick={() => handleSelectPlan(plan)}
                                        className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${colorClasses.button}`}
                                    >
                                        Choose {plan.name}
                                    </button>
                                </div>
                            </div>
                        );
                    })}
                </div>

                {/* No Plans Message */}
                {filteredPlans.length === 0 && (
                    <div className="text-center py-12">
                        <p className="text-gray-600">
                            No subscription plans available for your role at the moment.
                        </p>
                    </div>
                )}

                {/* Payment Method Prompt Modal */}
                {showPaymentMethodPrompt && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
                            <div className="text-center">
                                <div className="w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
                                    <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                                    </svg>
                                </div>
                                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                    Add Payment Method
                                </h3>
                                <p className="text-gray-600 mb-6">
                                    You need to add a payment method before subscribing to {selectedPlan?.name}.
                                </p>
                                <div className="flex space-x-3">
                                    <button
                                        onClick={() => setShowPaymentMethodPrompt(false)}
                                        className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        onClick={handleAddPaymentMethodFirst}
                                        className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                                    >
                                        Add Payment Method
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </Layout>
    );
};

export default SubscriptionPlans;
