import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { apiService } from '../services/apiService';
import Layout from '../components/Layout';

const CategorySelection = () => {
    const { user } = useAuth();
    const [categories, setCategories] = useState([]);
    const [userCategories, setUserCategories] = useState([]);
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [error, setError] = useState(null);
    const [success, setSuccess] = useState(null);

    // Pagination state
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [totalCategories, setTotalCategories] = useState(0);
    const [searchTerm, setSearchTerm] = useState('');
    const [searchLoading, setSearchLoading] = useState(false);
    const perPage = 12;

    useEffect(() => {
        fetchData();
    }, []);

    useEffect(() => {
        fetchCategories();
    }, [currentPage, searchTerm]);

    const fetchData = async () => {
        try {
            setLoading(true);
            const userCategoriesResponse = await apiService.get('/investment/user-categories');
            setUserCategories(userCategoriesResponse.data.data || []);
            await fetchCategories();
        } catch (err) {
            setError('Failed to load data');
            console.error('Error fetching data:', err);
        } finally {
            setLoading(false);
        }
    };

    const fetchCategories = async () => {
        try {
            if (currentPage === 1 && !searchTerm) {
                setLoading(true);
            } else {
                setSearchLoading(true);
            }

            const params = {
                paginate: 'true',
                page: currentPage,
                per_page: perPage,
            };

            if (searchTerm) {
                params.search = searchTerm;
            }

            const response = await apiService.get('/investment/categories', { params });

            setCategories(response.data.data || []);
            setTotalPages(response.data.pagination?.last_page || 1);
            setTotalCategories(response.data.pagination?.total || 0);
        } catch (err) {
            setError('Failed to load categories');
            console.error('Error fetching categories:', err);
        } finally {
            setLoading(false);
            setSearchLoading(false);
        }
    };

    const handleSearch = (term) => {
        setSearchTerm(term);
        setCurrentPage(1); // Reset to first page when searching
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    const handleCategoryToggle = async (categoryId) => {
        const isSelected = userCategories.some(cat => cat.id === categoryId);
        
        if (isSelected) {
            // Remove category
            await removeCategory(categoryId);
        } else {
            // Add category (check limit)
            if (userCategories.length >= 5) {
                setError('Maximum 5 categories allowed. Please remove a category first.');
                return;
            }
            await addCategory(categoryId);
        }
    };

    const addCategory = async (categoryId) => {
        try {
            setSaving(true);
            setError(null);
            
            const response = await apiService.post('/investment/user-categories', {
                category_id: categoryId
            });

            setUserCategories(response.data.data || []);
            setSuccess('Category added successfully');
            setTimeout(() => setSuccess(null), 3000);
        } catch (err) {
            setError(err.response?.data?.message || 'Failed to add category');
        } finally {
            setSaving(false);
        }
    };

    const removeCategory = async (categoryId) => {
        try {
            setSaving(true);
            setError(null);
            
            const response = await apiService.delete(`/investment/user-categories/${categoryId}`);
            
            setUserCategories(response.data.data || []);
            setSuccess('Category removed successfully');
            setTimeout(() => setSuccess(null), 3000);
        } catch (err) {
            setError(err.response?.data?.message || 'Failed to remove category');
        } finally {
            setSaving(false);
        }
    };

    const isCategorySelected = (categoryId) => {
        return userCategories.some(cat => cat.id === categoryId);
    };

    if (loading) {
        return (
            <Layout>
                <div className="flex items-center justify-center min-h-96">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                </div>
            </Layout>
        );
    }

    return (
        <Layout>
            <div className="space-y-6">
                {/* Header */}
                <div className="md:flex md:items-center md:justify-between">
                    <div className="min-w-0 flex-1">
                        <h2 className="text-2xl font-bold leading-7 text-gray-900 dark:text-white sm:truncate sm:text-3xl sm:tracking-tight">
                            Category Selection
                        </h2>
                        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            Select up to 5 categories that represent your {user?.role === 'investor' ? 'investment interests' : 'business focus'}. 
                            These will be used to match you with relevant {user?.role === 'investor' ? 'startups' : 'investors'}.
                        </p>
                    </div>
                </div>

                {/* Status Messages */}
                {error && (
                    <div className="rounded-md bg-red-50 dark:bg-red-900/20 p-4">
                        <div className="text-sm text-red-700 dark:text-red-400">{error}</div>
                    </div>
                )}

                {success && (
                    <div className="rounded-md bg-green-50 dark:bg-green-900/20 p-4">
                        <div className="text-sm text-green-700 dark:text-green-400">{success}</div>
                    </div>
                )}

                {/* Selected Categories Summary */}
                <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        Selected Categories ({userCategories.length}/5)
                    </h3>
                    
                    {userCategories.length > 0 ? (
                        <div className="flex flex-wrap gap-2">
                            {userCategories.map((category) => (
                                <span
                                    key={category.id}
                                    className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"
                                >
                                    {category.name}
                                    <button
                                        onClick={() => handleCategoryToggle(category.id)}
                                        disabled={saving}
                                        className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full bg-blue-200 text-blue-600 hover:bg-blue-300 dark:bg-blue-800 dark:text-blue-300 dark:hover:bg-blue-700 disabled:opacity-50"
                                    >
                                        ×
                                    </button>
                                </span>
                            ))}
                        </div>
                    ) : (
                        <p className="text-gray-500 dark:text-gray-400">No categories selected yet.</p>
                    )}
                </div>

                {/* Search and Filter */}
                <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 sm:mb-0">
                            Available Categories
                        </h3>
                        <div className="flex items-center space-x-4">
                            <div className="relative">
                                <input
                                    type="text"
                                    placeholder="Search categories..."
                                    value={searchTerm}
                                    onChange={(e) => handleSearch(e.target.value)}
                                    className="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                />
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                </div>
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                                {totalCategories} categories
                            </div>
                        </div>
                    </div>

                    {searchLoading ? (
                        <div className="flex items-center justify-center py-8">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        </div>
                    ) : (
                        <>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                        {categories.map((category) => {
                            const isSelected = isCategorySelected(category.id);
                            const isDisabled = !isSelected && userCategories.length >= 5;
                            
                            return (
                                <div
                                    key={category.id}
                                    className={`relative rounded-lg border p-4 cursor-pointer transition-colors ${
                                        isSelected
                                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                                            : isDisabled
                                            ? 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800 opacity-50 cursor-not-allowed'
                                            : 'border-gray-200 hover:border-gray-300 dark:border-gray-700 dark:hover:border-gray-600'
                                    }`}
                                    onClick={() => !isDisabled && handleCategoryToggle(category.id)}
                                >
                                    <div className="flex items-start">
                                        <div className="flex items-center h-5">
                                            <input
                                                type="checkbox"
                                                checked={isSelected}
                                                disabled={isDisabled || saving}
                                                onChange={() => {}} // Handled by parent onClick
                                                className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                                            />
                                        </div>
                                        <div className="ml-3 text-sm">
                                            <label className="font-medium text-gray-900 dark:text-white">
                                                {category.name}
                                            </label>
                                            {category.description && (
                                                <p className="text-gray-500 dark:text-gray-400 mt-1">
                                                    {category.description}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            );
                        })}
                            </div>

                            {/* Pagination */}
                            {totalPages > 1 && (
                                <div className="flex items-center justify-between border-t border-gray-200 dark:border-gray-700 pt-6">
                                    <div className="flex-1 flex justify-between sm:hidden">
                                        <button
                                            onClick={() => handlePageChange(currentPage - 1)}
                                            disabled={currentPage === 1}
                                            className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600"
                                        >
                                            Previous
                                        </button>
                                        <button
                                            onClick={() => handlePageChange(currentPage + 1)}
                                            disabled={currentPage === totalPages}
                                            className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600"
                                        >
                                            Next
                                        </button>
                                    </div>
                                    <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                        <div>
                                            <p className="text-sm text-gray-700 dark:text-gray-300">
                                                Showing page <span className="font-medium">{currentPage}</span> of{' '}
                                                <span className="font-medium">{totalPages}</span>
                                            </p>
                                        </div>
                                        <div>
                                            <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                                <button
                                                    onClick={() => handlePageChange(currentPage - 1)}
                                                    disabled={currentPage === 1}
                                                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600"
                                                >
                                                    <span className="sr-only">Previous</span>
                                                    <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                                                    </svg>
                                                </button>

                                                {/* Page numbers */}
                                                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                                                    let pageNum;
                                                    if (totalPages <= 5) {
                                                        pageNum = i + 1;
                                                    } else if (currentPage <= 3) {
                                                        pageNum = i + 1;
                                                    } else if (currentPage >= totalPages - 2) {
                                                        pageNum = totalPages - 4 + i;
                                                    } else {
                                                        pageNum = currentPage - 2 + i;
                                                    }

                                                    return (
                                                        <button
                                                            key={pageNum}
                                                            onClick={() => handlePageChange(pageNum)}
                                                            className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                                                                pageNum === currentPage
                                                                    ? 'z-10 bg-blue-50 border-blue-500 text-blue-600 dark:bg-blue-900 dark:border-blue-400 dark:text-blue-300'
                                                                    : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600'
                                                            }`}
                                                        >
                                                            {pageNum}
                                                        </button>
                                                    );
                                                })}

                                                <button
                                                    onClick={() => handlePageChange(currentPage + 1)}
                                                    disabled={currentPage === totalPages}
                                                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600"
                                                >
                                                    <span className="sr-only">Next</span>
                                                    <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                                                    </svg>
                                                </button>
                                            </nav>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </>
                    )}
                </div>

                {/* Help Text */}
                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                    <div className="flex">
                        <div className="flex-shrink-0">
                            <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <div className="ml-3">
                            <p className="text-sm text-blue-700 dark:text-blue-300">
                                <strong>Tip:</strong> Choose categories that best represent your {user?.role === 'investor' ? 'investment focus areas' : 'business sectors'}. 
                                This helps our discovery system connect you with the most relevant matches.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </Layout>
    );
};

export default CategorySelection;
