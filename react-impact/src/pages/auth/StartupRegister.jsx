import React, { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import EnhancedRegistrationForm from '../../components/startup/EnhancedRegistrationForm';
import apiService from '../../services/apiService';

const StartupRegister = () => {
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState({});
    const navigate = useNavigate();
    const submissionInProgress = useRef(false);

    const handleRegistration = async (formData) => {
        // Prevent duplicate submissions
        if (submissionInProgress.current || loading) {
            console.log('Registration already in progress, ignoring duplicate submission');
            return;
        }

        submissionInProgress.current = true;
        setLoading(true);
        setErrors({});

        try {
            console.log('Starting registration process...');
            const result = await apiService.registerStartup(formData);
            console.log('Registration API response:', result);

            if (result.success) {
                console.log('Registration successful, redirecting...');

                // Store token if provided
                if (result.data?.token) {
                    localStorage.setItem('token', result.data.token);
                }

                // Redirect to email verification or dashboard
                navigate('/email-verification', {
                    state: {
                        email: formData.email,
                        message: result.message || 'Registration successful! Please verify your email address.'
                    }
                });
            } else {
                console.log('Registration failed:', result);
                setErrors(result.errors || { general: result.message });
            }
        } catch (error) {
            console.error('Registration error:', error);

            // Handle specific error types
            if (error.response?.status === 422) {
                setErrors(error.response.data.errors || {
                    general: error.response.data.message || 'Validation failed. Please check your input.'
                });
            } else {
                setErrors({
                    general: 'Registration failed. Please try again.'
                });
            }
        } finally {
            setLoading(false);
            submissionInProgress.current = false;
        }
    };

    return (
        <EnhancedRegistrationForm
            onSubmit={handleRegistration}
            loading={loading}
            errors={errors}
        />
    );
};

export default StartupRegister;
