import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate, Link } from 'react-router-dom';
import apiService from '../../services/apiService';

const EmailVerification = () => {
    const [loading, setLoading] = useState(false);
    const [message, setMessage] = useState('');
    const [error, setError] = useState('');
    const [resendCooldown, setResendCooldown] = useState(0);
    
    const location = useLocation();
    const navigate = useNavigate();
    
    const email = location.state?.email || '';
    const initialMessage = location.state?.message || 'Please check your email for verification instructions.';

    useEffect(() => {
        setMessage(initialMessage);
    }, [initialMessage]);

    useEffect(() => {
        let timer;
        if (resendCooldown > 0) {
            timer = setTimeout(() => {
                setResendCooldown(resendCooldown - 1);
            }, 1000);
        }
        return () => clearTimeout(timer);
    }, [resendCooldown]);

    const handleResendVerification = async () => {
        if (resendCooldown > 0) return;
        
        setLoading(true);
        setError('');
        setMessage('');

        try {
            const result = await apiService.post('/email/verification-notification', { email });
            
            if (result.success) {
                setMessage('Verification email sent! Please check your inbox.');
                setResendCooldown(60); // 60 second cooldown
            } else {
                setError(result.message || 'Failed to send verification email.');
            }
        } catch (error) {
            console.error('Resend verification error:', error);
            setError('Failed to send verification email. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const handleCheckVerification = async () => {
        setLoading(true);
        setError('');

        try {
            const result = await apiService.get('/user');
            
            if (result.success && result.data?.email_verified_at) {
                navigate('/startup/dashboard', { 
                    state: { message: 'Email verified successfully!' }
                });
            } else {
                setError('Email not yet verified. Please check your inbox and click the verification link.');
            }
        } catch (error) {
            console.error('Check verification error:', error);
            setError('Unable to check verification status. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-md w-full space-y-8">
                <div className="text-center">
                    <div className="mx-auto h-12 w-12 text-blue-600">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
                        Verify Your Email
                    </h2>
                    <p className="mt-2 text-sm text-gray-600">
                        We've sent a verification link to
                    </p>
                    {email && (
                        <p className="text-sm font-medium text-blue-600">
                            {email}
                        </p>
                    )}
                </div>

                <div className="bg-white shadow rounded-lg p-6">
                    {message && (
                        <div className="mb-4 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded">
                            {message}
                        </div>
                    )}

                    {error && (
                        <div className="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                            {error}
                        </div>
                    )}

                    <div className="space-y-4">
                        <div className="text-sm text-gray-600">
                            <p className="mb-2">
                                Click the verification link in your email to activate your account.
                            </p>
                            <p>
                                If you don't see the email, check your spam folder.
                            </p>
                        </div>

                        <div className="flex flex-col space-y-3">
                            <button
                                onClick={handleCheckVerification}
                                disabled={loading}
                                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {loading ? 'Checking...' : 'I\'ve Verified My Email'}
                            </button>

                            <button
                                onClick={handleResendVerification}
                                disabled={loading || resendCooldown > 0}
                                className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {loading ? 'Sending...' : 
                                 resendCooldown > 0 ? `Resend in ${resendCooldown}s` : 
                                 'Resend Verification Email'}
                            </button>
                        </div>
                    </div>
                </div>

                <div className="text-center">
                    <p className="text-sm text-gray-600">
                        Need help?{' '}
                        <Link
                            to="/contact"
                            className="font-medium text-blue-600 hover:text-blue-500"
                        >
                            Contact Support
                        </Link>
                    </p>
                    <p className="mt-2 text-sm text-gray-600">
                        Wrong email address?{' '}
                        <Link
                            to="/register"
                            className="font-medium text-blue-600 hover:text-blue-500"
                        >
                            Register again
                        </Link>
                    </p>
                </div>
            </div>
        </div>
    );
};

export default EmailVerification;
