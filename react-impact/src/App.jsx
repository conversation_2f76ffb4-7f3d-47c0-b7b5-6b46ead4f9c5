import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import PublicRoute from './components/PublicRoute';

// Pages
import Login from './pages/auth/Login';
import Register from './pages/auth/Register';
import StartupRegister from './pages/auth/StartupRegister';
import EmailVerification from './pages/auth/EmailVerification';
import ProgressTrackingTest from './pages/test/ProgressTrackingTest';
import InvestorDashboard from './pages/investor/Dashboard';
import StartupDashboard from './pages/startup/Dashboard';
import AnalystDashboard from './pages/analyst/Dashboard';
import InvestorProfile from './pages/investor/Profile';
import StartupProfile from './pages/startup/Profile';
import ProfileManagement from './pages/startup/ProfileManagement';
import Discovery from './pages/Discovery';
import InvestorDiscovery from './pages/startup/InvestorDiscovery';
import InterestRequests from './pages/InterestRequests';
import ESGQuestionnaire from './pages/startup/ESGQuestionnaire';
import FundingManagement from './pages/startup/FundingManagement';
import DocumentManagement from './pages/startup/DocumentManagement';

import SubscriptionPlans from './pages/subscription/SubscriptionPlans';
import SubscriptionManagement from './pages/subscription/SubscriptionManagement';
import PaymentMethods from './pages/payment/PaymentMethods';
import PaymentMethodSuccess from './pages/payment/PaymentMethodSuccess';
import SubscriptionSuccess from './pages/subscription/SubscriptionSuccess';
import Invoices from './pages/billing/Invoices';
import RefundRequests from './pages/billing/RefundRequests';
import CategorySelection from './pages/CategorySelection';

// Blog and FAQ Pages
import BlogList from './pages/blog/BlogList';
import BlogPost from './pages/blog/BlogPost';
import FAQ from './pages/faq/FAQ';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="min-h-screen bg-gray-50">
          <Routes>
            {/* Public Routes */}
            <Route path="/login" element={
              <PublicRoute>
                <Login />
              </PublicRoute>
            } />
            <Route path="/register" element={
              <PublicRoute>
                <Register />
              </PublicRoute>
            } />
            <Route path="/startup/register" element={
              <PublicRoute>
                <StartupRegister />
              </PublicRoute>
            } />
            <Route path="/email-verification" element={
              <PublicRoute>
                <EmailVerification />
              </PublicRoute>
            } />
            <Route path="/test/progress-tracking" element={
              <ProgressTrackingTest />
            } />

            {/* Protected Routes */}
            <Route path="/investor/dashboard" element={
              <ProtectedRoute allowedRoles={['investor']}>
                <InvestorDashboard />
              </ProtectedRoute>
            } />
            <Route path="/startup/dashboard" element={
              <ProtectedRoute allowedRoles={['startup']}>
                <StartupDashboard />
              </ProtectedRoute>
            } />
            <Route path="/analyst/dashboard" element={
              <ProtectedRoute allowedRoles={['analyst']}>
                <AnalystDashboard />
              </ProtectedRoute>
            } />

            {/* App Routes with /app prefix */}
            <Route path="/app/investor/dashboard" element={
              <ProtectedRoute allowedRoles={['investor']}>
                <InvestorDashboard />
              </ProtectedRoute>
            } />
            <Route path="/app/startup/dashboard" element={
              <ProtectedRoute allowedRoles={['startup']}>
                <StartupDashboard />
              </ProtectedRoute>
            } />
            <Route path="/app/analyst/dashboard" element={
              <ProtectedRoute allowedRoles={['analyst']}>
                <AnalystDashboard />
              </ProtectedRoute>
            } />
            <Route path="/app/investor/profile" element={
              <ProtectedRoute allowedRoles={['investor']}>
                <InvestorProfile />
              </ProtectedRoute>
            } />
            <Route path="/app/startup/profile" element={
              <ProtectedRoute allowedRoles={['startup']}>
                <StartupProfile />
              </ProtectedRoute>
            } />
            <Route path="/app/startup/profile-management" element={
              <ProtectedRoute allowedRoles={['startup']}>
                <ProfileManagement />
              </ProtectedRoute>
            } />
            <Route path="/app/startup/investor-discovery" element={
              <ProtectedRoute allowedRoles={['startup']}>
                <InvestorDiscovery />
              </ProtectedRoute>
            } />
            <Route path="/app/discovery" element={
              <ProtectedRoute allowedRoles={['investor', 'startup', 'analyst']}>
                <Discovery />
              </ProtectedRoute>
            } />
            <Route path="/app/interest-requests" element={
              <ProtectedRoute allowedRoles={['startup', 'analyst']}>
                <InterestRequests />
              </ProtectedRoute>
            } />
            <Route path="/app/startup/esg-questionnaire" element={
              <ProtectedRoute allowedRoles={['startup']}>
                <ESGQuestionnaire />
              </ProtectedRoute>
            } />
            <Route path="/app/startup/funding" element={
              <ProtectedRoute allowedRoles={['startup']}>
                <FundingManagement />
              </ProtectedRoute>
            } />
            <Route path="/app/startup/documents" element={
              <ProtectedRoute allowedRoles={['startup']}>
                <DocumentManagement />
              </ProtectedRoute>
            } />


            {/* Legacy routes for backward compatibility */}
            <Route path="/investor/profile" element={
              <ProtectedRoute allowedRoles={['investor']}>
                <InvestorProfile />
              </ProtectedRoute>
            } />
            <Route path="/startup/profile" element={
              <ProtectedRoute allowedRoles={['startup']}>
                <StartupProfile />
              </ProtectedRoute>
            } />
            <Route path="/startup/profile-management" element={
              <ProtectedRoute allowedRoles={['startup']}>
                <ProfileManagement />
              </ProtectedRoute>
            } />
            <Route path="/startup/investor-discovery" element={
              <ProtectedRoute allowedRoles={['startup']}>
                <InvestorDiscovery />
              </ProtectedRoute>
            } />
            <Route path="/discovery" element={
              <ProtectedRoute allowedRoles={['investor', 'startup', 'analyst']}>
                <Discovery />
              </ProtectedRoute>
            } />
            <Route path="/interest-requests" element={
              <ProtectedRoute allowedRoles={['startup', 'analyst']}>
                <InterestRequests />
              </ProtectedRoute>
            } />
            <Route path="/startup/esg" element={
              <ProtectedRoute allowedRoles={['startup']}>
                <ESGQuestionnaire />
              </ProtectedRoute>
            } />
            <Route path="/subscription/plans" element={
              <ProtectedRoute>
                <SubscriptionPlans />
              </ProtectedRoute>
            } />
            <Route path="/subscription/manage" element={
              <ProtectedRoute>
                <SubscriptionManagement />
              </ProtectedRoute>
            } />
            <Route path="/subscription/success" element={
              <ProtectedRoute>
                <SubscriptionSuccess />
              </ProtectedRoute>
            } />

            {/* App subscription routes */}
            <Route path="/app/subscription/plans" element={
              <ProtectedRoute>
                <SubscriptionPlans />
              </ProtectedRoute>
            } />
            <Route path="/app/subscription/manage" element={
              <ProtectedRoute>
                <SubscriptionManagement />
              </ProtectedRoute>
            } />
            <Route path="/app/subscription/success" element={
              <ProtectedRoute>
                <SubscriptionSuccess />
              </ProtectedRoute>
            } />

            {/* Payment Methods */}
            <Route path="/app/payment-methods" element={
              <ProtectedRoute>
                <PaymentMethods />
              </ProtectedRoute>
            } />
            <Route path="/app/payment-methods/success" element={
              <ProtectedRoute>
                <PaymentMethodSuccess />
              </ProtectedRoute>
            } />

            {/* Billing & Invoices */}
            <Route path="/app/billing/invoices" element={
              <ProtectedRoute>
                <Invoices />
              </ProtectedRoute>
            } />
            <Route path="/app/billing/refunds" element={
              <ProtectedRoute>
                <RefundRequests />
              </ProtectedRoute>
            } />

            {/* Category Selection */}
            <Route path="/app/categories" element={
              <ProtectedRoute allowedRoles={['investor', 'startup']}>
                <CategorySelection />
              </ProtectedRoute>
            } />

            {/* Blog Routes */}
            <Route path="/app/blog" element={
              <ProtectedRoute>
                <BlogList />
              </ProtectedRoute>
            } />
            <Route path="/app/blog/:id" element={
              <ProtectedRoute>
                <BlogPost />
              </ProtectedRoute>
            } />

            {/* FAQ Routes */}
            <Route path="/app/faq" element={
              <ProtectedRoute>
                <FAQ />
              </ProtectedRoute>
            } />

            {/* Default redirect */}
            <Route path="/" element={<Navigate to="/login" replace />} />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
