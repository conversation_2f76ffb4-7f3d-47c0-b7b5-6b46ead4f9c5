import axios from 'axios';

class ApiService {
    constructor() {
        //get backend url from .env file
        const backendUrl = import.meta.env.VITE_BACKEND_URL;
        console.log('API Service - Backend URL:', backendUrl);
        this.api = axios.create({
            baseURL: backendUrl,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            },
        });

        // Track pending requests to prevent duplicates
        this.pendingRequests = new Map();

        // Add auth token to requests if available
        const token = localStorage.getItem('auth_token');
        if (token) {
            this.api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        }

        // Response interceptor for error handling
        this.api.interceptors.response.use(
            (response) => response,
            (error) => {
                if (error.response?.status === 401) {
                    // Token expired or invalid
                    this.removeAuthToken();
                    window.location.href = '/login';
                }
                return Promise.reject(error);
            }
        );
    }

    setAuthToken(token) {
        localStorage.setItem('auth_token', token);
        this.api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    }

    removeAuthToken() {
        localStorage.removeItem('auth_token');
        delete this.api.defaults.headers.common['Authorization'];
    }

    // Request deduplication helper
    generateRequestKey(method, url, data) {
        return `${method}:${url}:${JSON.stringify(data)}`;
    }

    // Generic HTTP methods
    async get(url, config = {}) {
        return this.api.get(url, config);
    }

    async post(url, data = {}, config = {}) {
        // For critical endpoints like registration, implement deduplication
        if (url.includes('/register')) {
            const requestKey = this.generateRequestKey('POST', url, data);

            // If request is already pending, return the existing promise
            if (this.pendingRequests.has(requestKey)) {
                console.log('Duplicate registration request detected, returning existing promise');
                return this.pendingRequests.get(requestKey);
            }

            // Create new request and track it
            const requestPromise = this.api.post(url, data, config)
                .finally(() => {
                    // Remove from pending requests when completed
                    this.pendingRequests.delete(requestKey);
                });

            this.pendingRequests.set(requestKey, requestPromise);
            return requestPromise;
        }

        return this.api.post(url, data, config);
    }

    async put(url, data = {}, config = {}) {
        return this.api.put(url, data, config);
    }

    async patch(url, data = {}, config = {}) {
        return this.api.patch(url, data, config);
    }

    async delete(url, config = {}) {
        return this.api.delete(url, config);
    }

    // Authentication endpoints
    async login(credentials) {
        return this.post('/login', credentials);
    }

    async register(userData) {
        return this.post('/register', userData);
    }

    async registerStartup(userData) {
        return this.post('/startup/register', userData);
    }

    async getRegistrationProgress() {
        return this.get('/startup/registration-progress');
    }

    async logout() {
        return this.post('/logout');
    }

    async getUser() {
        return this.get('/user');
    }

    // Investment Platform API endpoints
    
    // Categories
    async getCategories() {
        return this.get('/investment/categories');
    }

    // Investor Profile
    async getInvestorProfile() {
        return this.get('/investment/investor-profiles');
    }

    async createInvestorProfile(data) {
        return this.post('/investment/investor-profiles', data);
    }

    async updateInvestorProfile(data) {
        return this.post('/investment/investor-profiles', data);
    }

    // Startup Profile
    async getStartupProfile() {
        return this.get('/investment/startup-profiles');
    }

    async createStartupProfile(data) {
        return this.post('/investment/startup-profiles', data);
    }

    async updateStartupProfile(data) {
        return this.post('/investment/startup-profiles', data);
    }

    // ESG System
    async getESGQuestions() {
        return this.get('/investment/esg/questions');
    }

    async submitESGResponses(data) {
        return this.post('/investment/esg/responses', data);
    }

    async getESGResponses() {
        return this.get('/investment/esg/responses');
    }

    // Discovery
    async discoverStartups(params = {}) {
        return this.get('/investment/discovery/startups', { params });
    }

    async discoverInvestors(params = {}) {
        return this.get('/investment/discovery/investors', { params });
    }

    // Refunds
    async getRefunds(params = {}) {
        return this.get('/refunds', { params });
    }

    async requestRefund(data) {
        return this.post('/refunds/request', data);
    }

    async calculateProratedRefund(subscriptionId, cancellationDate) {
        return this.post('/refunds/calculate-prorated', {
            subscription_id: subscriptionId,
            cancellation_date: cancellationDate
        });
    }

    // Payments
    async getPayments(params = {}) {
        return this.get('/payments', { params });
    }

    // Interest Requests
    async getInterestRequests(params = {}) {
        return this.get('/investment/interest-requests', { params });
    }

    async createInterestRequest(data) {
        return this.post('/investment/interest-requests', data);
    }

    async getInterestRequest(id) {
        return this.get(`/investment/interest-requests/${id}`);
    }

    // Subscription System
    async getSubscriptionProducts() {
        return this.get('/subscription-products');
    }

    async getUserSubscriptions() {
        return this.get('/subscriptions');
    }

    async createSubscription(data) {
        return this.post('/subscriptions', data);
    }

    async updateSubscription(id, data) {
        return this.put(`/subscriptions/${id}`, data);
    }

    async cancelSubscription(id) {
        return this.delete(`/subscriptions/${id}`);
    }

    async createStripeCheckoutSession(data) {
        return this.post('/subscriptions/checkout', data);
    }

    async getSubscriptionStatus() {
        return this.get('/subscriptions/status');
    }

    // Dashboard Stats
    async getInvestorStats() {
        return this.get('/investment/dashboard/investor-stats');
    }

    async getStartupStats() {
        return this.get('/investment/dashboard/startup-stats');
    }

    async getAnalystStats() {
        return this.get('/investment/dashboard/analyst-stats');
    }

    // Analyst specific endpoints
    async getPendingApprovals() {
        return this.get('/investment/analyst/pending-approvals');
    }

    async getUserManagement() {
        return this.get('/investment/analyst/users');
    }

    async getPlatformAnalytics() {
        return this.get('/investment/analyst/analytics');
    }

    // Recent Activity
    async getRecentActivity() {
        return this.get('/investment/dashboard/recent-activity');
    }

    // Funding Management
    async getFundingData() {
        return this.get('/startup/funding');
    }

    async updateFundingData(data) {
        return this.put('/startup/funding', data);
    }

    // Document Management
    async getStartupDocuments() {
        return this.get('/startup/documents');
    }

    async uploadStartupDocument(formData) {
        return this.post('/startup/documents', formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
    }

    async deleteStartupDocument(id) {
        return this.delete(`/startup/documents/${id}`);
    }

    // Team Management
    async getTeamMembers() {
        return this.get('/startup/team');
    }

    async addTeamMember(data) {
        return this.post('/startup/team', data);
    }

    async updateTeamMember(id, data) {
        return this.put(`/startup/team/${id}`, data);
    }

    async deleteTeamMember(id) {
        return this.delete(`/startup/team/${id}`);
    }

    // Payment Method Management
    async getPaymentMethods() {
        return this.get('/payment-methods');
    }

    async createPaymentMethodSetupIntent() {
        return this.post('/payment-methods/setup-intent');
    }

    async createPaymentMethodCheckoutSession() {
        return this.post('/payment-methods/checkout-session');
    }

    async storePaymentMethod(data) {
        return this.post('/payment-methods', data);
    }

    async getPaymentMethod(id) {
        return this.get(`/payment-methods/${id}`);
    }

    async updatePaymentMethod(id, data) {
        return this.put(`/payment-methods/${id}`, data);
    }

    async deletePaymentMethod(id) {
        return this.delete(`/payment-methods/${id}`);
    }

    async setDefaultPaymentMethod(id) {
        return this.post(`/payment-methods/${id}/set-default`);
    }

    // User Category Management
    async getUserCategories() {
        return this.get('/investment/user-categories');
    }

    async updateUserCategories(categoryIds) {
        return this.put('/investment/user-categories', { category_ids: categoryIds });
    }

    async addUserCategory(categoryId) {
        return this.post('/investment/user-categories', { category_id: categoryId });
    }

    async removeUserCategory(categoryId) {
        return this.delete(`/investment/user-categories/${categoryId}`);
    }

    async getCategories() {
        return this.get('/investment/categories');
    }

    // Startup Registration Progress
    async getRegistrationProgress() {
        return this.get('/startup/registration-progress');
    }

    async registerStartup(formData) {
        console.log('Sending startup registration data:', formData);
        return this.post('/startup/register', formData);
    }

    // Startup Profile Management
    async getStartupProfile() {
        return this.get('/startup-profiles/show');
    }

    async saveStartupProfile(profileData) {
        return this.post('/startup-profiles', profileData);
    }

    async updateStartupProfile(profileData) {
        return this.put('/startup-profiles', profileData);
    }

    async getProfileTaxonomyOptions() {
        return this.get('/startup-profiles/taxonomy-options');
    }

    // Investor Discovery
    async searchInvestors(params) {
        return this.get('/investor-discovery/search', { params });
    }

    async getInvestorRecommendations(limit = 10) {
        return this.get(`/investor-discovery/recommendations?limit=${limit}`);
    }

    async getInvestorFilterOptions() {
        return this.get('/investor-discovery/filter-options');
    }

    // Interest Requests
    async sendInterestRequest(data) {
        return this.post('/interest-requests', data);
    }

    async getInterestRequests(params = {}) {
        return this.get('/interest-requests', { params });
    }

    async getInterestRequestStatistics() {
        return this.get('/interest-requests-statistics');
    }

    async approveInterestRequest(requestId) {
        return this.post(`/interest-requests/${requestId}/approve`);
    }

    async rejectInterestRequest(requestId, reason) {
        return this.post(`/interest-requests/${requestId}/reject`, { reason });
    }

    // Blog Management
    async getBlogs(params = {}) {
        return this.get('/blogs', { params });
    }

    async getBlog(id) {
        return this.get(`/blogs/${id}`);
    }

    async getBlogsByCategory(categoryId, params = {}) {
        return this.get(`/blogs/category/${categoryId}`, { params });
    }

    async searchBlogs(query, params = {}) {
        return this.get('/blogs/search', { params: { q: query, ...params } });
    }

    async getRelatedBlogs(blogId, limit = 3) {
        return this.get(`/blogs/${blogId}/related`, { params: { limit } });
    }

    async getBlogCategories() {
        return this.get('/blogs/categories');
    }

    // FAQ Management
    async getFAQs(params = {}) {
        return this.get('/faqs', { params });
    }

    async getFAQsByRole(role, params = {}) {
        return this.get(`/faqs/role/${role}`, { params });
    }

    async getFAQsByCategory(categoryId, params = {}) {
        return this.get(`/faqs/category/${categoryId}`, { params });
    }

    async searchFAQs(query, role = null, params = {}) {
        const searchParams = { q: query, ...params };
        if (role) {
            searchParams.role = role;
        }
        return this.get('/faqs/search', { params: searchParams });
    }

    async getFAQCategories(role = null) {
        const params = role ? { role } : {};
        return this.get('/faqs/categories', { params });
    }
}

export const apiService = new ApiService();
export default apiService;
