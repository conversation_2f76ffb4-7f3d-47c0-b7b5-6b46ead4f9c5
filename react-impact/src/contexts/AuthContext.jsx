import React, { createContext, useContext, useState, useEffect } from 'react';
import { apiService } from '../services/apiService';

const AuthContext = createContext();

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};

export const AuthProvider = ({ children }) => {
    const [user, setUser] = useState(null);
    const [token, setToken] = useState(localStorage.getItem('auth_token'));
    const [loading, setLoading] = useState(true);

    // Set up axios interceptor for authentication
    useEffect(() => {
        if (token) {
            apiService.setAuthToken(token);
            // Verify token and get user data
            fetchUser();
        } else {
            setLoading(false);
        }
    }, [token]);

    const fetchUser = async () => {
        try {
            const response = await apiService.get('/user');
            setUser(response.data.data);
        } catch (error) {
            console.error('Failed to fetch user:', error);
            logout();
        } finally {
            setLoading(false);
        }
    };

    const login = async (email, password) => {
        try {
            const response = await apiService.post('/login', { email, password });
            const userData = response.data.data;
            const newToken = userData.token;

            setToken(newToken);
            setUser(userData);
            localStorage.setItem('auth_token', newToken);
            apiService.setAuthToken(newToken);

            return { success: true };
        } catch (error) {
            return {
                success: false,
                message: error.response?.data?.message || 'Login failed'
            };
        }
    };

    const register = async (userData) => {
        try {
            const response = await apiService.post('/register', userData);
            const newUser = response.data.data;
            const newToken = newUser.token;

            setToken(newToken);
            setUser(newUser);
            localStorage.setItem('auth_token', newToken);
            apiService.setAuthToken(newToken);

            return { success: true };
        } catch (error) {
            return {
                success: false,
                message: error.response?.data?.message || 'Registration failed',
                errors: error.response?.data?.errors || {}
            };
        }
    };

    const logout = () => {
        setUser(null);
        setToken(null);
        localStorage.removeItem('auth_token');
        apiService.removeAuthToken();
    };

    const hasRole = (role) => {
        return user?.roles?.includes(role) || false;
    };

    const hasAnyRole = (roles) => {
        return roles.some(role => hasRole(role));
    };

    const value = {
        user,
        token,
        loading,
        login,
        register,
        logout,
        hasRole,
        hasAnyRole,
        isAuthenticated: !!token && !!user,
    };

    return (
        <AuthContext.Provider value={value}>
            {children}
        </AuthContext.Provider>
    );
};
