import React, { createContext, useContext, useEffect, useState } from 'react';

const ThemeContext = createContext();

export const useTheme = () => {
    const context = useContext(ThemeContext);
    if (!context) {
        throw new Error('useTheme must be used within a ThemeProvider');
    }
    return context;
};

export const ThemeProvider = ({ children }) => {
    const [theme, setTheme] = useState(() => {
        // Get theme from localStorage or default to 'light'
        const savedTheme = localStorage.getItem('theme');
        return savedTheme || 'light';
    });

    useEffect(() => {
        // Apply theme to document root
        const root = document.documentElement;
        
        // Remove all theme classes
        root.classList.remove('light', 'dark', 'semiDark');
        
        // Add current theme class
        root.classList.add(theme);
        
        // Save to localStorage
        localStorage.setItem('theme', theme);
    }, [theme]);

    const toggleTheme = () => {
        setTheme(prevTheme => {
            // Toggle between light and dark (following DashCode pattern)
            return prevTheme === 'light' ? 'dark' : 'light';
        });
    };

    const setLightTheme = () => setTheme('light');
    const setDarkTheme = () => setTheme('dark');
    const setSemiDarkTheme = () => setTheme('semiDark');

    const value = {
        theme,
        setTheme,
        toggleTheme,
        setLightTheme,
        setDarkTheme,
        setSemiDarkTheme,
        isLight: theme === 'light',
        isDark: theme === 'dark',
        isSemiDark: theme === 'semiDark'
    };

    return (
        <ThemeContext.Provider value={value}>
            {children}
        </ThemeContext.Provider>
    );
};
