<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\GeneralSettingsController;
use App\Http\Controllers\Api\GeneralSettingsMediaController;
use App\Http\Controllers\Api\ProfileController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\EnvironmentController;
use App\Http\Controllers\Api\DatabaseBackupController;
use App\Http\Controllers\Api\SubscriptionProductController;
use App\Http\Controllers\Api\SubscriptionController;
use App\Http\Controllers\Api\PaymentController;
use App\Http\Controllers\Api\PaymentMethodController;
use App\Http\Controllers\Api\InvoiceController;
use App\Http\Controllers\Api\PaymentHistoryController;
use App\Http\Controllers\Api\NotificationController;
use App\Http\Controllers\Api\AccountStatusController;
use App\Http\Controllers\Api\RefundController;
use App\Http\Controllers\Api\StripeWebhookController;
use App\Http\Controllers\Api\Admin\AdminUserController;
use App\Http\Controllers\Api\Admin\AdminSubscriptionController;
use App\Http\Controllers\Api\Admin\AdminFinancialController;
use App\Http\Controllers\Api\Admin\AdminRefundController;

use App\Http\Controllers\Api\InvestorProfileController;
use App\Http\Controllers\Api\InvestorDiscoveryController;
use App\Http\Controllers\Api\StartupProfileController;
use App\Http\Controllers\Api\EsgController;
use App\Http\Controllers\Api\InterestRequestController;
use App\Http\Controllers\Api\DashboardController;
use App\Http\Controllers\Api\DiscoveryController;
use App\Http\Controllers\Api\StartupRegistrationController;
use App\Http\Controllers\Api\SocialMediaLinkController;
use App\Http\Controllers\Api\TaxonomyController;
use App\Http\Controllers\Api\BlogController;
use App\Http\Controllers\Api\FaqController;
use Illuminate\Support\Facades\Route;

/*
 * API Routes
 */
Route::post('register', [AuthController::class, 'register']);
Route::post('login', [AuthController::class, 'login']);
// OAuth
Route::post('login-oauth', [AuthController::class, 'social']);

Route::post('forgot-password', [AuthController::class, 'forgotPassword']);

// Enhanced startup registration
Route::post('startup/register', [StartupRegistrationController::class, 'register']);

// Public taxonomy endpoints
Route::get('taxonomies', [TaxonomyController::class, 'index']);
Route::get('social-media-links/platforms', [SocialMediaLinkController::class, 'platforms']);

// Verify new email after change
Route::get('profile-verify-new-email/{token}',
    [ProfileController::class, 'verifyNewEmail'])->name('profile.verify-new-email');

// Public subscription product routes (for pricing pages)
Route::get('subscription-products/public', [SubscriptionProductController::class, 'publicIndex']);

// authenticated routes
Route::group(['middleware' => ['auth:sanctum', 'check.account.status']], function () {
    Route::post('resend-verification', [AuthController::class, 'resendVerification'])
        ->middleware('throttle:6,1');
    Route::get('user', [AuthController::class, 'user']);
    Route::post('logout', [AuthController::class, 'logout']);

    Route::apiSingleton('env', EnvironmentController::class);

    Route::group(['middleware' => 'verified', 'as' => 'api.v1.'], function () {
        Route::post('password-change', [AuthController::class, 'changePassword']);
        Route::apiResource('users', UserController::class);
        Route::delete('users-delete-many', [UserController::class, 'destroyMany']);
        Route::apiSingleton('profile', ProfileController::class);
        Route::put('general-settings-images', GeneralSettingsMediaController::class);

        // Subscription Management Routes
        Route::apiResource('subscription-products', SubscriptionProductController::class);
        Route::apiResource('subscriptions', SubscriptionController::class);
        Route::post('subscriptions/checkout-success', [SubscriptionController::class, 'handleCheckoutSuccess']);
        Route::post('subscriptions/{subscription}/upgrade', [SubscriptionController::class, 'upgrade']);
        Route::post('subscriptions/{subscription}/cancel', [SubscriptionController::class, 'cancel']);
        Route::post('subscriptions/cancel-with-refund', [SubscriptionController::class, 'cancelWithRefund']);
        Route::post('subscriptions/{subscription}/resume', [SubscriptionController::class, 'resume']);

        // Payment Management Routes
        Route::apiResource('payments', PaymentController::class)->only(['index', 'show']);
        Route::post('payments/create-intent', [PaymentController::class, 'createPaymentIntent']);
        Route::post('payments/confirm', [PaymentController::class, 'confirmPayment']);
        Route::post('payments/{payment}/retry', [PaymentController::class, 'retryPayment']);

        // Payment Method Management Routes
        Route::apiResource('payment-methods', PaymentMethodController::class);
        Route::post('payment-methods/setup-intent', [PaymentMethodController::class, 'createSetupIntent']);
        Route::post('payment-methods/checkout-session', [PaymentMethodController::class, 'createCheckoutSession']);
        Route::post('payment-methods/process-checkout-success', [PaymentMethodController::class, 'processCheckoutSuccess']);
        Route::post('payment-methods/{payment_method}/set-default', [PaymentMethodController::class, 'setDefault']);

        // Invoice Management Routes
        Route::apiResource('invoices', InvoiceController::class)->only(['index', 'show']);
        Route::post('invoices/generate', [InvoiceController::class, 'generate']);
        Route::get('invoices/{invoice}/download', [InvoiceController::class, 'download']);
        Route::post('invoices/{invoice}/pay', [InvoiceController::class, 'pay']);

        // Payment History Routes
        Route::get('payment-history', [PaymentHistoryController::class, 'index']);
        Route::get('payment-history/statistics', [PaymentHistoryController::class, 'statistics']);
        Route::get('payment-history/recent', [PaymentHistoryController::class, 'recent']);
        Route::get('payment-history/export', [PaymentHistoryController::class, 'export']);

        // Notification Routes
        Route::get('notifications', [NotificationController::class, 'index']);
        Route::get('notifications/unread-count', [NotificationController::class, 'unreadCount']);
        Route::get('notifications/statistics', [NotificationController::class, 'statistics']);
        Route::post('notifications/{notification}/mark-as-read', [NotificationController::class, 'markAsRead']);
        Route::post('notifications/mark-all-as-read', [NotificationController::class, 'markAllAsRead']);
        Route::delete('notifications/{notification}', [NotificationController::class, 'destroy']);

        // Account Status Routes
        Route::get('account/status', [AccountStatusController::class, 'show']);

        // Refund Management Routes
        Route::prefix('refunds')->group(function () {
            Route::get('/', [RefundController::class, 'index']);
            Route::get('/statistics', [RefundController::class, 'statistics']);
            Route::get('/{refund}', [RefundController::class, 'show']);
            Route::post('/request', [RefundController::class, 'requestRefund']);
            Route::post('/calculate-prorated', [RefundController::class, 'calculateProratedRefund']);
        });

        // Admin Routes
        Route::prefix('admin')->name('admin.')->group(function () {
            // User Management
            Route::apiResource('users', AdminUserController::class)->only(['index', 'show']);
            Route::put('users/{user}/account-status', [AdminUserController::class, 'updateAccountStatus']);
            Route::get('users/{user}/subscription-analytics', [AdminUserController::class, 'subscriptionAnalytics']);
            Route::get('users/{user}/payment-history', [AdminUserController::class, 'paymentHistory']);

            // Subscription Management
            Route::apiResource('subscriptions', AdminSubscriptionController::class)->only(['index', 'show']);
            Route::get('subscriptions-analytics', [AdminSubscriptionController::class, 'analytics']);
            Route::post('subscriptions/{subscription}/cancel', [AdminSubscriptionController::class, 'cancel']);
            Route::post('subscriptions/{subscription}/pause', [AdminSubscriptionController::class, 'pause']);

            // Financial Management
            Route::get('financial/overview', [AdminFinancialController::class, 'overview']);
            Route::get('financial/payments', [AdminFinancialController::class, 'payments']);
            Route::get('financial/invoices', [AdminFinancialController::class, 'invoices']);
            Route::get('financial/refunds', [AdminFinancialController::class, 'refunds']);
            Route::post('financial/process-refund', [AdminFinancialController::class, 'processRefund']);

            // Refund Management Routes
            Route::prefix('refunds')->group(function () {
                Route::get('/', [AdminRefundController::class, 'index']);
                Route::get('/statistics', [AdminRefundController::class, 'statistics']);
                Route::get('/{refund}', [AdminRefundController::class, 'show']);
                Route::post('/process', [AdminRefundController::class, 'processRefund']);
                Route::post('/process-prorated', [AdminRefundController::class, 'processProratedRefund']);
            });

            // Account Status Management Routes
            Route::post('users/{user}/lock', [AccountStatusController::class, 'lock']);
            Route::post('users/{user}/unlock', [AccountStatusController::class, 'unlock']);
            Route::post('users/{user}/suspend', [AccountStatusController::class, 'suspend']);
            Route::post('users/{user}/activate', [AccountStatusController::class, 'activate']);
            Route::post('users/bulk-status-update', [AccountStatusController::class, 'bulkUpdate']);
        });

        // Enhanced Startup Registration and Profile Management
        Route::prefix('startup')->group(function () {
            Route::get('registration-progress', [StartupRegistrationController::class, 'getRegistrationProgress']);
            // TODO: Add company-information endpoint when method is implemented
        });

        // Social Media Links Management
        Route::apiResource('social-media-links', SocialMediaLinkController::class);

        // Taxonomy Management (Enhanced Category System)
        Route::prefix('taxonomies')->group(function () {
            Route::get('user', [TaxonomyController::class, 'getUserTaxonomies']);
            Route::post('attach', [TaxonomyController::class, 'attachToUser']);
            Route::post('detach', [TaxonomyController::class, 'detachFromUser']);
            Route::get('suggestions', [TaxonomyController::class, 'getSuggestions']);
        });

        // Investment Platform Routes
        Route::prefix('investment')->name('investment.')->middleware(['auth:sanctum'])->group(function () {
            // Dashboard Statistics
            Route::prefix('dashboard')->group(function () {
                Route::get('investor-stats', [DashboardController::class, 'getInvestorStats'])
                    ->middleware('investment.role:investor');
                Route::get('startup-stats', [DashboardController::class, 'getStartupStats'])
                    ->middleware('investment.role:startup');
                Route::get('analyst-stats', [DashboardController::class, 'getAnalystStats'])
                    ->middleware('investment.role:analyst');
                Route::get('recent-activity', [DashboardController::class, 'getRecentActivity']);
            });

            // Categories (legacy - redirected to taxonomy system)
            Route::get('categories', [TaxonomyController::class, 'index'])->defaults('type', 'category');
            Route::get('categories-grouped', [TaxonomyController::class, 'index'])->defaults('type', 'category');

            // User Category Management (legacy - use taxonomy endpoints instead)
            Route::get('user-categories', [TaxonomyController::class, 'getUserTaxonomies'])->defaults('type', 'category');
            Route::put('user-categories', [TaxonomyController::class, 'attachToUser'])->defaults('type', 'category');
            Route::post('user-categories', [TaxonomyController::class, 'attachToUser'])->defaults('type', 'category');
            Route::delete('user-categories/{categoryId}', [TaxonomyController::class, 'detachFromUser']);

            // Investor Profiles (investor role required)
            Route::middleware(['investment.role:investor'])->group(function () {
                Route::apiResource('investor-profiles', InvestorProfileController::class)
                    ->only(['index', 'store', 'show', 'update', 'destroy']);
            });

            // Startup Profiles (startup role required)
            Route::middleware(['investment.role:startup'])->group(function () {
                Route::apiResource('startup-profiles', StartupProfileController::class)
                    ->only(['index', 'store', 'show', 'update', 'destroy']);
                Route::get('startup-profiles/taxonomy-options', [StartupProfileController::class, 'getTaxonomyOptions']);

                // Investor Discovery
                Route::get('investor-discovery/search', [InvestorDiscoveryController::class, 'searchInvestors']);
                Route::get('investor-discovery/recommendations', [InvestorDiscoveryController::class, 'getRecommendations']);
                Route::get('investor-discovery/filter-options', [InvestorDiscoveryController::class, 'getFilterOptions']);
            });

            // ESG Questionnaire
            Route::prefix('esg')->group(function () {
                Route::get('questions', [EsgController::class, 'questions']);
                Route::middleware(['investment.role:startup'])->group(function () {
                    Route::post('responses', [EsgController::class, 'submitResponses']);
                    Route::get('responses', [EsgController::class, 'responses']);
                });
                Route::middleware(['investment.role:analyst'])->group(function () {
                    Route::get('analytics', [EsgController::class, 'analytics']);
                });
            });

            // Interest Requests
            Route::apiResource('interest-requests', InterestRequestController::class)
                ->only(['index', 'store', 'show'])
                ->middleware('subscription.access');
            Route::middleware(['investment.role:analyst'])->group(function () {
                Route::post('interest-requests/{interestRequest}/approve', [InterestRequestController::class, 'approve']);
                Route::post('interest-requests/{interestRequest}/reject', [InterestRequestController::class, 'reject']);
                Route::get('interest-requests-statistics', [InterestRequestController::class, 'statistics'])
                    ->middleware('subscription.access:advanced_analytics');
            });

            // Discovery
            Route::prefix('discovery')->group(function () {
                Route::middleware(['investment.role:investor', 'subscription.access'])->group(function () {
                    Route::get('startups', [DiscoveryController::class, 'discoverStartups']);
                    Route::get('category-matches/startups', [DiscoveryController::class, 'getCategoryMatchedStartups']);
                });
                Route::middleware(['investment.role:startup', 'subscription.access'])->group(function () {
                    Route::get('investors', [DiscoveryController::class, 'discoverInvestors']);
                    Route::get('category-matches/investors', [DiscoveryController::class, 'getCategoryMatchedInvestors']);
                });
            });
        });

        // Blog API Routes
        Route::prefix('blogs')->group(function () {
            Route::get('/', [BlogController::class, 'index']);
            Route::get('/search', [BlogController::class, 'search']);
            Route::get('/categories', [BlogController::class, 'categories']);
            Route::get('/category/{categoryId}', [BlogController::class, 'byCategory']);
            Route::get('/{id}', [BlogController::class, 'show']);
            Route::get('/{id}/related', [BlogController::class, 'related']);
        });

        // FAQ API Routes
        Route::prefix('faqs')->group(function () {
            Route::get('/', [FaqController::class, 'index']);
            Route::get('/search', [FaqController::class, 'search']);
            Route::get('/categories', [FaqController::class, 'categories']);
            Route::get('/role/{role}', [FaqController::class, 'byRole']);
            Route::get('/category/{categoryId}', [FaqController::class, 'byCategory']);
        });

        // Database Backup
        Route::apiResource('database-backups', DatabaseBackupController::class)->only(['index', 'destroy']);
        Route::get('database-backups-create', [DatabaseBackupController::class,'createBackup']);
        Route::get('database-backups-download/{fileName}', [DatabaseBackupController::class, 'databaseBackupDownload']);
    });
});

// General Settings
Route::get('general-settings', GeneralSettingsController::class);

// Stripe Webhook (no authentication required)
Route::post('stripe/webhook', [StripeWebhookController::class, 'handleWebhook']);
