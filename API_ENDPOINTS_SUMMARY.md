# Investment Platform API Endpoints

## Authentication
All endpoints require `Authorization: Bear<PERSON> {token}` header with Laravel Sanctum token.

## Categories
- `GET /api/investment/categories` - List all active categories
- **Status**: ✅ Working (HTTP 200)

## Investor Profiles
- `POST /api/investment/investor-profiles` - Create/update investor profile
- `GET /api/investment/investor-profiles` - Get current investor profile
- **Status**: ✅ Working (HTTP 200/201)
- **Features**: Investment preferences, budget range, risk tolerance, category selection

## Startup Profiles  
- `POST /api/investment/startup-profiles` - Create/update startup profile
- `GET /api/investment/startup-profiles` - Get current startup profile
- **Status**: ✅ Working (HTTP 200/201)
- **Features**: Company details, funding stage, amount sought, business model, category selection

## ESG System
- `GET /api/investment/esg/questions` - Get ESG questionnaire
- `POST /api/investment/esg/responses` - Submit ESG responses (startups only)
- `GET /api/investment/esg/responses` - Get current ESG responses
- **Status**: ✅ Working (HTTP 200/201)
- **Features**: Weighted scoring, automatic calculation, environmental/social/governance breakdown

## Discovery & Matching
- `GET /api/investment/discovery/startups` - Discover startups (investors only)
- `GET /api/investment/discovery/investors` - Discover investors (startups only)
- **Status**: ✅ Working (HTTP 200)
- **Features**: 
  - Budget-based filtering
  - Category matching
  - ESG score filtering
  - Contact history exclusion
  - Match score calculation

## Interest Requests
- `POST /api/investment/interest-requests` - Submit interest request
- `GET /api/investment/interest-requests` - List interest requests
- `GET /api/investment/interest-requests/{id}` - Get specific request
- **Status**: ✅ Working (HTTP 200/201)
- **Features**:
  - Duplicate prevention
  - Role-based request types
  - Approval workflow ready
  - Bidirectional visibility

## Query Parameters

### Discovery Filters
- `min_esg_score` - Minimum ESG score filter
- `funding_stages` - Comma-separated funding stages
- `per_page` - Results per page (default: 10)

### Interest Request Filters
- `status` - Filter by status (pending, approved, rejected)
- `type` - Filter by type (investment_interest, funding_request)

## Response Format
All endpoints return JSON in this format:
```json
{
  "success": true,
  "message": "Operation successful",
  "data": { ... }
}
```

## Error Handling
- **401**: Unauthenticated
- **403**: Insufficient permissions
- **422**: Validation errors
- **404**: Resource not found
- **500**: Server error
