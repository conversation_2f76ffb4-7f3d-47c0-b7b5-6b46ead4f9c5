<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Check if columns exist before adding them
            if (!Schema::hasColumn('users', 'account_status')) {
                $table->enum('account_status', ['active', 'locked', 'suspended', 'inactive'])->default('active')->after('email_verified_at');
            }

            // Account locking fields
            if (!Schema::hasColumn('users', 'account_locked_at')) {
                $table->timestamp('account_locked_at')->nullable()->after('account_status');
            }
            if (!Schema::hasColumn('users', 'account_locked_reason')) {
                $table->text('account_locked_reason')->nullable()->after('account_locked_at');
            }
            if (!Schema::hasColumn('users', 'account_locked_by')) {
                $table->unsignedBigInteger('account_locked_by')->nullable()->after('account_locked_reason');
            }

            // Account suspension fields
            if (!Schema::hasColumn('users', 'account_suspended_at')) {
                $table->timestamp('account_suspended_at')->nullable()->after('account_locked_by');
            }
            if (!Schema::hasColumn('users', 'account_suspended_reason')) {
                $table->text('account_suspended_reason')->nullable()->after('account_suspended_at');
            }
            if (!Schema::hasColumn('users', 'account_suspended_by')) {
                $table->unsignedBigInteger('account_suspended_by')->nullable()->after('account_suspended_reason');
            }

            // Account activation fields
            if (!Schema::hasColumn('users', 'account_activated_at')) {
                $table->timestamp('account_activated_at')->nullable()->after('account_suspended_by');
            }
            if (!Schema::hasColumn('users', 'account_activated_by')) {
                $table->unsignedBigInteger('account_activated_by')->nullable()->after('account_activated_at');
            }
            if (!Schema::hasColumn('users', 'account_unlocked_at')) {
                $table->timestamp('account_unlocked_at')->nullable()->after('account_activated_by');
            }
            if (!Schema::hasColumn('users', 'account_unlocked_by')) {
                $table->unsignedBigInteger('account_unlocked_by')->nullable()->after('account_unlocked_at');
            }

            // Foreign key constraints (only add if columns were added)
            if (Schema::hasColumn('users', 'account_locked_by')) {
                $table->foreign('account_locked_by')->references('id')->on('users')->onDelete('set null');
            }
            if (Schema::hasColumn('users', 'account_suspended_by')) {
                $table->foreign('account_suspended_by')->references('id')->on('users')->onDelete('set null');
            }
            if (Schema::hasColumn('users', 'account_activated_by')) {
                $table->foreign('account_activated_by')->references('id')->on('users')->onDelete('set null');
            }
            if (Schema::hasColumn('users', 'account_unlocked_by')) {
                $table->foreign('account_unlocked_by')->references('id')->on('users')->onDelete('set null');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['account_locked_by']);
            $table->dropForeign(['account_suspended_by']);
            $table->dropForeign(['account_activated_by']);
            $table->dropForeign(['account_unlocked_by']);

            $table->dropColumn([
                'account_status',
                'account_locked_at',
                'account_locked_reason',
                'account_locked_by',
                'account_suspended_at',
                'account_suspended_reason',
                'account_suspended_by',
                'account_activated_at',
                'account_activated_by',
                'account_unlocked_at',
                'account_unlocked_by',
            ]);
        });
    }
};
