<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('startup_profiles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('company_name');
            $table->text('company_description');
            $table->date('founding_date')->nullable();
            $table->integer('employee_count')->nullable();
            $table->string('website')->nullable();
            $table->string('linkedin')->nullable();
            $table->enum('funding_stage', ['pre_seed', 'seed', 'series_a', 'series_b', 'series_c', 'later_stage'])->nullable();
            $table->decimal('funding_amount_sought', 15, 2)->nullable();
            $table->decimal('current_valuation', 15, 2)->nullable();
            $table->json('business_model')->nullable(); // B2B, B2C, marketplace, etc.
            $table->decimal('esg_score', 5, 2)->nullable(); // 0-100 ESG score
            $table->json('esg_breakdown')->nullable(); // Environmental, Social, Governance scores
            $table->boolean('esg_completed')->default(false);
            $table->boolean('profile_completed')->default(false);
            $table->timestamps();

            $table->unique('user_id');
            $table->index(['profile_completed', 'funding_stage']);
            $table->index(['esg_completed', 'esg_score']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('startup_profiles');
    }
};
