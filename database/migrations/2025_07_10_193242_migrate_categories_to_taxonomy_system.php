<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Migrate existing categories to taxonomy system and update user relationships
     */
    public function up(): void
    {
        // Migrate existing categories to taxonomies
        $categories = DB::table('categories')->get();

        foreach ($categories as $category) {
            // Create taxonomy entry
            $taxonomy = Taxonomy::create([
                'name' => $category->name,
                'slug' => \Illuminate\Support\Str::slug($category->name),
                'type' => 'category',
                'description' => $category->description,
                'sort_order' => $category->sort_order ?? 0,
                'meta' => json_encode([
                    'original_type' => $category->type,
                    'is_active' => $category->is_active ?? true,
                    'migrated_from_categories' => true,
                ]),
            ]);

            // Migrate user-category relationships to taxonomables
            $userCategories = DB::table('user_categories')
                ->where('category_id', $category->id)
                ->get();

            foreach ($userCategories as $userCategory) {
                DB::table('taxonomables')->insert([
                    'taxonomy_id' => $taxonomy->id,
                    'taxonomable_type' => 'App\\Models\\User',
                    'taxonomable_id' => $userCategory->user_id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove migrated taxonomies
        Taxonomy::where('type', 'category')
            ->whereJsonContains('meta->migrated_from_categories', true)
            ->delete();
    }
};
