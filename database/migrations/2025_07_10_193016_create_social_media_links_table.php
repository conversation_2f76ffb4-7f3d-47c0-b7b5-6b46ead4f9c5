<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Create polymorphic social media links table to replace individual social media fields
     */
    public function up(): void
    {
        Schema::create('social_media_links', function (Blueprint $table) {
            $table->id();
            $table->morphs('linkable'); // Can attach to User, StartupProfile, etc.
            $table->enum('platform', ['linkedin', 'twitter', 'facebook', 'instagram', 'website', 'github', 'youtube', 'tiktok']);
            $table->string('url');
            $table->string('username')->nullable(); // For platforms that use usernames
            $table->boolean('is_primary')->default(false);
            $table->boolean('is_public')->default(true);
            $table->timestamps();

            // Indexes
            $table->index(['platform']);
            $table->index(['is_primary']);
            $table->index(['is_public']);

            // Ensure only one primary link per platform per linkable
            $table->unique(['linkable_type', 'linkable_id', 'platform', 'is_primary'], 'unique_primary_platform_per_linkable');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('social_media_links');
    }
};
