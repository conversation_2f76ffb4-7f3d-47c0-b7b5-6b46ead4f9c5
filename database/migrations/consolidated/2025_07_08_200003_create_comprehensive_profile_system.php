<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * This consolidated migration includes all profile-related tables:
     * - 2025_07_05_103119_create_investor_profiles_table.php
     * - 2025_07_05_103139_create_startup_profiles_table.php
     * - 2025_07_05_103309_create_user_categories_table.php
     */
    public function up(): void
    {
        // Investor Profiles Table
        Schema::create('investor_profiles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->text('bio')->nullable();
            $table->json('preferred_sectors')->nullable(); // Array of preferred investment sectors
            $table->decimal('investment_range_min', 15, 2)->nullable();
            $table->decimal('investment_range_max', 15, 2)->nullable();
            $table->enum('investment_stage_preference', ['pre_seed', 'seed', 'series_a', 'series_b', 'series_c', 'growth', 'all'])->default('all');
            $table->json('geographic_preferences')->nullable(); // Array of preferred regions/countries
            $table->enum('investment_experience', ['beginner', 'intermediate', 'experienced', 'expert'])->default('beginner');
            $table->integer('portfolio_size')->nullable(); // Number of companies in portfolio
            $table->text('investment_philosophy')->nullable();
            $table->json('esg_priorities')->nullable(); // Environmental, Social, Governance priorities
            $table->boolean('accredited_investor')->default(false);
            $table->string('linkedin_url')->nullable();
            $table->string('website_url')->nullable();
            $table->string('company_name')->nullable();
            $table->string('job_title')->nullable();
            $table->boolean('profile_completed')->default(false);
            $table->boolean('is_public')->default(true);
            $table->timestamps();
            
            $table->index(['user_id']);
            $table->index(['profile_completed']);
            $table->index(['is_public']);
            $table->index(['investment_experience']);
        });

        // Startup Profiles Table
        Schema::create('startup_profiles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('company_name');
            $table->text('description');
            $table->string('industry');
            $table->enum('funding_stage', ['idea', 'pre_seed', 'seed', 'series_a', 'series_b', 'series_c', 'growth', 'ipo'])->default('idea');
            $table->decimal('funding_goal', 15, 2)->nullable();
            $table->decimal('current_funding', 15, 2)->default(0);
            $table->integer('team_size')->nullable();
            $table->date('founded_date')->nullable();
            $table->string('location')->nullable();
            $table->string('website_url')->nullable();
            $table->string('linkedin_url')->nullable();
            $table->json('business_model')->nullable(); // B2B, B2C, B2B2C, etc.
            $table->json('revenue_streams')->nullable();
            $table->decimal('monthly_revenue', 15, 2)->nullable();
            $table->decimal('annual_revenue', 15, 2)->nullable();
            $table->integer('customer_count')->nullable();
            $table->text('competitive_advantage')->nullable();
            $table->text('market_size')->nullable();
            $table->text('target_market')->nullable();
            $table->json('key_metrics')->nullable(); // Custom metrics important to the startup
            $table->text('use_of_funds')->nullable(); // How they plan to use investment
            $table->boolean('profile_completed')->default(false);
            $table->boolean('esg_completed')->default(false);
            $table->boolean('is_public')->default(true);
            $table->timestamps();
            
            $table->index(['user_id']);
            $table->index(['industry']);
            $table->index(['funding_stage']);
            $table->index(['profile_completed']);
            $table->index(['esg_completed']);
            $table->index(['is_public']);
        });

        // User Categories Junction Table
        Schema::create('user_categories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->timestamps();
            
            $table->unique(['user_id', 'category_id']);
            $table->index(['user_id']);
            $table->index(['category_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_categories');
        Schema::dropIfExists('startup_profiles');
        Schema::dropIfExists('investor_profiles');
    }
};
