<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * This consolidated migration includes all ESG-related tables:
     * - 2025_07_05_103203_create_esg_questions_table.php
     * - 2025_07_05_103226_create_esg_responses_table.php
     */
    public function up(): void
    {
        // ESG Questions Table
        Schema::create('esg_questions', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('question');
            $table->enum('category', ['environmental', 'social', 'governance']); // E, S, or G
            $table->enum('type', ['multiple_choice', 'scale', 'text', 'boolean', 'numeric'])->default('multiple_choice');
            $table->json('options')->nullable(); // For multiple choice questions
            $table->integer('min_value')->nullable(); // For scale questions
            $table->integer('max_value')->nullable(); // For scale questions
            $table->text('description')->nullable(); // Additional context for the question
            $table->text('help_text')->nullable(); // Help text to guide responses
            $table->boolean('is_required')->default(true);
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->decimal('weight', 5, 2)->default(1.00); // Weight for scoring calculations
            $table->timestamps();
            
            $table->index(['category', 'is_active']);
            $table->index(['type']);
            $table->index(['sort_order']);
            $table->index(['is_required']);
        });

        // ESG Responses Table
        Schema::create('esg_responses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('startup_profile_id')->constrained()->onDelete('cascade');
            $table->foreignId('esg_question_id')->constrained()->onDelete('cascade');
            $table->text('response_value'); // Stores the actual response (text, number, selected option, etc.)
            $table->decimal('score', 5, 2)->nullable(); // Calculated score for this response
            $table->text('notes')->nullable(); // Additional notes or explanations
            $table->timestamps();
            
            $table->unique(['startup_profile_id', 'esg_question_id']);
            $table->index(['startup_profile_id']);
            $table->index(['esg_question_id']);
            $table->index(['score']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('esg_responses');
        Schema::dropIfExists('esg_questions');
    }
};
