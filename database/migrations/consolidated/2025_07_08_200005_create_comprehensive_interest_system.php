<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * This consolidated migration includes all interest-related tables:
     * - 2025_07_05_103245_create_interest_requests_table.php
     */
    public function up(): void
    {
        // Interest Requests Table
        Schema::create('interest_requests', function (Blueprint $table) {
            $table->id();
            $table->foreignId('requester_id')->constrained('users')->onDelete('cascade'); // User making the request
            $table->foreignId('target_id')->constrained('users')->onDelete('cascade'); // User receiving the request
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null'); // Analyst who approved
            $table->enum('requester_type', ['investor', 'startup']); // Type of user making request
            $table->enum('target_type', ['investor', 'startup']); // Type of user receiving request
            $table->text('message')->nullable(); // Optional message from requester
            $table->text('investment_amount')->nullable(); // Proposed investment amount (for investor requests)
            $table->text('equity_offered')->nullable(); // Equity percentage offered (for startup requests)
            $table->json('terms')->nullable(); // Additional terms or conditions
            $table->enum('status', ['pending', 'approved', 'rejected', 'withdrawn'])->default('pending');
            $table->timestamp('approved_at')->nullable();
            $table->timestamp('rejected_at')->nullable();
            $table->timestamp('withdrawn_at')->nullable();
            $table->text('rejection_reason')->nullable();
            $table->text('admin_notes')->nullable(); // Notes from analyst/admin
            $table->boolean('requires_approval')->default(true); // Whether this needs analyst approval
            $table->timestamps();
            
            $table->index(['requester_id', 'status']);
            $table->index(['target_id', 'status']);
            $table->index(['approved_by']);
            $table->index(['status']);
            $table->index(['requester_type', 'target_type']);
            $table->index(['requires_approval']);
            
            // Prevent duplicate requests
            $table->unique(['requester_id', 'target_id'], 'unique_interest_request');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('interest_requests');
    }
};
