<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * This consolidated migration includes all user-related fields from multiple migrations:
     * - 2014_10_12_000000_create_users_table.php
     * - 2022_11_29_075437_add_columns_to_users_table.php
     * - 2025_07_03_162829_add_stripe_fields_to_users_table.php
     * - 2025_07_04_082231_add_account_status_fields_to_users_table.php
     * - 2025_07_06_000001_add_role_column_to_users_table.php
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            // Basic user information
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            
            // Contact information (from add_columns_to_users_table)
            $table->string('phone')->nullable();
            $table->string('post_code')->nullable();
            $table->string('city')->nullable();
            $table->string('country')->nullable();
            
            // Stripe integration (from add_stripe_fields_to_users_table)
            $table->string('stripe_customer_id')->nullable();
            
            // Account management (from add_account_status_fields_to_users_table)
            $table->enum('account_status', ['active', 'suspended', 'pending'])->default('active');
            $table->boolean('account_locked')->default(false);
            $table->timestamp('account_locked_at')->nullable();
            $table->text('account_locked_reason')->nullable();
            
            // Role management (from add_role_column_to_users_table)
            $table->enum('role', ['investor', 'startup', 'analyst', 'admin', 'super-admin'])->default('investor');
            
            // Standard Laravel fields
            $table->rememberToken();
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['email']);
            $table->index(['role']);
            $table->index(['account_status']);
            $table->index(['stripe_customer_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
