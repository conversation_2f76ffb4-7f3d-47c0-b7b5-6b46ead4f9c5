<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add role column if it doesn't exist
            if (!Schema::hasColumn('users', 'role')) {
                $table->enum('role', ['user', 'admin', 'super-admin', 'analyst', 'investor', 'startup'])
                      ->default('user')
                      ->after('email_verified_at');
            }
        });

        // Migrate existing Spatie Permission roles to the new role column
        $this->migrateExistingRoles();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('role');
        });
    }

    /**
     * Migrate existing Spatie Permission roles to the new role column
     */
    private function migrateExistingRoles(): void
    {
        try {
            // Check if model_has_roles table exists (Spatie Permission)
            if (Schema::hasTable('model_has_roles')) {
                // Get users with roles from Spatie Permission
                $usersWithRoles = DB::table('model_has_roles')
                    ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
                    ->where('model_type', 'App\\Models\\User')
                    ->select('model_id as user_id', 'roles.name as role_name')
                    ->get();

                foreach ($usersWithRoles as $userRole) {
                    // Map Spatie roles to our simplified roles
                    $newRole = $this->mapSpatieRoleToNewRole($userRole->role_name);
                    
                    // Update user with new role
                    DB::table('users')
                        ->where('id', $userRole->user_id)
                        ->update(['role' => $newRole]);
                }
            }

            // Set default roles for users without roles
            DB::table('users')
                ->whereNull('role')
                ->orWhere('role', '')
                ->update(['role' => 'user']);

        } catch (\Exception $e) {
            // If migration fails, just set all users to 'user' role
            DB::table('users')->update(['role' => 'user']);
            
            // Set first user as super-admin
            $firstUser = DB::table('users')->orderBy('id')->first();
            if ($firstUser) {
                DB::table('users')
                    ->where('id', $firstUser->id)
                    ->update(['role' => 'super-admin']);
            }
        }
    }

    /**
     * Map Spatie Permission roles to our simplified role system
     */
    private function mapSpatieRoleToNewRole(string $spatieRole): string
    {
        return match(strtolower($spatieRole)) {
            'super-admin', 'super_admin', 'superadmin' => 'super-admin',
            'admin' => 'admin',
            'analyst' => 'analyst',
            'investor' => 'investor',
            'startup' => 'startup',
            default => 'user',
        };
    }
};
