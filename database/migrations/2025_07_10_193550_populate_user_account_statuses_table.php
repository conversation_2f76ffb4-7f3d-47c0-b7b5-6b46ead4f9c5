<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Populate user account statuses table with existing user data
     */
    public function up(): void
    {
        // Get all users and create corresponding account status records
        $users = DB::table('users')->get();

        foreach ($users as $user) {
            DB::table('user_account_statuses')->insert([
                'user_id' => $user->id,
                'status' => $user->account_status ?? 'active',
                'stripe_customer_id' => $user->stripe_customer_id,
                'is_locked' => $user->account_locked ?? false,
                'locked_at' => $user->account_locked_at,
                'locked_reason' => $user->account_locked_reason ?? null,
                'locked_by' => $user->account_locked_by ?? null,
                'suspended_at' => $user->account_suspended_at ?? null,
                'suspended_reason' => $user->account_suspended_reason ?? null,
                'suspended_by' => $user->account_suspended_by ?? null,
                'activated_at' => $user->account_activated_at ?? null,
                'activated_by' => $user->account_activated_by ?? null,
                'unlocked_at' => $user->account_unlocked_at ?? null,
                'unlocked_by' => $user->account_unlocked_by ?? null,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('user_account_statuses')->truncate();
    }
};
