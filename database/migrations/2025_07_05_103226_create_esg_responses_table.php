<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('esg_responses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('startup_profile_id')->constrained()->onDelete('cascade');
            $table->foreignId('esg_question_id')->constrained()->onDelete('cascade');
            $table->text('response_value'); // Store the actual response
            $table->decimal('score', 5, 2)->nullable(); // Calculated score for this response
            $table->timestamps();

            $table->unique(['startup_profile_id', 'esg_question_id']);
            $table->index('startup_profile_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('esg_responses');
    }
};
