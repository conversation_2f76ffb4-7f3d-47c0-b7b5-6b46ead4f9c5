<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Clean up obsolete database columns and tables after architectural improvements
     */
    public function up(): void
    {
        // Remove obsolete columns from users table
        Schema::table('users', function (Blueprint $table) {
            // Account status fields (moved to user_account_statuses table)
            $table->dropColumn([
                'stripe_customer_id',
                'account_locked',
                'account_locked_at',
                'account_locked_reason',
                'account_locked_by',
                'account_suspended_at',
                'account_suspended_reason',
                'account_suspended_by',
                'account_activated_at',
                'account_activated_by',
                'account_unlocked_at',
                'account_unlocked_by',
                'account_status',
            ]);

            // Social media fields (replaced by polymorphic social_media_links table)
            $table->dropColumn([
                'linkedin_url',
                'twitter_handle',
                'facebook_url',
                'personal_website',
            ]);
        });

        // Drop obsolete tables
        Schema::dropIfExists('document_uploads'); // Replaced by Spatie Media Library
        Schema::dropIfExists('user_categories'); // Replaced by taxonomables table
        Schema::dropIfExists('categories'); // Replaced by taxonomy system
    }

    /**
     * Reverse the migrations.
     *
     * Restore dropped columns and tables for rollback purposes
     */
    public function down(): void
    {
        // Recreate categories table
        Schema::create('categories', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('type', ['investor', 'startup', 'both'])->default('both');
            $table->unsignedBigInteger('parent_id')->nullable();
            $table->foreign('parent_id')->references('id')->on('categories')->onDelete('cascade');
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->index(['type']);
            $table->index(['is_active']);
            $table->index(['sort_order']);
        });

        // Recreate user_categories table
        Schema::create('user_categories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->timestamps();

            $table->unique(['user_id', 'category_id']);
        });

        // Recreate document_uploads table
        Schema::create('document_uploads', function (Blueprint $table) {
            $table->id();
            $table->morphs('uploadable');
            $table->string('document_type');
            $table->string('original_filename');
            $table->string('stored_filename');
            $table->string('file_path');
            $table->string('mime_type');
            $table->unsignedBigInteger('file_size');
            $table->json('metadata')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamp('uploaded_at');
            $table->timestamps();

            $table->index(['document_type']);
            $table->index(['is_active']);
        });

        // Restore columns to users table
        Schema::table('users', function (Blueprint $table) {
            // Account status fields
            $table->string('stripe_customer_id')->nullable()->after('country');
            $table->enum('account_status', ['active', 'suspended', 'pending'])->default('active')->after('stripe_customer_id');
            $table->boolean('account_locked')->default(false)->after('account_status');
            $table->timestamp('account_locked_at')->nullable()->after('account_locked');
            $table->text('account_locked_reason')->nullable()->after('account_locked_at');
            $table->foreignId('account_locked_by')->nullable()->constrained('users')->after('account_locked_reason');
            $table->timestamp('account_suspended_at')->nullable()->after('account_locked_by');
            $table->text('account_suspended_reason')->nullable()->after('account_suspended_at');
            $table->foreignId('account_suspended_by')->nullable()->constrained('users')->after('account_suspended_reason');
            $table->timestamp('account_activated_at')->nullable()->after('account_suspended_by');
            $table->foreignId('account_activated_by')->nullable()->constrained('users')->after('account_activated_at');
            $table->timestamp('account_unlocked_at')->nullable()->after('account_activated_by');
            $table->foreignId('account_unlocked_by')->nullable()->constrained('users')->after('account_unlocked_at');

            // Social media fields
            $table->string('linkedin_url')->nullable()->after('account_unlocked_by');
            $table->string('twitter_handle')->nullable()->after('linkedin_url');
            $table->string('facebook_url')->nullable()->after('twitter_handle');
            $table->string('personal_website')->nullable()->after('facebook_url');
        });
    }
};
