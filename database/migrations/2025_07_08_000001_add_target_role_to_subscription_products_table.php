<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscription_products', function (Blueprint $table) {
            $table->enum('target_role', ['investor', 'startup', 'analyst', 'all'])
                  ->default('all')
                  ->after('sort_order');
        });

        // Update existing plans based on their names
        $this->updateExistingPlans();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscription_products', function (Blueprint $table) {
            $table->dropColumn('target_role');
        });
    }

    /**
     * Update existing subscription plans with appropriate target roles
     */
    private function updateExistingPlans(): void
    {
        try {
            // Update plans that contain 'investor' in their name
            DB::table('subscription_products')
                ->where('name', 'like', '%investor%')
                ->update(['target_role' => 'investor']);

            // Update plans that contain 'startup' in their name
            DB::table('subscription_products')
                ->where('name', 'like', '%startup%')
                ->update(['target_role' => 'startup']);

            // Update plans that contain 'analyst' in their name
            DB::table('subscription_products')
                ->where('name', 'like', '%analyst%')
                ->update(['target_role' => 'analyst']);

            // Plans that don't match any specific role remain as 'all'
        } catch (\Exception $e) {
            // If migration fails, log the error but don't fail the migration
            \Log::warning('Failed to update existing subscription plans with target roles: ' . $e->getMessage());
        }
    }
};
