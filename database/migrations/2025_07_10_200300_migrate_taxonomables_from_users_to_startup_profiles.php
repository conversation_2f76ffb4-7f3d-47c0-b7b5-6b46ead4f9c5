<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Migrate taxonomable relationships from users to startup_profiles
     */
    public function up(): void
    {
        // Get all taxonomable relationships for users with startup role
        $userTaxonomables = DB::table('taxonomables')
            ->where('taxonomable_type', 'App\\Models\\User')
            ->join('users', 'users.id', '=', 'taxonomables.taxonomable_id')
            ->where('users.role', 'startup')
            ->select('taxonomables.*', 'users.id as user_id')
            ->get();

        foreach ($userTaxonomables as $taxonomable) {
            // Find the corresponding startup profile
            $startupProfile = DB::table('startup_profiles')
                ->where('user_id', $taxonomable->user_id)
                ->first();

            if ($startupProfile) {
                // Check if this relationship already exists for the startup profile
                $exists = DB::table('taxonomables')
                    ->where('taxonomy_id', $taxonomable->taxonomy_id)
                    ->where('taxonomable_type', 'App\\Models\\StartupProfile')
                    ->where('taxonomable_id', $startupProfile->id)
                    ->exists();

                if (!$exists) {
                    // Create new taxonomable relationship for startup profile
                    DB::table('taxonomables')->insert([
                        'taxonomy_id' => $taxonomable->taxonomy_id,
                        'taxonomable_type' => 'App\\Models\\StartupProfile',
                        'taxonomable_id' => $startupProfile->id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }

        // Remove old user taxonomable relationships for startup users
        DB::table('taxonomables')
            ->where('taxonomable_type', 'App\\Models\\User')
            ->whereIn('taxonomable_id', function ($query) {
                $query->select('id')
                    ->from('users')
                    ->where('role', 'startup');
            })
            ->delete();
    }

    /**
     * Reverse the migrations.
     *
     * Restore taxonomable relationships back to users
     */
    public function down(): void
    {
        // Get all taxonomable relationships for startup profiles
        $profileTaxonomables = DB::table('taxonomables')
            ->where('taxonomable_type', 'App\\Models\\StartupProfile')
            ->join('startup_profiles', 'startup_profiles.id', '=', 'taxonomables.taxonomable_id')
            ->select('taxonomables.*', 'startup_profiles.user_id')
            ->get();

        foreach ($profileTaxonomables as $taxonomable) {
            // Check if this relationship already exists for the user
            $exists = DB::table('taxonomables')
                ->where('taxonomy_id', $taxonomable->taxonomy_id)
                ->where('taxonomable_type', 'App\\Models\\User')
                ->where('taxonomable_id', $taxonomable->user_id)
                ->exists();

            if (!$exists) {
                // Create new taxonomable relationship for user
                DB::table('taxonomables')->insert([
                    'taxonomy_id' => $taxonomable->taxonomy_id,
                    'taxonomable_type' => 'App\\Models\\User',
                    'taxonomable_id' => $taxonomable->user_id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }

        // Remove startup profile taxonomable relationships
        DB::table('taxonomables')
            ->where('taxonomable_type', 'App\\Models\\StartupProfile')
            ->delete();
    }
};
