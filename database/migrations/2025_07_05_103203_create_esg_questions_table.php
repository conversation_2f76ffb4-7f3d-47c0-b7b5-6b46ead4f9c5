<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('esg_questions', function (Blueprint $table) {
            $table->id();
            $table->string('question_text');
            $table->enum('category', ['environmental', 'social', 'governance']);
            $table->enum('type', ['multiple_choice', 'yes_no', 'scale', 'text']);
            $table->json('options')->nullable(); // For multiple choice questions
            $table->integer('weight')->default(1); // Weight for scoring
            $table->integer('sort_order')->default(0);
            $table->boolean('is_required')->default(true);
            $table->boolean('is_active')->default(true);
            $table->text('help_text')->nullable();
            $table->timestamps();

            $table->index(['category', 'is_active']);
            $table->index(['sort_order', 'category']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('esg_questions');
    }
};
