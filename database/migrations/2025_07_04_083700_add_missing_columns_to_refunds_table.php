<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('refunds', function (Blueprint $table) {
            // Add missing columns for pro-rated refund system
            if (!Schema::hasColumn('refunds', 'invoice_id')) {
                $table->foreignId('invoice_id')->nullable()->constrained()->onDelete('set null');
            }

            if (!Schema::hasColumn('refunds', 'stripe_payment_intent_id')) {
                $table->string('stripe_payment_intent_id')->nullable();
            }

            if (!Schema::hasColumn('refunds', 'stripe_charge_id')) {
                $table->string('stripe_charge_id')->nullable();
            }

            if (!Schema::hasColumn('refunds', 'original_amount')) {
                $table->unsignedBigInteger('original_amount')->nullable();
            }

            if (!Schema::hasColumn('refunds', 'prorated_amount')) {
                $table->unsignedBigInteger('prorated_amount')->nullable();
            }

            if (!Schema::hasColumn('refunds', 'type')) {
                $table->enum('type', ['full', 'partial', 'prorated'])->default('full');
            }

            if (!Schema::hasColumn('refunds', 'proration_details')) {
                $table->json('proration_details')->nullable();
            }

            if (!Schema::hasColumn('refunds', 'service_start_date')) {
                $table->date('service_start_date')->nullable();
            }

            if (!Schema::hasColumn('refunds', 'service_end_date')) {
                $table->date('service_end_date')->nullable();
            }

            if (!Schema::hasColumn('refunds', 'cancellation_date')) {
                $table->date('cancellation_date')->nullable();
            }

            if (!Schema::hasColumn('refunds', 'days_used')) {
                $table->integer('days_used')->nullable();
            }

            if (!Schema::hasColumn('refunds', 'total_days')) {
                $table->integer('total_days')->nullable();
            }

            if (!Schema::hasColumn('refunds', 'internal_notes')) {
                $table->text('internal_notes')->nullable();
            }

            if (!Schema::hasColumn('refunds', 'processed_by')) {
                $table->unsignedBigInteger('processed_by')->nullable();
                $table->foreign('processed_by')->references('id')->on('users')->onDelete('set null');
            }

            if (!Schema::hasColumn('refunds', 'stripe_created_at')) {
                $table->timestamp('stripe_created_at')->nullable();
            }

            // Add indexes if they don't exist
            // Note: Laravel doesn't have a built-in way to check for index existence
            // These will be added manually if needed
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('refunds', function (Blueprint $table) {
            // Drop added columns
            $table->dropColumn([
                'invoice_id',
                'stripe_payment_intent_id',
                'stripe_charge_id',
                'original_amount',
                'prorated_amount',
                'type',
                'proration_details',
                'service_start_date',
                'service_end_date',
                'cancellation_date',
                'days_used',
                'total_days',
                'internal_notes',
                'processed_by',
                'stripe_created_at'
            ]);
        });
    }
};
