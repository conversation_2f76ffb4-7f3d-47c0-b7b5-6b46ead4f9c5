<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Create user account statuses table to normalize users table
     */
    public function up(): void
    {
        Schema::create('user_account_statuses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');

            // Account status fields moved from users table
            $table->enum('status', ['active', 'suspended', 'pending'])->default('active');
            $table->string('stripe_customer_id')->nullable();

            // Account locking fields
            $table->boolean('is_locked')->default(false);
            $table->timestamp('locked_at')->nullable();
            $table->text('locked_reason')->nullable();
            $table->foreignId('locked_by')->nullable()->constrained('users');

            // Account suspension fields
            $table->timestamp('suspended_at')->nullable();
            $table->text('suspended_reason')->nullable();
            $table->foreignId('suspended_by')->nullable()->constrained('users');

            // Account activation fields
            $table->timestamp('activated_at')->nullable();
            $table->foreignId('activated_by')->nullable()->constrained('users');

            // Account unlocking fields
            $table->timestamp('unlocked_at')->nullable();
            $table->foreignId('unlocked_by')->nullable()->constrained('users');

            $table->timestamps();

            // Indexes
            $table->index(['status']);
            $table->index(['is_locked']);
            $table->index(['stripe_customer_id']);
            $table->unique(['user_id']); // One status record per user
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_account_statuses');
    }
};
