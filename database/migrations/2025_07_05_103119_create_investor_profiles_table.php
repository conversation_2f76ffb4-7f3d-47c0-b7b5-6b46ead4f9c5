<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('investor_profiles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->decimal('investment_budget_min', 15, 2)->nullable();
            $table->decimal('investment_budget_max', 15, 2)->nullable();
            $table->enum('risk_tolerance', ['low', 'medium', 'high'])->nullable();
            $table->enum('investment_experience', ['beginner', 'intermediate', 'experienced', 'expert'])->nullable();
            $table->text('bio')->nullable();
            $table->string('website')->nullable();
            $table->string('linkedin')->nullable();
            $table->json('investment_preferences')->nullable(); // sectors, stages, etc.
            $table->boolean('profile_completed')->default(false);
            $table->timestamps();

            $table->unique('user_id');
            $table->index(['profile_completed', 'risk_tolerance']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('investor_profiles');
    }
};
