<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class EsgQuestionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $questions = [
            // Environmental Questions
            [
                'question_text' => 'Does your company have a formal environmental policy?',
                'category' => 'environmental',
                'type' => 'yes_no',
                'options' => json_encode(['Yes', 'No']),
                'weight' => 3,
                'sort_order' => 1,
                'help_text' => 'A formal environmental policy demonstrates commitment to environmental responsibility.',
            ],
            [
                'question_text' => 'What percentage of your energy consumption comes from renewable sources?',
                'category' => 'environmental',
                'type' => 'multiple_choice',
                'options' => json_encode(['0%', '1-25%', '26-50%', '51-75%', '76-100%']),
                'weight' => 4,
                'sort_order' => 2,
                'help_text' => 'Renewable energy usage indicates environmental commitment and sustainability.',
            ],
            [
                'question_text' => 'Does your company measure and track its carbon footprint?',
                'category' => 'environmental',
                'type' => 'yes_no',
                'options' => json_encode(['Yes', 'No']),
                'weight' => 3,
                'sort_order' => 3,
                'help_text' => 'Carbon footprint tracking is essential for environmental impact management.',
            ],
            [
                'question_text' => 'Rate your company\'s waste reduction and recycling efforts',
                'category' => 'environmental',
                'type' => 'scale',
                'options' => json_encode(['1 - No efforts', '2 - Minimal efforts', '3 - Moderate efforts', '4 - Significant efforts', '5 - Comprehensive program']),
                'weight' => 2,
                'sort_order' => 4,
                'help_text' => 'Waste reduction demonstrates environmental stewardship and resource efficiency.',
            ],
            [
                'question_text' => 'Does your company have water conservation measures in place?',
                'category' => 'environmental',
                'type' => 'yes_no',
                'options' => json_encode(['Yes', 'No']),
                'weight' => 2,
                'sort_order' => 5,
                'help_text' => 'Water conservation is crucial for environmental sustainability.',
            ],

            // Social Questions
            [
                'question_text' => 'What is the gender diversity ratio in your leadership team?',
                'category' => 'social',
                'type' => 'multiple_choice',
                'options' => json_encode(['Less than 20% women', '20-30% women', '31-40% women', '41-50% women', 'More than 50% women']),
                'weight' => 4,
                'sort_order' => 6,
                'help_text' => 'Gender diversity in leadership promotes inclusive decision-making.',
            ],
            [
                'question_text' => 'Does your company have a formal diversity and inclusion policy?',
                'category' => 'social',
                'type' => 'yes_no',
                'options' => json_encode(['Yes', 'No']),
                'weight' => 3,
                'sort_order' => 7,
                'help_text' => 'Formal D&I policies demonstrate commitment to workplace equality.',
            ],
            [
                'question_text' => 'Rate your employee satisfaction and retention efforts',
                'category' => 'social',
                'type' => 'scale',
                'options' => json_encode(['1 - Poor', '2 - Below average', '3 - Average', '4 - Good', '5 - Excellent']),
                'weight' => 3,
                'sort_order' => 8,
                'help_text' => 'Employee satisfaction indicates positive workplace culture and social responsibility.',
            ],
            [
                'question_text' => 'Does your company provide professional development and training opportunities?',
                'category' => 'social',
                'type' => 'yes_no',
                'options' => json_encode(['Yes', 'No']),
                'weight' => 2,
                'sort_order' => 9,
                'help_text' => 'Professional development shows investment in employee growth and well-being.',
            ],
            [
                'question_text' => 'Does your company engage in community outreach or social impact initiatives?',
                'category' => 'social',
                'type' => 'yes_no',
                'options' => json_encode(['Yes', 'No']),
                'weight' => 3,
                'sort_order' => 10,
                'help_text' => 'Community engagement demonstrates social responsibility and positive impact.',
            ],
            [
                'question_text' => 'Rate your company\'s commitment to fair labor practices',
                'category' => 'social',
                'type' => 'scale',
                'options' => json_encode(['1 - Poor', '2 - Below average', '3 - Average', '4 - Good', '5 - Excellent']),
                'weight' => 4,
                'sort_order' => 11,
                'help_text' => 'Fair labor practices are fundamental to social responsibility.',
            ],

            // Governance Questions
            [
                'question_text' => 'Does your company have an independent board of directors?',
                'category' => 'governance',
                'type' => 'yes_no',
                'options' => json_encode(['Yes', 'No']),
                'weight' => 4,
                'sort_order' => 12,
                'help_text' => 'Independent board oversight ensures proper corporate governance.',
            ],
            [
                'question_text' => 'Rate your company\'s transparency in financial reporting',
                'category' => 'governance',
                'type' => 'scale',
                'options' => json_encode(['1 - Poor', '2 - Below average', '3 - Average', '4 - Good', '5 - Excellent']),
                'weight' => 4,
                'sort_order' => 13,
                'help_text' => 'Financial transparency is crucial for stakeholder trust and good governance.',
            ],
            [
                'question_text' => 'Does your company have a formal code of ethics and conduct?',
                'category' => 'governance',
                'type' => 'yes_no',
                'options' => json_encode(['Yes', 'No']),
                'weight' => 3,
                'sort_order' => 14,
                'help_text' => 'A code of ethics establishes standards for ethical business conduct.',
            ],
            [
                'question_text' => 'Does your company have anti-corruption and anti-bribery policies?',
                'category' => 'governance',
                'type' => 'yes_no',
                'options' => json_encode(['Yes', 'No']),
                'weight' => 4,
                'sort_order' => 15,
                'help_text' => 'Anti-corruption policies are essential for ethical business operations.',
            ],
            [
                'question_text' => 'Rate your company\'s data privacy and security measures',
                'category' => 'governance',
                'type' => 'scale',
                'options' => json_encode(['1 - Poor', '2 - Below average', '3 - Average', '4 - Good', '5 - Excellent']),
                'weight' => 3,
                'sort_order' => 16,
                'help_text' => 'Data privacy and security are critical governance responsibilities.',
            ],
            [
                'question_text' => 'Does your company conduct regular risk assessments?',
                'category' => 'governance',
                'type' => 'yes_no',
                'options' => json_encode(['Yes', 'No']),
                'weight' => 2,
                'sort_order' => 17,
                'help_text' => 'Regular risk assessments demonstrate proactive governance and risk management.',
            ],
        ];

        foreach ($questions as $question) {
            DB::table('esg_questions')->insert([
                'question_text' => $question['question_text'],
                'category' => $question['category'],
                'type' => $question['type'],
                'options' => $question['options'],
                'weight' => $question['weight'],
                'sort_order' => $question['sort_order'],
                'help_text' => $question['help_text'],
                'is_required' => true,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
