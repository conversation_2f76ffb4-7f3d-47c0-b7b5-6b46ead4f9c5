<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\InvestorProfile;
use App\Models\StartupProfile;
use App\Models\EsgQuestion;
use App\Models\EsgResponse;
use App\Models\InterestRequest;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class DemoDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating demo data...');

        // Create demo taxonomies if they don't exist
        $this->createDemoTaxonomies();

        // Create demo investors with profiles
        $this->createDemoInvestors();

        // Create demo startups with profiles
        $this->createDemoStartups();

        // Create demo ESG responses
        $this->createDemoEsgResponses();

        // Create demo interest requests
        $this->createDemoInterestRequests();

        $this->command->info('Demo data created successfully!');
    }

    private function createDemoTaxonomies()
    {
        $categories = [
            ['name' => 'FinTech', 'description' => 'Financial Technology', 'type' => 'category'],
            ['name' => 'HealthTech', 'description' => 'Healthcare Technology', 'type' => 'category'],
            ['name' => 'EdTech', 'description' => 'Education Technology', 'type' => 'category'],
            ['name' => 'CleanTech', 'description' => 'Clean Technology', 'type' => 'category'],
            ['name' => 'AI/ML', 'description' => 'Artificial Intelligence & Machine Learning', 'type' => 'category'],
        ];

        $industries = [
            ['name' => 'Software', 'description' => 'Software Development', 'type' => 'industry'],
            ['name' => 'Healthcare', 'description' => 'Healthcare Services', 'type' => 'industry'],
            ['name' => 'Finance', 'description' => 'Financial Services', 'type' => 'industry'],
            ['name' => 'Energy', 'description' => 'Energy & Utilities', 'type' => 'industry'],
            ['name' => 'Education', 'description' => 'Education Services', 'type' => 'industry'],
        ];

        $technologies = [
            ['name' => 'React', 'description' => 'React Framework', 'type' => 'technology'],
            ['name' => 'Laravel', 'description' => 'Laravel Framework', 'type' => 'technology'],
            ['name' => 'Python', 'description' => 'Python Programming', 'type' => 'technology'],
            ['name' => 'Machine Learning', 'description' => 'ML Technologies', 'type' => 'technology'],
            ['name' => 'Blockchain', 'description' => 'Blockchain Technology', 'type' => 'technology'],
        ];

        foreach (array_merge($categories, $industries, $technologies) as $taxonomy) {
            Taxonomy::firstOrCreate(
                ['name' => $taxonomy['name'], 'type' => $taxonomy['type']],
                $taxonomy
            );
        }
    }

    private function createDemoInvestors()
    {
        $investors = [
            [
                'name' => 'Sarah Johnson',
                'email' => '<EMAIL>',
                'investment_range_min' => 50000,
                'investment_range_max' => 500000,
                'preferred_sectors' => ['FinTech', 'HealthTech'],
                'bio' => 'Experienced angel investor with 15+ years in tech startups.',
                'total_investments' => 1250000,
                'active_interests' => 8,
                'startups_discovered' => 45,
                'pending_reviews' => 3
            ],
            [
                'name' => 'Michael Chen',
                'email' => '<EMAIL>',
                'investment_range_min' => 100000,
                'investment_range_max' => 1000000,
                'preferred_sectors' => ['AI/ML', 'CleanTech'],
                'bio' => 'Venture capitalist focused on AI and sustainability.',
                'total_investments' => 2750000,
                'active_interests' => 12,
                'startups_discovered' => 67,
                'pending_reviews' => 5
            ],
            [
                'name' => 'Emma Rodriguez',
                'email' => '<EMAIL>',
                'investment_range_min' => 25000,
                'investment_range_max' => 250000,
                'preferred_sectors' => ['EdTech', 'HealthTech'],
                'bio' => 'Impact investor specializing in education and healthcare.',
                'total_investments' => 875000,
                'active_interests' => 6,
                'startups_discovered' => 32,
                'pending_reviews' => 2
            ]
        ];

        foreach ($investors as $investorData) {
            $user = User::firstOrCreate(
                ['email' => $investorData['email']],
                [
                    'name' => $investorData['name'],
                    'password' => Hash::make('password123'),
                    'email_verified_at' => now(),
                    'role' => 'investor',
                    'city' => 'San Francisco',
                    'country' => 'United States',
                ]
            );

            // Create investor profile
            $profile = InvestorProfile::updateOrCreate(
                ['user_id' => $user->id],
                [
                    'investment_budget_min' => $investorData['investment_range_min'],
                    'investment_budget_max' => $investorData['investment_range_max'],
                    'investment_preferences' => ['esg_focused' => true],
                    'bio' => $investorData['bio'],
                    'risk_tolerance' => 'medium',
                    'investment_experience' => 'experienced',
                    'profile_completed' => true,
                ]
            );

            // Attach taxonomies (categories) to the user
            $categoryNames = $investorData['preferred_sectors'];
            $categoryIds = Taxonomy::whereIn('name', $categoryNames)
                ->where('type', 'category')
                ->pluck('id')
                ->toArray();

            if (!empty($categoryIds)) {
                $user->taxonomies()->syncWithoutDetaching($categoryIds);
            }
        }
    }

    private function createDemoStartups()
    {
        $startups = [
            [
                'name' => 'David Kim',
                'email' => '<EMAIL>',
                'company_name' => 'GreenFinTech Solutions',
                'description' => 'Sustainable financial technology platform for eco-conscious investments.',
                'industry' => 'FinTech',
                'funding_stage' => 'series_a',
                'funding_amount' => 2500000,
                'team_size' => 15,
                'founded_year' => 2021,
                'esg_score' => 85,
                'profile_completion' => 95,
                'investor_interests' => 7,
                'profile_views' => 234
            ],
            [
                'name' => 'Lisa Wang',
                'email' => '<EMAIL>',
                'company_name' => 'HealthAI Diagnostics',
                'description' => 'AI-powered medical diagnostics for early disease detection.',
                'industry' => 'HealthTech',
                'funding_stage' => 'seed',
                'funding_amount' => 750000,
                'team_size' => 8,
                'founded_year' => 2022,
                'esg_score' => 78,
                'profile_completion' => 87,
                'investor_interests' => 12,
                'profile_views' => 189
            ],
            [
                'name' => 'Carlos Martinez',
                'email' => '<EMAIL>',
                'company_name' => 'EduLearn Platform',
                'description' => 'Personalized learning platform using adaptive AI technology.',
                'industry' => 'EdTech',
                'funding_stage' => 'pre_seed',
                'funding_amount' => 300000,
                'team_size' => 5,
                'founded_year' => 2023,
                'esg_score' => 72,
                'profile_completion' => 76,
                'investor_interests' => 4,
                'profile_views' => 156
            ],
            [
                'name' => 'Priya Patel',
                'email' => '<EMAIL>',
                'company_name' => 'CleanEnergy Innovations',
                'description' => 'Smart grid solutions for renewable energy optimization.',
                'industry' => 'CleanTech',
                'funding_stage' => 'series_a',
                'funding_amount' => 3200000,
                'team_size' => 22,
                'founded_year' => 2020,
                'esg_score' => 92,
                'profile_completion' => 100,
                'investor_interests' => 15,
                'profile_views' => 312
            ]
        ];

        foreach ($startups as $startupData) {
            $user = User::firstOrCreate(
                ['email' => $startupData['email']],
                [
                    'name' => $startupData['name'],
                    'password' => Hash::make('password123'),
                    'email_verified_at' => now(),
                    'role' => 'startup',
                    'city' => 'San Francisco',
                    'country' => 'United States',
                ]
            );

            // Role is already set in the user creation above

            // Create startup profile
            StartupProfile::updateOrCreate(
                ['user_id' => $user->id],
                [
                    'company_name' => $startupData['company_name'],
                    'company_description' => $startupData['description'],
                    'funding_stage' => $startupData['funding_stage'],
                    'funding_amount_sought' => $startupData['funding_amount'],
                    'employee_count' => $startupData['team_size'],
                    'founding_date' => Carbon::create($startupData['founded_year'], 1, 1),
                    'esg_score' => $startupData['esg_score'],
                    'profile_completed' => $startupData['profile_completion'] > 80,
                    'esg_completed' => $startupData['esg_score'] > 0,
                    'business_model' => [$startupData['industry']],
                ]
            );
        }
    }

    private function createDemoEsgResponses()
    {
        $startups = User::where('role', 'startup')->with('startupProfile')->get();
        $esgQuestions = EsgQuestion::all();

        foreach ($startups as $startup) {
            $startupProfile = $startup->startupProfile;
            if (!$startupProfile) continue;

            foreach ($esgQuestions->take(5) as $question) {
                EsgResponse::updateOrCreate(
                    [
                        'startup_profile_id' => $startupProfile->id,
                        'esg_question_id' => $question->id,
                    ],
                    [
                        'response_value' => $this->getRandomEsgResponse($question->type),
                        'score' => rand(60, 100),
                    ]
                );
            }
        }
    }

    private function createDemoInterestRequests()
    {
        $investors = User::where('role', 'investor')->get();
        $startups = User::where('role', 'startup')->get();

        $statuses = ['pending', 'approved', 'rejected'];
        $created = [];

        // Create 15 demo interest requests
        $attempts = 0;
        while (count($created) < 15 && $attempts < 50) {
            $investor = $investors->random();
            $startup = $startups->random();
            $key = $investor->id . '-' . $startup->id;

            if (!in_array($key, $created)) {
                InterestRequest::updateOrCreate([
                    'requester_id' => $investor->id,
                    'target_id' => $startup->id,
                    'type' => 'investment_interest',
                ], [
                    'message' => $this->getRandomInterestMessage(),
                    'status' => $statuses[array_rand($statuses)],
                    'created_at' => Carbon::now()->subDays(rand(1, 30)),
                    'updated_at' => Carbon::now()->subDays(rand(0, 15)),
                ]);
                $created[] = $key;
            }
            $attempts++;
        }
    }

    private function getRandomEsgResponse($type)
    {
        $responses = [
            'yes_no' => ['Yes', 'No'],
            'multiple_choice' => ['Option A', 'Option B', 'Option C'],
            'scale' => [1, 2, 3, 4, 5],
            'text' => [
                'We have implemented comprehensive environmental policies.',
                'Our company prioritizes social responsibility in all operations.',
                'We maintain transparent governance practices.',
                'Sustainability is core to our business model.',
                'We actively measure and reduce our carbon footprint.',
            ],
        ];

        return $responses[$type][array_rand($responses[$type])];
    }

    private function getRandomInterestMessage()
    {
        $messages = [
            'I am very interested in your innovative approach to sustainable technology. Would love to discuss potential investment opportunities.',
            'Your company aligns perfectly with our investment thesis. Let\'s schedule a meeting to explore partnership possibilities.',
            'Impressed by your team\'s background and market traction. Interested in learning more about your funding round.',
            'Your ESG scores and business model are compelling. Would like to discuss investment terms and due diligence process.',
            'Great work on building a scalable solution. Our fund is actively looking for opportunities in your sector.',
        ];

        return $messages[array_rand($messages)];
    }
}
