<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;

class TaxonomySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->createIndustryKeywords();
        $this->createTechnologyKeywords();
        $this->createBusinessModelTypes();
        $this->createMarketTypes();
        $this->createFundingStages();
        $this->createBusinessCategories();
    }

    private function createIndustryKeywords(): void
    {
        $industries = [
            'FinTech' => 'Financial Technology and Services',
            'HealthTech' => 'Healthcare Technology and Medical Innovation',
            'EdTech' => 'Education Technology and Learning Platforms',
            'CleanTech' => 'Clean Technology and Renewable Energy',
            'AgriTech' => 'Agricultural Technology and Food Innovation',
            'PropTech' => 'Property Technology and Real Estate',
            'RetailTech' => 'Retail Technology and E-commerce',
            'LogisticsTech' => 'Logistics and Supply Chain Technology',
            'TravelTech' => 'Travel and Hospitality Technology',
            'SportsTech' => 'Sports Technology and Fitness',
            'FoodTech' => 'Food Technology and Delivery',
            'InsurTech' => 'Insurance Technology',
            'LegalTech' => 'Legal Technology and Services',
            'HRTech' => 'Human Resources Technology',
            'MarketingTech' => 'Marketing and Advertising Technology',
        ];

        foreach ($industries as $name => $description) {
            Taxonomy::create([
                'name' => $name,
                'slug' => strtolower(str_replace(['Tech', ' '], ['tech', '-'], $name)),
                'type' => 'industry',
                'description' => $description,
                'sort_order' => 0,
            ]);
        }
    }

    private function createTechnologyKeywords(): void
    {
        $technologies = [
            'Artificial Intelligence' => 'AI and Machine Learning Technologies',
            'Machine Learning' => 'ML Algorithms and Data Science',
            'Blockchain' => 'Distributed Ledger Technology',
            'IoT' => 'Internet of Things and Connected Devices',
            'Cloud Computing' => 'Cloud Infrastructure and Services',
            'Mobile Apps' => 'Mobile Application Development',
            'Web Development' => 'Web Applications and Platforms',
            'Data Analytics' => 'Big Data and Analytics Platforms',
            'Cybersecurity' => 'Security and Privacy Technologies',
            'AR/VR' => 'Augmented and Virtual Reality',
            'Robotics' => 'Robotics and Automation',
            'API' => 'Application Programming Interfaces',
            'SaaS' => 'Software as a Service',
            'DevOps' => 'Development and Operations',
            'Microservices' => 'Microservices Architecture',
        ];

        foreach ($technologies as $name => $description) {
            Taxonomy::create([
                'name' => $name,
                'slug' => strtolower(str_replace(['/', ' '], ['-', '-'], $name)),
                'type' => 'technology',
                'description' => $description,
                'sort_order' => 0,
            ]);
        }
    }

    private function createBusinessModelTypes(): void
    {
        $models = [
            'B2B' => 'Business to Business',
            'B2C' => 'Business to Consumer',
            'B2B2C' => 'Business to Business to Consumer',
            'Marketplace' => 'Multi-sided Marketplace Platform',
            'SaaS' => 'Software as a Service',
            'Subscription' => 'Subscription-based Revenue Model',
            'Freemium' => 'Free with Premium Features',
            'E-commerce' => 'Electronic Commerce Platform',
            'On-demand' => 'On-demand Service Platform',
            'Platform' => 'Technology Platform Business',
            'Licensing' => 'Technology Licensing Model',
            'Franchise' => 'Franchise Business Model',
            'Direct Sales' => 'Direct to Consumer Sales',
            'Affiliate' => 'Affiliate Marketing Model',
            'Commission' => 'Commission-based Revenue',
        ];

        foreach ($models as $name => $description) {
            Taxonomy::create([
                'name' => $name,
                'slug' => strtolower(str_replace(' ', '-', $name)),
                'type' => 'model',
                'description' => $description,
                'sort_order' => 0,
            ]);
        }
    }

    private function createMarketTypes(): void
    {
        $markets = [
            'Global' => 'Global Market Focus',
            'North America' => 'North American Market',
            'Europe' => 'European Market',
            'Asia Pacific' => 'Asia Pacific Region',
            'Latin America' => 'Latin American Market',
            'Middle East' => 'Middle East and Africa',
            'Enterprise' => 'Enterprise Market Segment',
            'SMB' => 'Small and Medium Business',
            'Consumer' => 'Consumer Market',
            'Government' => 'Government and Public Sector',
            'Healthcare' => 'Healthcare Market Vertical',
            'Education' => 'Education Market Vertical',
            'Financial Services' => 'Financial Services Vertical',
            'Retail' => 'Retail Market Vertical',
            'Manufacturing' => 'Manufacturing Industry',
        ];

        foreach ($markets as $name => $description) {
            Taxonomy::create([
                'name' => $name,
                'slug' => strtolower(str_replace(' ', '-', $name)),
                'type' => 'market',
                'description' => $description,
                'sort_order' => 0,
            ]);
        }
    }

    private function createFundingStages(): void
    {
        $stages = [
            'Pre-Seed' => 'Pre-Seed Funding Stage',
            'Seed' => 'Seed Funding Round',
            'Series A' => 'Series A Funding Round',
            'Series B' => 'Series B Funding Round',
            'Series C' => 'Series C Funding Round',
            'Series D+' => 'Series D and Later Rounds',
            'Bridge' => 'Bridge Financing',
            'Convertible Note' => 'Convertible Note Financing',
            'Debt Financing' => 'Debt-based Financing',
            'Grant' => 'Government or Foundation Grant',
            'Crowdfunding' => 'Crowdfunding Campaign',
            'Revenue-based' => 'Revenue-based Financing',
            'IPO Ready' => 'Initial Public Offering Ready',
            'Acquisition' => 'Acquisition Target',
            'Bootstrap' => 'Self-funded/Bootstrapped',
        ];

        foreach ($stages as $name => $description) {
            Taxonomy::create([
                'name' => $name,
                'slug' => strtolower(str_replace([' ', '+'], ['-', 'plus'], $name)),
                'type' => 'stage',
                'description' => $description,
                'sort_order' => 0,
            ]);
        }
    }

    private function createBusinessCategories(): void
    {
        $categories = [
            'Software' => 'Software Development and Technology',
            'Hardware' => 'Hardware and Physical Products',
            'Services' => 'Professional and Business Services',
            'Platform' => 'Technology Platform and Infrastructure',
            'Marketplace' => 'Online Marketplace and Aggregation',
            'Analytics' => 'Data Analytics and Business Intelligence',
            'Security' => 'Cybersecurity and Privacy Solutions',
            'Communication' => 'Communication and Collaboration Tools',
            'Productivity' => 'Productivity and Workflow Tools',
            'Entertainment' => 'Entertainment and Media Technology',
            'Social' => 'Social Networking and Community',
            'Gaming' => 'Gaming and Interactive Entertainment',
            'Content' => 'Content Creation and Management',
            'Infrastructure' => 'Technology Infrastructure and Tools',
            'Integration' => 'System Integration and APIs',
        ];

        foreach ($categories as $name => $description) {
            Taxonomy::create([
                'name' => $name,
                'slug' => strtolower(str_replace(' ', '-', $name)),
                'type' => 'category',
                'description' => $description,
                'sort_order' => 0,
            ]);
        }
    }
}
