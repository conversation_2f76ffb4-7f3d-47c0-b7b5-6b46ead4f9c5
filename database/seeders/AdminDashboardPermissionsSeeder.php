<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class AdminDashboardPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin dashboard permissions if they don't exist
        $adminPermissions = [
            'admin dashboard access',
            'admin user management',
            'admin subscription management',
            'admin financial management',
            'admin product management',
        ];

        foreach ($adminPermissions as $permission) {
            Permission::firstOrCreate([
                'name' => $permission,
                'guard_name' => 'web'
            ], [
                'module_name' => 'admin',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // Give admin permissions to admin and super-admin roles
        $adminRole = Role::where('name', 'admin')->where('guard_name', 'web')->first();
        $superAdminRole = Role::where('name', 'super-admin')->where('guard_name', 'web')->first();

        if ($adminRole) {
            $adminRole->givePermissionTo($adminPermissions);
        }

        if ($superAdminRole) {
            $superAdminRole->givePermissionTo($adminPermissions);
        }

        $this->command->info('Admin dashboard permissions created and assigned successfully.');
    }
}
