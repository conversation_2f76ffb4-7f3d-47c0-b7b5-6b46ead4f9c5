<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class InvestmentPlatformRolesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create new roles for investment platform
        $roles = [
            'investor',
            'startup',
            'analyst',
        ];

        foreach ($roles as $roleName) {
            // Create for web guard
            Role::firstOrCreate([
                'name' => $roleName,
                'guard_name' => 'web',
            ]);

            // Create for sanctum guard (API)
            Role::firstOrCreate([
                'name' => $roleName,
                'guard_name' => 'sanctum',
            ]);
        }

        // Create investment platform permissions
        $permissions = [
            // Investor permissions
            ['name' => 'investor profile manage', 'module_name' => 'investor'],
            ['name' => 'investor discover startups', 'module_name' => 'investor'],
            ['name' => 'investor submit interest', 'module_name' => 'investor'],
            ['name' => 'investor view requests', 'module_name' => 'investor'],

            // Startup permissions
            ['name' => 'startup profile manage', 'module_name' => 'startup'],
            ['name' => 'startup discover investors', 'module_name' => 'startup'],
            ['name' => 'startup submit funding request', 'module_name' => 'startup'],
            ['name' => 'startup esg questionnaire', 'module_name' => 'startup'],
            ['name' => 'startup view requests', 'module_name' => 'startup'],

            // Analyst permissions
            ['name' => 'analyst view all profiles', 'module_name' => 'analyst'],
            ['name' => 'analyst manage requests', 'module_name' => 'analyst'],
            ['name' => 'analyst approve requests', 'module_name' => 'analyst'],
            ['name' => 'analyst manage categories', 'module_name' => 'analyst'],
            ['name' => 'analyst view analytics', 'module_name' => 'analyst'],

            // Category permissions
            ['name' => 'category select', 'module_name' => 'category'],
            ['name' => 'category view', 'module_name' => 'category'],
        ];

        foreach ($permissions as $permission) {
            // Create for web guard
            Permission::firstOrCreate([
                'name' => $permission['name'],
                'guard_name' => 'web',
            ], [
                'module_name' => $permission['module_name'],
            ]);

            // Create for sanctum guard (API)
            Permission::firstOrCreate([
                'name' => $permission['name'],
                'guard_name' => 'sanctum',
            ], [
                'module_name' => $permission['module_name'],
            ]);
        }

        // Assign permissions to roles
        $this->assignPermissionsToRoles();
    }

    private function assignPermissionsToRoles(): void
    {
        // Investor role permissions
        $investorWeb = Role::where(['name' => 'investor', 'guard_name' => 'web'])->first();
        $investorSanctum = Role::where(['name' => 'investor', 'guard_name' => 'sanctum'])->first();

        $investorPermissions = [
            'investor profile manage',
            'investor discover startups',
            'investor submit interest',
            'investor view requests',
            'category select',
            'category view',
        ];

        if ($investorWeb) {
            $investorWeb->givePermissionTo(
                Permission::whereIn('name', $investorPermissions)
                    ->where('guard_name', 'web')
                    ->get()
            );
        }

        if ($investorSanctum) {
            $investorSanctum->givePermissionTo(
                Permission::whereIn('name', $investorPermissions)
                    ->where('guard_name', 'sanctum')
                    ->get()
            );
        }

        // Startup role permissions
        $startupWeb = Role::where(['name' => 'startup', 'guard_name' => 'web'])->first();
        $startupSanctum = Role::where(['name' => 'startup', 'guard_name' => 'sanctum'])->first();

        $startupPermissions = [
            'startup profile manage',
            'startup discover investors',
            'startup submit funding request',
            'startup esg questionnaire',
            'startup view requests',
            'category select',
            'category view',
        ];

        if ($startupWeb) {
            $startupWeb->givePermissionTo(
                Permission::whereIn('name', $startupPermissions)
                    ->where('guard_name', 'web')
                    ->get()
            );
        }

        if ($startupSanctum) {
            $startupSanctum->givePermissionTo(
                Permission::whereIn('name', $startupPermissions)
                    ->where('guard_name', 'sanctum')
                    ->get()
            );
        }

        // Analyst role permissions
        $analystWeb = Role::where(['name' => 'analyst', 'guard_name' => 'web'])->first();
        $analystSanctum = Role::where(['name' => 'analyst', 'guard_name' => 'sanctum'])->first();

        $analystPermissions = [
            'analyst view all profiles',
            'analyst manage requests',
            'analyst approve requests',
            'analyst manage categories',
            'analyst view analytics',
            'category view',
            'admin dashboard access',
        ];

        if ($analystWeb) {
            $analystWeb->givePermissionTo(
                Permission::whereIn('name', $analystPermissions)
                    ->where('guard_name', 'web')
                    ->get()
            );
        }

        if ($analystSanctum) {
            $analystSanctum->givePermissionTo(
                Permission::whereIn('name', $analystPermissions)
                    ->where('guard_name', 'sanctum')
                    ->get()
            );
        }
    }
}
