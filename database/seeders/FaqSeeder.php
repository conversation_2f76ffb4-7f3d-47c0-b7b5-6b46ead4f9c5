<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Faq;
use App\Models\User;

class FaqSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get an admin user to assign as creator
        $adminUser = User::where('role', 'admin')->orWhere('role', 'super-admin')->first();
        
        if (!$adminUser) {
            $this->command->error('No admin user found. Please create an admin user first.');
            return;
        }

        $faqs = [
            // Investor FAQs
            [
                'question' => 'How do I find startups to invest in?',
                'answer' => 'You can browse startups through our discovery feature. Use filters to find companies that match your investment criteria, including industry, funding stage, and ESG scores. The platform will show you startups that align with your investment preferences.',
                'target_role' => 'investor',
                'status' => 'active',
                'category' => 'investment',
                'sort_order' => 1,
                'created_by' => $adminUser->id
            ],
            [
                'question' => 'What information do I need to complete my investor profile?',
                'answer' => 'To complete your investor profile, you need to provide your investment preferences, budget range, preferred industries, and ESG criteria. This helps us match you with relevant startups that meet your investment goals.',
                'target_role' => 'investor',
                'status' => 'active',
                'category' => 'profile',
                'sort_order' => 2,
                'created_by' => $adminUser->id
            ],
            [
                'question' => 'How does the matching algorithm work?',
                'answer' => 'Our matching algorithm considers your investment preferences, budget, industry focus, and ESG criteria to suggest relevant startups. It also takes into account the startup\'s funding needs and growth stage.',
                'target_role' => 'investor',
                'status' => 'active',
                'category' => 'matching',
                'sort_order' => 3,
                'created_by' => $adminUser->id
            ],

            // Startup FAQs
            [
                'question' => 'How do I create an effective startup profile?',
                'answer' => 'Include detailed information about your company, business model, team, funding needs, and growth plans. Upload a compelling pitch deck and ensure your ESG assessment is complete. The more comprehensive your profile, the better your chances of attracting investors.',
                'target_role' => 'startup',
                'status' => 'active',
                'category' => 'profile',
                'sort_order' => 1,
                'created_by' => $adminUser->id
            ],
            [
                'question' => 'What is the ESG assessment and why is it important?',
                'answer' => 'The ESG (Environmental, Social, Governance) assessment evaluates your company\'s sustainability practices. It\'s important because many investors prioritize ESG-compliant companies. A strong ESG score can significantly improve your chances of securing investment.',
                'target_role' => 'startup',
                'status' => 'active',
                'category' => 'general',
                'sort_order' => 2,
                'created_by' => $adminUser->id
            ],
            [
                'question' => 'How do I improve my startup\'s visibility to investors?',
                'answer' => 'Complete your profile thoroughly, maintain an active presence on the platform, respond promptly to investor inquiries, and keep your information up-to-date. Featured startups get more visibility, which you can achieve through our premium subscription.',
                'target_role' => 'startup',
                'status' => 'active',
                'category' => 'matching',
                'sort_order' => 3,
                'created_by' => $adminUser->id
            ],

            // General FAQs (both roles)
            [
                'question' => 'How do I manage my subscription?',
                'answer' => 'You can manage your subscription from your account settings. You can upgrade, downgrade, or cancel your subscription at any time. Changes take effect at the next billing cycle. You can also view your billing history and download invoices.',
                'target_role' => 'both',
                'status' => 'active',
                'category' => 'subscription',
                'sort_order' => 1,
                'created_by' => $adminUser->id
            ],
            [
                'question' => 'How do I contact support?',
                'answer' => 'You can contact our support team via <NAME_EMAIL> or through the live chat feature available on all pages. We typically respond within 24 hours during business days.',
                'target_role' => 'both',
                'status' => 'active',
                'category' => 'support',
                'sort_order' => 2,
                'created_by' => $adminUser->id
            ],
            [
                'question' => 'Is my data secure on the platform?',
                'answer' => 'Yes, we take data security very seriously. All data is encrypted in transit and at rest. We comply with GDPR and other data protection regulations. Your sensitive information is only shared with parties you explicitly approve.',
                'target_role' => 'both',
                'status' => 'active',
                'category' => 'general',
                'sort_order' => 3,
                'created_by' => $adminUser->id
            ],
            [
                'question' => 'How do I update my account information?',
                'answer' => 'You can update your account information by going to your profile settings. Make sure to keep your contact information current so you don\'t miss important communications.',
                'target_role' => 'both',
                'status' => 'active',
                'category' => 'account',
                'sort_order' => 4,
                'created_by' => $adminUser->id
            ],
            [
                'question' => 'What payment methods do you accept?',
                'answer' => 'We accept all major credit cards (Visa, MasterCard, American Express) and bank transfers. All payments are processed securely through Stripe. You can manage your payment methods in your account settings.',
                'target_role' => 'both',
                'status' => 'active',
                'category' => 'subscription',
                'sort_order' => 5,
                'created_by' => $adminUser->id
            ],
        ];

        foreach ($faqs as $faqData) {
            Faq::create($faqData);
        }

        $this->command->info('Sample FAQs created successfully!');
    }
}
