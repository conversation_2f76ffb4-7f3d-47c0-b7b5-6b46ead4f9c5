<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class AdminPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin permissions for subscription system
        $permissions = [
            // User Management
            ['name' => 'manage users', 'module_name' => 'admin_user'],
            ['name' => 'view user details', 'module_name' => 'admin_user'],
            ['name' => 'lock user accounts', 'module_name' => 'admin_user'],
            ['name' => 'unlock user accounts', 'module_name' => 'admin_user'],

            // Subscription Management
            ['name' => 'manage subscriptions', 'module_name' => 'admin_subscription'],
            ['name' => 'view subscription analytics', 'module_name' => 'admin_subscription'],
            ['name' => 'cancel subscriptions', 'module_name' => 'admin_subscription'],
            ['name' => 'pause subscriptions', 'module_name' => 'admin_subscription'],
            ['name' => 'modify subscriptions', 'module_name' => 'admin_subscription'],

            // Financial Management
            ['name' => 'view financial data', 'module_name' => 'admin_financial'],
            ['name' => 'process refunds', 'module_name' => 'admin_financial'],
            ['name' => 'view payment history', 'module_name' => 'admin_financial'],
            ['name' => 'manage invoices', 'module_name' => 'admin_financial'],

            // Product Management
            ['name' => 'manage subscription products', 'module_name' => 'subscription_product'],
            ['name' => 'create subscription products', 'module_name' => 'subscription_product'],
            ['name' => 'edit subscription products', 'module_name' => 'subscription_product'],
            ['name' => 'delete subscription products', 'module_name' => 'subscription_product'],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate([
                'name' => $permission['name'],
                'module_name' => $permission['module_name']
            ]);
        }

        // Create admin roles
        $superAdminRole = Role::firstOrCreate(['name' => 'Super Admin']);
        $adminRole = Role::firstOrCreate(['name' => 'Admin']);
        $financialAdminRole = Role::firstOrCreate(['name' => 'Financial Admin']);
        $supportAdminRole = Role::firstOrCreate(['name' => 'Support Admin']);

        // Assign permissions to roles
        $superAdminRole->givePermissionTo(Permission::all());

        $adminRole->givePermissionTo([
            'manage users',
            'view user details',
            'lock user accounts',
            'unlock user accounts',
            'manage subscriptions',
            'view subscription analytics',
            'cancel subscriptions',
            'pause subscriptions',
            'view financial data',
            'view payment history',
            'manage subscription products',
            'create subscription products',
            'edit subscription products',
        ]);

        $financialAdminRole->givePermissionTo([
            'view financial data',
            'process refunds',
            'view payment history',
            'manage invoices',
            'view subscription analytics',
        ]);

        $supportAdminRole->givePermissionTo([
            'view user details',
            'manage subscriptions',
            'cancel subscriptions',
            'pause subscriptions',
            'view payment history',
        ]);

        $this->command->info('Admin permissions and roles created successfully!');
    }
}
