<?php

namespace Database\Seeders;

use App\Models\SubscriptionProduct;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class InvestmentPlatformSubscriptionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Investor Subscription Plans
        $investorPlans = [
            [
                'name' => 'Investor Basic',
                'description' => 'Essential features for individual investors to discover and connect with startups.',
                'price' => 29.99,
                'billing_cycle' => 'monthly',
                'features' => [
                    'Browse up to 50 startups per month',
                    'Send up to 5 interest requests per month',
                    'Basic startup profiles and ESG scores',
                    'Email support',
                    'Standard search filters',
                ],
                'limits' => [
                    'startup_views_per_month' => 50,
                    'interest_requests_per_month' => 5,
                    'advanced_analytics' => false,
                    'priority_support' => false,
                    'custom_reports' => false,
                ],
                'is_active' => true,
                'sort_order' => 1,
                'target_role' => 'investor',
            ],
            [
                'name' => 'Investor Premium',
                'description' => 'Advanced features for serious investors with enhanced discovery and analytics capabilities.',
                'price' => 79.99,
                'billing_cycle' => 'monthly',
                'features' => [
                    'Unlimited startup browsing',
                    'Send up to 25 interest requests per month',
                    'Detailed startup analytics and ESG insights',
                    'Advanced search and filtering options',
                    'Priority email support',
                    'Investment tracking dashboard',
                    'Custom investment reports',
                ],
                'limits' => [
                    'startup_views_per_month' => -1, // unlimited
                    'interest_requests_per_month' => 25,
                    'advanced_analytics' => true,
                    'priority_support' => true,
                    'custom_reports' => true,
                ],
                'is_active' => true,
                'sort_order' => 2,
                'target_role' => 'investor',
            ],
            [
                'name' => 'Investor Enterprise',
                'description' => 'Complete investment platform access for institutional investors and investment firms.',
                'price' => 199.99,
                'billing_cycle' => 'monthly',
                'features' => [
                    'Unlimited startup browsing and connections',
                    'Unlimited interest requests',
                    'Full ESG analytics and reporting suite',
                    'API access for data integration',
                    'Dedicated account manager',
                    'Custom investment workflows',
                    'White-label reporting options',
                    'Priority phone and email support',
                ],
                'limits' => [
                    'startup_views_per_month' => -1, // unlimited
                    'interest_requests_per_month' => -1, // unlimited
                    'advanced_analytics' => true,
                    'priority_support' => true,
                    'custom_reports' => true,
                    'api_access' => true,
                    'dedicated_support' => true,
                ],
                'is_active' => true,
                'sort_order' => 3,
                'target_role' => 'investor',
            ],
        ];

        // Startup Subscription Plans
        $startupPlans = [
            [
                'name' => 'Startup Basic',
                'description' => 'Essential visibility for early-stage startups to connect with investors.',
                'price' => 39.99,
                'billing_cycle' => 'monthly',
                'features' => [
                    'Basic startup profile listing',
                    'Receive up to 10 investor inquiries per month',
                    'Basic ESG assessment tools',
                    'Email support',
                    'Standard profile analytics',
                ],
                'limits' => [
                    'investor_inquiries_per_month' => 10,
                    'profile_visibility' => 'basic',
                    'esg_tools' => 'basic',
                    'analytics' => 'basic',
                    'priority_listing' => false,
                ],
                'is_active' => true,
                'sort_order' => 1,
                'target_role' => 'startup',
            ],
            [
                'name' => 'Startup Growth',
                'description' => 'Enhanced features for growing startups seeking serious investment opportunities.',
                'price' => 99.99,
                'billing_cycle' => 'monthly',
                'features' => [
                    'Enhanced startup profile with media gallery',
                    'Unlimited investor inquiries',
                    'Advanced ESG assessment and scoring',
                    'Priority listing in search results',
                    'Detailed analytics and investor insights',
                    'Priority email support',
                    'Fundraising milestone tracking',
                ],
                'limits' => [
                    'investor_inquiries_per_month' => -1, // unlimited
                    'profile_visibility' => 'enhanced',
                    'esg_tools' => 'advanced',
                    'analytics' => 'detailed',
                    'priority_listing' => true,
                ],
                'is_active' => true,
                'sort_order' => 2,
                'target_role' => 'startup',
            ],
            [
                'name' => 'Startup Enterprise',
                'description' => 'Complete platform access for established startups and scale-ups seeking institutional investment.',
                'price' => 249.99,
                'billing_cycle' => 'monthly',
                'features' => [
                    'Premium startup profile with custom branding',
                    'Unlimited investor connections',
                    'Comprehensive ESG certification support',
                    'Featured placement in investor searches',
                    'Advanced analytics and market insights',
                    'Dedicated account manager',
                    'Custom fundraising campaign tools',
                    'API access for data management',
                    'Priority phone and email support',
                ],
                'limits' => [
                    'investor_inquiries_per_month' => -1, // unlimited
                    'profile_visibility' => 'premium',
                    'esg_tools' => 'comprehensive',
                    'analytics' => 'advanced',
                    'priority_listing' => true,
                    'featured_placement' => true,
                    'api_access' => true,
                    'dedicated_support' => true,
                ],
                'is_active' => true,
                'sort_order' => 3,
                'target_role' => 'startup',
            ],
        ];

        // Create investor plans
        foreach ($investorPlans as $plan) {
            $targetRole = $plan['target_role'];
            unset($plan['target_role']);
            
            SubscriptionProduct::updateOrCreate(
                ['name' => $plan['name']],
                $plan
            );
        }

        // Create startup plans
        foreach ($startupPlans as $plan) {
            $targetRole = $plan['target_role'];
            unset($plan['target_role']);
            
            SubscriptionProduct::updateOrCreate(
                ['name' => $plan['name']],
                $plan
            );
        }

        $this->command->info('Investment platform subscription products created successfully!');
    }
}
